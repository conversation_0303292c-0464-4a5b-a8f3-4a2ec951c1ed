import { createSelector } from "reselect";
import { Volt } from "omf-changepackage-components";
import { IStoreState, IObject } from "../models";
import { getValue } from "./utility";

export const getLineOfBusiness = (arg: IObject<boolean>): string =>
  Object.keys(arg)
    .reduce((acc: string[], cur: string) =>
      arg[cur] ? [...acc, cur] : acc, []
    )
    .join("_")
    .toUpperCase();

/* everything must be pure */
export namespace selectors {
  /* non-memoized selectors */

  /*
   * @param {string} property the properties of store state, e.g. "summary.lineOfBusiness.TV.New", etc.
   * @param {IStoreState} state the only arguement implicitly passed in by redux useSelector hook
   */
  export const select = (property: string) => (state: IStoreState): any => getValue(property, state);

  /* memoized selectors */
  export const lineOfBusiness = (groupType: string, businessLine: string) => () => createSelector(
    select(`summary.lineOfBusiness.${businessLine}.${groupType}`),
    (offeringGroup: Array<Volt.IProductOfferingGroup>) => offeringGroup || []
  );

  export const totalCharge = (totalType: string) => () => createSelector(
    select(`summary.${totalType}`),
    (total: Volt.IPriceDetail) => total
  );

  export const additionalCharge = (groupType: string) => () => createSelector(
    [lineOfBusiness(groupType, "TV")(), lineOfBusiness(groupType, "Internet")()],
    (...args: Array<Array<Volt.IProductOfferingGroup>>): Volt.IPriceDetail => {
      let type = "",
        charge = args
          .reduce((acc: Array<Volt.IProductOfferingGroup>, cur: Array<Volt.IProductOfferingGroup>) => [...acc, ...cur], [])
          .reduce((acc: Volt.IChargeDetail[], cur: Volt.IProductOfferingGroup) =>
            cur.additionalCharges ? [...acc, ...cur.additionalCharges] : acc, [])
          .reduce((acc: number, cur: Volt.IChargeDetail) => {
            type = cur.priceDetail.priceType;
            return acc + +cur.priceDetail.price;
          }, 0);
      return {
        price: charge,
        priceType: type
      } as Volt.IPriceDetail;
    }
  );

  export const productOfferings = (groupType: string, businessLine: string) => () => createSelector(
    lineOfBusiness(groupType, businessLine)(),
    (offeringGroup: Array<Volt.IProductOfferingGroup>) =>
      offeringGroup
        .reduce((acc: Array<Volt.IProductOffering>, cur: Volt.IProductOfferingGroup) =>
          [...acc, ...cur.productOfferings], [])
  );

  export const sortAndGroupByOfferingType = (offering: Array<Volt.IProductOffering>) => {
    offering.sort((a, b) => {
      // if (a.displayGroupKey === Volt.EDIsplayGroupKey.BASE_PROGRAMMING) return -1;
      if (a.displayGroupKey === Volt.EDIsplayGroupKey.PROMOTION) return 1;
      // if (b.displayGroupKey === Volt.EDIsplayGroupKey.BASE_PROGRAMMING) return 1;
      if (b.displayGroupKey === Volt.EDIsplayGroupKey.PROMOTION) return -1;
      return (a.sortPriority > b.sortPriority) ? 1 : -1;
    });
    return offering.reduce((h: any, obj: any) => Object.assign(h, { [obj.displayGroupKey]: (h[obj.displayGroupKey] || []).concat(obj) }), {});
  };
}
