import { Actions, EModals, Omniture, Volt, Components, ValueOf, Utils, EFlowType } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage, FormattedDate } from "react-intl";
import { useDispatch } from "react-redux";
import { IOrderConfirmation } from "../../models";

interface ComponentProps {
  offerings: Array<Volt.IProductOffering>;
  isReview: boolean;
  isCurrent: boolean;
  confirmation?: IOrderConfirmation;
}

export const Flag: React.FC<{ message: string; }> = ({
  message
}) => <span className={
  message === "new" ?
    "review-label margin-10-left bgOrange txtWhite txtSize12 txtBold txtUppercase" :
    "review-label margin-10-left bgGray txtBlack txtSize12 txtBold txtUppercase"
}>
  <FormattedMessage id={message} />
</span>;

const Charge: React.FC<{
  offer: Volt.IProductOffering,
  isCurrent?: boolean;
  supressFlag?: boolean;
}> = ({
  offer, supressFlag
}) => <>
  <p className="txtBlack clear floatL flexCenter no-margin">{offer.name || offer.id}
    <Components.Visible when={offer.displayGroupKey === Volt.EDIsplayGroupKey.ALACARTE && offer.childOfferings && offer.childOfferings.length > 0}>
      : <FormattedMessage id="CHANNELS_COUNT" values={{ value: ValueOf(offer, "childOfferings.length", 0) }} />
    </Components.Visible>
  </p>
  <Components.Visible when={!supressFlag &&
                ValueOf(offer, "state") === Volt.EOfferingState.Add ||
                ValueOf(offer, "state") === Volt.EOfferingState.Added ||
                ValueOf(offer, "state") === Volt.EOfferingState.Create ||
                ValueOf(offer, "state") === Volt.EOfferingState.NewlySelected}>
    <Flag message={"new"} />
  </Components.Visible>
  <Components.Visible when={!supressFlag &&
                ValueOf(offer, "state") === Volt.EOfferingState.Remove ||
                ValueOf(offer, "state") === Volt.EOfferingState.Removed ||
                ValueOf(offer, "state") === Volt.EOfferingState.Delete
  }>
    <Flag message={"removed"} />
  </Components.Visible>
  <Components.Visible when={
    ValueOf(offer, "displayGroupKey") === Volt.EDIsplayGroupKey.PROMOTION &&
                ValueOf(offer, "promotionDetails.expiryDate", false)
  }>
    <br />
    <span className="d-sm-block pad-5-left-xs">
      <FormattedDate value={ValueOf(offer, "promotionDetails.expiryDate", "")} format="yMMMMd" timeZone="UTC">
        {
          (expiryDate) => <FormattedMessage id="PromotionExpires" values={{ expiryDate }} />
        }
      </FormattedDate>
    </span>
  </Components.Visible>
  <Components.Visible when={ValueOf(offer, "promotionDetails.description", false)}>
    <p className="txtBlack clear floatL flexCenter no-margin">
      <span className="spacer15 d-none d-sm-block" aria-hidden="true" />
      {ValueOf(offer, "promotionDetails.description")}
      <Components.Visible when={!supressFlag &&
                        ValueOf(offer, "promotionDetails.state") === Volt.EOfferingState.Remove ||
                        ValueOf(offer, "promotionDetails.state") === Volt.EOfferingState.Removed ||
                        ValueOf(offer, "promotionDetails.state") === Volt.EOfferingState.Delete
      }>
        <Flag message={"removed"} />
      </Components.Visible>
      <Components.Visible when={!supressFlag && ValueOf(offer, "promotionDetails.state") === Volt.EOfferingState.Add}>
        <Flag message={"new"} />
      </Components.Visible>
      <Components.Visible when={ValueOf(offer, "promotionDetails.expiryDate", false)}>
        <span className="d-sm-block pad-5-left-xs">
          <FormattedDate value={ValueOf(offer, "promotionDetails.expiryDate", "")} format="yMMMMd" timeZone="UTC">
            {
              (expiryDate: any) => <FormattedMessage id="PromotionExpires" values={{ expiryDate }} />
            }
          </FormattedDate>
        </span>
      </Components.Visible>
      <Components.Visible when={ValueOf(offer, "promotionDetails.discountDuration", false)}>
        <span className="d-block">
          <FormattedMessage id="PromotionValid" values={{ price: Math.abs(ValueOf(offer, "promotionDetails.discountPrice.price", 0)), discountDuration: ValueOf(offer, "promotionDetails.discountDuration", "") }} />
        </span>
      </Components.Visible>
    </p>
  </Components.Visible>
</>;

export const ChargeItem: React.FC<ComponentProps> = (props) => {
  const { offerings, isReview, confirmation, isCurrent } = props;
  const displayGroupKey: string = ValueOf(offerings, "0.displayGroupKey", "NULL");
  const isAlacarte: boolean = displayGroupKey === "ALACARTE";
  const dispatch = useDispatch();
  const flowType = React.useMemo(() => Utils.getFlowType(), []);
  return (
    <div className="flexCol">
      <span className="spacer15 d-block d-sm-none" aria-hidden="true"></span>
      <div className="flexRow flexJustifySpace">
        <p className="txtBlack txtSize18 no-margin floatL">
          {<FormattedMessage id={displayGroupKey} />}
          <Components.Visible when={
            !isCurrent &&
                        isReview &&
                        flowType !== EFlowType.INTERNET &&
                        !ValueOf<boolean>(confirmation, "confirmationNumber", false) &&
                        displayGroupKey !== Volt.EDIsplayGroupKey.PROMOTION
          }>
            <span className="flexRow flexCenter floatR pad-2-top">
              <a id={`offering_edit_${displayGroupKey}`} className="txtBlue txtSize14 margin-20-left special-underline" href="#" onClick={
                (e) => {
                  e.preventDefault();
                  Omniture.useOmniture().trackAction({
                    id: "editClick",
                    s_oAPT: {
                      actionId: 647
                    },
                    s_oBTN: `Edit - ${displayGroupKey}`
                  });
                  dispatch(Actions.broadcastUpdate(Actions.historyGo(displayGroupKey)) as any);
                  dispatch(Actions.broadcastUpdate(Actions.closeLightbox(EModals.PREVIEWMODAL)) as any);
                }
              }>
                <FormattedMessage id="Edit" /> <span className="virgin-icon icon-edit txtBlue txtSize14" aria-hidden="true"></span>
              </a>
            </span>
          </Components.Visible>
        </p>
      </div>
      <div className="flexCol">
        <div className="list-unstyled no-margin" role="list">
          {
            isAlacarte ?
              <>
                <p aria-hidden="true"><FormattedMessage id={`ALACARTE_DESC`} values={{ value: offerings.length }} /></p>
                {
                  offerings.map(offer => <div className="flexRow alaCarteList flexCenter" role="listitem">
                    <p className="noMargin flexRow flexGrow flexCenter">
                      <Charge offer={offer} isCurrent={isCurrent} supressFlag={displayGroupKey === Volt.EDIsplayGroupKey.PROMOTION} />
                    </p>
                    <p className="txtSize14 noMargin txtBold txtRight floatL-xs">
                      <Components.BellCurrency value={ValueOf(offer, "regularPrice.price") || 0} />{
                        offer.regularPrice && offer.regularPrice.priceType === "Recurring" && <FormattedMessage id="PER_MO" />
                      }
                    </p>
                  </div>)
                }
              </>
              : offerings.map(offer => <div className="flexRow flexJustifySpace">
                <p className="noMargin">
                  <Charge offer={offer} isCurrent={isCurrent} supressFlag={displayGroupKey === Volt.EDIsplayGroupKey.PROMOTION} />
                </p>
                <p className="txtSize14 noMargin txtBold txtRight floatL-xs">
                  <p className="noMargin txtBold txtSize14">
                    <Components.BellCurrency value={ValueOf(offer, "regularPrice.price") || 0} />{
                      offer.regularPrice && offer.regularPrice.priceType === "Recurring" && <FormattedMessage id="PER_MO" />
                    }
                  </p>
                  <Components.Visible when={ValueOf(offer, "promotionDetails.description", false)}>
                    <p className="noMargin txtBold txtSize14 txtNoWrap">
                      <span className="spacer15 d-none d-sm-block" aria-hidden="true" />
                      <Components.BellCurrency value={ValueOf(offer, "promotionDetails.discountPrice.price") || 0} />{
                        ValueOf(offer, "promotionDetails.discountPrice.priceType", "") === "Recurring" && <FormattedMessage id="PER_MO" />
                      }
                    </p>
                  </Components.Visible>
                </p>
              </div>)
          }
        </div>
      </div>
      <div className="spacer25" aria-hidden="true"></div>
      <div className="spacer1 bgGrayLight6 margin-25left-xs margin-25right-xs" aria-hidden="true">
      </div>
      <span className="spacer25 visible-lg" aria-hidden="true"></span>
      <span className="spacer15 d-block d-sm-none" aria-hidden="true"></span>
    </div>
  );
};

ChargeItem.displayName = "ChargeItem";
