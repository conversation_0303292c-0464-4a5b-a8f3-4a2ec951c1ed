﻿@font-face {font-family:'bell-icon';
src:url('../../core/fonts/bell-icon.eot?#iefix') format('embedded-opentype'), url('../../core/fonts/bell-icon.woff') format('woff'), url('../../core/fonts/bell-icon.ttf') format('truetype'), url('../../core/fonts/bell-icon.svg') format('svg');
font-weight:normal;
font-style:normal}

.bgVirginMyAccountImg {
    background: #000 url(../img/MyAccount-BG.jpg) top center no-repeat;
}

/*
.welcome .container.subscriberDropdown {
    padding: 20px 0 0 0;
}
*/

/*.welcome .container.subscriberDropdown .form-control-select-box {
    display: block;
}*/

.welcome .subscriberDropdown hr {
    margin-bottom: 0;
    border-color: #ccc;
    width:100%;
}

.welcome .subscriberDropdown .form-control {
    display: block;
    width: 100%;
    height: 40px;
    line-height: 1;
    background-color: #fff;
    background-image: none;
    padding: 0 40px 0 20px;
    border: none;
}

.welcome .subscriberDropdown .form-control-select-box:after {
    font-family: "virgin-icons";
    content: "\e927";
    font-size: 4px;
    background-color: transparent;
    color: #00b4e1;
    right: 2px;
    top: 11px;
    padding: 8px 14px 10px 0;
    height: 30px;
    position: absolute;
    pointer-events: none;
    z-index: 2;
}

.welcome .subscriberDropdown .form-control-select-box:before {
     content: "";
    background: #eee;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    position: absolute;
    top: 12px;
    right: 11px;
    z-index: 1;
    pointer-events: none;
}

.welcome .myplan hr,
.welcome .members hr {
    clear: both;
}

.welcome .currentBalance .balance {
    margin: 0;
    font-size: 20px;
}

.welcome .balance-panel.panel.panel-body {
    padding-top: 30px;
    padding-bottom: 30px;
}

.welcome  .currentBalance {
    padding-bottom: 10px;
}

.welcome .myplanaddonsAndUsage {
    margin-top: 20px;
}

main{
	padding-bottom: 40px;
}

.myusage ul.tabs{
    display: table;
    table-layout: fixed;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

.myusage ul.tabs li a {
    color: #fff;
}

.myusage ul.tabs li{
   cursor: pointer;
    background-color: #666;
    color: #fff;
    display: table-cell;
    vertical-align: middle;
    float: none;
    position: relative;
    text-align: center;
    padding-top: 14px;
    padding-bottom: 14px;
}

.myusage ul.tabs li label{
     cursor: pointer;
}

.myusage ul.tabs li.active_tabs{
    cursor: default;
     background-color: #333;
     font-size: 24px;
     z-index: 2;
     padding-top: 1px;
     padding-bottom: 10px;
}

.myusage ul.tabs li.active_tabs label{
     cursor: default;
}

.myusage ul.tabs li.active_tabs .active-tab-top{
    background-color: #333;
    display: block;
    height: 10px;
    left: 0px;
    opacity: 1;
    position: absolute;
    top: -10px;
    width: 100%;
    z-index: -1;   
}

.myusage ul.tabs li:first-child {
    border-right: solid 1px #efefef;
}

.myusage ul.tabs li:last-child{
    border-left: solid 1px #efefef;
}

.myusage ul.tabs li label {
    text-transform: uppercase;
    margin-bottom: 0;
}

.myusage ul.tabs li.active_tabs:after {
    content: "";
    position: absolute;
    bottom: -15px;
    border-width: 15px 15px 0;
    border-style: solid;
    border-color: #333 transparent;
    display: block;
    width: 0;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
     bottom: -12px;
}

.myusage .title{
    display: table;
    margin-bottom: 20px;
    margin-top: 30px;
}

.welcome .myusageInformation {
    width: 100%;
    padding: 0;
}

html[lang="fr"] .welcome .myusage .usageAllocation .allocation {
    width: 260px;
}

.welcome .myusage .usageAllocationWidget .meter .used.unlimited {
    width: 96%;
}

.myusage .title h1,
.welcome .title  .title h1{
    margin: 0;
}

.welcome .myplanaddonsAndUsage .myplaninfo .title span {
    font-size: 18px;
    margin-left: 5px;
}

.myusage .usage-period .label, .myusage .usage-period .field{
     display: table-cell;
    vertical-align: middle;
}

.myusage .form-control.form-control-gray {
    background-color: #f0f0f0;
}


.myusage .usage-period .label{
    font-size: 14px;
    color: #333;
    padding-right: 20px;
}

.myusage .usage-period .usage-period-select {
    border: solid 2px #333;
    border-radius: 3px;
    padding: 12px;
    display: block;
}

.myusage .usage-period .usage-period-select:after {
    font-family: "bell-icon";
    content: "\e601";
    font-size: 18px;
    background-color: #efefef;
    color: #00b4e1;
    right: 2px;
    top: 2px;
    padding: 11px 14px 10px 0;
    height: 44px;
    position: absolute;
    pointer-events: none;
}
.myusage .myusageInformation .col1{
    display: table;
}

.welcome .subscriberTable{
    display: table;
}

.myusage .currentMonth {
    display: table;
}

.myusage .currentmonthWidget {
    min-height: 40px;
}

.myusage .currentmonthWidget .meter {
    width: 100%;
    height: 10px;
}

.myusage .currentmonthWidget .meterBlock {
    background-color: #eee;
    height: 10px;
    float: left;
    width: 3.2258%;
}

.myusage .currentmonthWidget .meterBlock .meterBlockSpace{
    float: right;
    background-color: #fff;
    width: 1px;
}

.myusage .currentmonthWidget .meterBlock.daysPassed {
    background-color: #00b4e1;
}

.myusage .usageAllocationWidget .meter .used {
     float: left;
     background-color: #00b4e1;
}

.myusage .usageAllocationWidget .meter .used.unlimited {
    position: relative;
    width: 98%;
}

.myusage .usageAllocationWidget .meter .used.unlimited:after {
   content: "";
    display: block;
    position: absolute;
    top: -4px;
    right: -17px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-left: 17px solid #00b4e1;
    z-index: 5;
}

.myusage .usageAllocationWidget .meter .left {
    background-color: #eee;
    float: left;
}

.myusage .currentmonthWidget .meter .meterBlock .line {
    height: 40px;
    float: right;
    width: 1px;
    background-color: #333;
}

.myusage .currentmonthWidget .meter .today {
    position: relative;
    top: -20px;
    float: right;
}

.myusage .currentmonthWidget .meter .today.shiftLeft {
     left: -20px !important;
}

html[lang="en"] .myusage .currentmonthWidget .meter .today {
    left: 50px;
}

html[lang="fr"] .myusage .currentmonthWidget .meter .today {
    left: 80px;
}

html[lang="en"] .myusage .currentmonthWidget .meter .today.move-left {
    left: -8px;
}

html[lang="fr"] .myusage .currentmonthWidget .meter .today.move-left {
    left: -8px;
}

.myusage .allocation .base {
    display: inline-block;
    padding-right: 10px;
}



.myusage .allocation .left{
    display: inline-block;
    padding-left: 10px;
}

.welcome .myusage .allocation .base {
    padding-right: 0px;
}


html[lang="fr"] .welcome .myusage .allocation .left {
     padding-left: 0px;
}

.myusage .allocation .left.over {
    color: #c01a18;
}

.myusage .usageAllocationWidget .meter {
    height: 11px;
    width: 100%;
    overflow: hidden;
    display: inline-block;
}

.myusage .usageAllocationWidget .meter .left.over{
    background-color: #bf1a18;
}

.myusage  .overageWarning {
    background-color: #666;
    padding:20px;
    position: relative;
}

.myusage  .overageWarning .col1 {
    overflow: hidden;
}

.myusage  .overageWarning h4 {
    margin-top: 0;
     font-size: 16px;
}

.myusage  .overageWarning h4:last-child{
    margin-bottom: 0;
}

.myusage  .overageWarning:after {
     -webkit-transform: translateX(-50%) translateY(-100%);
    -ms-transform: translateX(-50%) translateY(-100%);
    transform: translateX(-50%) translateY(-100%);
     top: 0;
    right: 20px;
     border-width: 8px;
     content: '';
    right: 10px;
    display: block;
    position: absolute;
    z-index: 5;
    width: 0;
    height: 0;
    border-style: solid;
    border-color: #fff;
}

.myusage  .overageWarning.carett:after {
      border-bottom-color: #666;
}

.myusage .overageWarning div {
    display: table-cell;
}

.myusage .overageWarning .iconn{
    width: 30px;
}

.myusage .overageWarning .iconn span{
    margin-left: 1px;
    top: -2px;
}

.myusage .overageWarning .iconn i:before {
    top: -1px;
}

.myusage .details {
    padding-top: 30px;
    padding-bottom: 30px;
}

.myusage .details .col1 div:first-child{
    padding-bottom: 10px;
}

.myusage .details a{
    text-decoration: underline;
}


/*.myusage .container.myusageInformation{
    padding-top: 20px;
    padding-bottom: 20px;
}*/

.myusage .six-month-trend {
    padding-bottom: 20px;
}
    
.welcome .usagebuttons button:first-child{
    margin-right: 10px;
}

.welcome .myplanaddonsAndUsage {
    display: table;
    width: 100%;
}

.welcome .myplanaddonsAndUsage .myplan,
.welcome .myplanaddonsAndUsage .myusage {
    border: solid 1px #ccc;
}

.welcome .myplanaddonsAndUsage h2 {
    margin-top: 0;
}

.welcome .members {
    margin-top: 20px;
}

.welcome .myplanaddonsAndUsage .myplaninfo .homeinternet ul{
    margin-bottom: 0;
}

.welcome .content-container {
    padding: 30px;
 }

@media (min-width: 992px) {
	.welcome .usagebuttons{
		text-align: right;
	}
    .welcome .myplanaddonsAndUsage .myplan,
	.welcome .myplanaddonsAndUsage .myusage{
	    display: table-cell;
	    float: none;
	    width: 50%;
        padding: 20px;
	}
     .welcome .myplanaddonsAndUsage .divider {
         width: 20px;
     }
     .welcome .container.subscriberDropdown .form-control-select-box {
         width: 400px;
    }
      .welcome .myplan .accordion .accordion-inner .homeinternet {
        padding: 30px 40px;
    }
}

@media  (min-width: 520px) and (max-width: 991px) {
    .welcome .container.subscriberDropdown .form-control-select-box {
        width:50%;
    }
       .welcome .myplan .accordion .accordion-inner .homeinternet {
        padding: 30px 30px 30px 70px;
    }
    .myusage ul.tabs li label {
        text-transform: none;
        font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
        font-size: 16px;
    }
}

@media (max-width: 991px) {
    .myusage .myusageInformation,
    .myusage .six-month-trend {
        padding-left: 0;
        padding-right: 0;
    }
	.myusage.container{
		width: auto;
	}
	.myusage .vm-panel-body{
		padding-left: 30px;
		padding-right: 30px;
	}
	
	.myusage h1{
		padding-left: 0;
	}
	
	.welcome .usagebuttons{
		text-align: left;
	}
     .welcome .myplanaddonsAndUsage .myplan,
	.welcome .myplanaddonsAndUsage .myusage{
	    display: block;
	    width: 100%;
         padding: 30px 20px;
  	}
     .welcome .myplanaddonsAndUsage .divider {
         display: none;
     }
     .welcome .myplanaddonsAndUsage .myusage {
         margin-top: 20px;
     }
    .welcome .subscriberDropdown a {
        display: block;
        width: 100%;
        margin-top: 20px;
        margin-bottom: 10px;
     }
}


@media (min-width: 768px) {
    .myusage .legend span {
        display: inline-block;
    }
     .myusage .legend span:first-child {
        padding-right: 40px;
    }
     .myusage .currentMonth,
     .myusage .currentMonthCom,
     .myusage .usageAllocation,
     .myusage .legend,
     .myusage .note,
     .myusage .usageAllocationCom {
         padding-left: 20px;
         padding-right: 20px;
     }
     /*
    .myusage .container.myusageInformation,
    .myusage .details {
        padding-left: 0;
        padding-right: 0;
    }
         */
     .welcome .myusage .currentMonth,
     .welcome .myusage .currentMonthCom,
     .welcome .myusage .usageAllocation,
     .welcome .myusage .legend,
     .welcome .myusage .note,
     .welcome .myusage .usageAllocationCom {
         padding-left: 0;
         padding-right: 0;
     }
}

@media (max-width: 767px) and (min-width: 520px) {
     .myusage .currentMonth,
     .myusage .currentMonthCom,
     .myusage .usageAllocation,
     .myusage .legend,
     .myusage .note,
     .myusage .usageAllocationCom {
         padding-left: 20px;
         padding-right: 20px;
     }
}

@media (max-width: 767px) {
    .myusage .currentMonth .col6 {
        display: inline;
    }
   .myusage .legend span {
        display: block;
    }
   .myusage .details {
        padding-left: 20px;
        padding-right: 20px;
    }
}



@media (min-width: 520px) {
    .myusage .title .col6,
    .myusage .currentMonth .col6,
    .myusage .usageAllocation .col6
      {
        display: table-cell;
        vertical-align: middle;
    }
    .myusage .usage-period .usage-period-group,
    .myusage .currentMonth .days,
    .myusage .usageAllocation .allocation{
        float: right;
    }
    
      .welcome .currentBalance .balance, 
      .welcome .currentBalance .payBy{
         display: inline-block;
     }
}

@media (max-width: 519px) {
     .myusage .title .col6,
     .myusage .usageAllocation .col6{
        display: block;
         width: 100%;
    }
    .myusage .title h1 {
        padding-bottom: 20px;
     }
    .myusage .currentMonth,
     .myusage .currentMonthCom,
     .myusage .usageAllocation,
     .myusage .legend,
     .myusage .note,
     .myusage .usageAllocationCom {
         padding-left: 20px;
         padding-right: 20px;
     }
     .welcome .myusage .currentMonth,
     .welcome .myusage .currentMonthCom,
     .welcome .myusage .usageAllocation,
     .welcome .myusage .legend,
     .welcome .myusage .note,
     .welcome .myusage .usageAllocationCom {
         padding-left: 0;
         padding-right: 0;
     }
    .myusage .container.myusageInformation{
        padding-left: 0;
        padding-right: 0;
    }
     .myusage .btn{
		 width: 100%;
		 margin-bottom: 10px;
	 }
	 .vm-panel-body{
		 padding: 0px;
	 }
	 .panel{
		 border: 0px;
	 }
	 .myusage.container{
		 margin-bottom: 20px;
	 }

	 .members .panel{
		 padding-left: 0px;
		 padding-right: 0px;
	 }
	 .myplan .planstats{
		 text-align: left;
		 padding-top: 10px;
		 padding-bottom: 10px;
	 }
     .welcome .currentBalance .balance, 
      .welcome .currentBalance .payBy{
         display: block;
     }
     .welcome .usagebuttons button,
     .welcome .usagebuttons a.btn{
	    display: block;
	    width: 100%;
        margin-top: 10px;
    } 
     .welcome .usagebuttons button:first-child {
         margin-bottom: 10px;
     }
     .welcome .container.content-container {
         padding: 0;
         background-color: #efefef;
     }
      .welcome .myplanaddonsAndUsage .myplaninfo .homeinternet {
        padding: 20px;
    }
      .welcome .container.title {
           padding-left: 20px;
      }
       .welcome .container.subscriberDropdown .form-control-select-box {
        width:100%;
    }
    .welcome .container.subscriberDropdown {
        padding-left: 20px;
        padding-right: 20px;
    }
      .myusage ul.tabs li label {
        text-transform: none;
        font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
        font-size: 14px;
    }
    .myusage ul.tabs li {
        padding-left: 20px;
        padding-right: 20px;
    }
    .welcome .content {
        padding: 0;
    }
    .welcome .content .content-container {
        padding: 0;
        background-color: #efefef;
    }
    .welcome .myusage .usageAllocationWidget .meter .used.unlimited {
        width: 90%;
    }
}

/* form elements */

.myusage .usage-period-group .form-control {
    display: block;
    width: 100%;
    height: 50px;   
    line-height: 1;
    background-color: #efefef;
    background-image: none;
    border: 2px solid #333;
    padding: 0 40px 0 20px;
    border-radius: 5px;
}

select::-ms-expand {
    display: none;
}
select::-ms-value {
    background: none;
    color:#5a5a5a;
}

.form-control-select {
    box-shadow: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
    padding-right: 38px;
}

.form-control-select-box {
    position: relative;
    display: inline-block;
}

.myusage .usage-period-group .form-control-select-box:after {
    font-family: "bell-icon";
    content: "\e601";
    font-size: 18px;
    background-color: #efefef;
    color: #00b4e1;
    right: 2px;
    top: 2px;
    padding: 11px 14px 10px 0;
    height: 44px;
    position: absolute;
    pointer-events: none;
}

.myusage .usage-period-group  .form-group {
    margin-bottom: 0;
}

.form-inline .form-group,
.form-inline .form-control {
      display: inline-block;
      width: auto;
      vertical-align: middle;
}

@media (min-width: 520px) {
    .form-group {
        display: table;
        width: 100%;
    }

    .form-group > .row {
        display: table-row;
    }

    .form-group > .form-label-col,
    .form-group > .form-control-col,
    .form-group > .row > .form-label-col,
    .form-group > .row > .form-control-col {
        display: table-cell;
        float: none;
        vertical-align: middle;
    }

    .form-group > .form-label-col[class^="col-"],
    .form-group > .row > .form-label-col[class^="col-"]  {
        text-align: right;
        padding-right: 40px;
    }  
    
    .form-label-col .form-label {
        margin-bottom: 0;
    }  
    /*
    .welcome .container.title {
        padding: 30px 0;
    }
        */
}

/*
.welcome .container {
    padding: 30px;
}
*/
.welcome .vm-panel-body {
    padding: 0;
}

.welcome .vm-panel-body .panel{
    border: solid 1px #ccc;
}

.welcome .panel {
    margin-bottom: 0;
}

.welcome .panel-body {
    padding: 20px;
}

html[lang="fr"] .loader-fixed {
    width: 350px;
    margin-left: -175px;
}


.welcome .myplan .icon {
    font-style: normal;
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.welcome .myplan a.accordion-toggle {
    text-decoration: none;
}
.welcome .myplan .accordion .icon-plus-solid:before {
    content: "\e910";
}

.welcome .myplan .accordion .icon-plus-solid.icon-collapse-outline-circled.icon-exapnd-outline-circled:before {
    content: "\e911";
}

.welcome .myplan .icon-collapse-outline-circled:before {
    content: "\e90e";
}

.welcome .myplan .icon:before {
    font-family: 'bell-icon';
    position: relative;
    top: 1px;
    left: -1px;
}

.welcome .myplan .accordion i {
    font-size: 24px;
    color: #00b4e1;
}

.welcome .myplan .accordion i:before {
    position: relative;
    top: 4px;
}


.welcome .myplan .title .icon {
    color: #fff;
}

.welcome .myplan .title .icon span {
    display: inline-block;
    float: left;
    width: 22px;
    height: 22px;
    background-color: #00b4e1;
    border-radius: 50%;
    margin-right: -1em;
    margin-top: 6px;
}

.welcome .myplan .title {
    padding: 30px 20px;
}

.welcome .myplan .accordion .accordion-inner {
    border-left: solid 4px #efefef;
    border-right: solid 4px #efefef;
    border-bottom: solid 4px #efefef;
    font-size: 12px;
}

.myusage .col-md-6 {
    width: 100%;
}

.table {
    display: table;
}

.virgin-modal.modal.modal-vm {
    padding-right: 0 !important;
}
select {
    -moz-appearance: none;
    text-indent: 0.01px;
    text-overflow: '';
}
.form-control {
    display: block;
    width: 100%;
    height: 50px;
    line-height: 1;
    background-color: #efefef;
    background-image: none;
    border: 2px solid #333;
    padding: 0 40px 0 20px;
    border-radius: 5px;
}
    .form-control-select-box:after {
    font-family: "bell-icon";
    content: "\e601";
    font-size: 18px;
    background-color: #efefef;
    color: #00b4e1;
    right: 2px;
    top: 2px;
    padding: 11px 14px 10px 0;
    height: 44px;
    position: absolute;
    pointer-events: none;
}
    .form-control-select-welcome {
    position: relative;
    display: inline-block;
}
    .form-control-welcome{
    display: block;
    width: 100%;
    height: 40px;
    line-height: 1;
    background-color: #fff;
    background-image: none;
    padding: 0 40px 0 20px;
    border: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}
.form-control-select-welcome:before{
    content: "";
    background: #eee;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    position: absolute;
    top: 12px;
    right: 11px;
    z-index: 1;
    pointer-events: none;
}
    .form-control-select-welcome:after{
    font-family: "virgin-icons";
    content: "\e927";
    font-size: 4px;
    background-color: transparent;
    color: #00b4e1;
    right: 2px;
    top: 11px;
    padding: 8px 14px 10px 0;
    height: 30px;
    position: absolute;
    pointer-events: none;
    z-index: 2;
}
    .accordion-heading {
    padding: 30px 20px;
}
    .accordion-inner {
    border-left: solid 4px #efefef;
    border-right: solid 4px #efefef;
    border-bottom: solid 4px #efefef;
    font-size: 12px;
    padding: 30px 40px;
}
    .virgin-form-input{
    height: 38px;
    padding: 6px 12px;
    display: block;
    width: 100%;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 0px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    -webkit-transition: border-color ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
}
    .has-error .virgin-form-input {
    border-color: #fe0000;
}
    .virgin-title-margin5{    
    margin-left: 5px;
    }
    .txtLightBlue{
    color: #00b4e1;
    }
    