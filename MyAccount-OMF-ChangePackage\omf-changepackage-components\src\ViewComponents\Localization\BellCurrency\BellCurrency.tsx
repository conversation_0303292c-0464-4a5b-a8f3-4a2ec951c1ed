import * as React from "react";
import { LocalizationState } from "bwtk";

function padBellCurrency(cents: string): string {
  if (!cents) return "00";
  if (cents.length === 1) return `${cents}0`;
  return cents.slice(0, 2);
}

export function getBellCurrency(localization: LocalizationState, value: any): string {
  try {
    const n: Array<string> = String(value).split(".");
    let dollars = n[0];
    const cents = n[1];
    // let showCents = true;
    const credit: boolean = value < 0 ? true : false;
    dollars = dollars.replace("-", "");
    // If cents is undefined or null or empty, then dont show cents
    if (Number(dollars) > 0 && (!cents || Number(cents) === 0)) {
      // showCents = false;
    }

    switch (localization.locale) {
      case "en":
        const enDollars = parseInt(dollars).toLocaleString("en");
        if (credit) {
          return `CR $${enDollars}.${padBellCurrency(cents)}`;
        } else {
          return `$${enDollars}.${padBellCurrency(cents)}`;
          // return `$${enDollars}${showCents ? "." + padBellCurrency(cents) : ""}`;
        }
      case "fr":
        const frDollars = parseInt(dollars).toLocaleString("fr");
        if (credit) {
          return `CR ${frDollars},${padBellCurrency(cents)}&nbsp;$`;
        } else {
          return `${frDollars},${padBellCurrency(cents)}&nbsp;$`;
        }
      default:
        // Default to English format for unknown locales
        const defaultDollars = parseInt(dollars).toLocaleString("en");
        if (credit) {
          return `CR $${defaultDollars}.${padBellCurrency(cents)}`;
        } else {
          return `$${defaultDollars}.${padBellCurrency(cents)}`;
        }
    }
  } catch (e) {
    // _log.error(e);
    return value;
  }
}

export interface BellCurrencyProps {
  value: any;
  localization: any;
  credit?: boolean;
  className?: string;
  tag?: string;
  tagProps?: any;
}

const Componenet = ({ value, className, localization, tag, tagProps, credit }: BellCurrencyProps) => {
  const CustomTag = tag || "span";
  return <CustomTag {...tagProps} className={`txtCurrency ${className || ""}`} dangerouslySetInnerHTML={{ __html: getBellCurrency(localization, value) }} />;
};

export default Componenet;
