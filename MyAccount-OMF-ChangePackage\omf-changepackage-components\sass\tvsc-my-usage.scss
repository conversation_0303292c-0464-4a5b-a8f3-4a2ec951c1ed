@import "includes/mixins";

.bell-my-usage-page {
    @import "partials/brf-table";

    .section-usage-details {
        @media #{$media-mobile} {
        }
    }

    .bell-usage-month-picker {
        min-width: 177px;
    }

    .form-control-datepicker {
        .form-control {
            min-width: 170px;
            padding-left: 14px;
            padding-right: 40px;
        }
    }

    .bell-usage-filters-holder {
        padding-top: 20px;
        border-bottom: none;

        @media #{$media-tab-mobile} {
            box-shadow: inset 0 -20px 20px -20px #ccc;
        }

        .bell-usage-filters-trigger {
            top: 30px;
            right: 30px;

            i:before {
                top: 1px;
            }

            @media #{$media-mobile} {
                top: 15px;
                right: 15px;
            }
        }

        .bell-usage-filters-tray {
            @media #{$media-tab-mobile} {
                overflow: hidden;
                max-height: 0;
                transition: max-height 150ms;

                &.expanded {
                    max-height: 200px;
                }
            }

            @media #{$media-mobile} {
                .form-control-select-box {
                    width: 80%
                }
            }
        }
    }

    .bell-usage-table-holder {
        border-top: none;

        @media #{$media-desktop} {
            padding-top: 0;
        }

        @media #{$media-mobile} {
            padding-top: 10px;
        }

        .bell-table-usage > table {
            > tbody > tr > td {
                vertical-align: top;
                padding-top: 20px;
                padding-bottom: 20px;
            }

            thead {
                th {
                    // Carret
                    &[aria-sort="ascending"],
                    &[aria-sort="descending"],
                    &.sortedAsc,
                    &.sortedDesc {
                        &:after {
                            font-family: 'bell-icon';
                            position: absolute;
                            top: 50%;
                            right: 20px;
                            font-size: 20px;
                            margin-top: -12px;
                        }
                    }

                    &[aria-sort="ascending"]:after,
                    &.sortedAsc:after {
                        content: "\e91e";
                    }

                    &[aria-sort="descending"]:after,
                    &.sortedDesc:after {
                        content: "\e91d";
                    }
                    // Columns width
                    &:nth-child(1) {
                        width: 22.5%;

                        @media #{$media-tablet} {
                            width: 24%;
                        }
                    }

                    &:nth-child(2) {
                        width: 34%;

                        @media #{$media-tablet} {
                            width: 35%;
                        }
                    }

                    &:nth-child(3) {
                        width: 26.5%;

                        @media #{$media-tablet} {
                            width: 24%;
                        }
                    }

                    &:nth-child(4) {
                        width: auto
                    }
                }
            }

            tfoot {
                th,
                td {
                    vertical-align: middle;
                    border-color: #00549a;
                    padding: 20px;

                    @media #{$media-tablet} {
                        padding: 20px 15px;
                    }

                    @media #{$media-mobile} {
                        .fontSans-xs {
                            letter-spacing: unset
                        }
                    }
                }
            }
        }

        table.table-cards {
            @media #{$media-mobile} {
                &,
                tbody,
                tr,
                td {
                    display: block;
                    border: none;
                    padding: 0;
                    // background: none;
                    font-size: 14px;
                }

                td > .txtBlack2 {
                    color: #555
                }

                tfoot {
                    display: table;
                    border-color: #00549a;
                    width: 100%;
                    margin-top: 20px;

                    tr {
                        display: table-row
                    }

                    td {
                        display: table-cell;
                        font-size: 14px;
                        padding: 20px;

                        &:first-child {
                            text-transform: uppercase
                        }

                        &:last-child {
                            text-align: right
                        }
                    }
                }

                tr.bell-usage-card {
                    padding: 15px 20px;
                    background-color: #eee;
                    border: 1px solid #d4d4d4;

                    &:not(:last-child) {
                        margin-bottom: 10px;
                    }

                    td.bell-usage-card-row {
                        display: flex;
                        flex-direction: row;
                        height: unset;

                        &:not(:first-child) {
                            padding-top: 5px;
                        }

                        &:not(:last-child) {
                            padding-bottom: 5px;
                            border-bottom: 1px solid #d4d4d4;
                        }

                        .txtBlue {
                            color: inherit;
                        }

                        .bell-usage-card-title {
                            width: 120px;
                            flex-shrink: 0;
                        }
                    }
                }
            }
        }
    }

    .bell-my-usage-warning-icon {
        -ms-transform: scaleY(-1);
        -webkit-transform: scaleY(-1);
        transform: scaleY(-1);

        @media #{$media-tablet} {
            padding-left: 5px;
        }

        @media #{$media-mobile} {
            padding-left: 0px;
        }

        &:after {
            font-family: 'bell-icon';
            font-size: 40px;
            content: "\e90D";
            color: #D1A542;
            padding-right: 17px;
            position: relative;
            bottom: 3px;
            left: -1px;

            @media #{$media-mobile} {
                font-size: 37px;
                left: 6px;
            }
        }
    }

    .bell-my-usage-noUsageTitle {

        @media #{$media-mobile} {
            padding-top: 18px;
        }
    }

    .bell-my-usage-noUsageDescription {
        @media #{$media-mobile} {
            padding-top: 9px;
        }
    }

    .txtCenter-xs {
        @media #{$media-mobile} {
            text-align: center;
        }
    }

    .bell-noUsageContainer {
        height: 518px;

        @media #{$media-tablet} {
            height: 454px;
        }

        @media #{$media-mobile} {
            height: 192px;
        }
    }
}
