import { Component, IInstallationProps, IInstallationDispatches } from "./Installation";
import { connect } from "react-redux";
import { IStoreState } from "../../../models";
import { initSlickSlider } from "../../../store";

export const Installation = connect<IInstallationProps, IInstallationDispatches>(
  ({ installationAddress, availableDates, duration  }: IStoreState) =>
    ({ installationAddress, availableDates, duration }),
  (dispatch) => ({
    initSlickSlider: () => dispatch(initSlickSlider())
  })
)(Component);
