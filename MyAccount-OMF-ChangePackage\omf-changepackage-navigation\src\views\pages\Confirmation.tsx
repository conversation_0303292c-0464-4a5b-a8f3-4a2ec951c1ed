import { WidgetLoader } from "bwtk";
import { EWidgetName, EWidgetRoute } from "omf-changepackage-components";
import * as React from "react";

interface IPage {
  title: string;
}

export const Confirmation: React.FC<IPage> = ({ title }) => {
  // Set page title
  document.title = `${title} - ${document.title}`;
  return <WidgetLoader widget={EWidgetName.CONFIRMATION} mode={EWidgetRoute.CONFIRMATION} />;
};
