import { Models } from "omf-changepackage-components";
import { IAvailableDates, IInstallationAddress, IContactInformation, IAdditionalDetails } from "./App";
import { EDuration } from "./Enums";

export interface IStoreState extends Models.IBaseStoreState {
  availableDates?: Array<IAvailableDates>;
  // preferredDate?: IAvailableDates;
  duration?: EDuration;
  installationAddress?: IInstallationAddress;
  contactInformation?: IContactInformation;
  additionalDetails?: IAdditionalDetails;
  isInstallationRequired?: boolean;
}
