import { Action } from "redux-actions";
import { EWidgetRoute, EWidgetStatus, ILightboxPayload, Models, Volt } from "../Models";
export declare namespace Actions {
    const setWidgetStatus: (payload: EWidgetStatus) => Action<EWidgetStatus>;
    const setWidgetProps: (payload: Models.IBaseWidgetProps | any) => Action<Models.IBaseWidgetProps | any>;
    const getData: import("redux-actions").ActionFunctionAny<Action<any>>;
    const showHideLoader: (payload: boolean | null) => Action<boolean>;
    const errorOccured: (error: Models.IErrorHandlerProps) => Action<Models.IErrorHandlerProps>;
    const openLightbox: (lightboxId: string | ILightboxPayload) => ReduxActions.Action<string | ILightboxPayload>;
    const closeLightbox: (lightboxId: string) => ReduxActions.Action<string>;
    const setlightboxData: (payload: any) => ReduxActions.Action<any>;
    const broadcastUpdate: (action: Action<any>, delay?: number) => ReduxActions.Action<Action<any>>;
    const refreshTotals: import("redux-actions").ActionFunctionAny<Action<any>>;
    const toggleTVCategoriesTray: import("redux-actions").ActionFunctionAny<Action<any>>;
    const setProductConfigurationTotal: (totals: Volt.IProductConfigurationTotal) => ReduxActions.Action<Volt.IProductConfigurationTotal>;
    const continueFlow: (route: EWidgetRoute) => Action<EWidgetRoute>;
    const handleNav: (payload: boolean) => Action<boolean>;
    const onContinue: import("redux-actions").ActionFunctionAny<Action<any>>;
    const historyGo: (payload: string) => ReduxActions.Action<string>;
    const historyBack: (payload?: any) => ReduxActions.Action<any>;
    const historyForward: import("redux-actions").ActionFunctionAny<Action<any>>;
    const applicationReset: import("redux-actions").ActionFunctionAny<Action<any>>;
    const applicationExit: import("redux-actions").ActionFunctionAny<Action<any>>;
    const applicationLogout: import("redux-actions").ActionFunctionAny<Action<any>>;
    const setHistoryProvider: (payload: any) => ReduxActions.Action<any>;
    const setAppointmentVisited: import("redux-actions").ActionFunctionAny<Action<any>>;
    const widgetRenderComplete: import("redux-actions").ActionFunctionAny<Action<any>>;
    const raiseRestriction: (message: Volt.IRestriction, onComplete?: any) => ReduxActions.Action<Volt.IRestriction>;
    const acceptRestriction: (action: Volt.IHypermediaAction) => ReduxActions.Action<Volt.IHypermediaAction>;
    const declineRestriction: (action?: Volt.IHypermediaAction) => ReduxActions.Action<Volt.IHypermediaAction>;
    const finalizeRestriction: (data?: any) => ReduxActions.Action<any>;
    const clearCachedState: (payload: string[]) => ReduxActions.Action<string[]>;
    const omniPageLoaded: (name?: string, data?: any) => ReduxActions.Action<{
        name: string;
        data?: any;
    }>;
    const omniPageSubmit: (payload?: string) => ReduxActions.Action<string>;
    const omniModalOpen: (payload?: string) => ReduxActions.Action<string>;
}
