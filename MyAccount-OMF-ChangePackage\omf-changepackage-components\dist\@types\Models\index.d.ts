import { LocalizationState } from "bwtk";
import { ObservableInput } from "rxjs";
import { Volt } from "./VOLT";
export * from "./VOLT";
export declare enum EModals {
    PREVIEWMODAL = "PREVIEW_MODAL"
}
export declare enum EWidgetStatus {
    INIT = 0,
    RENDERED = 1,
    UPDATING = 2,
    ERROR = 3,
    OUTAGERROR = 4
}
export declare enum EWidgetRoute {
    INTERNET = "/Internet",
    TV = "/TV",
    TV_Packages = "/Packages",
    TV_MoviesSeries = "/Movies",
    TV_Addons = "/Addons",
    TV_Alacarte = "/Alacarte",
    TV_International = "/International",
    TV_InternationalCombos = "/International/Combos",
    TV_InternationalAlacarte = "/International/Alacarte",
    TV_Browse = "/Browse",
    TV_Search = "/Search",
    APPOINTMENT = "/Appointment",
    REVIEW = "/Review",
    CONFIRMATION = "/Confirmation"
}
export declare enum EReviewMode {
    Summary = "summary",
    Review = "review",
    Confirmation = "confirmation"
}
export declare enum EWidgetName {
    NAVIGATION = "omf-changepackage-navigation",
    INTERNET = "omf-changepackage-internet",
    TV = "omf-changepackage-tv",
    APPOINTMENT = "omf-changepackage-appointment",
    PREVIEW = "omf-changepackage-review",
    REVIEW = "omf-changepackage-review",
    CONFIRMATION = "omf-changepackage-review"
}
export declare enum EFlowType {
    INTERNET = "Internet",
    TV = "TV",
    ADDTV = "AddTV",
    BUNDLE = "Bundle"
}
export interface AjaxResponse<T> {
    headers: {
        [key: string]: any;
    };
    redirected: boolean;
    status: number;
    statusText: string;
    type: string;
    url: string;
    dataType: string;
    data: T;
}
export interface ILightboxPayload {
    lightboxId: string;
    data?: any;
}
export declare namespace Models {
    enum EBrand {
        BELL = "B",
        VIRGIN = "V",
        LUCKY = "L"
    }
    interface IBaseEnvironmentVariables {
        province: string;
        brand: EBrand;
        language: "en" | "en-ca" | "fr" | "fr-ca";
        transactionIdentifier: string;
        useMockData: boolean;
    }
    interface IBaseConfig {
        api: IBaseWidgetAPI;
        headers: {
            [key: string]: string;
        };
        mockdata?: {
            [key: string]: {
                [key: string]: any;
            };
        };
        environmentVariables: IBaseEnvironmentVariables;
    }
    interface IBaseComponentProps {
        id?: string;
        className?: string;
        style?: {
            [key: string]: string;
        };
    }
    interface IBaseWidgetProps {
    }
    interface IBaseWidgetAPI {
        base: string;
    }
    interface IBaseStoreState {
        localization: LocalizationState;
        widgetStatus: EWidgetStatus;
        lightboxData?: any;
        restriction?: Volt.IMessage;
        error: IErrorHandlerProps;
    }
    interface IBaseAppProps {
        localization: LocalizationState;
        widgetStatus: EWidgetStatus;
        errorHandlerProps: IErrorHandlerProps;
    }
    interface IWidgetContext<T> {
        config: T;
    }
    interface IWidgetContext<T> {
        config: T;
    }
    interface IErrorHandlerProps extends Error {
        type: "API" | "widget" | "logic";
        response?: Response;
        componentStack?: any;
        debug: boolean;
    }
    class ErrorHandler extends Error implements IErrorHandlerProps {
        type: "API" | "widget" | "logic";
        response?: Response;
        componentStack?: any;
        debug: boolean;
        constructor(message: string | Error, details?: any, debug?: boolean);
    }
    function ErrorHandlerObservable(action: any): (response: {
        error: Error;
    }, source: ObservableInput<any>) => import("rxjs").Observable<any>;
    const noSpecialCharRegex: RegExp;
    const emailRegex: RegExp;
    const phoneRegex: RegExp;
    const hashCommaRegex: RegExp;
    const onlyNumbersRegex: RegExp;
}
