import { Injectable, CommonFeatures } from "bwtk";
import { Models, EWidgetRoute } from "omf-changepackage-components";

const { BaseConfig, configProperty } = CommonFeatures;

interface IAppConfig extends Models.IBaseConfig {
  defaultRoute: EWidgetRoute;
  linkURL: { [key: string]: string };
}

interface IAppAPI extends Models.IBaseWidgetAPI {
}

/**
 * Widget configuration provider
 * Allows the external immutable
 * config setting
 * @export
 * @class Config
 * @extends {BaseConfig<IAppConfig>}
 */
@Injectable
export class Config extends BaseConfig<IAppConfig> {
  @configProperty("") flowType: string;
  @configProperty({}) environmentVariables: any;
  @configProperty({}) mockdata: any;
  @configProperty({}) headers: any;
  @configProperty({ base: "http://127.0.0.1:8881" }) api: IAppAPI;
  @configProperty("") defaultRoute: EWidgetRoute;
  @configProperty({}) linkURL: { [key: string]: string };
}
