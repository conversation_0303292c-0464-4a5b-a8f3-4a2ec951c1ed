@font-face {
    font-family: 'bellslimregular';
    src: url('../core/fonts/bellslim_regular-webfont.eot');
    src: url('../core/fonts/bellslim_regular-webfont.eot?#iefix') format('embedded-opentype'), url('../core/fonts/bellslim_regular-webfont.ttf') format('truetype'), url('../core/fonts/bellslim_regular-webfont.svg#bellslimregular') format('svg');
    font-weight: normal;
    font-style: normal
}
@font-face {
    font-family: 'bellslim_mediumregular';
    src: url('../core/fonts/bellslim_medium-webfont.eot');
    src: url('../core/fonts/bellslim_medium-webfont.eot?#iefix') format('embedded-opentype'), url('../core/fonts/bellslim_medium-webfont.ttf') format('truetype'), url('../core/fonts/bellslim_medium-webfont.svg#bellslim_mediumregular') format('svg');
    font-weight: normal;
    font-style: normal
}
@font-face {
    font-family: 'bellslim_semiboldregular';
    src: url('../core/fonts/bellslim_semibold-webfont.eot');
    src: url('../core/fonts/bellslim_semibold-webfont.eot?#iefix') format('embedded-opentype'), url('../core/fonts/bellslim_semibold-webfont.ttf') format('truetype'), url('../core/fonts/bellslim_semibold-webfont.svg#bellslim_semiboldregular') format('svg');
    font-weight: normal;
    font-style: normal
}
/*Bell icon fonts*/
@font-face {font-family:'bell-icon';
    src:url('../core/fonts/bell-icon.eot?#iefix') format('embedded-opentype'), url('../core/fonts/bell-icon.woff') format('woff'), url('../core/fonts/bell-icon.ttf') format('truetype'), url('../core/fonts/bell-icon.svg') format('svg');
    font-weight:normal;
    font-style:normal}
    @font-face {font-family:'bell-icon-outline';
    src:url('../core/fonts/bell-icon-outline.eot?iw8dli');
    src:url('../core/fonts/bell-icon-outline.eot?#iefixiw8dli') format('embedded-opentype'), url('../core/fonts/bell-icon-outline.ttf?iw8dli') format('truetype'), url('../core/fonts/bell-icon-outline.woff?iw8dli') format('woff'), url('../core/fonts/bell-icon-outline.svg?iw8dli#bell-icon-outline') format('svg');
    font-weight:normal;
    font-style:normal}
    @font-face {font-family:'bell-icon2';
    src:url('../core/fonts/bell-icon2.eot?#iefix') format('embedded-opentype'), url('../core/fonts/bell-icon2.woff') format('woff'), url('../core/fonts/bell-icon2.ttf') format('truetype'), url('../core/fonts/bell-icon2.svg') format('svg');
    font-weight:normal;
    font-style:normal}
.brf {
    // Backported from e-Care
    // For compatibility
    font-size: 14px;
    line-height: 1.42857; //line-height: 20px; // text-size-adjust: 100%;
    // --
    @import "lib/virgin";
    @import "lib/bootstrap.min";
    @import "lib/allBrowsers_framework";
    @import "partials/tvsc-base"; // Page styles
    @import "tvsc-equipment"; // Special case for lineup because
    @import "tvsc-my-usage";
    @import "tvsc-overview";
    .bell-tv-payperview-page,
    .bell-tv-video-on-demand-page {
        @import "partials/tvsc-slider";
        @import "partials/tvsc-posters";
        @import "partials/tvsc-filter-options";
        @import "partials/tvsc-search-field"; // overflow: -webkit-paged-x;
    } // Lightboxes
    @import "partials/brf-lightbox";
    @import "lightbox/tvcs-generic-lightbox";
    @import "lightbox/tvcs-change-receiver";
    @import "lightbox/tvcs-update-smartcard";
    @import "lightbox/tvcs-lineup-lightbox";
    @import "lightbox/tvcs-synchronize-programming-lightbox";
    @import "lightbox/tvcs-pin-lightbox";
    @import "lightbox/tvsc-how-to-lightbox";
    // only a small section of in is in MBM
    @import "mbm/tvcs-base"; // Page styles
    @import "mbm/tvcs-line-up";
    @import "mbm/tvsc-my-usage";
    &.changeflow {
        @import "partials/tvsc-posters";
        @import "partials/tvsc-slider";
        @import "changeflow/slider";
        @import "changeflow/base-pack";
        @import "changeflow/package";
        @import "changeflow/channel";
        @import "changeflow/details";
        @import "changeflow/expandable-tray";
        @import "changeflow/filters";
        @import "changeflow/menu";
        @import "changeflow/panel";
        @import "changeflow/movie-pack";
        @import "changeflow/individual-channels";
        @import "changeflow/additional-channels";
        @import "changeflow/progress-steps";
        @import "changeflow/progress-steps-genesis";
        @import "changeflow/banner-tabs";
        @import "changeflow/review-programming"; // only a small section of in is in MBM
        @import "changeflow/mobile-layouts";
        @import "changeflow/search";
        @import "changeflow/channel-details-lightbox";
        @import "mbm/changeflow";
        @import "mbm/review-lightbox";
        @import "mbm/channel-details-lightbox";
    } //Error page
    @import "partials/tvcs-error";
}

// @import "lib/bell-fonts.scss";