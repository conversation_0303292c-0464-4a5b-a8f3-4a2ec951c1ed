import * as React from "react";
import { FormattedMessage } from "react-intl";
import { useFormContext } from "react-hook-form";
import { FormattedHTMLMessage } from "omf-changepackage-components";

interface ComponentProps {
  label: string;
  required?: boolean;
  value?: string;
  subLabel?: string;
  handleChange: Function;
  requiredInput?: boolean;
  maxLength?: number;
}

export const TextArea = (props: ComponentProps) => {
  const privateProps = { ...defaultProps, ...props };
  const { label, required, value, subLabel, handleChange, requiredInput, maxLength } = privateProps;
  const { register }: any = useFormContext();
  const [crCount, setCount] = React.useState(
    (maxLength || 0) - (value || "").length
  );

  return (
    <div className="flexBlock flexCol-xs margin-15-bottom">
      <label htmlFor={label} className="installation-form-label">
        <span className="txtBold"><FormattedMessage id={label} /></span>
        {required ? <span className="txtNormal">(optional)</span> : ""}
        {subLabel ? <span className="txtItalic block txtNormal"><FormattedHTMLMessage id={subLabel} /></span> : null}
      </label>
      <div className="flexCol">
        <textarea
          ref={register({ required: requiredInput })}
          id={label}
          name={label}
          defaultValue={value}
          maxLength={maxLength}
          className="brf3-textarea form-control"
          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
            setCount(
              (maxLength || 0) - (e.currentTarget.value || "").length
            );
            handleChange(e);
          }}>
        </textarea>
        <p>
          <FormattedMessage id={label + "_DESCRIPTION"} values={{ max: maxLength, count: crCount }} />
        </p>
      </div>
    </div>
  );
};

const defaultProps = {
  required: false,
  requiredInput: false,
  value: "",
  subLabel: ""
};
