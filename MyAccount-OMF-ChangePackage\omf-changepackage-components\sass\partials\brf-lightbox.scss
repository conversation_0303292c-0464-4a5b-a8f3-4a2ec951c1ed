@import "mixins";
.modal {
    // This fixes iOS issue with the lightbox
    // overflowing the screen and appearing
    // out of sight
    @media #{$media-mobile} {
        &:before {
            content: unset;
        }
    }
    .modal-dialog {
        @media #{$media-mobile} {
            margin: 0;
        }
        .modal-content {
            // .modal-header {
            //     min-height: 75px;
            //     @media #{$media-mobile} {
            //         min-height: 60px;
            //         padding: 15px;
            //     }
            //     .close {
            //         margin: -4px -15px 0 0;
            //         -webkit-appearance: none;
            //         cursor: pointer;
            //         background: 0 0;
            //         border: 0;
            //         float: right;
            //         font-size: 21px;
            //         font-weight: 700;
            //         line-height: 1;
            //         opacity: 1;
            //         @media #{$media-mobile} {
            //             height: 50px;
            //             padding: 12px 15px 0px 20px;
            //             margin-top: -17px;
            //             .icon {
            //                 font-size: 18px;
            //             }
            //         }
            //         .icon {
            //             font-weight: bold;
            //         }
            //     }
            //     .modal-title {
            //         line-height: 45px;
            //         letter-spacing: normal;
            //         @media #{$media-mobile} {
            //             font-family: Arial;
            //             font-size: 18px;
            //             letter-spacing: unset;
            //             line-height: 30px;
            //             font-weight: normal;
            //         }
            //     }
            //     &.modal-header-active {}
            // }
            // .modal-body {
            //     // @media #{$media-mobile} {
            //     //     padding: 0;
            //     // }
            // }
            // .modal-footer {
            //     @media #{$media-mobile} {
            //         .modal-body{
            //             padding: 30px 15px;
            //         }
            //         button {
            //             width: 100%;
            //             margin-bottom: 15px;
            //         }
            //         button:last-child {
            //             margin-bottom: 0;
            //         }
            //     }
            // }
            // @media #{$media-mobile} {
            //     &.modal-md.bell-modal-md {
            //         height: auto;
            //     }
            // }
            .modal-header-gray {
                .close {
                    float: right;
                    background: transparent;
                }
            }
        }
    }
    &.in {
        overflow-x: hidden;
        overflow-y: scroll;
    }
}