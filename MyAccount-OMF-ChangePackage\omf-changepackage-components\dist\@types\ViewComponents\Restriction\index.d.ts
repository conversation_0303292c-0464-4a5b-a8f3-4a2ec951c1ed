import * as React from "react";
import { Volt } from "../../Models";
export interface IComponentProps {
    id?: string;
}
export interface IComponentConnectedProps extends Volt.IRestriction {
    onComplete?: any;
}
export interface IComponentDispatches {
    onAction: (button: Volt.IHypermediaAction) => void;
    onDismiss: () => void;
}
export declare const RestrictionModalView: import("react-redux").ConnectedComponent<React.FC<IComponentProps & IComponentConnectedProps & IComponentDispatches>, {
    id?: string | undefined;
    context?: React.Context<import("react-redux").ReactReduxContextValue<any, import("redux").UnknownAction> | null> | undefined;
    store?: import("redux").Store | undefined;
}>;
