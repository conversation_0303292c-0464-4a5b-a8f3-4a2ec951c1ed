import { Injectable } from "bwtk";
import { Epic, combineEpics } from "redux-observable";
import { EWidgetStatus, Actions, Omniture, Utils, EFlowType } from "omf-changepackage-components";
import { CatalogEpics } from "./Epics/Catalog";
import { OrderingEpics } from "./Epics/Order";
import { getAccountDetails } from "./Actions";
import { UserAccountEpics } from "./Epics/UserAccount";
import { OmnitureEpics } from "./Epics/Omniture";

const { setWidgetStatus } = Actions;

// const { concat } = ActionsObservable;

@Injectable
export class Epics {
  constructor(
    public omnitureEpics: OmnitureEpics,
    public userAccountEpic: UserAccountEpics,
    public catalogEpics: CatalogEpics,
    public orderingEpics: OrderingEpics
  ) {}

  combineEpics() {
    return combineEpics(this.onWidgetStatusEpic);
  }

  private get onWidgetStatusEpic(): GeneralEpic {
    return (action$: any) =>
      action$
        .ofType(setWidgetStatus.toString())
        .filter(({ payload }: ReduxActions.Action<EWidgetStatus>) => payload === EWidgetStatus.INIT)
        .mergeMap(() => {
          let s_oSS2 = "~";
          switch (Utils.getFlowType()) {
            case EFlowType.TV:
              s_oSS2 = "TV";
              break;
            case EFlowType.ADDTV:
              s_oSS2 = "TV";
              break;
            case EFlowType.BUNDLE:
              s_oSS2 = "Bundle";
              break;
          }
          switch (Utils.getFlowType()) {
            case EFlowType.TV:
              Omniture.useOmniture().updateContext({
                s_oSS2,
                s_oSS3: "Change package",
              });
              break;
            case EFlowType.ADDTV:
              Omniture.useOmniture().updateContext({
                s_oSS2,
                s_oSS3: "Add Tv",
                s_oAPT: {
                  actionId: 507,
                  actionresult: 1,
                  applicationState: 0,
                },
              });
              break;
            case EFlowType.BUNDLE:
              Omniture.useOmniture().updateContext({
                s_oSS2,
                s_oSS3: "Add Tv",
                s_oAPT: {
                  actionId: 508,
                  actionresult: 1,
                  applicationState: 0,
                },
              });
              break;
          }
          return [getAccountDetails()];
        });
  }
}

type GeneralEpic = Epic<ReduxActions.Action<any>, any>;
