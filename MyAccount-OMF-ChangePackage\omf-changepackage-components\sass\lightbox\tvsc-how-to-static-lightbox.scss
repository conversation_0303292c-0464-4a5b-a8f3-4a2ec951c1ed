@import "mixins";
.bell-how-to-static-lightbox {
    .modal-body {
        @media #{$media-mobile} {
            padding: 30px 15px;
        }
    }
    .bell-how-to-lightbox-information {}
    .bell-how-to-lightbox-order {
        counter-reset: item;
        @media #{$media-desktop} {
            .txtCurrency {
                position: absolute;
                top: -6px;
            }
        }
        ol.bell-how-to-list {
            list-style: none;
            padding-left: 20px;
            >li {
                counter-increment: item;
                margin-bottom: 10px;
                padding-left: 10px;
                &:before {
                    display: inline-block;
                    content: counter(item);
                    text-align: center;
                    margin-left: -30px;
                    margin-right: 10px;
                    padding: 1.5px;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background: #00549a;
                    color: white;              
                }
            }
        }
    }
}