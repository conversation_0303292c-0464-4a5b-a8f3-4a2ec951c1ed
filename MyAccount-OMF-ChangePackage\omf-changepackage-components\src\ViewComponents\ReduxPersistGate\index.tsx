import * as React from "react";
import { persistStore } from "redux-persist";
import { PersistGate } from "redux-persist/integration/react";

export interface IProps {
  render: Function;
  store: any;
}

/*
 * consumer is the root render function of individual consuming widgets.
 * usage example:
 *
 *   import { Components } from "omf-changepackage-components";
 *   render(root: Element) {
 *    const { store } = this;
 *    ReactDOM.render(
 *     <ContextProvider value={{ config: this.config }}>
 *       <StoreProvider {...{ store }}>
 *         <Components.PersistGate render={() => <App />} store={store} />
 *       </StoreProvider>
 *     </ContextProvider>
 *     , root
 *    );
 *   }
 */
export const ReduxPersistGate: React.FC<IProps> = (props: IProps) => (
  <PersistGate loading={null} persistor={persistStore(props.store)}>
    {props.render()}
  </PersistGate>
);
