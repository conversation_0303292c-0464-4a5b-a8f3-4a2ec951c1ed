import * as React from "react";
import { Models, EWidgetStatus } from "../../Models";
export interface IApplicationRootProps {
    propsfilter?: (props: Models.IBaseAppProps) => Models.IBaseAppProps;
    placeholder?: any | null;
    children?: any;
}
export declare const ApplicationRootComponent: import("react-redux").ConnectedComponent<React.FC<Models.IBaseAppProps & IApplicationRootProps>, {
    children?: any;
    localization: import("bwtk").LocalizationState;
    widgetStatus: EWidgetStatus;
    propsfilter?: ((props: Models.IBaseAppProps) => Models.IBaseAppProps) | undefined;
    placeholder?: any | null;
    errorHandlerProps: Models.IErrorHandlerProps;
    context?: React.Context<import("react-redux").ReactReduxContextValue<any, import("redux").UnknownAction> | null> | undefined;
    store?: import("redux").Store | undefined;
}>;
