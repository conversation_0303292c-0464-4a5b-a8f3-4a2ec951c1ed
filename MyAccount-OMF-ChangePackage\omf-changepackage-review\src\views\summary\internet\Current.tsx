import { Components, ValueOf, Volt } from "omf-changepackage-components";
import * as React from "react";
import { FormattedDate, FormattedMessage } from "react-intl";
import { Spacer } from "../Spacer";
import { Flag } from "../ChargeItem";

interface IComponentProps {
  productOfferings: Array<Volt.IProductOffering>;
}

const CurrentInternet: React.FunctionComponent<IComponentProps> = React.memo(({ productOfferings }) => (
  <React.Fragment>
    {
      productOfferings
        .map((productOffering: Volt.IProductOffering, idx: number) => (
          <React.Fragment>
            <div className="flexCol" key={productOffering.id}>
              <div className="flexRow flex-justify-space-between">
                <p className="txtBlack txtSize18 no-margin floatL">
                  <FormattedMessage id={`${productOffering.displayGroupKey}`} />
                </p>
              </div>
              <div className="flexRow flex-justify-space-between block-xs">
                <p className="noMargin">
                  {productOffering.name || productOffering.id}
                  <Components.Visible when={
                    // ValueOf(productOffering, "displayGroupKey") !== Volt.EDIsplayGroupKey.PROMOTION &&
                    (ValueOf(productOffering, "state") === Volt.EOfferingState.Remove ||
                        ValueOf(productOffering, "state") === Volt.EOfferingState.Removed ||
                        ValueOf(productOffering, "state") === Volt.EOfferingState.Delete)
                  }>
                    <Flag message={"removed"} />
                  </Components.Visible>
                  <Components.Visible when={
                    ValueOf(productOffering, "displayGroupKey") === Volt.EDIsplayGroupKey.PROMOTION &&
                      ValueOf(productOffering, "promotionDetails.expiryDate", false)
                  }>
                    <br />
                    <span className="d-sm-block pad-5-left-xs">
                      <FormattedDate value={ValueOf(productOffering, "promotionDetails.expiryDate", "")} format="yMMMMd" timeZone="UTC">
                        {
                          (expiryDate) => <FormattedMessage id="PromotionExpires" values={{ expiryDate }} />
                        }
                      </FormattedDate>
                    </span>
                  </Components.Visible>
                </p>
                <p className="noMargin txtBold txtSize14">
                  <Components.BellCurrency value={ValueOf(productOffering, "regularPrice.price") || 0} />{
                    productOffering.regularPrice && productOffering.regularPrice.priceType === "Recurring" && <FormattedMessage id="PER_MO" />
                  }
                </p>
              </div>
              <Components.Visible when={ValueOf(productOffering, "promotionDetails.description", false)}>
                <div className="flexRow flex-justify-space-between block-xs">
                  <p className="noMargin">
                    <span className="spacer15 d-none d-sm-block" aria-hidden="true" />
                    {ValueOf(productOffering, "promotionDetails.description")}
                    <Components.Visible when={
                      // ValueOf(productOffering, "displayGroupKey") !== Volt.EDIsplayGroupKey.PROMOTION &&
                      (ValueOf(productOffering, "promotionDetails.state") === Volt.EOfferingState.Remove ||
                          ValueOf(productOffering, "promotionDetails.state") === Volt.EOfferingState.Removed ||
                          ValueOf(productOffering, "promotionDetails.state") === Volt.EOfferingState.Delete)
                    }>
                      <Flag message={"removed"} />
                    </Components.Visible>
                    <Components.Visible when={ValueOf(productOffering, "promotionDetails.expiryDate", false)}>
                      <span className="d-sm-block pad-5-left-xs">
                        <FormattedDate value={ValueOf(productOffering, "promotionDetails.expiryDate", "")} format="yMMMMd" timeZone="UTC">
                          {
                            (expiryDate) => <FormattedMessage id="PromotionExpires" values={{ expiryDate }} />
                          }
                        </FormattedDate>
                      </span>
                    </Components.Visible>
                  </p>
                  <p className="noMargin txtBold txtSize14">
                    <span className="spacer15 d-none d-sm-block" aria-hidden="true" />
                    <Components.BellCurrency value={ValueOf(productOffering, "promotionDetails.discountPrice.price") || 0} />{
                      ValueOf(productOffering, "promotionDetails.discountPrice.priceType", "") === "Recurring" && <FormattedMessage id="PER_MO" />
                    }
                  </p>
                </div>
              </Components.Visible>
            </div>
            <Spacer />
          </React.Fragment>
        ))
    }
  </React.Fragment>
));

export default CurrentInternet;
