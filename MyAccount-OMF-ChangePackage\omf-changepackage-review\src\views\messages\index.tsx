import * as React from "react";
import { useSelector, shallowEqual } from "react-redux";
import { FormattedMessage } from "react-intl";
import { IStoreState } from "../../models";
import { Volt, Utils, FormattedHTMLMessage } from "omf-changepackage-components";

const CurrentFlowType = Utils.getFlowType();

export const Messages: React.FunctionComponent = () => {
  const messages: Array<Volt.IMessage> = useSelector((state: IStoreState) => state.messages, shallowEqual);

  return (
    <React.Fragment>
      <div className="bgWhite flexBlock pad-20-left pad-20-right pad-25-top pad-25-bottom margin-15-bottom txtBlack">
        <span className="virgin-icon icon-warning txtSize36">
          <span className="virgin-icon path1 yellowIcon"></span>
          <span className="volt-icon path2"></span>
        </span>
        <div  id={`MSG_PARAGRAPH_${CurrentFlowType}`} className="pad-15-left content-width valign-top pad-0-xs">
          <h2 className="virginUltraReg txtSize18 txtDarkGrey1 no-margin-top txtUppercase accss-info-text-header">
            <FormattedMessage id="HEADS_UP" />
          </h2>  
          <span>
            <FormattedHTMLMessage id={`MSG_PARAGRAPH_${CurrentFlowType}`} />
          </span>

          {/* <p className="txtSize14 txtGray4A sans-serif no-margin">
            <FormattedMessage id="MSG_PARAGRAPH_2" />
          </p> */}

          {
            (messages || []).map((message: Volt.IMessage) => (
              <p className="txtSize14 txtGray4A sans-serif no-margin" key={message.messageCode} dangerouslySetInnerHTML={{ __html: message.messageBody }} />
            ))
          }

        </div>
      </div>
    </React.Fragment>
  );
};
