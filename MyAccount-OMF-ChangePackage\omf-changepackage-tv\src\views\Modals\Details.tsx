import { Actions, Components, ValueOf, Omniture } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { IStoreState, ITVChannel } from "../../models";
import { toCharacteristicsJSON, translateStringList } from "../../utils/Characteristics";

const {
  Modal
} = Components;

interface IComponentProps extends ITVChannel {
}

interface IComponentDispatches {
  closeLightbox: () => void;
}

export const ModalId: string = "CHANNEL_DETIALS";

const Component: React.FC<IComponentProps & IComponentDispatches> = ({
  name,
  imagePath,
  characteristics,
  shortDescription,
  longDescription,
  channelNumber,
  closeLightbox
}) => {
  const { language, genre, culture } = toCharacteristicsJSON(characteristics);
  const onShown = () => {
    Omniture.useOmniture().trackFragment({
      id: "channelDetailsLightbox",
      s_oAPT: {
        actionId: 104
      },
      s_oPRM: "Channel details"
    });
  };
  const onDismissed = () => {
    Omniture.useOmniture().trackAction({
      id: "channelDetailsLightbox",
      s_oAPT: {
        actionId: 647
      },
      s_oBTN: "Close"
    });
  };
  return <Modal
    modalId={ModalId}
    onDismiss={onDismissed}
    onShown={onShown}
    title={<FormattedMessage id="CHANNEL_DETIALS_TITLE" values={{ name }} />}>
    <div className="pad-30 pad-15-left-right-xs">
      <div className="d-flex flex-column flex-sm-row">
        <div className="heightFitContent flexStatic">
          <div style={{ width: "94px", height: "94px" }} className="margin-15-bottom margin-30-right borderGrayLight6 flex align-center">
            <img src={imagePath} alt={name} className="fill pad-5" />
          </div>
        </div> 
        <div className="pad-30-left">
          <span className="sr-only"><FormattedMessage id="Channel number" /></span>
          <p className="no-margin txtSize16 txtVirginBlue line-height-18">{channelNumber}</p>
          <div className="spacer5" aria-hidden="true"></div>
          <p className="no-margin txtSize16 vm-dark-grey2 line-height-18 txtBold">{[translateStringList(genre), translateStringList(culture), translateStringList(language)].filter(i => Boolean(i)).join(" / ")}</p>
          <div className="spacer15" aria-hidden="true"></div>
          <p className="no-margin txtSize14 vm-dark-grey2 line-height-18" dangerouslySetInnerHTML={{ __html: longDescription || shortDescription }} />
        </div>
      </div>
    </div>
  </Modal>;
};

export default connect<IComponentProps, IComponentDispatches>(
  ({ lightboxData }: IStoreState) =>
    ValueOf<ITVChannel>(lightboxData, undefined, {}),
  (dispatch) => ({
    closeLightbox: () => dispatch(Actions.closeLightbox(ModalId)),
  })
)(Component);
