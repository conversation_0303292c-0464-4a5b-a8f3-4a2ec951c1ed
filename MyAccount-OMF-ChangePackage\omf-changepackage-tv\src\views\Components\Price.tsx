import * as React from "react";
import { FormattedMessage, FormattedDate } from "react-intl";
import {
  Volt,
  Components,
  ValueOf
} from "omf-changepackage-components";

const { Visible, Currency } = Components;

interface IPriceProps {
  regularPrice: Volt.IPriceDetail;
  promotionDetails: Volt.IPromotionDetail;
}

const Price: React.FC<IPriceProps> = ({
  regularPrice,
  promotionDetails
}) => <>
  <div className="spacer5" />
  <Visible when={ValueOf(promotionDetails, "description", false)}>
    <span className="package-name pad-5 fill-xs txtSize12 txtGray border-radius-3 bgGrey sans-serif txtBold pad-10-left pad-10-right inline-block">
      <Visible when={ValueOf(promotionDetails, "discountDuration", false)}>&nbsp;
        <FormattedMessage id="PromotionValid" values={{ price: Math.abs(ValueOf(promotionDetails, "discountPrice.price", 0)), discountDuration: ValueOf(promotionDetails, "discountDuration", "") }} />
      </Visible>
      <Visible when={ValueOf(promotionDetails, "expiryDate", false)}>&nbsp;
        <FormattedDate value={ValueOf(promotionDetails, "expiryDate", "")} format="yMMMMd" timeZone="UTC">
          {
            (expiryDate) => <FormattedMessage id="PromotionExpires" values={{ expiryDate }} />
          }
        </FormattedDate>
      </Visible>
    </span>
  </Visible>
  <div className="txtCurrency virginUltraReg txtBlack txtSize40 fill-xs">
    <Visible when={ValueOf(promotionDetails, undefined, false)}>
      <FormattedMessage id="Now" />&nbsp;
    </Visible>
    <Currency
      value={
        isNaN(ValueOf(promotionDetails, "promotionalPrice", {}).price)
          ? ValueOf(regularPrice, "price", 0)
          : ValueOf(promotionDetails, "promotionalPrice.price", 0)
      }
      monthly={true}
    />
    <Visible when={ValueOf(promotionDetails, undefined, false)}>
      <p className="txtSize12 txtGray txtBold fill-xs flex">
        <FormattedMessage
          id="Current Price"
          values={ValueOf(regularPrice, undefined, {}) as any}
        />
      </p>
    </Visible>
  </div>
  <Visible when={ValueOf(promotionDetails, "legalMessage", false)}>
    <p className="txtSize12 txtGray">
      {ValueOf(
        promotionDetails,
        "legalMessage",
        <FormattedMessage id="Prices may increase legal" />
      )}
    </p>
  </Visible>
</>;

export default Price;
