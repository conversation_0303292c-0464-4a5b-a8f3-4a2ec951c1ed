import { Injectable } from "bwtk";
import { Actions, EFlowType, EWidgetStatus, Omniture, Utils } from "omf-changepackage-components";
import { combineEpics, Epic, ofType } from "redux-observable";
import { mergeMap, catchError , of } from "rxjs";


const {
  omniPageLoaded
} = Actions;

@Injectable
export class OmnitureEpics {
  widgetState: EWidgetStatus = EWidgetStatus.INIT;

  combineEpics() {
    return combineEpics(
      this.pageLoadedEpic
    );
  }

  private get pageLoadedEpic(): UserAccountEpic {
    return (action$: any, store) =>
      action$.pipe(
        ofType(omniPageLoaded.toString()),
        mergeMap(() => {
          const omniture = Omniture.useOmniture();
          const currentFlowType = Utils.getFlowType();
          let s_oSS3, s_oSS2;
          switch (currentFlowType) {
            case EFlowType.INTERNET:
              s_oSS2 = "Internet";
              s_oSS3 = "Change package";
              break;
            case EFlowType.TV:
              break;
            case EFlowType.ADDTV:
              break;
            case EFlowType.BUNDLE:
              s_oSS2 = "Bundle";
              s_oSS3 = "Add Tv";
              break;
            default:
              // No specific handling needed for other flow types
              break;
          }
          omniture.trackPage({
            id: "AppointmentPage",
            s_oSS1: "~",
            s_oSS2: s_oSS2 ? s_oSS2 : "~",
            s_oSS3: s_oSS3 ? s_oSS3 : "Change package",
            s_oPGN: "Installation",
            s_oPLE: {
              type: Omniture.EMessageType.Warning,
              content: {
                ref: "IstallationMessageBanner"
              }
            }
          });
          return of([]);
        }),
        catchError((error: Response) => of([]))
      );
  }
}

type UserAccountEpic = Epic<any, any, void, any>;
