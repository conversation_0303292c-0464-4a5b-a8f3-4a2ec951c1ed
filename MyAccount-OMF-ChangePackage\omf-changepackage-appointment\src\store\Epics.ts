import { Injectable } from "bwtk";
import { Epic, combineEpics, ofType } from "redux-observable";
import { EWidgetStatus, Actions } from "omf-changepackage-components";
import { filter, mergeMap , of } from "rxjs";

import { getAppointment } from "./Actions";
import { AppointmentEpics } from "./Epics/Appointment";
import { OmnitureEpics } from "./Epics/Omniture";

const {
  setWidgetStatus,
  broadcastUpdate,
  setAppointmentVisited
} = Actions;

@Injectable
export class Epics {
  constructor(
    public omnitureEpics: OmnitureEpics,
    public appointmentEpics: AppointmentEpics
  ) {}

  combineEpics() {
    return combineEpics(
      this.onWidgetStatusEpic
    );
  }

  private get onWidgetStatusEpic(): GeneralEpic {
    return (action$: any) =>
      action$.pipe(
        ofType(setWidgetStatus.toString()),
        filter(({ payload }: ReduxActions.Action<EWidgetStatus>) => payload === EWidgetStatus.INIT),
        mergeMap(() => of(
          broadcastUpdate(setAppointmentVisited()),
          getAppointment()
        ))
      );
  }

}

type GeneralEpic = Epic<any, any, void, any>;
