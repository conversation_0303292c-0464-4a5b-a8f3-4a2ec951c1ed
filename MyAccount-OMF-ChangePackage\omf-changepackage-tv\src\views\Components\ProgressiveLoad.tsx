import * as React from "react";
import { usePrevious } from "../../utils/Filter";

const MIN_BLOCK = 15;

interface IComponentProps {
  list: Array<any>;
  formatter: (item: any, index?: number, list?: Array<any>) => any;
}

export const ProgressiveLoader: React.FC<IComponentProps> = ({
  list,
  formatter
}) => {
  const [progress, setProgress] = React.useState(list.length > MIN_BLOCK ? 0 : list.length);
  const _list = usePrevious(list);
  React.useEffect(() => {
    if (!_list || _list.length !== list.length)
      setProgress(list.length > MIN_BLOCK ? 0 : list.length);
  }, [list]);
  React.useEffect(() => {
    if (progress < list.length) {
      requestAnimationFrame(() => setProgress(progress + MIN_BLOCK));
    }
  }, [progress]);
  return <>
    {
      list.slice(0, progress).map(formatter)
    }
  </>;
};
