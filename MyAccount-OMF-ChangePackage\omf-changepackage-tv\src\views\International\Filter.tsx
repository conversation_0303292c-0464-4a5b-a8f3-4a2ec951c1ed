import * as React from "react";
import { FormattedMessage } from "react-intl";

interface IFilterProps {
  filter: string;
  languages: Array<string>;
  setFitler: (state: string) => void;
}
export const Filter: React.FC<IFilterProps> = ({
  filter,
  languages,
  setFitler
}) => <div id="filtersContainer" className="tvcsfilters-filtersContainer bell-tv-package col-xs-12 bgGray19 container-full-width-xs">
  <div id="filterInputsContainer" className="tvcsfilters-filtersInputsContainer bell-tv-package-body clearfix bgWhite">
    <div className="tvcsfilters-conditionalInlineBlock flexBlock flexCenter flexCol-xs">
      <div className="col-xs-12 col-sm-3">
        <label htmlFor="Select_Language" className="tvcsfilters-conditionalTitleFormatting noMargin txtLightGray3"><FormattedMessage id="Select a language region" /></label>
        <span className="spacer10 col-xs-12 visible-xs" />
      </div>
      <div className="col-xs-12 col-sm-9">
        <div className="tvcsfilters-conditionalFilterPadding15 tvcsfilters-conditionalInlineBlock">
          <div className="form-control-select-box tvcsfilters-xs-select-dropdown col-xs-12 col-sm-8">
            <select id="Select_Language" value={filter} onChange={(e) => setFitler(e.target.value)} className="form-control form-control-select tvcsfilters-select-dropdown-filter txtSize14 bgGrayLight1">
              <FormattedMessage id="All languages" >
                {
                  txt => <option id="all" value="all">{txt}</option>
                }
              </FormattedMessage>
              {
                languages.map((option, i) => (<FormattedMessage id={option}>
                  {
                    txt => <option id={`option_${i}`} value={option}>{txt}</option>
                  }
                </FormattedMessage>))
              }
            </select>
            <span aria-hidden="true"
              style={{
                backgroundImage: "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAYBAMAAADT3mpnAAAAD1BMVEUAAADhCgrhCgrhCgrhCiGX/cTIAAAAA3RSTlMAv5hn/23fAAAAPklEQVQI12MAAgUGMGByhNDCJmABRmNjRzDX2NhEAMwFCoC4IAAUoCqAmwuxxxAqIABxhyFEBdRWRhAX5m4AQWUIfOEz3hMAAAAASUVORK5CYII=)",
                backgroundPosition: "center left",
                backgroundRepeat: "no-repeat",
                width: "24px",
                backgroundSize: "40%"
              }}></span>
          </div>
        </div>
      </div>
    </div>
    <div className="tvcsfilters-conditionalSpacerShadow" />
  </div>
</div >;
