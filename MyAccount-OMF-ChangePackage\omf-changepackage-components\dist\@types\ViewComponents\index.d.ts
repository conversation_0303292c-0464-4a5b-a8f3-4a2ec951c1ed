import * as React from "react";
import { IErrorComponent } from "./Error";
import { IContainerComponent, IPanelComponent, IBRF3ContainerComponent } from "./Container";
import { LightboxContainer } from "./Lightbox";
import { RestrictionModalView } from "./Restriction";
import { IEllipsisText } from "./EllipsisText";
import { IApplicationRootProps } from "./Application";
import { ICurrencyComponetProps, BellCurrencyConnectedProps } from "./Localization";
import { ReduxPersistGate } from "./ReduxPersistGate";
import { VisibleComponent } from "./VisibilityContainer";
export declare namespace Components {
    const Error: IErrorComponent;
    const Container: IContainerComponent;
    const Panel: IPanelComponent;
    const BRF3Container: IBRF3ContainerComponent;
    const Modal: typeof LightboxContainer;
    const RestrictionModal: typeof RestrictionModalView;
    const ApplicationRoot: React.FC<IApplicationRootProps>;
    const EllipsisText: IEllipsisText;
    const Currency: React.FC<ICurrencyComponetProps>;
    const BellCurrency: React.FC<BellCurrencyConnectedProps>;
    const BrandedMessage: React.FC<any>;
    const PersistGate: typeof ReduxPersistGate;
    const Visible: typeof VisibleComponent;
}
