@import "mixins";

/* Bell channel steps module
 * 
 * A ribbon-like interface
 * used to track the A la carte
 * activity
*/

$animationSpeed: 150ms;
$selectionIndicatorPlacement: 165px;

/**
 * Slide in animation for glued bits
 */

@keyframes glue-slide-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.bell-tv-channels-stepflow {
    padding: 16px 0 16px 38px;
    @media #{$media-mobile} {
        padding: 16px 38px 16px 38px;
    }
    .step-arrow {
        position: absolute;
        font-size: 0;
        top: 0;
        width: 38px;
        height: 130px;
        padding: 0;
        cursor: pointer;
        color: transparent;
        border: none;
        background: transparent;
        overflow: hidden;
        z-index: 2;
        @media #{$media-mobile} {
            display: block;
            height: 100%;
        }
    }
    .step-prev {
        margin-left: -38px;
        &:before {
            font-family: "bell-icon2";
            content: "\ea04";
        }
    }
    .step-next {
        margin-right: calc(#{$selectionIndicatorPlacement} - 38px);
        @media #{$media-mobile} {
            margin-right: 0;
        }
        right: 0;
        &:before {
            font-family: "bell-icon";
            content: "\e012";
        }
    }
    .step-prev:before,
    .step-next:before {
        color: #00549a;
        width: 75px;
        font-size: 20px
    }
    .bell-tv-channel-steps {
        display: block;
        width: 100%;
        height: 100px;
        overflow-y: hidden;
        overflow-x: scroll;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: none;
        overflow: -moz-scrollbars-none;
        &::-webkit-scrollbar {
            display: none;
        } // margin: 0 $selectionIndicatorPlacement 0 0px;
        @media #{$media-mobile} {
            margin: 0;
            height: 126px;
        }
        .bell-tv-channel-steps-container {
            top: 0;
            width: 100%;
            height: 100%;
            position: absolute;
            .bell-tv-channel-steps-track {
                transform: translateX(0);
                -webkit-transform: translateX(0);
                -ms-transform: translateX(0);
                -moz-transform: translateX(0);
                height: 96px;
                @media #{$media-mobile} {
                    height: 126px;
                }
            }
            .bell-tv-channel-steps-indicator-container {
                left: 30px;
                position: relative;
                @media #{$media-mobile} {
                    width: unset !important;
                }
            }
            .bell-tv-channel-steps-progressbar,
            .bell-tv-channel-steps-list {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                margin: 0;
                @media #{$media-mobile} {
                    // width: 200%;
                    // margin: 0 15px;
                }
            }
            .bell-tv-channel-steps-progressbar {
                top: 50%;
                @media #{$media-mobile} {
                    top: 49%;
                }
                background-color: #D4D4D4;
                left: 30px;
                z-index: 0;
                box-sizing: content-box;
                &,
                .bell-tv-channel-steps-progress {
                    height: 2px;
                }
                .bell-tv-channel-steps-progress {
                    position: relative;
                    background-color: #00549A;
                    transition: width $animationSpeed;
                }
                &:after {
                    background-image: repeating-linear-gradient(to right, #fff, #fff 8px, transparent 8px, transparent 16px);
                    content: '';
                    display: block;
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    z-index: -1;
                }
                &.additionalProgress:after {
                    background-image: repeating-linear-gradient(to right, #00549A, #00549A 8px, #FFF 8px, #FFF 16px) !important;
                }
            }
            .bell-tv-channel-steps-list {
                list-style: none;
                padding: 0;
                left: 32px;
                z-index: 1;
                .bell-tv-channel-step {
                    position: absolute;
                    margin-top: -12px;
                    top: 50%;
                    list-style: none;
                    @media #{$media-mobile} {
                        top: 49%;
                    }
                    &.txtLeft {
                        margin-left: -12px;
                    }
                    &.txtCenter {
                        transform: translateX(-50%);
                        -webkit-transform: translateX(-50%);
                        -ms-transform: translateX(-50%);
                        -moz-transform: translateX(-50%);
                    }
                    &.txtRight {
                        margin-left: 12px;
                        transform: translateX(-100%);
                        -webkit-transform: translateX(-100%);
                        -ms-transform: translateX(-100%);
                        -moz-transform: translateX(-100%);
                    }
                    &.bell-tv-channel-step-first {
                        .icon:before {
                            content: " ";
                            width: 24px;
                            height: 24px;
                            background-color: #00549A;
                            display: block;
                            border-radius: 50%;
                        }
                    }
                    .icon {
                        display: inline-block;
                        text-align: center;
                        line-height: 26px;
                        color: #D4D4D4;
                        &:before {
                            top: 0;
                            vertical-align: top;
                        }
                    }
                    &.bell-tv-channel-step-active {
                        .icon {
                            color: #00549A;
                            background-color: #fff;
                            border-radius: 50%;
                            margin-bottom: -3px;
                        }
                    }
                    .description {
                        margin-top: 5px;
                        white-space: nowrap;
                        width: 140px; // @media #{$media-mobile} {
                        //     width: 150px;
                        //     white-space: normal;
                        //     text-align: left;
                        //     padding-left: 60px;
                        // }
                    }
                    &.bell-tv-channel-step-disabled {
                        .description {
                            color: #d4d4d4;
                        }
                    }
                    .bell-tv-channel-step-message {
                        position: absolute;
                        z-index: 1;
                        top: -39px;
                        width: 200%;
                        left: -50%;
                        a {
                            text-decoration: underline;
                        }
                    }
                }
            }
        }
    }
}

// Cahnnel selection indicator
// circular bit showing the number of
// currently selected channels
// out of total available number
.bell-tv-channel-steps-indicator {
    position: absolute;
    padding: 2px 8px;
    z-index: 2;
    transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    background-color: #00549a;
    color: #fff;
    white-space: nowrap;
    top: 0;
    transition: left $animationSpeed;
    @media #{$media-mobile} {
        position: static;
        transform: unset;
        -webkit-transform: unset;
        -ms-transform: unset;
        -moz-transform: unset;
        margin: 0; // We need to suppress the position
        // set in HTML
        // left: 0!important;
    }
    .bell-tv-channel-steps-indicator-pointer {
        content: " ";
        display: block;
        position: absolute;
        width: 0;
        height: 0;
        bottom: -7px;
        margin-left: -8px;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid #00549a;
        transition: left $animationSpeed;
    }
}

.bell-tv-channel-selection-indicator {
    width: 83px;
    height: 83px;
    line-height: 1;
    border-radius: 50%;
    padding-top: 16px;
    box-shadow: 0 2px 20px 0 rgba(0, 0, 0, 0.25);
    background-color: rgba(255, 255, 255, 0.95);
    position: absolute;
    margin-top: 8px;
    right: 0;
    cursor: pointer;
}

.bell-tv-channel-selection-indicator span:last-child {
    line-height: 16px;
}

// Attache the channels selection indicator
// to the top of the page at all times!
// .bell-tv-channel-selection-indicator {
//     position: sticky;
//     top: 45px;
// }
.bell-tv-channel-selection-indicator-container {
    position: absolute;
    left: 0;
    width: $selectionIndicatorPlacement;
    padding: 0 30px 0 30px;
}

.bell-tv-tooltip-container {
    position: absolute;
    width: 100%;
    @media #{$media-mobile} {
        position: static;
        height: 0;
    }
}

.glue {
    position: fixed !important;
    top: 0 !important;
    width: 1200px;
    @media (max-width: 1200px) {
        width: 100%;
    }
    left: 50%;
    transform: translate(-50%, 0);
    -webkit-transform: translate(-50%, 0);
    -ms-transform: translate(-50%, 0);
    -moz-transform: translate(-50%, 0);
    z-index: 100;
    padding: 0 15px;
    @media (min-width:1240px) {
        padding: 0;
    }
    @media #{$media-mobile} {
        padding: 0;
    }
    .glue-container {
        width: 66.66666667%; // col-8
        padding-left: 30px;
        @media #{$media-tab-mobile} {
            width: 100%;
            padding-left: 0px;
        }
        @media #{$media-mobile} {
            padding: 0 15px;
        }
        float: right;
        .bell-tv-channel-steps-indicator {
            position: relative;
            width: 100%;
            transform: translateX(0);
            -webkit-transform: translateX(0);
            -ms-transform: translateX(0);
            -moz-transform: translateX(0);
            left: 0 !important;
            display: block;
            margin-left: 0;
            transition: unset;
            animation: glue-slide-in $animationSpeed;
            .bell-tv-channel-steps-indicator-pointer {
                display: none;
            }
        }
        .bell-tv-channel-selection-indicator {
            float: right;
            top: 46px;
            position: absolute;
            right: 20px;
            animation: glue-slide-in $animationSpeed;
            @media #{$media-tablet} {
                right: 35px;
            }
        }
    }
}

.leftShaddow.rightShaddow {
    background: radial-gradient(circle at -5% 50%, rgba(0, 00, 00, 0.2), rgba(0, 0, 10, 0.0) 10%), radial-gradient(circle at 105% 50%, rgba(0, 00, 00, 0.2), rgba(0, 0, 10, 0.0) 10%);
}

.leftShaddow {
    background: radial-gradient(circle at -5% 50%, rgba(0, 00, 00, 0.2), rgba(0, 0, 10, 0.0) 10%);
}

.rightShaddow {
    background: radial-gradient(circle at 105% 50%, rgba(0, 00, 00, 0.2), rgba(0, 0, 10, 0.0) 10%);
}

.modal-content {
    .bell-tv-channel-steps-indicator {
        left: 0 !important;
        transform: none;
        -webkit-transform: none;
        -ms-transform: none;
        -moz-transform: none;
        position: relative;
        display: inline-block;
        @media #{$media-mobile} {
            width: 100%;
        }
        width: fit-content;
        top: 10px;
        .bell-tv-channel-steps-indicator-pointer {
            transform: rotate(-180deg);
            -webkit-transform: rotate(-180deg);
            -ms-transform: rotate(-180deg);
            -moz-transform: rotate(-180deg);
            top: -8px;
        }
    }
}