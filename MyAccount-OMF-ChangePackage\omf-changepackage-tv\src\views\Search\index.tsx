
import * as React from "react";
import { useDispatch } from "react-redux";
import { FormattedMessage } from "react-intl";
import { useLocation } from "react-router-dom";
import { Components, ValueOf, Actions } from "omf-changepackage-components";
import { useSearch } from "../../utils/Search";
import Filter from "../Components/Filter";

const { Visible } = Components;

let _query = "";

function parseQueryString(query: string): any {
  if (!Boolean(query)) return {};
  const frags = query.replace("?", "").split("&");
  return frags.reduce(
    (acc: any, frag: any) => {
      const pair = frag.split("=");
      acc[pair[0]] = decodeURIComponent(pair[1] || "");
      return acc;
    }, {}
  );
}

interface ComponentProps {
  location?: any;
}

const Search = (props: ComponentProps) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const query = React.useMemo<string>(() => ValueOf<string>(parseQueryString(location.search), "query", ""), [location]);
  const results = useSearch(query);

  React.useEffect(() => {
    _query !== query
      && results
      && dispatch(Actions.omniPageLoaded("search", {
        id: "Search result",
        s_oAPT: {
          actionId: 395,
          actionresult: 2,
          applicationState: (results.length > 0) ? 1 : 2
        },
        s_oSRT: (_query = query)
      }));
  }, [results]);

  return results ? (<>
    <div className="flexRow flex-justify-space-between">
      <div className="margin-xs">
        <h2
          id="Search"
          className="virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"
        >
          <Visible
            when={results.length > 0}
            placeholder={
              <FormattedMessage
                id="NO_SEARCH_RESULT_FOR"
                values={{ value: query }}
              >
                {(no_search) => <span aria-live="polite" role="alert">{no_search}</span>}
              </FormattedMessage>
            }
          >
            <FormattedMessage
              id="SEARCH_RESULT_FOR"
              values={{ value: query }}
            >
              {(search_search) => <span aria-live="polite" role="alert">{search_search}</span>}
            </FormattedMessage>
          </Visible>
        </h2>
      </div>
    </div>
    <div className="spacer15" aria-hidden="true" />
    <Filter
      groupName="Search"
      channels={results}
      allowSelection={true}
      forceSelectable={true}
      allowMultipleWaysToAdd={true}
      showFilters={false}
      showHeader={false}
    />
  </>) : null;
};

export default Search;
