/*! omf-changepackage-tv (widget) 0.1.0 | bwtk 6.1.0 | 2025-08-22T20:30:18.334Z */
!function(e,t){var n,a;if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("bwtk"),require("omf-changepackage-components"),require("react"),require("react-redux"),require("react-router-dom"),require("react-intl"),require("redux"),require("redux-actions"),require("redux-observable"),require("js-search"),require("rxjs"));else if("function"==typeof define&&define.amd)define(["bwtk","omf-changepackage-components","react","react-redux","react-router-dom","react-intl","redux","redux-actions","redux-observable","js-search","rxjs"],t);else for(a in n="object"==typeof exports?t(require("bwtk"),require("omf-changepackage-components"),require("react"),require("react-redux"),require("react-router-dom"),require("react-intl"),require("redux"),require("redux-actions"),require("redux-observable"),require("js-search"),require("rxjs")):t(e.bwtk,e.OMFChangepackageComponents,e.React,e.ReactRedux,e.ReactRouterDOM,e.ReactIntl,e.Redux,e.ReduxActions,e.ReduxObservable,e.JSSearch,e.rxjs))("object"==typeof exports?exports:e)[a]=n[a]}(self,function(e,t,n,a,r,l,i,c,o,s,u){return function(){"use strict";function m(e){var t,n=En[e];return void 0!==n?n.exports:(t=En[e]={exports:{}},gn[e](t,t.exports,m),t.exports)}function d(e,t){function n(){this.constructor=e}if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");R(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function p(e,t,n,a){var r,l,i=arguments.length,c=i<3?t:null===a?a=Object.getOwnPropertyDescriptor(t,n):a;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)c=Reflect.decorate(e,t,n,a);else for(l=e.length-1;l>=0;l--)(r=e[l])&&(c=(i<3?r(c):i>3?r(t,n,c):r(t,n))||c);return i>3&&c&&Object.defineProperty(t,n,c),c}function f(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function g(e,t){var n,a,r,l,i="function"==typeof Symbol&&e[Symbol.iterator];if(!i)return e;n=i.call(e),r=[];try{for(;(void 0===t||t-- >0)&&!(a=n.next()).done;)r.push(a.value)}catch(c){l={error:c}}finally{try{a&&!a.done&&(i=n.return)&&i.call(n)}finally{if(l)throw l.error}}return r}function E(e,t,n){if(n||2===arguments.length)for(var a,r=0,l=t.length;r<l;r++)!a&&r in t||(a||(a=Array.prototype.slice.call(t,0,r)),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))}function h(e){var t=e.map(function(e){var t=e.characteristics.find(function(e){return"callSign"===e.name});return V(V({},e),{callSign:t?t.value:""})});(q=new H.Search("id")).addIndex("name"),q.addIndex("channelNumber"),q.addIndex("callSign"),q.addDocuments(t)}function v(e){function t(e){e.productOfferingType===B.Volt.EProductOfferingType.CHANNEL&&n.indexOf(e.id)<0&&(a.push(e),n.push(e.id))}var n=[],a=[];return e.forEach(function(e){Array.isArray(e.childOfferings)?e.childOfferings.forEach(t):t(e)}),a}function b(e){var t=(0,B.ValueOf)(e,"productOfferingDetail.productOfferingGroups",[]).find(function(e){return"TV"===e.lineOfBusiness}),n=(0,B.ValueOf)(t,"productOfferings",[]),a=n.filter(function(e){return e.displayGroupKey&&e.displayGroupKey!==B.Volt.EDIsplayGroupKey.NONE}).reduce(function(e,t){var n=t.displayGroupKey;return e[n]=e[n]||[],e[n].push(t),e},{}),r={index:n,offerings:a,channels:v(n)};return r.refresh=1e3*Math.random(),h(r.channels),r}function y(e){return(0,B.ValueOf)(e,void 0,[]).reduce(function(e,t){return t.name&&(e[t.name]=(t.value||"").trim()),e},{})}function N(e){return(0,B.ValueOf)(e,void 0,[]).reduce(function(e,t){var n=y(t.characteristics).language;return Boolean(n)&&n.split(",").map(function(e){return e.trim()}).filter(Boolean).forEach(function(t){e.indexOf(t)<0&&e.push(t)}),e.sort()},[]).filter(Boolean).sort()}function x(e,t){return e.filter(function(e){return(y(e.characteristics).language||"").indexOf(t)>-1})}function S(e,t){return void 0===t&&(t="asc"),(0,B.ValueOf)(e,void 0,[]).sort(function(e,n){return((0,B.ValueOf)(y(e.characteristics),"sortPriority",0)-(0,B.ValueOf)(y(n.characteristics),"sortPriority",0))*("asc"===t?1:-1)})}function A(e){return Boolean(e)?e.split(",").map(function(e){return e=e.trim(),Ne.getLocalizedString(e)}).join(", "):e}function _(){for(var e in He)He[e]&&He[e].destroy();Array.from(document.querySelectorAll(".channel-tooltip")).forEach(function(e){return e.remove()}),He={}}function O(e,t){var n=e.current,a=n.parentElement,r=n.clientHeight,l=a.getBoundingClientRect(),i=l.top<-r;t({isFloating:i,leftPos:i?l.right-85:"auto"})}function k(e){return JSON.parse(JSON.stringify(e))}function C(){return k(it)}function T(e,t){var n=e.indexOf(t);return n>-1?e.splice(n,1):e.push(t),e}function w(e){var t=g(G.useState(e||C()),2),n=t[0],a=t[1],r={toggleGenre:function(e){return a(V(V({},n),{genre:T(n.genre,e)}))},setGenre:function(e){return a(V(V({},n),{genre:e?[e]:[]}))},toggleLanguage:function(e){return a(V(V({},n),{language:T(n.language,e)}))},setLanguage:function(e){return a(V(V({},n),{language:[e]}))},toggleDontHave:function(){return a(V(V({},n),{isDontHave:!n.isDontHave}))},toggleHave:function(){return a(V(V({},n),{isHave:!n.isHave}))},reset:function(){return a(C())},setSort:function(e){return a(V(V({},n),{sortBy:e,sortOrder:n.sortBy===e?"desc"===n.sortOrder?"asc":"desc":"asc"}))},hasGenre:function(e){return e?n.genre.indexOf(e)>-1:0===n.genre.length},onlyGenre:function(e){return n.genre.indexOf(e)>-1&&1===n.genre.length},hasLanguage:function(e){return n.language.indexOf(e)>-1},hasDontHave:function(){return n.isDontHave},hasHave:function(){return n.isHave},whichSort:function(){return n.sortBy+n.sortOrder},selectedGenre:function(){return n.genre[0]||"All"},getState:function(){return k(n)},setState:function(e){return a(k(e))}};return[n,r]}function M(e){var t=G.useRef(null);return G.useEffect(function(){t.current=e},[e]),t.current}var L,I,R,V,D,P,B,G,F,W,z,K,U,j,H,q,Y,X,J,Z,Q,ee,te,ne,ae,re,le,ie,ce,oe,se,ue,me,de,pe,fe,ge,Ee,he,ve,be,ye,Ne,xe,Se,Ae,_e,Oe,ke,Ce,Te,we,Me,Le,Ie,Re,Ve,De,Pe,Be,Ge,Fe,We,ze,Ke,Ue,je,He,qe,Ye,Xe,$e,Je,Ze,Qe,et,tt,nt,at,rt,lt,it,ct,ot,st,ut,mt,dt,pt,ft,gt,Et,ht,vt,bt,yt,Nt,xt,St,At,_t,Ot,kt,Ct,Tt,wt,Mt,Lt,It,Rt,Vt,Dt,Pt,Bt,Gt,Ft,Wt,zt,Kt,Ut,jt,Ht,qt,Yt,Xt,$t,Jt,Zt,Qt,en,tn,nn,an,rn,ln,cn,on,sn,un,mn,dn,pn,fn,gn={81:function(e){e.exports=s},102:function(t){t.exports=e},418:function(e){e.exports=u},419:function(e){e.exports=l},442:function(e){e.exports=n},446:function(e){e.exports=t},541:function(e){e.exports=c},634:function(e){e.exports=r},750:function(e){e.exports=i},769:function(e){e.exports=o},999:function(e){e.exports=a}},En={};return m.d=function(e,t){for(var n in t)m.o(t,n)&&!m.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},m.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},m.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},L={},m.r(L),m.d(L,{default:function(){return fn}}),I={},m.r(I),m.d(I,{getAccountDetails:function(){return Y},getCatalog:function(){return J},setAccountDetails:function(){return X},setCatalog:function(){return Z},setNavigation:function(){return Q},toggleSelection:function(){return ee},updateCatalog:function(){return te}}),R=function(e,t){return R=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},R(e,t)},V=function(){return V=Object.assign||function(e){var t,n,a,r;for(n=1,a=arguments.length;n<a;n++)for(r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},V.apply(this,arguments)},Object.create,Object.create,D=function(e){return D=Object.getOwnPropertyNames||function(e){var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},D(e)},"function"==typeof SuppressedError&&SuppressedError,P=m(102),B=m(446),G=m(442),F=m(999),W=m(634),z=m(419),K=m(750),U=m(541),j=m(769),H=m(81),Y=(0,U.createAction)("GET_ACCOUNT_DETAILS"),X=(0,U.createAction)("SET_ACCOUNT_DETAILS",function(e){var t=(0,B.ValueOf)(e,"ProductOfferings",[]),n=[],a=[],r=function(e){var t=(0,B.ValueOf)(e,"Characteristics",[]).find(function(e){return"sortPriority"===e.Name});return 1*(0,B.ValueOf)(t,"Value",0)};return t.forEach(function(e){e.DisplayGroupKey&&a.indexOf(e.DisplayGroupKey)<0&&(a.push(e.DisplayGroupKey),n.push({displayGroupKey:e.DisplayGroupKey,offerings:t.filter(function(t){return t.DisplayGroupKey===e.DisplayGroupKey}).sort(function(e,t){return r(e)-r(t)})}))}),n.sort(function(e,t){switch(!0){case"BASE_PROGRAMMING"===e.displayGroupKey:return-1;case"PROMOTION"===e.displayGroupKey:return 1;default:return 0}})}),J=(0,U.createAction)("GET_TV_CATALOG"),Z=(0,U.createAction)("SET_TV_CATALOG",b),Q=(0,U.createAction)("SET_TV_NAVIGATION",function(e){var t,n=function(e,t){return e.sortPriority-t.sortPriority},a=(0,B.ValueOf)(e,"productOfferingDetail.displayGroup",{}),r=(0,B.ValueOf)(a,"baseOffering",null),l=(0,B.ValueOf)(a,"additionalOfferings",[]).filter(function(e){return e.key&&e.key!==B.Volt.EDIsplayGroupKey.NONE}).map(function(e){return V(V({},e),{offeringKey:e.key})}),i=l.filter(function(e){return e.isRoot}),c=l.filter(function(e){return!e.isRoot});return r&&(r.count=void 0),(t=r?E([r],g(i),!1):i).push({offeringKey:B.Volt.EDIsplayGroupKey.TV_BROWSE_ALL,sortPriority:99}),t.forEach(function(e){switch(e.offeringKey=e.offeringKey||e.key,e.children=c.filter(function(t){return t.parentKey===e.key}).sort(n).map(function(e){switch(e.offeringKey){case B.Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS:e.route=B.EWidgetRoute.TV_InternationalCombos;break;case B.Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE:e.route=B.EWidgetRoute.TV_InternationalAlacarte}return e}),e.offeringKey){case B.Volt.EDIsplayGroupKey.BASE_PROGRAMMING:case B.Volt.EDIsplayGroupKey.TV_BASE_PRODUCT:e.route=B.EWidgetRoute.TV_Packages;break;case B.Volt.EDIsplayGroupKey.ALACARTE:e.route=B.EWidgetRoute.TV_Alacarte;break;case B.Volt.EDIsplayGroupKey.MOVIE:e.route=B.EWidgetRoute.TV_MoviesSeries;break;case B.Volt.EDIsplayGroupKey.ADD_ON:e.route=B.EWidgetRoute.TV_Addons;break;case B.Volt.EDIsplayGroupKey.INTERNATIONAL:e.route=B.EWidgetRoute.TV_International;break;case B.Volt.EDIsplayGroupKey.TV_BROWSE_ALL:e.route=B.EWidgetRoute.TV_Browse}e.refresh=1e3*Math.random()}),t.filter(function(e){return!Boolean(e.parentDisplayGroup)}),t.sort(n)}),ee=(0,U.createAction)("TOGGLE_TV_SELECTION"),te=(0,U.createAction)("UPDATE_TV_CATALOG",function(e,t){var n=function(e){return function(t){var a=e.find(function(e){return e.id===t.id});a&&(a.isCurrent=t.isCurrent,a.isDisabled=t.isDisabled,a.isSelectable=t.isSelectable,a.isSelected=t.isSelected,a.isAlreadyIncludedIn=t.isAlreadyIncludedIn,a.offeringAction=t.offeringAction,a.promotionDetails=t.promotionDetails,a.childOfferings&&t.childOfferings&&t.childOfferings.forEach(n(a.childOfferings)))}},a=(0,B.ValueOf)(e,"productOfferingDetail.productOfferingGroups",[]).find(function(e){return"TV"===e.lineOfBusiness}),r=(0,B.ValueOf)(a,"productOfferings",[]);return"Delta"===(0,B.ValueOf)(a,"productOfferingGroupType","")?(r.forEach(n(t.index)),t.offerings.refresh=1e3*Math.random(),t.channels=E([],g(t.channels),!1),h(t.channels)):t=b(e),V(V({},t),{refresh:1e3*Math.random()})}),ne=m(418),ae=P.CommonFeatures.BaseConfig,re=P.CommonFeatures.configProperty,le=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return d(t,e),p([re({languages:["English","French"],genres:["Family","Movies","News","Sports"],sortBy:"name",sortOrder:"desc"}),f("design:type",String)],t.prototype,"filters",void 0),p([re(""),f("design:type",String)],t.prototype,"flowType",void 0),p([re({}),f("design:type",Object)],t.prototype,"environmentVariables",void 0),p([re({}),f("design:type",Object)],t.prototype,"mockdata",void 0),p([re({}),f("design:type",Object)],t.prototype,"headers",void 0),p([re({base:"http://127.0.0.1:8881"}),f("design:type",Object)],t.prototype,"api",void 0),p([P.Injectable],t)}(ae),ie=function(e){function t(t,n){return e.call(this,t,n)||this}return d(t,e),p([P.Injectable,f("design:paramtypes",[P.AjaxServices,le])],t)}(B.BaseClient),ce=B.Actions.errorOccured,oe=B.Actions.setWidgetStatus,se=function(){function e(e,t){this.client=e,this.config=t,this.widgetState=B.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return(0,j.combineEpics)(this.requestCatalogEpic)},Object.defineProperty(e.prototype,"requestCatalogEpic",{get:function(){var e=this;return function(t){return t.pipe((0,j.ofType)(J.toString()),(0,ne.filter)(function(){return e.widgetState!==B.EWidgetStatus.UPDATING}),(0,ne.mergeMap)(function(){var t;return(0,ne.concat)((0,ne.of)(oe(e.widgetState=B.EWidgetStatus.UPDATING)),e.client.get(B.Utils.appendRefreshOnce(B.Utils.getURLByFlowType((t={},t[B.EFlowType.TV]=e.config.api.catalogAPI,t[B.EFlowType.ADDTV]=e.config.api.addCatalogAPI,t[B.EFlowType.BUNDLE]=e.config.api.bundleCatalogAPI,t)))).pipe((0,ne.mergeMap)(function(t){return(0,B.FilterRestrictionObservable)(t,[Z(t.data),Q(t.data),B.Actions.omniPageLoaded(),oe(e.widgetState=B.EWidgetStatus.RENDERED)])})))}),(0,ne.catchError)(function(e){return(0,ne.of)(ce(new B.Models.ErrorHandler("getCatalog",e)))}))}},enumerable:!1,configurable:!0}),p([P.Injectable,f("design:paramtypes",[ie,le])],e)}(),ue=B.Actions.setWidgetStatus,me=B.Actions.finalizeRestriction,de=B.Actions.clearCachedState,pe=function(){function e(e){this.client=e,this.widgetState=B.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return(0,j.combineEpics)(this.toggleSelectionEpic,this.finalizeRestrictionEpic)},Object.defineProperty(e.prototype,"toggleSelectionEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,j.ofType)(ee.toString()),(0,ne.filter)(function(t){var n=t.payload;return Boolean(n)&&e.widgetState!==B.EWidgetStatus.UPDATING}),(0,ne.mergeMap)(function(t){var a=t.payload;return(0,ne.concat)((0,ne.of)(ue(e.widgetState=B.EWidgetStatus.UPDATING)),e.client.action(a).pipe((0,ne.mergeMap)(function(t){return(0,B.FilterRestrictionObservable)(t,[te(t.data,n.value.catalog),Q(t.data),de([B.EWidgetName.PREVIEW]),ue(e.widgetState=B.EWidgetStatus.RENDERED)])})))}),(0,ne.catchError)(B.Models.ErrorHandlerObservable(ee)))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"finalizeRestrictionEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,j.ofType)(me.toString()),(0,ne.filter)(function(t){var n=t.payload;return Boolean(n)&&Boolean(n.productOfferingDetail)&&e.widgetState!==B.EWidgetStatus.UPDATING}),(0,ne.mergeMap)(function(t){var a=t.payload;return(0,ne.of)(B.Actions.broadcastUpdate(B.Actions.setProductConfigurationTotal((0,B.ValueOf)(a,"productOfferingDetail.productConfigurationTotal"))),te(a,n.value.catalog),Q(a),de([B.EWidgetName.PREVIEW]),ue(e.widgetState=B.EWidgetStatus.RENDERED))}))}},enumerable:!1,configurable:!0}),p([P.Injectable,f("design:paramtypes",[ie])],e)}(),fe=function(){function e(e,t){this.client=e,this.config=t,this.widgetState=B.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return(0,j.combineEpics)(this.requestDataEpic)},Object.defineProperty(e.prototype,"requestDataEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,ne.filter)(function(e){return e.type===Y.toString()}),(0,ne.filter)(function(){return e.widgetState!==B.EWidgetStatus.UPDATING}),(0,ne.mergeMap)(function(){return e.client.get(e.config.api.serviceAccountAPI).pipe((0,ne.mergeMap)(function(e){var t=e.data;return[X(t),J()]}),(0,ne.catchError)(function(){return(0,ne.of)(J())}))}))}},enumerable:!1,configurable:!0}),p([P.Injectable,f("design:paramtypes",[ie,le])],e)}(),ge=B.Actions.omniPageLoaded,Ee=B.Actions.omniPageSubmit,he=function(){function e(){this.widgetState=B.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return(0,j.combineEpics)(this.pageLoadedEpic,this.pageSubmitEpic)},Object.defineProperty(e.prototype,"pageLoadedEpic",{get:function(){return function(e,t){return e.pipe((0,j.ofType)(ge.toString()),(0,ne.filter)(function(e){var t=e.payload;return Boolean(t)}),(0,ne.mergeMap)(function(e){var t=e.payload,n=t.name,a=t.data,r=void 0===a?{}:a,l=B.Omniture.useOmniture(),i=V({id:"".concat(n,"Pageload"),s_oSS1:"~",s_oSS2:"~",s_oSS3:"~",s_oPGN:"Setup your service:"+n,s_oAPT:"~"},r);return B.Utils.getFlowType()!==B.EFlowType.TV||i.s_oAPT||(i.s_oAPT={actionId:394,actionresult:1}),B.Utils.getFlowType()!==B.EFlowType.TV&&l.trackFragment(i),(0,ne.of)()}),(0,ne.catchError)(function(e){return(0,ne.of)()}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageSubmitEpic",{get:function(){return function(e,t){return e.pipe((0,j.ofType)(Ee.toString()),(0,ne.mergeMap)(function(){var e=t.value.catalog;return B.Omniture.useOmniture().trackAction({id:"tvPageSubmit",s_oAPT:{actionId:647},s_oBTN:"Continue",s_oPRD:e.index.filter(function(e){return e.isSelected}).map(function(e){return{category:e.displayGroupKey,name:e.name,sku:"",quantity:"1",price:(0,B.ValueOf)(e,"regularPrice.price","0"),promo:(0,B.ValueOf)(e,"promotionDetails.promotionalPrice.price","")}})}),(0,ne.of)()}),(0,ne.catchError)(function(e){return(0,ne.of)()}))}},enumerable:!1,configurable:!0}),p([P.Injectable],e)}(),ve=B.Actions.setWidgetStatus,be=function(){function e(e,t,n,a){this.omnitureEpics=e,this.userAccountEpic=t,this.catalogEpics=n,this.orderingEpics=a}return e.prototype.combineEpics=function(){return(0,j.combineEpics)(this.onWidgetStatusEpic)},Object.defineProperty(e.prototype,"onWidgetStatusEpic",{get:function(){return function(e){return e.ofType(ve.toString()).filter(function(e){return e.payload===B.EWidgetStatus.INIT}).mergeMap(function(){var e="~";switch(B.Utils.getFlowType()){case B.EFlowType.TV:case B.EFlowType.ADDTV:e="TV";break;case B.EFlowType.BUNDLE:e="Bundle"}switch(B.Utils.getFlowType()){case B.EFlowType.TV:B.Omniture.useOmniture().updateContext({s_oSS2:e,s_oSS3:"Change package"});break;case B.EFlowType.ADDTV:B.Omniture.useOmniture().updateContext({s_oSS2:e,s_oSS3:"Add Tv",s_oAPT:{actionId:507,actionresult:1,applicationState:0}});break;case B.EFlowType.BUNDLE:B.Omniture.useOmniture().updateContext({s_oSS2:e,s_oSS3:"Add Tv",s_oAPT:{actionId:508,actionresult:1,applicationState:0}})}return[Y()]})}},enumerable:!1,configurable:!0}),p([P.Injectable,f("design:paramtypes",[he,fe,se,pe])],e)}(),ye=P.CommonFeatures.BaseLocalization,Ne=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}var n;return d(t,e),n=t,t.getLocalizedString=function(e){var t,a;return n.Instance=n.Instance||P.ServiceLocator.instance.getService(P.CommonServices.Localization),a=(t=n.Instance)?t.getLocalizedString(B.EWidgetName.TV,e,t.locale):e,Boolean(a)?a:e},t.Instance=null,n=p([P.Injectable],t)}(ye),xe=P.CommonFeatures.BaseStore,Ae=(Se=P.CommonFeatures.actionsToComputedPropertyName)(I),_e=Ae.setNavigation,Oe=Ae.setCatalog,ke=Ae.updateCatalog,Ce=Ae.setAccountDetails,Te=Se(B.Actions).handleNav,we=function(e){function t(t,n,a,r){var l=e.call(this,n)||this;return l.client=t,l.epics=a,l.localization=r,l}return d(t,e),Object.defineProperty(t.prototype,"reducer",{get:function(){var e,t,n,a;return(0,K.combineReducers)(V(V(V(V({},B.Reducers.WidgetBaseLifecycle(this.localization)),B.Reducers.WidgetLightboxes()),B.Reducers.WidgetRestrictions()),{navigation:(0,U.handleActions)((e={},e[_e]=function(e,t){return t.payload||e},e),[]),accountDetails:(0,U.handleActions)((t={},t[Ce]=function(e,t){return t.payload||e},t),[]),catalog:(0,U.handleActions)((n={},n[Oe]=function(e,t){return t.payload||e},n[ke]=function(e,t){return t.payload||e},n),{}),navStatus:(0,U.handleActions)((a={},a[Te]=function(e,t){return t.payload},a),!1)}))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"middlewares",{get:function(){return(0,j.combineEpics)(this.epics.omnitureEpics.combineEpics(),this.epics.userAccountEpic.combineEpics(),this.epics.catalogEpics.combineEpics(),this.epics.orderingEpics.combineEpics(),this.epics.combineEpics(),(new B.ModalEpics).combineEpics(),new B.RestricitonsEpics(this.client,"TV_RESTRICTION_MODAL").combineEpics(),(new B.LifecycleEpics).combineEpics())},enumerable:!1,configurable:!0}),p([P.Injectable,f("design:paramtypes",[ie,P.Store,be,Ne])],t)}(xe),Me=B.Components.Modal,Le="CHANNEL_DETIALS",Ie=function(e){var t=e.name,n=e.imagePath,a=e.characteristics,r=e.shortDescription,l=e.longDescription,i=e.channelNumber,c=(e.closeLightbox,y(a)),o=c.language,s=c.genre,u=c.culture;return G.createElement(Me,{modalId:Le,onDismiss:function(){B.Omniture.useOmniture().trackAction({id:"channelDetailsLightbox",s_oAPT:{actionId:647},s_oBTN:"Close"})},onShown:function(){B.Omniture.useOmniture().trackFragment({id:"channelDetailsLightbox",s_oAPT:{actionId:104},s_oPRM:"Channel details"})},title:G.createElement(z.FormattedMessage,{id:"CHANNEL_DETIALS_TITLE",values:{name:t}})},G.createElement("div",{className:"pad-30 pad-15-left-right-xs"},G.createElement("div",{className:"d-flex flex-column flex-sm-row"},G.createElement("div",{className:"heightFitContent flexStatic"},G.createElement("div",{style:{width:"94px",height:"94px"},className:"margin-15-bottom margin-30-right borderGrayLight6 flex align-center"},G.createElement("img",{src:n,alt:t,className:"fill pad-5"}))),G.createElement("div",{className:"pad-30-left"},G.createElement("span",{className:"sr-only"},G.createElement(z.FormattedMessage,{id:"Channel number"})),G.createElement("p",{className:"no-margin txtSize16 txtVirginBlue line-height-18"},i),G.createElement("div",{className:"spacer5","aria-hidden":"true"}),G.createElement("p",{className:"no-margin txtSize16 vm-dark-grey2 line-height-18 txtBold"},[A(s),A(u),A(o)].filter(function(e){return Boolean(e)}).join(" / ")),G.createElement("div",{className:"spacer15","aria-hidden":"true"}),G.createElement("p",{className:"no-margin txtSize14 vm-dark-grey2 line-height-18",dangerouslySetInnerHTML:{__html:l||r}})))))},Re=(0,F.connect)(function(e){var t=e.lightboxData;return(0,B.ValueOf)(t,void 0,{})},function(e){return{closeLightbox:function(){return e(B.Actions.closeLightbox(Le))}}})(Ie),Ve=B.Components.Visible,De=B.Components.Currency,Pe=function(e){var t=e.regularPrice,n=e.promotionDetails;return G.createElement(G.Fragment,null,G.createElement("div",{className:"spacer5"}),G.createElement(Ve,{when:(0,B.ValueOf)(n,"description",!1)},G.createElement("span",{className:"package-name pad-5 fill-xs txtSize12 txtGray border-radius-3 bgGrey sans-serif txtBold pad-10-left pad-10-right inline-block"},G.createElement(Ve,{when:(0,B.ValueOf)(n,"discountDuration",!1)}," ",G.createElement(z.FormattedMessage,{id:"PromotionValid",values:{price:Math.abs((0,B.ValueOf)(n,"discountPrice.price",0)),discountDuration:(0,B.ValueOf)(n,"discountDuration","")}})),G.createElement(Ve,{when:(0,B.ValueOf)(n,"expiryDate",!1)}," ",G.createElement(z.FormattedDate,{value:(0,B.ValueOf)(n,"expiryDate",""),format:"yMMMMd",timeZone:"UTC"},function(e){return G.createElement(z.FormattedMessage,{id:"PromotionExpires",values:{expiryDate:e}})})))),G.createElement("div",{className:"txtCurrency virginUltraReg txtBlack txtSize40 fill-xs"},G.createElement(Ve,{when:(0,B.ValueOf)(n,void 0,!1)},G.createElement(z.FormattedMessage,{id:"Now"})," "),G.createElement(De,{value:isNaN((0,B.ValueOf)(n,"promotionalPrice",{}).price)?(0,B.ValueOf)(t,"price",0):(0,B.ValueOf)(n,"promotionalPrice.price",0),monthly:!0}),G.createElement(Ve,{when:(0,B.ValueOf)(n,void 0,!1)},G.createElement("p",{className:"txtSize12 txtGray txtBold fill-xs flex"},G.createElement(z.FormattedMessage,{id:"Current Price",values:(0,B.ValueOf)(t,void 0,{})})))),G.createElement(Ve,{when:(0,B.ValueOf)(n,"legalMessage",!1)},G.createElement("p",{className:"txtSize12 txtGray"},(0,B.ValueOf)(n,"legalMessage",G.createElement(z.FormattedMessage,{id:"Prices may increase legal"})))))},Be=Pe,Ge=B.Components.Modal,Fe=B.Components.Visible,We="MULTIPLE_WAYS_TO_ADD",ze=function(e){var t=e.id,n=e.name,a=e.regularPrice,r=e.promotionDetails,l=e.childOfferings,i=e.isSelected,c=e.onSelect,o=(0,B.ValueOf)(l,"length",0)-1;return G.createElement("div",{className:"boxContainer borderGrayLight6 pad-15 margin-20-bottom ".concat(i?"borderBlack":"")},G.createElement("label",{id:"".concat(t,"selectCTA"),className:"graphical_ctrl pointer ctrl_radioBtn txtSize15",onClick:function(){return c(e)}},G.createElement("input",{type:"radio",name:"offering",checked:i}),G.createElement("span",{className:"ctrl_element"}),G.createElement("span",{className:"radio-text"},n),G.createElement(Fe,{when:o>0},G.createElement("p",{className:"no-margin"},G.createElement(z.FormattedMessage,{id:"Get additional channels",values:{additionalCahnnels:o}}))),G.createElement(Be,{regularPrice:a,promotionDetails:r})))},Ke={id:"NO",productOfferingType:B.Volt.EProductOfferingType.NONE},Ue=function(e){function t(e){N(e)}var n,a,r,l,i,c,o,s,u,m,d=e.channel,p=e.parents,f=e.defaultSelection,E=e.closeLightbox,h=e.onContinueClick,v=g(G.useState(Ke),2),b=v[0],N=v[1];return G.useEffect(function(){N(f)},[f]),n=d.name,a=d.imagePath,r=d.channelNumber,l=d.characteristics,i=d.shortDescription,c=d.longDescription,s=(o=y(l)).language,u=o.genre,m=o.culture,G.createElement(Ge,{modalId:We,className:"do-not-center-in",onShown:function(){B.Omniture.useOmniture().trackFragment({id:"mutipleWaysLightbox",s_oAPT:{actionId:104},s_oPRM:"Multiple ways to order"})},onDismiss:function(){B.Omniture.useOmniture().trackAction({id:"mutipleWaysLightbox",s_oAPT:{actionId:647},s_oBTN:"Close"})},title:G.createElement(z.FormattedMessage,{id:"MULTIPLE_WAYS_TO_ADD_TITLE",values:{name:n}})},G.createElement("div",{className:""},G.createElement("div",{className:"pad-30 pad-15-left-right-xs"},G.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),G.createElement("div",{className:"d-flex flex-column flex-sm-row"},G.createElement("div",{className:"heightFitContent flexStatic"},G.createElement("div",{style:{width:"94px",height:"94px"},className:"margin-15-bottom margin-30-right borderGrayLight6 flex align-center"},G.createElement("img",{src:a,alt:n,className:"fill pad-5"}))),G.createElement("div",{className:"pad-30-left"},G.createElement("span",{className:"sr-only"},G.createElement(z.FormattedMessage,{id:"Channel number"})),G.createElement("p",{className:"no-margin txtSize16 txtVirginBlue line-height-18"},r),G.createElement("div",{className:"spacer5","aria-hidden":"true"}),G.createElement("p",{className:"no-margin txtSize16 vm-dark-grey2 line-height-18 txtBold"},[A(u),A(m),A(s)].filter(function(e){return Boolean(e)}).join(" / ")),G.createElement("div",{className:"spacer15","aria-hidden":"true"}),G.createElement("p",{className:"no-margin txtSize14 vm-dark-grey2 line-height-18",dangerouslySetInnerHTML:{__html:c||i}})))),G.createElement("form",{className:"pad-30 pad-15-left-right-xs"},G.createElement("div",{className:"spacer1 bgGrayLight3","aria-hidden":"true"}),G.createElement("div",{className:"spacer25 clear","aria-hidden":"true"}),G.createElement("p",{className:"txtSize16"},G.createElement(z.FormattedMessage,{id:"Ways to add this channel"})),p.map(function(e){return G.createElement(ze,V({},e,{isSelected:e.id===b.id,onSelect:t}))}),G.createElement(z.FormattedMessage,{id:"No thanks",values:{name:n}},function(e){return G.createElement(ze,V({},Ke,{name:e,isSelected:Ke.id===b.id,onSelect:t}))}))),G.createElement("div",{className:"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg"},G.createElement("button",{id:"MULTIPLE_WAYS_CONTINUE",className:"btn btn-primary fill-xs",onClick:function(){if(b.id!==f.id){var e="NO"===b.id?f:b;h(e.offeringAction),B.Omniture.useOmniture().trackAction({id:"mutipleWaysLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"MULTIPLE_WAYS_CONTINUE"},s_oPRD:{category:e.displayGroupKey,name:e.name,sku:"",quantity:"1",price:(0,B.ValueOf)(e,"regularPrice.price",""),promo:(0,B.ValueOf)(e,"promotionDetails.description","")}})}else E()}},G.createElement(z.FormattedMessage,{id:"MULTIPLE_WAYS_TO_ADD_CONTINUE"})),G.createElement("div",{className:"vSpacer15","aria-hidden":"true"}),G.createElement("button",{id:"MULTIPLE_WAYS_CLOSE",className:"btn btn-default fill-xs",onClick:E},G.createElement(z.FormattedMessage,{id:"MULTIPLE_WAYS_TO_ADD_CLOSE"}))))},je=(0,F.connect)(function(e){var t=e.catalog,n=e.lightboxData,a=(0,B.ValueOf)(n,void 0,{}),r=(0,B.ValueOf)(a,"multipleWaysToAdd",[]).map(function(e){return t.index.find(function(t){return t.id===e})}).filter(Boolean),l=r.find(function(e){return e.isSelected});return{channel:a,parents:r,defaultSelection:l||Ke}},function(e){return{onContinueClick:function(t){t&&e(ee(t)),e(B.Actions.closeLightbox(We))},closeLightbox:function(){B.Omniture.useOmniture().trackAction({id:"mutipleWaysLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"MULTIPLE_WAYS_CLOSE"}}),e(B.Actions.closeLightbox(We))}}})(Ue),He={},qe=function(){function e(e){this.visible=!1,He[e]&&He[e].destroy(),this.elId=e,this.$triggerEl=$("#"+e),this.show=this.show.bind(this),this._hide=this._hide.bind(this),this.hide=this.hide.bind(this),this.destroy=this.destroy.bind(this),this._onTooltipClick=this._onTooltipClick.bind(this),this.$triggerEl.tooltip({trigger:"manual"}),this.triggerEl.addEventListener("mouseenter",this.show),He[e]=this}return Object.defineProperty(e.prototype,"triggerEl",{get:function(){return document.getElementById(this.elId)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tooltipEl",{get:function(){return document.querySelector("."+this.elId+"_inst")},enumerable:!1,configurable:!0}),e.prototype.show=function(){var e=this;document.body.addEventListener("mousemove",this._hide),this.visible||this._delayed||(window.addEventListener("scroll",this._hide),this._delayed=setTimeout(function(){e._delayed=null,e.visible=!0,e.$triggerEl.tooltip("show"),requestAnimationFrame(function(){return e.tooltipEl.addEventListener("click",e._onTooltipClick)})},500))},e.prototype._hide=function(e){var t=e.target;(function(e,t){return e.filter(function(e){return Boolean(e)}).map(function(e){return function(e,t){return e.top-10<t.y&&e.bottom+10>t.y&&e.left<t.x&&e.right>t.x}(e,t)}).find(function(e){return e})||!1})([this.triggerEl&&this.triggerEl.getBoundingClientRect(),this.tooltipEl&&this.tooltipEl.getBoundingClientRect()],e)&&!t.classList.contains("tooltip-interactive")||(this.hide(),clearTimeout(this._delayed),this._delayed=null)},e.prototype._onTooltipClick=function(e){this.onTooltipClick(e)},e.prototype.hide=function(){this.visible&&(this.visible=!1,this.tooltipEl.removeEventListener("click",this._onTooltipClick),document.body.removeEventListener("mousemove",this._hide),window.removeEventListener("scroll",this._hide),this.$triggerEl.tooltip("hide"))},e.prototype.destroy=function(){document.body.removeEventListener("mousemove",this._hide),window.removeEventListener("scroll",this._hide),this.triggerEl.removeEventListener("mouseenter",this.show),He[this.elId]=null},e}(),Ye=function(e){var t,n,a,r,l,i,c=e.id,o=e.name,s=e.channelNumber,u=e.imagePath,m=e.characteristics,d=e.shortDescription,p=e.children,f=e.className,g=e.connectCtrl,E=G.useMemo(function(){return"tooltip".concat(c).concat(Math.floor(100*Math.random()))},[c]);return G.useEffect(function(){var e=new qe(E);return g(e),function(){return e.destroy()}},[]),He[E]&&g(He[E]),n=(t=y(m)).culture,a=t.genre,r=t.language,l='<div style="display:flex; flex-direction: row;">\n      <div style="flex-shrink:0; padding-right:20px">\n        <img width="75" class="img-responsive channel-border" src="'.concat(u,'" alt="').concat(o,'" />\n      </div>\n      <div>\n        <div class="txtVirginBlue txtSize18 noMargin">').concat(s,'</div>\n        <div class="txtBlack txtSize18 noMargin">').concat([A(a),A(n),A(r)].filter(function(e){return Boolean(e)}).join(" / "),'</div>\n        <div class="tooltip-description txtSize14" style="color:#333">').concat((i=d,i.length>80?i.substr(0,80)+"...":i),'</div>\n        <div class="spacer15"></div>\n        <button id="viewDetails').concat(c,'" class="txtUnderline btn btn-link p-0 m-0 txtSize14 txtNormal txtVirginBlue">').concat(Ne.getLocalizedString("View details"),"</button>\n      </div></div>"),G.createElement("div",{className:f},G.createElement("div",{className:"floatL w-100"},G.createElement("div",{id:E,className:"tooltip-interactive w-100 alignIconWithText pointer",tabIndex:0,role:"tooltip","data-delay":"100","data-html":"true","data-placement":"top","data-container":"body","data-template":'<div class="tooltip channel-tooltip top in '.concat(E,'_inst" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'),"data-title":l},p)))},Xe=B.Components.Visible,$e=function(e){var t,n=e.id,a=e.name,r=e.imagePath,l=e.regularPrice,i=e.promotionDetails,c=e.offeringAction,o=e.characteristics,s=e.isAlreadyIncludedIn,u=e.isSelectable,m=e.isSelected,d=e.isDisabled,p=e.multipleWaysToAdd,f=e.onActionClick,g=e.onInfoClick,E=e.onMultipleWaysToAdd,h=e.channelNumber,v=y(o).callSign,b=u&&(0,B.ValueOf)(p,"length",0)>0,N=function(a){a&&(a.preventDefault(),a.stopPropagation()),t&&t.hide(),g(e,"channel_".concat(n))};return G.createElement(Ye,V({key:n,connectCtrl:function(e){t=e,e.onTooltipClick=N}},e,{className:"col-12 col-sm-3 col-md-3 pad-15-left"}),G.createElement("div",{className:"",id:n,"data-cs":v},G.createElement("div",{className:"bell-tv-channel flexCol flexRow-xs\n              ".concat(u&&m?" selected":"","\n              ").concat(u?"":" bell-tv-channel-nonselectable","\n              ").concat(u&&d?" disabled":"")},G.createElement("div",{className:"bell-tv-channel-icon flexBlock flexCenter floatL-xs","aria-hidden":"true"},G.createElement("img",{src:r,alt:a})),G.createElement("div",{className:"bell-tv-channel-description flexGrow flex flex-column"},G.createElement("div",{className:"spacer5 d-none d-sm-block","aria-hidden":"true"}),Boolean(a)&&G.createElement("button",{id:"channel_".concat(n),className:"bell-tv-channel-title txtUnderline btn btn-link p-0 m-0 txtSize14 txtNormal flexGrow links-blue-on-bg-white",onClick:N},a),G.createElement("p",{className:"bell-tv-channel-number noMargin"},h),G.createElement(Xe,{when:u&&(0,B.ValueOf)(l,"price",0)>0},G.createElement("p",{className:"bell-tv-channel-price txtBlue noMargin"},G.createElement(z.FormattedNumber,{value:(0,B.ValueOf)(i,"promotionalPrice.price",!1)||(0,B.ValueOf)(l,"price",0),format:"CAD"}),G.createElement(Xe,{when:(0,B.ValueOf)(i,"promotionalPrice.price",!1)}," ",G.createElement("del",null,G.createElement(z.FormattedNumber,{value:(0,B.ValueOf)(l,"price",0),format:"CAD"})))))),G.createElement(Xe,{when:!d&&b},G.createElement("div",{className:"bell-tv-channel-tile-description txtSize12"},G.createElement("dfn",null,G.createElement(z.FormattedMessage,{id:"Multipleways to add"})))),G.createElement(Xe,{when:u&&d&&!Boolean(s)},G.createElement("div",{className:"bell-tv-channel-tile-description txtSize12"},G.createElement("dfn",null,G.createElement(z.FormattedMessage,{id:"Already selected"})))),G.createElement(Xe,{when:Boolean(s)},G.createElement("div",{className:"bell-tv-channel-tile-description txtSize12"},G.createElement("dfn",null,G.createElement(z.FormattedMessage,{id:"Already included in",values:{name:s}})))),G.createElement(Xe,{when:u},G.createElement("label",{htmlFor:"offeringWays_".concat(n),className:"bell-tv-channel-checkbox graphical_ctrl graphical_ctrl_checkbox absolute",onClick:function(t){t.preventDefault(),t.stopPropagation(),d||(b?E(e,"channel_".concat(n)):f(c))}},G.createElement("input",{id:"offeringWays_".concat(n),type:"checkbox",name:"packages",checked:m,disabled:d}),G.createElement("span",{className:"ctrl_element chk_radius"}),G.createElement("span",{className:"sr-only"},a))))))},Je=(0,F.connect)(function(e){return{}},function(e){return{onInfoClick:function(t,n){return e(B.Actions.openLightbox({lightboxId:Le,data:V(V({},t),{relativeId:n})}))},onMultipleWaysToAdd:function(t,n){return e(B.Actions.openLightbox({lightboxId:We,data:V(V({},t),{relativeId:n})}))},onActionClick:function(t){return e(ee(t))}}})($e),Ze=B.Components.Visible,Qe=function(e){var t,n=e.id,a=e.name,r=e.imagePath,l=e.isAlreadyIncludedIn,i=e.isSelectable,c=e.isSelected,o=(e.isCurrent,e.isDisabled),s=e.regularPrice,u=e.shortDescription,m=e.longDescription,d=e.promotionDetails,p=e.offeringAction,f=e.childOfferings,E=e.onActionClick,h=g(G.useState(!1),2),v=h[0],b=h[1],y=(0,z.useIntl)();return G.useEffect(function(){v&&B.Omniture.useOmniture().trackAction({id:"showChannelsClick",s_oAPT:{actionId:648},s_oEPN:"Show Channel"})},[v]),t=(0,B.ValueOf)(f,"length",0)>0,G.createElement("div",{className:"bell-tv-package bell-tv-movie-pack noBorder ".concat(c?"selected":""," ").concat(o?"disabled":""),id:n},G.createElement("div",{className:"bell-tv-package-body flexRow"},G.createElement("div",{className:"bell-tv-package-left flexGrow flexCol"},G.createElement("label",{id:"combo_".concat(n),onClick:function(e){e.preventDefault(),e.stopPropagation(),i&&!o&&E(p)},className:"bell-tv-package-checkbox graphical_ctrl graphical_ctrl_checkbox txtSize15 block"},G.createElement("input",{type:"checkbox",name:"packages",checked:c,disabled:o||!i}),G.createElement("span",{className:"block txtSize16 pad-5-left txtBlack"},a),G.createElement(Ze,{when:i&&o&&!Boolean(l)},G.createElement("span",{className:"block bell-tv-channel-tile-description txtSize12"},G.createElement("dfn",null,G.createElement(z.FormattedMessage,{id:"Already selected"})))),G.createElement(Ze,{when:Boolean(l)},G.createElement("span",{className:"block bell-tv-channel-tile-description txtSize12"},G.createElement("dfn",null,G.createElement(z.FormattedMessage,{id:"Already included in",values:{name:l}})))),G.createElement(Be,{regularPrice:s,promotionDetails:d}),G.createElement("span",{className:"ctrl_element chk_radius borderGrayLight7"})),G.createElement("ul",{className:"flexRow flexWrap bell-tv-individual-channels virgin-channel-block"},(0,B.ValueOf)(f,void 0,[]).slice(0,3).map(function(e){return G.createElement("li",null,G.createElement("img",{src:(0,B.ValueOf)(e,"imagePath",""),alt:(0,B.ValueOf)(e,"name",""),title:(0,B.ValueOf)(e,"name","")}))})),G.createElement("div",{className:"flexGrow","aria-hidden":"true"}),G.createElement("div",{className:"spacer15","aria-hidden":"true"}),G.createElement(Ze,{when:t},G.createElement("div",{className:" flexBlock flexRow"},G.createElement("button",{id:"View_all_channels_".concat(n),onClick:function(){return b(!v)},className:"btn btn-link no-pad links-blue-on-bg-white txtDecorationNoneHover txtSize14","aria-controls":"View_all_channels_".concat(n),"aria-expanded":v,"aria-label":"".concat(y.formatMessage({id:"Show channels"})," ").concat(y.formatMessage({id:"FOR_TEXT"})," ").concat(a)},G.createElement("span",{className:"volt-icon links-blue-on-bg-white margin-5-top ".concat(v?"icon-Collapse":"icon-Expand")},G.createElement("span",{className:"volt-icon path1 icon-Collapse"}),G.createElement("span",{className:"volt-icon path2 icon-Collapse"})),G.createElement("span",{className:"sans-serif margin-10-left"},G.createElement(z.FormattedMessage,{id:"Show channels"})))))),G.createElement("div",{className:"spacer10 flexStatic d-block d-sm-none","aria-hidden":"true"}),G.createElement("div",{className:"bell-tv-package-right flexStatic block-xs"},G.createElement(Ze,{when:Boolean(r)},G.createElement("img",{src:r,alt:a})))),G.createElement(Ze,{when:v},G.createElement("div",{className:"bell-tv-package-footer bgGrayLight4 pad-30 pad-15-left-right-xs expanded",role:"region","aria-hidden":!v},G.createElement("div",{className:"bell-tv-package-filters-row no-pad"},G.createElement(Ze,{when:Boolean(m||u)},G.createElement("p",{dangerouslySetInnerHTML:{__html:m||u}}),G.createElement("hr",null)),G.createElement("div",{className:"bell-tv-channels bell-tv-channel-picker flexRow"},S((0,B.ValueOf)(f,void 0,[])).map(function(e){return G.createElement(Je,V({key:e.id},e,{isSelectable:!1}))}))))))},et=(0,F.connect)(function(e){return{}},function(e){return{onActionClick:function(t){return e(ee(t))}}})(Qe),tt=et,nt=function(e){e.pageName;var t=e.label,n=e.content,a=g(G.useState(!1),2),r=a[0],l=a[1];return G.useEffect(function(){r&&B.Omniture.useOmniture().trackAction({id:"ligalStuffClick",s_oAPT:{actionId:648},s_oEPN:"Legal Stuff"})},[r]),G.createElement("div",{className:"virginUltraReg more-info pad-15-top accss-focus-outline-override-grey-bg",id:"moreInfo"},G.createElement("button",{id:"Legal",className:"btn btn-link noUnderlineAll noPaddingImp links-blue-on-bg-gray",onClick:function(){return l(!r)},"aria-expanded":r},G.createElement("span",{className:"volt-icon ".concat(r?"icon-collapse_m":"icon-expand_m"),"aria-hidden":"true"}),"  ",G.createElement(z.FormattedMessage,{id:t})),G.createElement("div",{className:"spacer30","aria-hidden":"true"}),G.createElement(B.Components.Visible,{when:r},G.createElement("div",{className:"moreInfoBox"},G.createElement("button",{id:"legal_close",type:"button",onClick:function(){return l(!1)},className:"close moreInfoLink x-inner txtDarkGrey txtSize18","aria-label":"close"},G.createElement("span",{className:"virgin-icon icon-big_X","aria-hidden":"true"})),G.createElement(B.FormattedHTMLMessage,{id:n}))))},at=function(e){var t=e.name,n=e.data,a=e.children,r=(0,F.useDispatch)();return G.useEffect(function(){r(B.Actions.omniPageLoaded(t,n))},[]),G.createElement(G.Fragment,null,a)},rt=function(e){var t=e.packages;return G.createElement(at,{name:"Addons"},G.createElement("div",{className:"flexRow flex-justify-space-between"},G.createElement("div",{className:"margin-xs"},G.createElement("h2",{className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},G.createElement(z.FormattedMessage,{id:"Addons page"})),G.createElement(B.FormattedHTMLMessage,{id:"Addons page description"},function(e){return G.createElement(B.Components.Visible,{when:Boolean(e)},G.createElement("div",{className:"spacer5"}),G.createElement("p",{className:"noMargintxtSize14"},e))}))),G.createElement("div",{className:"spacer15","aria-hidden":"true"}),S(t).map(function(e){return G.createElement(tt,V({key:e.id},e))}),G.createElement(nt,{pageName:B.Volt.EDIsplayGroupKey.ADD_ON,label:"LEGAL_LABEL_".concat(B.Volt.EDIsplayGroupKey.BASE_PROGRAMMING),content:"LEGAL_COPY_".concat(B.Volt.EDIsplayGroupKey.ADD_ON)}))},lt=(0,F.connect)(function(e){var t=e.catalog;return{packages:(0,B.ValueOf)(t,"offerings."+B.Volt.EDIsplayGroupKey.ADD_ON,[]).filter(function(e){return e.productOfferingType===B.Volt.EProductOfferingType.COMBO})}})(rt),it={genre:[],language:[],isDontHave:!1,isHave:!1,sortBy:"name",sortOrder:"asc"},ct=function(e,t,n){32!==e.keyCode&&"keydown"===e.type||(e.preventDefault(),e.stopPropagation(),n(t))},ot=function(e,t){32!==e.keyCode&&"keydown"===e.type||(e.preventDefault(),e.stopPropagation(),t())},st=function(e){var t=e.list,n=e.formatter,a=g(G.useState(t.length>15?0:t.length),2),r=a[0],l=a[1],i=M(t);return G.useEffect(function(){i&&i.length===t.length||l(t.length>15?0:t.length)},[t]),G.useEffect(function(){r<t.length&&requestAnimationFrame(function(){return l(r+15)})},[r]),G.createElement(G.Fragment,null,t.slice(0,r).map(n))},ut=B.Components.Visible,mt=function(){return G.createElement("div",{className:"pad-30"},G.createElement("div",{className:"flexBlock flexRow"},G.createElement("div",{className:""},G.createElement("span",{className:"virgin-icon icon-BIG_WARNING txtSize38"},G.createElement("span",{className:"virgin-icon path1"}),G.createElement("span",{className:"virgin-icon path2"}))),G.createElement("div",{className:"pad-15-left"},G.createElement("h3",{className:"noMargin txtSize20 pad-15-bottom"},G.createElement(z.FormattedMessage,{id:"NO_CHANNELS_FOUND_MESSAGE"})),G.createElement("p",{className:"noMargin"},G.createElement(z.FormattedMessage,{id:"NO_CHANNELS_FOUND_DESCRIPTION"})))))},dt=function(e){var t=e.Filter,n=e.filters,a=e.toggleTray,r=w(t.getState())[1];return G.createElement("div",{className:"bell-tv-package-filters-tray",style:{maxHeight:"9999em"}},G.createElement("div",{className:"spacer1 bgGray","aria-hidden":"true"}),G.createElement("form",{className:"bell-tv-package-filters-row bgGray19",onSubmit:function(e){e.preventDefault(),B.Omniture.useOmniture().trackAction({id:"advanceFilterApply",s_oAPT:{actionId:647},s_oBTN:"Advance Filters"}),t.setState(r.getState()),requestAnimationFrame(function(){return a(!1)})},onReset:function(e){t.reset(),requestAnimationFrame(function(){return a(!1)})}},G.createElement("div",{className:"flexRow flex-justify-space-between txtSize12-xs"},G.createElement("p",{className:"txtBold txtBlack1"},G.createElement(z.FormattedMessage,{id:"Refine by"}))),G.createElement("div",{className:"flexRow flex-justify-space-between flexCol-xs"},G.createElement("div",{className:"flexCol",role:"group","aria-labelledby":"refineGenresLabel"},G.createElement("p",{className:"txtBlack1",id:"refineGenresLabel"},G.createElement(z.FormattedMessage,{id:"Genres"})),G.createElement("ul",{className:"noMargin noBullets"},(0,B.ValueOf)(n,"genres",[]).sort().map(function(e){return G.createElement("li",{key:e},G.createElement("label",{id:"checkboxLabel_".concat(e),className:"graphical_ctrl graphical_ctrl_checkbox pointer",onClick:function(t){return ct(t,e,r.toggleGenre)},onKeyDown:function(t){return ct(t,e,r.toggleGenre)}},G.createElement("input",{id:"checkbox_".concat(e),type:"checkbox",name:"genres",value:e,checked:r.hasGenre(e)}),G.createElement(z.FormattedMessage,{id:e}),G.createElement("span",{className:"ctrl_element chk_radius"})),G.createElement("div",{className:"spacer10","aria-hidden":"true"}))}))),G.createElement("div",{className:"flexCol",role:"group","aria-labelledby":"refineLanguageLabel"},G.createElement("p",{className:"txtBlack1",id:"refineLanguageLabel"},G.createElement(z.FormattedMessage,{id:"Language"})),G.createElement("ul",{className:"noMargin noBullets"},(0,B.ValueOf)(n,"languages",[]).sort().map(function(e){return G.createElement("li",{key:e},G.createElement("label",{id:"label_Lang_".concat(e),className:"graphical_ctrl graphical_ctrl_checkbox pointer",onClick:function(t){return ct(t,e,r.toggleLanguage)},onKeyDown:function(t){return ct(t,e,r.toggleLanguage)}},G.createElement("input",{id:"checkbox_Lang_".concat(e),type:"checkbox",name:"languages",value:e,checked:r.hasLanguage(e)}),G.createElement(z.FormattedMessage,{id:e}),G.createElement("span",{className:"ctrl_element chk_radius"})),G.createElement("div",{className:"spacer10","aria-hidden":"true"}))}))),G.createElement("div",{className:"flexCol flex-justify-space-between",role:"group","aria-labelledby":"refineOtherLabel"},G.createElement("div",null,G.createElement("p",{className:"txtBlack1",id:"refineOtherLabel"},G.createElement(z.FormattedMessage,{id:"Other"})),G.createElement("ul",{className:"noMargin noBullets"},G.createElement("li",null,G.createElement("label",{id:"label_dont_have",className:"graphical_ctrl graphical_ctrl_checkbox pointer",onClick:function(e){return ot(e,r.toggleDontHave)},onKeyDown:function(e){return ot(e,r.toggleDontHave)}},G.createElement("input",{id:"checkbox_dont_have",type:"checkbox",name:"dontHave",value:"yes",checked:r.hasDontHave()}),G.createElement("span",null,G.createElement(z.FormattedMessage,{id:"Channels I dont have"})),G.createElement("span",{className:"ctrl_element chk_radius"})),G.createElement("div",{className:"spacer10","aria-hidden":"true"})),G.createElement("li",null,G.createElement("label",{id:"label_have_channels",className:"graphical_ctrl graphical_ctrl_checkbox pointer",onClick:function(e){return ot(e,r.toggleHave)},onKeyDown:function(e){return ot(e,r.toggleHave)}},G.createElement("input",{id:"checkbox_have_channels",type:"checkbox",name:"have",value:"yes",checked:r.hasHave()}),G.createElement("span",null,G.createElement(z.FormattedMessage,{id:"Channels I have"})),G.createElement("span",{className:"ctrl_element chk_radius"})),G.createElement("div",{className:"spacer10","aria-hidden":"true"})))),G.createElement("div",{className:" txtRight"},G.createElement("button",{id:"RESET_FILTER",type:"reset",className:"btn btn-link txtVirginBlueFix txtDecoration_hover"},G.createElement(z.FormattedMessage,{id:"Reset filters"})),G.createElement("span",{className:"vSpacer15","aria-hidden":"true"}),G.createElement("button",{id:"APPLY_BTN",type:"submit",className:"btn btn-primary txtSize12-xs"},G.createElement(z.FormattedMessage,{id:"Apply"})))))))},pt=function(e){var t=e.allowMultipleWaysToAdd,n=e.allowSelection,a=e.forceSelectable,r=e.channels,l=e.label,i=(e.groupName,e.showHeader),c=e.showFilters,o=g(G.useState(!1),2),s=o[0],u=o[1],m=g(function(e){var t=g(G.useState({filters:{},channels:[]}),2),n=t[0],a=t[1],r=g(w(C()),2),l=r[0],i=r[1],c=M(e);return G.useEffect(function(){var t,r,i=n.filters;i.genres&&i.languages&&e.length===c.length||(i={genres:(r=e,(0,B.ValueOf)(r,void 0,[]).reduce(function(e,t){var n=y(t.characteristics).genre;return Boolean(n)&&n.split(",").map(function(e){return e.trim()}).filter(Boolean).forEach(function(t){e.indexOf(t)<0&&e.push(t)}),e.sort()},[]).filter(Boolean).sort()),languages:["English","French","Other"]}),t=e.filter(function(e){var t=e.isSelected,n=e.isCurrent,a=y(e.characteristics),r=a.language,i=a.genre,c=!0;return l.isDontHave?c=!(t||n):l.isHave&&(c=t||n),c&&l.genre.length>0&&(c=!!l.genre.find(function(e){return(i||"").indexOf(e)>-1})),c&&l.language.length>0&&(c=l.language.indexOf("Other")>-1?!/(english|french)/i.test(r||""):!!l.language.find(function(e){return(r||"").indexOf(e)>-1})),c}).sort(function(e,t){var n=e[l.sortBy]||y(e.characteristics)[l.sortBy]||"",a=t[l.sortBy]||y(t.characteristics)[l.sortBy]||"";return n.localeCompare(a,void 0,{numeric:!0,sensitivity:"base"})*("desc"===l.sortOrder?-1:1)}),a({filters:i,channels:t})},[l,e]),[n,i]}(r),2),d=m[0],p=m[1],f=G.useContext(B.WidgetContext).config.filters,E=(0,B.ValueOf)(d,"channels",[]),h=!(r&&r.length>0);return G.createElement("div",{className:"panel panel-shadow"},G.createElement(ut,{when:i},G.createElement("nav",{className:"bell-tv-package-filters-row bgWhite flexRow border-radius-top flexCenter",role:"tablist"},G.createElement("ul",{className:"noMargin noBullets bell-tv-package-filters-nav margin-10-bottom-xs",role:"presentation"},G.createElement("li",{tabIndex:p.hasGenre()?0:-1,className:"table-cell ".concat(p.hasGenre()?"active":""),role:"tab",onClick:function(){return p.setGenre()},onKeyUp:function(){return p.setGenre()},"aria-selected":p.hasGenre()?"true":"false"},G.createElement("label",{id:"filter_All",className:"pointer"},G.createElement(z.FormattedMessage,{id:"FILTER_TEXT_All"}))),(0,B.ValueOf)(d,"filters.genres",[]).filter(function(e){return f.genres.indexOf(e)>-1}).map(function(e){return G.createElement("li",{tabIndex:p.onlyGenre(e)?0:-1,role:"tab",id:e,key:e,onKeyUp:function(t){void 0!==t.keyCode&&13!==t.keyCode||(p.setGenre(e),u(!1))},onClick:function(){p.setGenre(e),u(!1)},className:"table-cell ".concat(p.onlyGenre(e)?"active":""),"aria-selected":p.onlyGenre(e)?"true":"false"},G.createElement("label",{className:"pointer",id:"filter_".concat(e)},G.createElement(z.FormattedMessage,{id:"FILTER_TEXT_".concat(e)})))})),G.createElement("div",{className:"spacer2 fill-xs border-filter bgVirginCustomGray1 d-block d-sm-none","aria-hidden":"true"}),G.createElement("div",{className:"vSpacer10 flexGrow hidden-xs","aria-hidden":"true"}),G.createElement("div",{className:"spacer10","aria-hidden":"true"}),G.createElement("ul",{className:"noMargin noBullets bell-tv-package-filters-nav",role:"presentation"},G.createElement("li",null,G.createElement("button",{id:"Advanced_Filters",onClick:function(){return u(!s)},"aria-expanded":s,disabled:h,className:"btn btn-link no-pad txtDecorationNoneHover"},G.createElement("span",{className:"sans-serif icon-blue showText txtDecoration_hover links-blue-on-bg-white"},G.createElement(z.FormattedMessage,{id:"Advanced Filters"}),"  ",G.createElement("span",{className:"volt-icon txtSize16 icon-blue icon-".concat(s?"Collapse":"Expand")},G.createElement("span",{className:"volt-icon path1 icon-".concat(s?"Collapse":"Expand")}),G.createElement("span",{className:"volt-icon path2 icon-".concat(s?"Collapse":"Expand")})))))))),G.createElement(ut,{when:s},G.createElement(dt,{filters:d.filters,Filter:p,toggleTray:u})),G.createElement("div",{className:"bell-tv-package-filters-row bgGray19 block-xs"},G.createElement("div",{className:"spacer6 visible-xs","aria-hidden":"true"}),G.createElement("div",{className:"txtSize18 flexGrow"},G.createElement(ut,{when:Boolean(l),placeholder:G.createElement("span",null,G.createElement(z.FormattedMessage,{id:"Number of channels"})," (",(0,B.ValueOf)(d,"channels.length",0),")")},l),G.createElement(ut,{when:c},G.createElement("div",{className:"spacer15"}))),G.createElement(ut,{when:c},G.createElement("div",{className:"flexRow block-xs flexCenter"},G.createElement("div",{className:"flexStatic",role:"group","aria-labelledby":"languageLabel"},G.createElement("span",{className:"txtBold txtBlack",id:"languageLabel"},G.createElement(z.FormattedMessage,{id:"Language"})),G.createElement("span",{className:"vSpacer15","aria-hidden":"true"}),(0,B.ValueOf)(d,"filters.languages",[]).filter(function(e){return f.languages.indexOf(e)>-1}).map(function(e,t){return G.createElement(G.Fragment,{key:t},G.createElement("label",{id:"lang_".concat(e),onClick:function(t){ct(t,e,p.toggleLanguage),u(!1)},onKeyDown:function(t){ct(t,e,p.toggleLanguage),u(!1)},className:"graphical_ctrl graphical_ctrl_checkbox pointer"},G.createElement("input",{id:"input_".concat(e),type:"checkbox",name:"packages",disabled:h,checked:p.hasLanguage(e)}),G.createElement(z.FormattedMessage,{id:e}),G.createElement("span",{className:"ctrl_element chk_radius"})),G.createElement("span",{className:"vSpacer30","aria-hidden":"true"}))})),G.createElement("div",{className:"flexGrow spacer15","aria-hidden":"true"}),G.createElement("div",{className:"flexStatic"},G.createElement("span",{className:"txtBold txtBlack"},G.createElement(z.FormattedMessage,{id:"Sort by"})),G.createElement("button",{id:"sortBy_num",onClick:function(){return p.setSort("channelNumber")},disabled:h,className:"btn btn-link noBorder bgTransparent txtVirginBlue txtDecoration_hover filter_arrow links-blue-on-bg-gray"},G.createElement("span",{className:"sr-only"},G.createElement(z.FormattedMessage,{id:"Sort by"})),G.createElement(ut,{when:"channelNumberdesc"===p.whichSort(),placeholder:G.createElement(z.FormattedMessage,{id:"0-9"})},G.createElement(z.FormattedMessage,{id:"9-0"}))),G.createElement("button",{id:"sortBy_alpha",onClick:function(){return p.setSort("name")},disabled:h,className:"btn btn-link noBorder bgTransparent txtVirginBlue txtDecoration_hover filter_arrow links-blue-on-bg-gray"},G.createElement("span",{className:"sr-only"},G.createElement(z.FormattedMessage,{id:"Sort by"})),G.createElement(ut,{when:"namedesc"===p.whichSort(),placeholder:G.createElement(z.FormattedMessage,{id:"A-Z"})},G.createElement(z.FormattedMessage,{id:"Z-A"}))))))),G.createElement("div",{role:"group","aria-labelledby":"filter_".concat(p.selectedGenre()),className:"bell-tv-package-filters-row bgWhite"},G.createElement(ut,{when:!h,placeholder:G.createElement(mt,null)},G.createElement(ut,{when:(0,B.ValueOf)(E,"length",0)>0,placeholder:G.createElement(mt,null)},G.createElement("div",{className:"bell-tv-channels bell-tv-channel-picker flexRow"},G.createElement(st,{list:E,formatter:function(e){return G.createElement(Je,V({key:e.id},e,{isSelectable:a||e.isSelectable&&!!n,multipleWaysToAdd:t?e.multipleWaysToAdd:[]}))}}))))))},ft=pt,gt=B.Components.Modal,Et="SELECTED_CANNELS",ht=function(e){return e.filter(function(e){return e.isSelected})},vt=function(e){var t=e.channels,n=e.totalPrice,a=e.onRemoveChannel,r=(0,z.useIntl)();return G.createElement(gt,{modalId:Et,className:t.length>8?"do-not-center-in":"",onShown:function(){B.Omniture.useOmniture().trackFragment({id:"selectedChannelsLightbox",s_oAPT:{actionId:104},s_oPRM:"Selected channels"})},onDismiss:function(){B.Omniture.useOmniture().trackAction({id:"selectedChannelsLightbox",s_oAPT:{actionId:647},s_oBTN:"Close"})},flexDisplay:!0,title:G.createElement(z.FormattedMessage,{id:"SELECTED_CANNELS_TITLE"})},G.createElement("div",{className:"pad-30 pad-15-left-right-xs"},G.createElement("p",{className:"no-margin txtSize18 txtBold vm-dark-grey2 line-height-18"},t?G.createElement(z.FormattedMessage,{id:"SELECTED_CANNELS_LABEL",values:{total:ht(t).length,price:n}}):null),G.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),G.createElement("div",{className:"flexRow flexCol-xs flexWrap "},t&&ht(t).map(function(e){var t=e.id,n=e.name,l=e.imagePath,i=e.offeringAction,c=y(e.characteristics).callsign;return G.createElement("div",{className:"selectchannelBlock pad-15 fill-xs txtCenter-xs margin-15-right pad-0-bottom"},G.createElement("button",{id:"".concat(t,"RemoveChannel"),onClick:function(){return a(i)},type:"button",className:"no-pad close no-margin","aria-label":"".concat(r.formatMessage({id:"REMOVE_TEXT"})," ").concat(n)},G.createElement("span",{className:"volt-icon icon-big_X txtSize16 close-channel"})),G.createElement("div",{className:"channelImage margin-15-bottom margin-10-left justify-center no-margin-xs fill-xs"},G.createElement("img",{src:l,alt:n})),G.createElement("div",{className:"pad-5-left"},G.createElement("p",{className:"channelName no-margin txtSize14 txtVirginBlue line-height-18 txtUnderline"},n),G.createElement("div",{className:"spacer10","aria-hidden":"true"}),G.createElement("p",{className:"margin-15-bottom"},c)))}),G.createElement("div",{className:"spacer30 clear","aria-hidden":"true"}))),G.createElement("div",{className:"modal-footer bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg"},G.createElement("div",{className:"col1 flexRow"},G.createElement("button",{id:"SEL_CANNELS_CLOSE",className:"btn btn-primary",onClick:function(){B.Omniture.useOmniture().trackAction({id:"selectedChannelsLightbox",s_oAPT:{actionId:647},s_oBTN:"Close"})},"data-dismiss":"modal"},G.createElement(z.FormattedMessage,{id:"SELECTED_CANNELS_CLOSE"})))))},bt=(0,F.connect)(function(e){return{}},function(e){return{onRemoveChannel:function(t){return e(ee(t))}}})(vt),yt=bt,Nt=function(e){var t=e.channels,n=e.navigation,a=e.openLightbox,r=G.useRef(null),l=g(G.useState({isFloating:!1,leftPos:"auto"}),2),i=l[0],c=l[1],o=(0,B.ValueOf)(n,"count",0),s=(0,B.ValueOf)(n,"subTotalPrice.price",0),u="Alacarte";return G.useEffect(function(){var e=function(){O(r,c)};return window.addEventListener("scroll",e),function(){window.removeEventListener("scroll",e)}},[]),G.createElement(at,{name:"Alacarte"},G.createElement("div",{className:"flexRow flex-justify-space-between"},G.createElement("div",{className:"margin-xs"},G.createElement("h2",{id:"group-".concat(u),className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},G.createElement(z.FormattedMessage,{id:"A la carte page"})),G.createElement(B.FormattedHTMLMessage,{id:"A la carte page Description"},function(e){return G.createElement(B.Components.Visible,{when:Boolean(e)},G.createElement("div",{className:"spacer5"}),G.createElement("p",{className:"noMargintxtSize14"},e))})),G.createElement("div",{id:"wrap",ref:r,tabIndex:0,role:"button",onClick:function(e){return o>0&&a("wrap")},onKeyDown:function(e){return("Enter"===e.key||32===e.keyCode||"keydown"!==e.type)&&o>0&&a("wrap")},className:"floating-div line-height-1 txtWhite txtSize12 txtCenter sans-serif pointer ".concat(i.isFloating?"fix-floating-notification":""),style:{left:i.leftPos}},G.createElement("div",{className:" txtSize26 virginUltraReg pad-10-top"},o),G.createElement("span",{className:""},G.createElement(z.FormattedMessage,{id:"CHANNELS_SELECTED"})),G.createElement("div",{className:"txtBold"},G.createElement(B.Components.BellCurrency,{value:s})))),G.createElement("div",{className:"spacer15","aria-hidden":"true"}),G.createElement(ft,{groupName:u,channels:t,allowSelection:!0}),G.createElement(nt,{pageName:B.Volt.EDIsplayGroupKey.ALACARTE,label:"LEGAL_LABEL_".concat(B.Volt.EDIsplayGroupKey.BASE_PROGRAMMING),content:"LEGAL_COPY_".concat(B.Volt.EDIsplayGroupKey.ALACARTE)}),G.createElement(yt,{channels:t.filter(function(e){return e.isSelected}),totalPrice:s}))},xt=(0,F.connect)(function(e){var t=e.catalog,n=e.navigation;return{channels:(0,B.ValueOf)(t,"offerings."+B.Volt.EDIsplayGroupKey.ALACARTE,[]),navigation:(0,B.ValueOf)(n,void 0,[]).find(function(e){return e.key===B.Volt.EDIsplayGroupKey.ALACARTE})}},function(e){return{openLightbox:function(t){return e(B.Actions.openLightbox({lightboxId:Et,data:{relativeId:t}}))}}})(Nt),St=function(e){var t=e.channels;return e.refresh,G.createElement(at,{name:"Browse all Channels"},G.createElement("div",{className:"flexRow flex-justify-space-between"},G.createElement("div",{className:"margin-xs"},G.createElement("h2",{id:"group-BrowseAll",className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},G.createElement(z.FormattedMessage,{id:"All channels page"})),G.createElement(B.FormattedHTMLMessage,{id:"All channels page description"},function(e){return G.createElement(B.Components.Visible,{when:Boolean(e)},G.createElement("div",{className:"spacer5"}),G.createElement("p",{className:"noMargintxtSize14"},e))}))),G.createElement("div",{className:"spacer15","aria-hidden":"true"}),G.createElement(ft,{groupName:"group-BrowseAll",channels:t,allowSelection:!0,forceSelectable:!0,allowMultipleWaysToAdd:!0}),G.createElement(nt,{pageName:B.Volt.EDIsplayGroupKey.TV_BROWSE_ALL,label:"LEGAL_LABEL_".concat(B.Volt.EDIsplayGroupKey.BASE_PROGRAMMING),content:"LEGAL_COPY_".concat(B.Volt.EDIsplayGroupKey.TV_BROWSE_ALL)}))},At=(0,F.connect)(function(e){var t=e.catalog;return{channels:(0,B.ValueOf)(t,"channels",[]),refresh:1e3*Math.random()}})(St),_t=function(e){var t=e.find(function(e){return e.key===B.Volt.EDIsplayGroupKey.INTERNATIONAL});return(0,B.ValueOf)(t,"children",[]).find(function(e){return e.key===B.Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE})},Ot=function(e){var t=e.languages,n=e.channels,a=e.navigation,r=e.openLightbox,l=(e.refresh,_t(a)),i=G.useRef(null),c=g(G.useState({isFloating:!1,leftPos:"auto"}),2),o=c[0],s=c[1],u=(0,B.ValueOf)(l,"subTotalPrice.price",0);return G.useEffect(function(){var e=function(){O(i,s)};return window.addEventListener("scroll",e),function(){window.removeEventListener("scroll",e)}},[]),G.createElement(at,{name:"International Alacarte"},G.createElement("div",{className:"section-bell-tv-international-channels-individual-tv-channels clearfix"},G.createElement("div",{className:"spacer5"}),G.createElement("div",{className:"flexRow flex-justify-space-between"},G.createElement("div",{className:"margin-xs"},G.createElement("h2",{id:B.Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE,className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},G.createElement(z.FormattedMessage,{id:"International a la carte page"})),G.createElement(B.FormattedHTMLMessage,{id:"International a la carte page description"},function(e){return G.createElement(B.Components.Visible,{when:Boolean(e)},G.createElement("div",{className:"spacer5"}),G.createElement("p",{className:"noMargintxtSize14"},e))})),G.createElement("div",{id:"wrap",ref:i,onClick:function(){return r("wrap")},tabIndex:0,role:"button",className:"floating-div line-height-1 txtWhite txtSize12 txtCenter sans-serif pointer ".concat(o.isFloating?"fix-floating-notification":null),style:{left:o.leftPos}},G.createElement("div",{className:" txtSize26 virginUltraReg pad-10-top"},(0,B.ValueOf)(l,"count",0)),G.createElement("span",{className:""},G.createElement(z.FormattedMessage,{id:"CHANNELS_SELECTED"})),G.createElement("div",{className:"txtBold"},G.createElement(B.Components.BellCurrency,{value:(0,B.ValueOf)(l,"subTotalPrice.price",0)})))),G.createElement("div",{className:"spacer15"}),G.createElement("div",{className:"panel-body bell-tv-package-filters-row bgWhite"},t.map(function(e){var t=x(n,e);return G.createElement(B.Components.Visible,{key:e,when:t.length>0},G.createElement("fieldset",null,G.createElement("legend",{className:"txtSize18 txtBlack txtBold"},G.createElement(z.FormattedMessage,{id:e})),G.createElement("div",{className:"bell-tv-channels bell-tv-channel-picker flexRow"},S(t).map(function(e){return G.createElement(Je,V({key:e.id},e,{multipleWaysToAdd:[]}))}))),G.createElement("div",{className:"spacer40"}))}))),G.createElement(yt,{channels:n.filter(function(e){return e.isSelected}),totalPrice:u}))},kt=function(e){var t=e.languages,n=e.combos;return G.createElement(at,{name:"International Combos"},G.createElement("div",{className:"section-bell-tv-international-channels-combo"},G.createElement("div",{className:"spacer10"}),G.createElement("h3",{id:B.Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS,className:"virginUltraReg noMargin txtBlack text-uppercase txtSize20"},G.createElement(z.FormattedMessage,{id:"International Combos page"})),G.createElement("div",{className:"spacer15"}),t.map(function(e){var t=x(n,e);return G.createElement(B.Components.Visible,{key:e,when:t.length>0},G.createElement("h4",{className:"txtSize18 txtBlack txtBold"},G.createElement(z.FormattedMessage,{id:e})),S(t).map(function(e){return G.createElement(tt,V({key:e.id},e))}))})))},Ct=function(e){var t=e.filter,n=e.languages,a=e.setFitler;return G.createElement("div",{id:"filtersContainer",className:"tvcsfilters-filtersContainer bell-tv-package col-xs-12 bgGray19 container-full-width-xs"},G.createElement("div",{id:"filterInputsContainer",className:"tvcsfilters-filtersInputsContainer bell-tv-package-body clearfix bgWhite"},G.createElement("div",{className:"tvcsfilters-conditionalInlineBlock flexBlock flexCenter flexCol-xs"},G.createElement("div",{className:"col-xs-12 col-sm-3"},G.createElement("label",{htmlFor:"Select_Language",className:"tvcsfilters-conditionalTitleFormatting noMargin txtLightGray3"},G.createElement(z.FormattedMessage,{id:"Select a language region"})),G.createElement("span",{className:"spacer10 col-xs-12 visible-xs"})),G.createElement("div",{className:"col-xs-12 col-sm-9"},G.createElement("div",{className:"tvcsfilters-conditionalFilterPadding15 tvcsfilters-conditionalInlineBlock"},G.createElement("div",{className:"form-control-select-box tvcsfilters-xs-select-dropdown col-xs-12 col-sm-8"},G.createElement("select",{id:"Select_Language",value:t,onChange:function(e){return a(e.target.value)},className:"form-control form-control-select tvcsfilters-select-dropdown-filter txtSize14 bgGrayLight1"},G.createElement(z.FormattedMessage,{id:"All languages"},function(e){return G.createElement("option",{id:"all",value:"all"},e)}),n.map(function(e,t){return G.createElement(z.FormattedMessage,{id:e},function(n){return G.createElement("option",{id:"option_".concat(t),value:e},n)})})),G.createElement("span",{"aria-hidden":"true",style:{backgroundImage:"url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAYBAMAAADT3mpnAAAAD1BMVEUAAADhCgrhCgrhCgrhCiGX/cTIAAAAA3RSTlMAv5hn/23fAAAAPklEQVQI12MAAgUGMGByhNDCJmABRmNjRzDX2NhEAMwFCoC4IAAUoCqAmwuxxxAqIABxhyFEBdRWRhAX5m4AQWUIfOEz3hMAAAAASUVORK5CYII=)",backgroundPosition:"center left",backgroundRepeat:"no-repeat",width:"24px",backgroundSize:"40%"}}))))),G.createElement("div",{className:"tvcsfilters-conditionalSpacerShadow"})))},Tt=function(e){var t=e.comboLanguages,n=e.channelLanguages,a=e.combos,r=e.channels,l=e.navigation,i=e.openLightbox,c=e.refresh,o=g(G.useState("all"),2),s=o[0],u=o[1],m=(0,W.useLocation)();return G.useEffect(function(){u("all")},[m]),G.createElement(G.Fragment,null,G.createElement("h2",{className:"virginUltraReg txtSize28 noMargin txtBlack text-uppercase"},G.createElement(z.FormattedMessage,{id:"International page"})),G.createElement("div",{className:"spacer10"}),G.createElement(W.Switch,null,G.createElement(W.Route,{path:B.EWidgetRoute.TV_InternationalCombos},G.createElement(Ct,{filter:s,setFitler:u,languages:t}),G.createElement(kt,{combos:a,languages:t.filter(function(e){return"all"===s||e.indexOf(s)>-1})})),G.createElement(W.Route,{path:B.EWidgetRoute.TV_InternationalAlacarte},G.createElement(Ct,{filter:s,setFitler:u,languages:n}),G.createElement(Ot,{openLightbox:i,channels:r,navigation:l,languages:n.filter(function(e){return"all"===s||e.indexOf(s)>-1}),refresh:c})),G.createElement(W.Redirect,{to:B.EWidgetRoute.TV_InternationalCombos})),G.createElement(nt,{pageName:B.Volt.EDIsplayGroupKey.INTERNATIONAL,label:"LEGAL_LABEL_".concat(B.Volt.EDIsplayGroupKey.BASE_PROGRAMMING),content:"LEGAL_COPY_".concat(B.Volt.EDIsplayGroupKey.INTERNATIONAL)}))},wt=(0,F.connect)(function(e){var t=e.catalog,n=e.navigation,a=(0,B.ValueOf)(t,"offerings."+B.Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS,[]),r=(0,B.ValueOf)(t,"offerings."+B.Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE,[]);return{comboLanguages:N(a),channelLanguages:N(r),combos:a,channels:r,navigation:n,refresh:1e3*Math.random()}},function(e){return{openLightbox:function(t){return e(B.Actions.openLightbox({lightboxId:Et,data:{relativeId:t}}))}}})(Tt),Mt=function(e){var t=e.packages;return G.createElement(at,{name:"Movies and Series"},G.createElement("h2",{className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},G.createElement(z.FormattedMessage,{id:"Movies and Series page"})),G.createElement(B.FormattedHTMLMessage,{id:"Movies and Series page Description"},function(e){return G.createElement(B.Components.Visible,{when:Boolean(e)},G.createElement("div",{className:"spacer5"}),G.createElement("p",{className:"noMargintxtSize14"},e))}),G.createElement("div",{className:"spacer20"}),G.createElement("div",{className:"section-bell-tv-packages accss-focus-outline-override-white-bg"},S(t).map(function(e){return G.createElement(tt,V({key:e.id},e))})),G.createElement(nt,{pageName:B.Volt.EDIsplayGroupKey.MOVIE,label:"LEGAL_LABEL_".concat(B.Volt.EDIsplayGroupKey.BASE_PROGRAMMING),content:"LEGAL_COPY_".concat(B.Volt.EDIsplayGroupKey.MOVIE)}))},Lt=(0,F.connect)(function(e){var t=e.catalog;return{refresh:1e3*Math.random(),packages:(0,B.ValueOf)(t,"offerings."+B.Volt.EDIsplayGroupKey.MOVIE,[])}})(Mt),It=function(e){function t(e){e&&e.preventDefault&&e.preventDefault();var t=(("string"==typeof e?e:d)||"").trim();Boolean(t)&&(B.Omniture.useOmniture().trackAction({id:"searchSubmit",s_oAPT:{actionId:395,actionresult:0,applicationState:0},s_oSRT:t}),a.push({pathname:B.EWidgetRoute.TV_Search,search:"?query="+encodeURIComponent(t)}),p(t),u(!0),c([]))}e.catalog;var n,a=(0,W.useHistory)(),r=(0,W.useLocation)(),l=g(G.useState([]),2),i=l[0],c=l[1],o=g(G.useState(!0),2),s=o[0],u=o[1],m=g(G.useState(""),2),d=m[0],p=m[1];return G.useEffect(function(){"/Search"!==r.pathname&&p("")},[r]),G.useEffect(function(){function e(e){0===$(e.target).closest("#search_area").length&&c([])}return document.addEventListener("click",e),function(){document.removeEventListener("click",e)}},[]),n=function(e){c([]),u(!0)},G.createElement("div",{id:"search_area"},G.createElement("form",{id:"search-bar",className:"bell-tv-search-bar",onSubmit:t},G.createElement("div",{className:"bell-search-field relative accss-focus-outline-override-grey-bg"},G.createElement(B.FormattedHTMLMessage,{id:"Search channels"},function(e){return G.createElement("input",{"aria-label":"Search",onChange:function(e){return function(e){var t=e.target.value;p(t),Boolean(t)&&u(!1),c(function(e){return q&&e.length>0?q.search(e):[]}(t))}(e)},value:d,type:"text",className:"form-control bell-search-field-input",placeholder:e,"aria-autocomplete":"both",onFocus:function(){return u(!1)}})}),G.createElement("div",{className:"absolute bell-search-field-button"},G.createElement("button",{id:"SEARCH_ICON",type:"submit",className:"btn btn-search-submi txtSize20",onFocus:function(e){return n()},"aria-label":"Search"},G.createElement("span",{className:"volt-icon icon-search txtDarkGrey"}))))),G.createElement("div",{role:"status","aria-live":"assertive","aria-relevant":"additions",className:"sr-only"},!s&&Boolean(d)?(0,B.ValueOf)(i,"length",0)>0?G.createElement(z.FormattedMessage,{id:"AVAILABLE_SEARCH_RESULTS",values:{value:i.length}}):G.createElement(z.FormattedMessage,{id:"NO_SEARCH_RESULTS"}):null),G.createElement(B.Components.Visible,{when:(0,B.ValueOf)(i,"length",0)>0},G.createElement("div",{role:"tooltip","aria-label":"Search suggestions",className:"bell-search-suggestions tooltip fade bottom in bs-tooltip-bottom"},G.createElement("div",{className:"arrow",style:{left:"50%"},"aria-hidden":"true"}),i&&Boolean(i.length)&&G.createElement("div",{className:"tooltip-inner"},G.createElement("ul",{className:"noBullets",role:"listbox"},i.map(function(e){return G.createElement("li",null,G.createElement("button",{id:"SEARCH_".concat(e.name),role:"option",onClick:function(){t(e.name)},className:"btn txtLeft pad-0 txtNormal"},e.name))}))))))},Rt=(0,F.connect)(function(e){return{catalog:e.catalog}},function(e){return{}})(It),Vt=B.Components.Visible,Dt=function(e){return G.createElement(W.Link,{id:"MENU_".concat(e.offeringId),to:e.route,role:"link",className:"bell-tv-navigator-tab-row flexRow ".concat(e.isActive?"active":"")},G.createElement("div",{className:"bell-tv-navigator-tabs-text flexGrow"},G.createElement("span",{className:"".concat(e.subMenu?"sans-serif txtSize14":"virginUltraReg txtSize16 text-uppercase"," noPadding block submenu-name")},G.createElement(z.FormattedMessage,{id:e.offeringKey||"NONE"}),G.createElement(Vt,{when:void 0!==e.count}," (",e.count,")")),G.createElement(Vt,{when:Boolean(e.name)},G.createElement("span",{className:"virginUltraReg txtSize16 noPadding submenu-name text-uppercase"},e.name," - ")),G.createElement(Vt,{when:Boolean(e.subTotalPrice)},G.createElement("span",{className:"noPadding submenu-price submenu-price txtSize14"},G.createElement(B.Components.BellCurrency,{value:(0,B.ValueOf)(e,"subTotalPrice.price",0)}),G.createElement("span",{"aria-hidden":!0},G.createElement(z.FormattedMessage,{id:"PER_MO"})),G.createElement("span",{className:"sr-only"},G.createElement(z.FormattedMessage,{id:"PER_MONTH"},function(e){return G.createElement(G.Fragment,null,e)}))))),G.createElement("div",{className:"bell-tv-navigator-tabs-pointer flexStatic"},G.createElement("span",{className:"volt-icon icon-Right_arrow txtSize15 inlineBlock","aria-hidden":!0})))},Pt=function(e){var t=e.navigation;return G.createElement("nav",{className:"bell-tv-navigator sticky",role:"tablist"},G.createElement("div",{className:"virginUltraReg txtSize22 bgGray mobile-menu-header text-uppercase d-block d-md-none"},G.createElement(z.FormattedMessage,{id:"YOUR_TV_CATEGORIES"})),G.createElement(Rt,null),G.createElement("div",{className:"spacer15 hidden-xs"}),G.createElement("ul",{className:"bell-tv-navigator-tabs noBullets virgin-scroll accss-focus-outline-override-grey-bg",role:"presentation"},t.map(function(e){var t=Boolean((0,W.useRouteMatch)(e.route));return G.createElement("li",{key:e.key,className:"bell-tv-navigator-tab ".concat(t?"active expanded":""),role:"tab","aria-selected":t?"true":"false"},G.createElement(Dt,V({},e)),G.createElement(Vt,{when:Array.isArray(e.children)},G.createElement("div",{className:"bell-tv-navigator-tab-more ".concat(t?"active":""," ").concat(t?"d-block":"d-none"),"aria-expanded":location.pathname===e.route,role:"tab"},(0,B.ValueOf)(e,"children",[]).map(function(e){return G.createElement(Dt,V({subMenu:!0,isActive:Boolean((0,W.useRouteMatch)(e.route))},e))}))))})),G.createElement("div",{id:"tv-sedebar-summary-portal",className:"dockbar-content d-block d-md-none pad-15-top"}))},Bt=(0,F.connect)(function(e){return{navigation:e.navigation}})(Pt),Gt=B.Components.Visible,Ft=function(e){var t,n=e.id,a=e.name,r=e.shortDescription,l=e.longDescription,i=e.regularPrice,c=e.promotionDetails,o=e.childOfferings,s=e.offeringAction,u=e.isSingle,m=(e.isSelectable,e.isSelected),d=e.isDisabled,p=e.isCurrent,f=e.onActionClick,E=g(G.useState(!1),2),h=E[0],v=E[1],b=(0,z.useIntl)();return G.useEffect(function(){h&&B.Omniture.useOmniture().trackAction({id:"showChannelsClick",s_oAPT:{actionId:648},s_oEPN:"Show Channel"})},[h]),t=(0,B.ValueOf)(o,"length",0)>0,G.createElement("div",{id:n,className:"bell-tv-package bell-tv-base-pack accss-focus-outline-override-white-bg ".concat(d?"disabled":""," ").concat(u||m?"selected":"")},G.createElement("div",{className:"bell-tv-package-main flexRow block-xs bgWhite"},G.createElement("div",{className:"relative bell-tv-package-controls flexStatic no-margin-xs"},G.createElement(Gt,{when:p},G.createElement("div",{className:"absolute pad-5-top pad-5-bottom pad-30-left pad-30-right bgOrange txtWhite current-flag"},G.createElement(z.FormattedMessage,{id:"Current package"})),G.createElement("div",{className:"spacer30"})),G.createElement("label",{id:"label_".concat(n),className:"graphical_ctrl ctrl_radioBtn pointer ".concat(d?"disabled":""),onClick:function(){return!d&&!m&&!u&&f(s)}},G.createElement("input",{id:"radio_".concat(n),type:"radio",checked:u||m,disabled:d}),G.createElement("span",{className:"ctrl_element"}),G.createElement("span",{className:"package-name block inlineBlock-xs txtSize18 txtNormal txtBlack"},a),G.createElement(Be,{regularPrice:i,promotionDetails:c}))),G.createElement("div",{className:"bell-tv-package-separator flexStatic"}),G.createElement("div",{className:"bell-tv-package-description relative flexBlock flexWrap"},G.createElement("div",{className:"bell-tv-package-channels-detail flexStatic flexBasis100 order1"},G.createElement("p",{className:"noMargin txtSize14"},l||r)),G.createElement("div",{className:"spacer30 flexStatic flexBasis100 order2"}," "),G.createElement("div",{className:"order4 flex1"},G.createElement("div",{className:"spacer10 visible-sm"}),G.createElement("div",{className:"bell-tv-package-icons noMargin flexBlock flexWrap","aria-hidden":"true"},(0,B.ValueOf)(o,void 0,[]).slice(0,10).map(function(e){return G.createElement("div",{className:"channel-item"},G.createElement("img",{src:(0,B.ValueOf)(e,"imagePath",""),alt:(0,B.ValueOf)(e,"name","")}))})),G.createElement("div",{className:"spacer15 col-xs-12 order5"}),G.createElement(Gt,{when:t},G.createElement("div",{className:"col-xs-12 order6 flex-container"},G.createElement("button",{id:"ACCORDION_ICON_".concat(n),onClick:function(){return v(!h)},"aria-controls":"div1-accessible","data-toggle":"collapse",className:"btn btn-link no-pad txtVirginBlue accordion-accessible-toggle txtSize18 txtDecorationNoneHover txtWhite flexRow align-items-center","aria-expanded":h,"aria-label":"".concat(b.formatMessage({id:"Show channels"})," ").concat(b.formatMessage({id:"FOR_TEXT"})," ").concat(a)},G.createElement("span",{className:"volt-icon txtSize22 icon-blue icon-".concat(h?"Collapse":"Expand")},G.createElement("span",{className:"volt-icon path1 icon-".concat(h?"Collapse":"Expand")}),G.createElement("span",{className:"volt-icon path2 icon-".concat(h?"Collapse":"Expand")})),G.createElement("span",{className:"txtSize12 sans-serif txtBlue margin-10-left"},G.createElement(z.FormattedMessage,{id:"Show channels"})))))),G.createElement("div",{className:"clear"}))),G.createElement(Gt,{when:h},G.createElement("div",{className:"bell-tv-package-footer bgGray19 expanded",role:"region"},G.createElement("div",{className:"spacer1 bgGray"}),G.createElement(ft,{groupName:"radio_".concat(n),channels:(0,B.ValueOf)(o,void 0,[]),label:G.createElement(z.FormattedMessage,{id:"Package channels",values:{name:a}})}))))},Wt=(0,F.connect)(function(e){return{}},function(e){return{onActionClick:function(t){return e(ee(t))}}})(Ft),zt=function(e){var t=e.packages;return G.createElement(at,{name:"Your TV package"},G.createElement("h2",{className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},G.createElement(z.FormattedMessage,{id:"Available Core Packages page"})),G.createElement(B.FormattedHTMLMessage,{id:"Available Core Packages page Description"},function(e){return G.createElement(B.Components.Visible,{when:Boolean(e)},G.createElement("div",{className:"spacer15"}),G.createElement("p",{className:"noMargintxtSize14"},e))}),G.createElement("div",{className:"spacer30 hidden-xs","aria-hidden":!0}),S(t).map(function(e){return G.createElement(Wt,V({key:e.displayGroupKey,isSingle:t.length<2},e,{isDisabled:!(0,B.ValueOf)(e,"offeringAction.href",!1)}))}),G.createElement(nt,{pageName:B.Volt.EDIsplayGroupKey.BASE_PROGRAMMING,label:"LEGAL_LABEL_".concat(B.Volt.EDIsplayGroupKey.BASE_PROGRAMMING),content:"LEGAL_COPY_".concat(B.Volt.EDIsplayGroupKey.BASE_PROGRAMMING)}))},Kt=(0,F.connect)(function(e){var t=e.catalog;return{packages:(0,B.ValueOf)(t,"offerings."+B.Volt.EDIsplayGroupKey.BASE_PROGRAMMING,[]).filter(function(e){return e.productOfferingType===B.Volt.EProductOfferingType.PACKAGE})}})(zt),Ut=B.Components.Visible,jt="",Ht=function(e){var t=(0,F.useDispatch)(),n=(0,W.useLocation)(),a=G.useMemo(function(){return(0,B.ValueOf)(function(e){return Boolean(e)?e.replace("?","").split("&").reduce(function(e,t){var n=t.split("=");return e[n[0]]=decodeURIComponent(n[1]||""),e},{}):{}}(n.search),"query","")},[n]),r=function(e){var t=g(G.useState(null),2),n=t[0],a=t[1],r=(0,F.useSelector)(function(e){return(0,B.ValueOf)(e,"catalog.channels",[])});return G.useEffect(function(){var t;a((t=e,q&&t.length>0?q.search(t):[]))},[e,r]),n}(a);return G.useEffect(function(){jt!==a&&r&&t(B.Actions.omniPageLoaded("search",{id:"Search result",s_oAPT:{actionId:395,actionresult:2,applicationState:r.length>0?1:2},s_oSRT:jt=a}))},[r]),r?G.createElement(G.Fragment,null,G.createElement("div",{className:"flexRow flex-justify-space-between"},G.createElement("div",{className:"margin-xs"},G.createElement("h2",{id:"Search",className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},G.createElement(Ut,{when:r.length>0,placeholder:G.createElement(z.FormattedMessage,{id:"NO_SEARCH_RESULT_FOR",values:{value:a}},function(e){return G.createElement("span",{"aria-live":"polite",role:"alert"},e)})},G.createElement(z.FormattedMessage,{id:"SEARCH_RESULT_FOR",values:{value:a}},function(e){return G.createElement("span",{"aria-live":"polite",role:"alert"},e)}))))),G.createElement("div",{className:"spacer15","aria-hidden":"true"}),G.createElement(ft,{groupName:"Search",channels:r,allowSelection:!0,forceSelectable:!0,allowMultipleWaysToAdd:!0,showFilters:!1,showHeader:!1})):null},qt=B.Components.BellCurrency,Yt=B.Components.Visible,Xt=function(e){var t=e.Name,n=e.RegularPrice,a=e.PromotionDetails,r=e.ChannelCount,l=e.displayGroupKey;return G.createElement("div",null,G.createElement("div",{className:"flexRow"},G.createElement("div",{className:"flexGrow"},t,G.createElement(Yt,{when:l===B.Volt.EDIsplayGroupKey.ALACARTE&&r>0},G.createElement(z.FormattedMessage,{id:"Count of channels",values:{count:r}}))),G.createElement("div",null,G.createElement(qt,{value:(0,B.ValueOf)(n,"Price",0)}),G.createElement("span",{"aria-hidden":!0},G.createElement(z.FormattedMessage,{id:"PER_MO"})),G.createElement("span",{className:"sr-only"},G.createElement(z.FormattedMessage,{id:"PER_MONTH"},function(e){return G.createElement(G.Fragment,null,e)})))),G.createElement(Yt,{when:!!a},G.createElement("div",{className:"spacer5","aria-hidden":"true"}),G.createElement(Yt,{when:(0,B.ValueOf)(a,"Description",!1)},G.createElement("div",{className:"flexRow"},G.createElement("div",{className:"flexGrow"},(0,B.ValueOf)(a,"Description","")),G.createElement("div",null,G.createElement(qt,{value:(0,B.ValueOf)(a,"PromotionalPrice.Price",0)}),G.createElement("span",{"aria-hidden":!0},G.createElement(z.FormattedMessage,{id:"PER_MO"})),G.createElement("span",{className:"sr-only"},G.createElement(z.FormattedMessage,{id:"PER_MONTH"},function(e){return G.createElement(G.Fragment,null,e)}))))),G.createElement(Yt,{when:(0,B.ValueOf)(a,"ExpiryDate",!1)},G.createElement("div",null,G.createElement(z.FormattedDate,{value:(0,B.ValueOf)(a,"ExpiryDate",""),format:"yMMMMd",timeZone:"UTC"},function(e){return G.createElement(z.FormattedMessage,{id:"PromotionExpires",values:{expiryDate:e}})})))))},$t=function(e){var t=e.accountDetails,n=g(G.useState(!1),2),a=n[0],r=n[1],l=a?"icon-Collapse":"icon-Expand";return G.useEffect(function(){a&&B.Omniture.useOmniture().trackAction({id:"myCurrentPackageCTA",s_oAPT:{actionId:648},s_oEPN:"My current TV package"})},[a]),G.createElement(Yt,{when:Array.isArray(t)&&t.length>0},G.createElement("section",{className:"bgVirginGradiant"},G.createElement("div",{className:"container liquid-container sans-serif"},G.createElement("div",{className:"accordion-group internet-current-package flexCol accss-focus-outline-override-black-bg"},G.createElement("div",{className:"accordion-heading col-xs-12 noPaddingImp accss-focus-outline-override-black-bg"},G.createElement("a",{role:"button",id:"my_currentPack",href:"javascript:void(0)",onClick:function(){return r(!a)},"aria-controls":"div1-accessible",className:"accordion-accessible-toggle txtSize18 txtDecorationNoneHover txtWhite flexRow align-items-center accss-width-fit-content","aria-expanded":a},G.createElement("span",{className:"sr-only accordion-label","aria-live":"polite","aria-atomic":"true","aria-hidden":"true"},G.createElement(z.FormattedMessage,{id:a?"Collapse":"Expand"})),G.createElement("span",{className:"".concat(l," virgin-icon txtSize24 virginRedIcon"),"aria-hidden":"true"},G.createElement("span",{className:"virgin-icon path1 ".concat(l)}),G.createElement("span",{className:"virgin-icon path2 ".concat(l)})),G.createElement("div",{className:"margin-15-left flexCol"},G.createElement("span",{className:"txtWhite txtBold txtSize18"},G.createElement(z.FormattedMessage,{id:"My current TV package"})),G.createElement("span",{className:"expand txtWhite txtSize12 no-margin-top",style:{display:a?"none":void 0}},G.createElement(z.FormattedMessage,{id:"Expand to view details"}))))),G.createElement("div",{id:"div1-accessible",className:"collapse-accordion-accessible-toggle accordion-body txtWhite col-xs-12 margin-5-top margin-40-left",style:{display:a?"block":"none"}},G.createElement("div",{className:"accordion-inner flexWrap flexJustifySpace flexRow"},t.map(function(e){var t=e.displayGroupKey,n=e.offerings;return G.createElement("div",{className:"col-sm-5 margin-15-bottom"},G.createElement("strong",null,G.createElement(z.FormattedMessage,{id:"HEADER_".concat(t)})),n.map(function(e){return G.createElement(Xt,V({},e,{displayGroupKey:t}))}))})))))))},Jt=(0,F.connect)(function(e){return{accountDetails:e.accountDetails||[]}})($t),Zt=B.Components.RestrictionModal,Qt=B.Actions.errorOccured,en=B.Actions.widgetRenderComplete,tn=B.Actions.handleNav,nn=function(e){var t,n=(0,F.useDispatch)(),a=(0,W.useHistory)();return G.useEffect(function(){n(B.Actions.broadcastUpdate(B.Actions.setHistoryProvider(a)))},[]),t=(0,W.useLocation)(),G.useEffect(function(){e.closeNav(),_(),window.scrollTo(0,0)},[t]),G.createElement("main",{id:"mainContent"},G.createElement("style",{dangerouslySetInnerHTML:{__html:"\n            html {\n                scroll-behavior: smooth;\n            }\n            @media (max-width: 992px) {\n                .channel-tooltip {\n                    display: none!important;\n                }\n            }\n        "}}),G.createElement(Jt,null),G.createElement("div",{className:"spacer30"}),G.createElement("div",{className:"container liquid-container flexRow"},G.createElement("div",{className:"col-md-3 col-xs-12 bell-tv-navigator-menu side-navigation d-md-block ".concat(e.navStatus?"open-nav-slider":"")},G.createElement(Bt,null)),G.createElement("div",{className:"floatR col-md-9 col-xs-12 bell-tv-navigator-page accss-focus-outline-override-white-bg"},G.createElement(W.Switch,null,G.createElement(W.Route,{exact:!0,path:B.EWidgetRoute.TV_Packages},G.createElement(Kt,null)),G.createElement(W.Route,{path:B.EWidgetRoute.TV_MoviesSeries},G.createElement(Lt,null)),G.createElement(W.Route,{path:B.EWidgetRoute.TV_Alacarte},G.createElement(xt,null)),G.createElement(W.Route,{path:B.EWidgetRoute.TV_International},G.createElement(wt,null)),G.createElement(W.Route,{path:B.EWidgetRoute.TV_Addons},G.createElement(lt,null)),G.createElement(W.Route,{path:B.EWidgetRoute.TV_Browse},G.createElement(At,null)),G.createElement(W.Route,{path:B.EWidgetRoute.TV_Search},G.createElement(Ht,null)),G.createElement(W.Route,{path:"*"},G.createElement(W.Redirect,{to:B.EWidgetRoute.TV_Packages}))))),G.createElement(Re,null),G.createElement(je,null),G.createElement(Zt,{id:"TV_RESTRICTION_MODAL"}),G.createElement("div",{id:"NAV_BACKDROP",onClick:e.closeNav,className:"nav-backdrop ".concat(e.navStatus?"show":"hide"),"aria-hidden":!0}))},an=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return d(t,e),t.prototype.componentDidCatch=function(e){this.props.onErrorEncountered(e)},t.prototype.componentWillMount=function(){this.baseRoute="/Ordering".concat(B.Utils.constructPageRoute(B.EWidgetRoute.TV))},t.prototype.componentDidMount=function(){this.props.widgetRenderComplete("omf-changepackage-tv")},t.prototype.render=function(){return G.createElement(W.BrowserRouter,{basename:this.baseRoute},G.createElement(nn,V({},this.props)),G.createElement("div",{className:"spacer60"}),G.createElement("div",{className:"spacer60"}))},t}(G.Component),rn=(0,F.connect)(function(e){return{navStatus:e.navStatus}},function(e){return{onErrorEncountered:function(t){return e(Qt(t))},widgetRenderComplete:function(){return e(en())},closeNav:function(){return e(tn(!1))}}})(an),ln=B.Components.ApplicationRoot,cn=function(){return G.createElement(ln,null,G.createElement(rn,null))},on=P.CommonFeatures.BasePipe,sn=function(e){function t(n){var a=e.call(this,n)||this;return t.instance=a,a}return d(t,e),t.Subscriptions=function(e){var t;return(t={})[B.Actions.handleNav.toString()]=function(t){var n=t.payload;_(),e.dispatch(B.Actions.handleNav(n))},t[B.Actions.onContinue.toString()]=function(){_(),e.dispatch(B.Actions.omniPageSubmit()),B.Actions.broadcastUpdate(B.Actions.historyForward())},t},t}(on),un=B.Actions.setWidgetProps,mn=B.Actions.setWidgetStatus,dn=F.Provider,pn=function(e){function t(t,n,a,r){var l=e.call(this)||this;return l.store=t,l.params=n,l.config=a,l.pipe=r,l}return d(t,e),t.prototype.init=function(){this.pipe.subscribe(sn.Subscriptions(this.store)),this.store.dispatch(un(this.config)),this.store.dispatch(un(this.params.props)),this.store.dispatch(mn(B.EWidgetStatus.INIT))},t.prototype.destroy=function(){this.pipe.unsubscribe(),this.store.destroy()},t.prototype.render=function(e){var t=this.store;e.render(G.createElement(B.ContextProvider,{value:{config:this.config}},G.createElement(dn,{store:t},G.createElement(cn,null))))},p([(0,P.Widget)({namespace:"Ordering"}),f("design:paramtypes",[we,P.ParamsProvider,le,sn])],t)}(P.ViewWidget),fn=pn,L}()});
//# sourceMappingURL=widget.js.map