import { createAction, Action } from "redux-actions";
// import { IOrderAPIResponse } from "../models";
// import { orederDetailsMutatorFn } from "../mutators";

// Widget actions
export const getOderDetails = createAction("GET_ORDER_DETAILS");
export const getAppointment = createAction("GET_APPOINTMENT");
export const setAppointment = createAction<any>("SET_APPOINTMENT") as (payload: any) => Action<any>;
export const setAvailableDates = createAction<any>("SET_AVAIALBLE_DATES") as (payload: any) => Action<any>;
// export const setPreferredDate = createAction<any>("SET_PREFERRED_DATE") as (payload: any) => Action<any>;
export const contactInformation = createAction<any>("SET_CONTACT_INFO") as (payload: any) => Action<any>;
export const setDuration = createAction<any>("SET_DURATION") as (payload: any) => Action<any>;
export const setInstallationAddress = createAction<any>("SET_INSTALLATION_ADDRESS") as (payload: any) => Action<any>;
export const setAdditionalDetails = createAction<any>("SET_ADDITIONAL_DETAILS") as (payload: any) => Action<any>;
export const setIsInstallationRequired = createAction<any>("SET_INSTALLATION_REQUIRED") as (payload: any) => Action<any>;
export const setForErrors = createAction<any>("SET_FORM_ERRORS") as (payload: any) => Action<any>;
// export const setOderDetails = createAction<IOrderdetails>("SET_FLOW_SUMMARY_TOTALS", orederDetailsMutatorFn as any) as (respones: IOrderAPIResponse) => Action<IOrderdetails>;

// Piped actions


// Action for Global Actions Listener
export const initSlickSlider = createAction("INIT_SLICK_SLIDER");
