import { Volt, ValueOf, Omniture, EFlowType } from "omf-changepackage-components";
import { ISummary, IAPIResponse } from "../models";

export function summaryTransformerFn(data: ISummary | IAPIResponse): ISummary {
  const priceOvertime = ValueOf(data, "priceOvertime", []);
  const summary: ISummary = {
    ...data,
    Internet: priceOvertime.find((price: Volt.ILineOfBusiness) => price.flowType === "Internet"),
    TV: priceOvertime.find((price: Volt.ILineOfBusiness) => price.flowType === "TV")
  } as ISummary;
  return summary;
}

export function setFlowTypeFn(type: string): string {
  // Update flow type in session storage:
  sessionStorage.setItem("omf:Flowtype", type);
  switch (type) {
    case EFlowType.INTERNET:
      Omniture.useOmniture().updateContext({
        s_oSS2: "Internet"
      });
      break;
    case EFlowType.TV:
      Omniture.useOmniture().updateContext({
        s_oSS2: "Change package"
      });
      break;
    case EFlowType.ADDTV:
      Omniture.useOmniture().updateContext({
        s_oSS2: "Add TV"
      });
      break;
    case EFlowType.BUNDLE:
      Omniture.useOmniture().updateContext({
        s_oSS2: "Bundle"
      });
      break;
  }
  return type;
}
