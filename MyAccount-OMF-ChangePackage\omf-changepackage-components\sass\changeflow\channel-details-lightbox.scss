@import "mixins";
.bell-channel-details {
    .offering-description {
        margin: 0;
        word-break: break-word;
        p {
            &:first-child {
                color: #00549a;
            }
            &:last-child {
                margin: 0;
            }
        }
    }
    @keyframes showWithDelay {
        0% {
            height: 0;
        }
        90% {
            height: 0;
        }
        100% {
            height: auto;
        }
    }
    .channel-details-popular-shows {
        max-width: 100%;
        .bell-posters {
            margin: 0 40px;
            overflow: hidden;
            height: 0;
            @media #{$media-mobile} {
                margin: 0 30px;
            }
            &.slick-initialized {
                overflow: visible;
                height: auto;
            }
            &.no-slider {
                height: auto;
                margin: auto;
                animation: 1s ease-out 0s 1 showWithDelay;
                .bell-poster-image {
                    &:first-child {
                        padding-left:0px;
                    }
                    padding-bottom: 0;
                }
            }
            .bell-poster-image .bell-poster-detail {
                background-color: #fff;
                color: #111;
                padding: 5px 0;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                >span {
                    text-overflow: unset;
                    overflow: unset;
                    display: unset;
                    height: unset;
                    -webkit-line-clamp: unset;
                    -webkit-box-orient: unset;
                }
            } // img.bell-movie-poster {
            //     max-height: 115px;
            // }
        }
    }
}

.tooltip-inner {
    .channel-description p:first-child {
        color: #00549a;
    }
}