import { ReducersMapObject } from "redux";
import { handleActions } from "redux-actions";
import { CommonFeatures } from "bwtk";
import { Actions } from "../Actions";
import { Volt } from "../Models";

const { actionsToComputedPropertyName } = CommonFeatures;
const {
  raiseRestriction,
  acceptRestriction,
  declineRestriction
} = actionsToComputedPropertyName(Actions);
export function getWidgetRestrictions(): ReducersMapObject {
  return {
    restriction: handleActions<Volt.IRestriction | null>({
      [raiseRestriction]: (state, {payload}) => payload || state,
      [acceptRestriction]: () => null,
      [declineRestriction]: () => null
    }, null) as any
  };
}
