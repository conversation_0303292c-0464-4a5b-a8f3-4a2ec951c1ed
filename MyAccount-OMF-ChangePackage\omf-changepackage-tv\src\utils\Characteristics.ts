import { Volt, ValueOf } from "omf-changepackage-components";
import { Localization } from "../Localization";

export function toCharacteristicsJSON(charactgerstics: Array<Volt.ICharacteristic>): { [key: string]: any } {
  return ValueOf<Array<Volt.ICharacteristic>>(charactgerstics, undefined, []).reduce(
    (json, charactgerstic) => {
      if (charactgerstic.name) {
        json[charactgerstic.name] = (charactgerstic.value || "").trim();
      }
      return json;
    }, {} as any
  );
}

export function toCharacteristicsJSONArray(charactgerstics: Array<Volt.ICharacteristic>): { [key: string]: Array<any> } {
  return ValueOf<Array<Volt.ICharacteristic>>(charactgerstics, undefined, []).reduce(
    (json, charactgerstic) => {
      if (charactgerstic.name) {
        json[charactgerstic.name] = (charactgerstic.value || "").split(",").map(s => s.trim());
      }
      return json;
    }, {} as any
  );
}
export function getSupportdLanguages(offerings: Array<Volt.IProductOffering>): Array<string> {
  return ValueOf<Array<Volt.IProductOffering>>(offerings, undefined, []).reduce(
    (acc, offering) => {
      const { language } = toCharacteristicsJSON(offering.characteristics);
      if (Boolean(language)) {
        language.split(",")
          .map((l: string) => l.trim())
          .filter(Boolean)
          .forEach((l: string) => {
            if (acc.indexOf(l) < 0) acc.push(l);
          });
      }
      return acc.sort();
    }, [] as Array<string>
  ).filter(Boolean).sort();
}

export function getSupportdGenres(offerings: Array<Volt.IProductOffering>): Array<string> {
  return ValueOf<Array<Volt.IProductOffering>>(offerings, undefined, []).reduce(
    (acc, offering) => {
      const { genre } = toCharacteristicsJSON(offering.characteristics);
      if (Boolean(genre)) {
        genre.split(",")
          .map((l: string) => l.trim())
          .filter(Boolean)
          .forEach((l: string) => {
            if (acc.indexOf(l) < 0) acc.push(l);
          });
      }
      return acc.sort();
    }, [] as Array<string>
  ).filter(Boolean).sort();
}

export function filterLanguage(offerings: Array<Volt.IProductOffering>, language: string): Array<Volt.IProductOffering> {
  return offerings.filter(
    offering => ((toCharacteristicsJSON(offering.characteristics)).language || "").indexOf(language) > -1
  );
}

export function sortOfferings(offerings: Array<Volt.IProductOffering>, direction: "asc" | "desc" = "asc"): Array<Volt.IProductOffering> {
  return ValueOf<Array<Volt.IProductOffering>>(offerings, undefined, []).sort(
    (a, b) => (
      (ValueOf<number>(toCharacteristicsJSON(a.characteristics), "sortPriority", 0) -
                ValueOf<number>(toCharacteristicsJSON(b.characteristics), "sortPriority", 0)) *
            (direction === "asc" ? 1 : -1)
    )
  );
}

export function translateStringList(list: string): string {
  return Boolean(list) ? list.split(",").map(t => {
    const trimmedT = t.trim();
    return Localization.getLocalizedString(trimmedT);
  }).join(", ") : list;
}
