/**
 * Returns the value of object nested propery
 * without failing, even when the obeject is indefined
 * @export
 * @param {*} from Object to extract the property value from
 * @param {string | null} [path] a string path to the property (optional)
 * @param {*} [propDefault] a default value to return in case any of the path asertions fail (optional)
 * @returns {*} returns the value of the propety, or default value
 * @example
 * const obj = {
 *	prop1: {
 *  	prop2: "test",
 *    props3: null
 *  },
 *  props2: [1,2,3]
 * }
 *
 * console.log("should return 'test'", ValueOf(obj, "prop1.prop2"));
 * console.log("should return [1,2,3]", ValueOf(obj, "props2"));
 * console.log("should return 'default'", ValueOf(obj, "?prop1.prop4.nothing", "default"));
 * console.log("should return 'null'", ValueOf(obj, "prop1.prop4.props3", "null"));
 * console.log("should return undefined", ValueOf(obj, "prop3.prop2.prop1"));
 */
export function ValueOf<T = any>(from: any, path?: string, propDefault?: any): T {
  if (!Boolean(path)) return from || propDefault;
  const passEmptyValues = /^\?/.test(path || "");
  const props = (path || "").replace("?", "").split(".");
  let result = from;
  for (let i = 0; i < props.length; i++) {
    if (result
            && result[props[i]] !== undefined
            && result[props[i]] !== null
            && (passEmptyValues
                || (
                  result[props[i]] !== 0
                    && result[props[i]] !== ""
                )
            )) {
      result = result[props[i]];
    } else {
      result = propDefault;
    }
  }
  return result;
}
