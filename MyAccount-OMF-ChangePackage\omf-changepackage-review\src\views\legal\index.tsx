import { Context, Omniture, Components, Utils, EFlowType, FormattedHTMLMessage } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { useDispatch, useSelector } from "react-redux";
import { Localization } from "../../Localization";
import { setAcceptedTerms } from "../../store";
import { selectors } from "../../utils";
import Expander from "./Expander";

const initialHeight = 220,
  expandedHeight = initialHeight * 2;

export const Legal: React.FunctionComponent = () => {
  const [height, setHeight] = React.useState(initialHeight),
    dispatch = useDispatch(),
    acceptedTerms = useSelector(selectors.select("acceptedTerms")) || [],
    context: any = React.useContext(Context as any);
  const isQC = context.config.environmentVariables.province === "QC";
  const fileName = Localization.getLocalizedString("DOWNLOAD_PDF_FILE" + (isQC ? "_QC" : ""));
  const flowType = Utils.getFlowType();

  return (
    <>
      <div className="spacer15 clear" aria-hidden="true" />
      <div className="spacer15 d-sm-block clear" aria-hidden="true" />
      <div className="panel-border bgGrey2 flexBlock">
        <div className="bgWhite box-border-gray-light pad-30-left pad-30-top pad-30-bottom pad-10-right col-xs-12 accss-link-override accss-focus-outline-override-white-bg" style={{ maxWidth: "100%" }}>
          <h3 className="no-margin virginUltraReg txtSize22 txtDarkGrey1 txtUppercase txtLineHeight-24" id="TOS_Label">
            <FormattedMessage id="Virgin Mobile Terms of Service" />
          </h3>
          <a id="DOWNLOAD_PDF_LINK" onClick={(e) => {
            e.stopPropagation();
            Omniture.useOmniture().trackAction({
              id: "downloadPDFClick",
              s_oAPT: {
                actionId: 341
              },
              s_oBTN: {
                ref: "DOWNLOAD_PDF_LINK"
              }
            });
          }}
          aria-describedby="TOS_Label"
          className="txtUnderline noBorder bgTransparent pointer txtBlue" href={`${context.config.pdfDownloadPath + fileName}`} download>
            <FormattedMessage id="DOWNLOAD_PDF">
              {(downloadPdf) => <span aria-hidden="true">{downloadPdf}</span>}
            </FormattedMessage>
            <span className="sr-only">
              <FormattedMessage id="DOWNLOAD_TERMS_PDF">
                {(downloadTerms) => <>{downloadTerms}</>}
              </FormattedMessage>
            </span>
          </a>
          <div className="pad-30-right">
            <div className="spacer10 clear" aria-hidden="true"></div>
            <div className="spacer1 bgGrayLight6 clear" aria-hidden="true"></div>
            <div className="spacer15 clear" aria-hidden="true"></div>
          </div>
          <div className="col-xs-12 ">
            <div className="accordion">
              <div className="accordion-group">
                <div id="terms-conditions" className="scrollAdjust" style={{ height: `${height}px` }}>
                  <FormattedHTMLMessage id={"TERMS_AND_CONDITIONS" + (isQC ? "_QC" : "")} />
                  <div className="clear"></div>
                </div>
                <div className="accordion-heading">
                  <div className="col-xs-12 pad-10-top">
                    <button id="EXPAND_TERMS"
                      className="expand-toggle txtUnderline expand-collapse noBorder bgTransparent pointer txtBlue accss-link-underline-override links-blue-on-bg-white"
                      onClick={
                        () => {
                          setHeight(height === initialHeight ? expandedHeight : initialHeight);
                        }
                      }
                    >
                      <FormattedMessage id={`${height === initialHeight ? "Expand terms and conditions" : "Collapse terms and conditions"}`} />
                    </button>
                  </div>
                  <div className="clear"></div>
                </div>
              </div>
            </div>
          </div>
          <div className="clear"></div>
        </div>
      </div>
      <Components.Visible when={flowType === EFlowType.ADDTV || flowType === EFlowType.BUNDLE}
        placeholder={<>
          <div className="spacer15 clear" aria-hidden="true" />
          <p className="txtRight"><FormattedHTMLMessage id="ACCEPT_TERMS_AND_DESCRIPTION" /></p>
        </>}>
        <div className="flexBlock bgGray19 pad-h-15-xs">
          <div className="">
            <div className="">
              {
                ["TERM_1"] // , "TERM_2"
                  .map((term: string) => (
                    <React.Fragment key={term}>
                      <div className="spacer1 bgGrayLight6" aria-hidden="true"></div>
                      <div className="pad-20 ">
                        <label id={`label_${term}`} className="graphical_ctrl pointer graphical_ctrl_checkbox">
                          <FormattedMessage id={term} />
                          <input id={`checkbox_${term}`}
                            type="checkbox"
                            value={term}
                            defaultChecked={acceptedTerms.some((e: any) => e === term)}
                            onClick={
                              () => { dispatch(setAcceptedTerms(acceptedTerms, term) as any); }
                            }
                          />
                          <span className="ctrl_element chk_radius"></span>
                        </label>
                      </div>
                    </React.Fragment>
                  ))
              }
            </div>
          </div>
        </div>
      </Components.Visible>
      <div className="spacer15 clear" aria-hidden="true" />
      <div className="spacer15 d-sm-block clear" aria-hidden="true" />
      <Expander />
    </>
  );
};
