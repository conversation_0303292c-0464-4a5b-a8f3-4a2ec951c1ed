@import "mixins";
.brf {
    .floatR {
        float: right;
        margin-top: 15px;
    }
    .bell-tv-navigator-menu {
        top: 0;
        .bell-tv-search-bar {
            input.bell-search-field-input::-ms-clear {
                display: none;
            }
        }
        .bell-search-suggestions {
            .tooltip-arrow {
                margin-top: 0;
            }
            .tooltip-inner {
                max-width: 100%;
            }
        }
    }
    @media #{$media-desktop} {
        .bell-tv-channel-picker .bell-channels-flex-grid {
            flex-basis: 23%;
        }
    }
    .bell-tv-individual-channels>li>img {
        width: 100%;
    }
    #overviewButton, #headerLogoutButton {
        margin-top: 5px;
    }
}