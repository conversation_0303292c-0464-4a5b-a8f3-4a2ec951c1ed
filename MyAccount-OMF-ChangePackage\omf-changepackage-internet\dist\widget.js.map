{"version": 3, "file": "widget.js", "mappings": ";CAAA,SAA2CA,EAAMC,GAAjD,IAMMC,EACIC,EANT,GAAsB,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUH,EAAQK,QAAQ,SAAUA,QAAQ,eAAgBA,QAAQ,gCAAiCA,QAAQ,QAASA,QAAQ,SAAUA,QAAQ,iBAAkBA,QAAQ,oBAAqBA,QAAQ,QAASA,QAAQ,oBAC3N,GAAqB,mBAAXC,QAAyBA,OAAOC,IAC9CD,OAAO,CAAC,QAAS,cAAe,+BAAgC,OAAQ,QAAS,gBAAiB,mBAAoB,OAAQ,cAAeN,QAG7I,IAAQE,KADJD,EAAuB,iBAAZE,QAAuBH,EAAQK,QAAQ,SAAUA,QAAQ,eAAgBA,QAAQ,gCAAiCA,QAAQ,QAASA,QAAQ,SAAUA,QAAQ,iBAAkBA,QAAQ,oBAAqBA,QAAQ,QAASA,QAAQ,eAAiBL,EAAQD,EAAY,MAAGA,EAAiB,WAAGA,EAAiC,2BAAGA,EAAW,KAAGA,EAAY,MAAGA,EAAmB,aAAGA,EAAsB,gBAAGA,EAAW,KAAGA,EAAgB,YAC1Z,iBAAZI,QAAuBA,QAAUJ,GAAMG,GAAKD,EAAEC,EAEvE,CATD,CASGM,KAAM,SAASC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,GAClS,O,wBCNA,SAASC,EAAoBC,GAA7B,IAOKf,EALAgB,EAAeC,GAAyBF,GAC5C,YAAqBG,IAAjBF,EACIA,EAAajB,SAGjBC,EAASiB,GAAyBF,GAAY,CAGjDhB,QAAS,CAAC,GAIXoB,GAAoBJ,GAAUf,EAAQA,EAAOD,QAASe,GAG/Cd,EAAOD,QACf,CCCO,SAASqB,EAAUC,EAAGC,GAI3B,SAASC,IAAOC,KAAKC,YAAcJ,CAAG,CAHtC,GAAiB,mBAANC,GAA0B,OAANA,EAC3B,MAAM,IAAII,UAAU,uBAAyBC,OAAOL,GAAK,iCAC7DM,EAAcP,EAAGC,GAEjBD,EAAEQ,UAAkB,OAANP,EAAaQ,OAAOC,OAAOT,IAAMC,EAAGM,UAAYP,EAAEO,UAAW,IAAIN,EACjF,CAyBO,SAASS,EAAWC,EAAYC,EAAQC,EAAKC,GAA7C,IACsHf,EAE7GvB,EAFVuC,EAAIC,UAAUC,OAAQC,EAAIH,EAAI,EAAIH,EAAkB,OAATE,EAAgBA,EAAON,OAAOW,yBAAyBP,EAAQC,GAAOC,EACrH,GAAuB,iBAAZM,SAAoD,mBAArBA,QAAQC,SAAyBH,EAAIE,QAAQC,SAASV,EAAYC,EAAQC,EAAKC,QACpH,IAAStC,EAAImC,EAAWM,OAAS,EAAGzC,GAAK,EAAGA,KAASuB,EAAIY,EAAWnC,MAAI0C,GAAKH,EAAI,EAAIhB,EAAEmB,GAAKH,EAAI,EAAIhB,EAAEa,EAAQC,EAAKK,GAAKnB,EAAEa,EAAQC,KAASK,GAChJ,OAAOH,EAAI,GAAKG,GAAKV,OAAOc,eAAeV,EAAQC,EAAKK,GAAIA,CAC9D,CAmDO,SAASK,EAAWC,EAAaC,GACtC,GAAuB,iBAAZL,SAAoD,mBAArBA,QAAQM,SAAyB,OAAON,QAAQM,SAASF,EAAaC,EAClH,CAoEO,SAASE,EAAOC,EAAGC,GAAnB,IAGDrD,EAAe0C,EAAGY,EAASC,EAF3BC,EAAsB,mBAAXC,QAAyBL,EAAEK,OAAOC,UACjD,IAAKF,EAAG,OAAOJ,EACXpD,EAAIwD,EAAEG,KAAKP,GAAOE,EAAK,GAC3B,IACI,WAAc,IAAND,GAAgBA,KAAM,MAAQX,EAAI1C,EAAE4D,QAAQC,MAAMP,EAAGQ,KAAKpB,EAAEqB,MACxE,CACA,MAAOC,GAAST,EAAI,CAAES,MAAOA,EAAS,CACtC,QACI,IACQtB,IAAMA,EAAEmB,OAASL,EAAIxD,EAAU,SAAIwD,EAAEG,KAAK3D,EAClD,CACA,QAAU,GAAIuD,EAAG,MAAMA,EAAES,KAAO,CACpC,CACA,OAAOV,CACT,CCjMO,SAASW,EAAsBC,GACpC,OAAO,IAAAC,SAAqCD,OAAiB9C,EAAW,IAAIgD,OAC1E,SAACC,EAAMC,GAIL,OAHIA,EAAeC,OACjBF,EAAKC,EAAeC,MAASD,EAAeP,OAEvCM,CACT,EAAG,CAAC,EAER,C,QDKIvC,EAeO0C,EA0OPC,E,cEnQSC,EACAC,EACAC,EACAC,EAEAC,EACAC,E,ECTLC,EAAYC,EAoBpB,ECVA,ECKEC,EACAC,EACAC,EACAC,EAIF,ECPA,ECREC,EACAC,EAQF,ECPE,EAMF,ECfQC,EAGR,ECQQC,EACF,EACJ,GACA,GACA,GAIF,GCjBQC,GAUR,G,GCLMC,GA8EOC,GC7EXC,GACAC,GAqBI,GA8GOC,GCzIAC,GCMP,GAgFOC,GCpFXC,GAIA,GACAC,GAWF,GAmBaC,GCtCXC,GAGWC,GCIXC,GACA,GAIF,G,uBCjBArG,EAAOD,QAAUS,C,kBCAjBR,EAAOD,QAAUa,C,kBCAjBZ,EAAOD,QAAUc,C,kBCAjBb,EAAOD,QAAUM,C,kBCAjBL,EAAOD,QAAUQ,C,kBCAjBP,EAAOD,QAAUW,C,kBCAjBV,EAAOD,QAAUU,C,kBCAjBT,EAAOD,QAAUY,C,kBCAjBX,EAAOD,QAAUO,C,G5BCbW,GAA2B,CAAC,E,O6BAhCH,EAAoBO,EAAI,SAAStB,EAASuG,GACzC,IAAI,IAAInE,KAAOmE,EACXxF,EAAoBoC,EAAEoD,EAAYnE,KAASrB,EAAoBoC,EAAEnD,EAASoC,IAC5EL,OAAOc,eAAe7C,EAASoC,EAAK,CAAEoE,YAAY,EAAMC,IAAKF,EAAWnE,IAG3E,ECPArB,EAAoBoC,EAAI,SAASuD,EAAKC,GAAQ,OAAO5E,OAAOD,UAAU8E,eAAelD,KAAKgD,EAAKC,EAAO,ECCtG5F,EAAoB0B,EAAI,SAASzC,GACX,oBAAXwD,QAA0BA,OAAOqD,aAC1C9E,OAAOc,eAAe7C,EAASwD,OAAOqD,YAAa,CAAE/C,MAAO,WAE7D/B,OAAOc,eAAe7C,EAAS,aAAc,CAAE8D,OAAO,GACvD,E,6T9BUIjC,EAAgB,SAASP,EAAGC,GAI9B,OAHAM,EAAgBE,OAAO+E,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAU1F,EAAGC,GAAKD,EAAEyF,UAAYxF,CAAG,GAC1E,SAAUD,EAAGC,GAAK,IAAK,IAAI0F,KAAK1F,EAAOQ,OAAOD,UAAU8E,eAAelD,KAAKnC,EAAG0F,KAAI3F,EAAE2F,GAAK1F,EAAE0F,GAAI,EAC7FpF,EAAcP,EAAGC,EAC1B,EAUWgD,EAAW,WAQpB,OAPAA,EAAWxC,OAAOmF,QAAU,SAAkBC,GAAlB,IACfC,EAAGrH,EAAOqD,EAEN6D,EAFb,IAAYlH,EAAI,EAAGqD,EAAIb,UAAUC,OAAQzC,EAAIqD,EAAGrD,IAE5C,IAASkH,KADTG,EAAI7E,UAAUxC,GACOgC,OAAOD,UAAU8E,eAAelD,KAAK0D,EAAGH,KAAIE,EAAEF,GAAKG,EAAEH,IAE9E,OAAOE,CACX,EACO5C,EAAS8C,MAAM5F,KAAMc,UAC9B,EAgH6BR,OAAOC,OA2GXD,OAAOC,OAM5BwC,EAAU,SAASrB,GAMrB,OALAqB,EAAUzC,OAAOuF,qBAAuB,SAAUnE,GAAV,IAE7BoE,EADLlE,EAAK,GACT,IAASkE,KAAKpE,EAAOpB,OAAOD,UAAU8E,eAAelD,KAAKP,EAAGoE,KAAIlE,EAAGA,EAAGb,QAAU+E,GACjF,OAAOlE,CACT,EACOmB,EAAQrB,EACjB,EAuDkD,mBAApBqE,iBAAiCA,gB,+DEjUlD/C,GAAoB,IAAAgD,cAAa,uBACjC/C,GAAoB,IAAA+C,cAAqC,sB6BJ/D,SAAiCC,GACtC,OAAO,IAAAxD,SAAgCwD,EAAU,mBAAoB,CAAC,CAAEC,aAAa,IACvF,G7BGahD,GAAqB,IAAA8C,cAAa,wBAClC7C,GAAqB,IAAA6C,cAA8B,uB6BFzD,SAA0BC,GAC/B,IAAME,GACA,IAAA1D,SAAQwD,EAAU,8CAA+C,IAC9DG,KAAK,SAACC,GAAsC,OAAAA,EAAMC,iBAAmB,EAAAC,KAAKC,gBAAgBC,UACrFJ,EAAMK,2BAA6B,EAAAH,KAAKI,0BAA0BC,OAD3B,GAErD,OAAO,IAAAnE,SAAQ0D,EAAsB,mBAAoB,GAC3D,G7BFa/C,GAAyB,IAAA4C,cAAqC,2BAC9D3C,GAAwB,IAAA2C,cAA8B,0B6BG5D,SAAwBC,EAA6BY,GAArD,IACCV,GACA,IAAA1D,SAAQwD,EAAU,8CAA+C,IAC9DG,KAAK,SAACC,GAAsC,OAAAA,EAAMC,iBAAmB,EAAAC,KAAKC,gBAAgBC,UACrFJ,EAAMK,2BAA6B,EAAAH,KAAKI,0BAA0BG,KAD3B,GAOrD,OALuD,IAAArE,SAAQ0D,EAAsB,mBAAoB,IACxFY,QAAQ,SAAAC,GACvB,IAAMC,EAAoBJ,EAAQT,KAAK,SAAAc,GAAO,OAAAA,EAAIC,KAAOH,EAAQG,EAAnB,IAA0B,CAAC,EACzE7G,OAAOmF,OAAOwB,EAASD,EACzB,G/B6LK,SAAuBI,EAAIC,EAAMC,GACtC,GAAIA,GAA6B,IAArBxG,UAAUC,OAAc,IAAK,IAA4Ba,EAAxBtD,EAAI,EAAGiJ,EAAIF,EAAKtG,OAAYzC,EAAIiJ,EAAGjJ,KACxEsD,GAAQtD,KAAK+I,IACRzF,IAAIA,EAAK2D,MAAMlF,UAAUmH,MAAMvF,KAAKoF,EAAM,EAAG/I,IAClDsD,EAAGtD,GAAK+I,EAAK/I,IAGrB,OAAO8I,EAAGK,OAAO7F,GAAM2D,MAAMlF,UAAUmH,MAAMvF,KAAKoF,GACpD,C+BpMS,MAAIR,IAAO,EACpB,G,S5BvBQvD,EAA+B,EAAAoE,eAAc,WAAjCnE,EAAmB,EAAAmE,eAAc,eAoBrD,2B,8CAMA,QAN4B,OACN,GAAnBnE,EAAe,I,wDACI,GAAnBA,EAAe,CAAC,G,oEACG,GAAnBA,EAAe,CAAC,G,wDACG,GAAnBA,EAAe,CAAC,G,uDACgC,GAAhDA,EAAe,CAACoE,KAAM,0B,mDALN,GADlB,EAAAC,YACYC,E,CAAb,CAA4BvE,GCV5B,cACE,WAAYwE,EAA0BC,GACpC,SAAK,UAACD,EAAYC,IAAO,IAC3B,CACF,OAJ4B,OAAT,GADlB,EAAAH,W,uBAEyB,EAAAI,aAAsBH,KADnCI,E,CAAb,CAA4B,EAAAC,YCK1B1E,EAIE,EAAA2E,QAAO,aAHT1E,EAGE,EAAA0E,QAAO,gBAFTzE,EAEE,EAAAyE,QAAO,iBADTxE,EACE,EAAAwE,QAAO,oBAGX,aAGE,WAAoBC,EAAwBL,GAAxB,KAAAK,OAAAA,EAAwB,KAAAL,OAAAA,EAF5C,KAAAM,YAA6B,EAAAC,cAAcC,IAEmB,CAmFhE,OAjFE,YAAAC,aAAA,WACE,OAAO,IAAAA,cACLxI,KAAKyI,mBACLzI,KAAK0I,wBACL1I,KAAK2I,wBAET,EAEA,sBAAY,iCAAkB,C,IAA9B,sBACE,OAAO,SAACC,GACN,OAAAA,EAAQC,MACN,IAAAC,QAAO,SAACC,GAAkC,OAAAA,EAAOC,OAAS9F,EAAmB+F,UAAnC,IAC1C,IAAAH,QAAO,WAAM,SAAKT,cAAgB,EAAAC,cAAcY,QAAnC,IACb,IAAAC,UAAS,W,MAAM,WAAA1B,SACb,IAAA2B,IAAG3F,EAAgB,EAAK4E,YAAc,EAAAC,cAAcY,WACpD,EAAKd,OAAOpD,IAAqC,EAAAqE,MAAMC,kBACrD,EAAAD,MAAME,kBAAgB,KACpB,EAAC,EAAAC,UAAUC,IAAK,EAAK1B,OAAO2B,IAAIC,WAChC,EAAC,EAAAH,UAAUI,UAAW,EAAK7B,OAAO2B,IAAIC,WACtC,EAAC,EAAAH,UAAUK,QAAS,EAAK9B,OAAO2B,IAAII,iB,MAErCjB,MACD,IAAAM,UAAS,SAAClD,GACR,WAAA8D,6BAA4B9D,EAAU,CACpC9C,EAAmB8C,EAAS+D,MAC5B,EAAA7B,QAAQvE,iBACRH,EAAgB,EAAK4E,YAAc,EAAAC,cAAc2B,WAHnD,IAVS,IAkBf,IAAAC,YAAW,SAAC5H,GAAoB,WAAA8G,IAC9B5F,EAAa,IAAI,EAAA2G,OAAOC,aAAa,qBAAsB9H,IAD7B,GArBlC,CAyBJ,E,gCAEA,sBAAY,sCAAuB,C,IAAnC,sBACE,OAAO,SAACsG,EAAkCyB,GACxC,OAAAzB,EAAQC,MACN,IAAAC,QAAO,SAACC,GACN,OAAAA,EAAOC,OAAS5F,EAAuB6F,UAAvC,IAEF,IAAAH,QAAO,WAAM,SAAKT,cAAgB,EAAAC,cAAcY,QAAnC,IACb,IAAAC,UAAS,SAAC,G,IAAEmB,EAAO,UACjB,WAAA7C,SACE,IAAA2B,IAAG3F,EAAgB,EAAK4E,YAAc,EAAAC,cAAcY,WACpD,EAAKd,OAAOW,OAAwCuB,GAASzB,MAC3D,IAAAM,UAAS,SAAClD,GACR,WAAA8D,6BAA4B9D,EAAU,CACpC5C,EAAsB4C,EAAS+D,KAAOK,EAAehI,MAAMwE,SAC3DnD,EAAiB,CAAC,EAAA6G,YAAYC,UAC9B/G,EAAgB,EAAK4E,YAAc,EAAAC,cAAc2B,WAHnD,IAJN,IAaF,IAAAC,YAAW,SAAC5H,GAAoB,WAAA8G,IAAG5F,EAAa,IAAI,EAAA2G,OAAOC,aAAa,yBAA0B9H,IAAlE,GAnBlC,CAqBJ,E,gCAEA,sBAAY,sCAAuB,C,IAAnC,sBACE,OAAO,SAACsG,EAAkCyB,GACxC,OAAAzB,EAAQC,MACN,IAAAC,QAAO,SAACC,GACN,OAAAA,EAAOC,OAASrF,EAAoBsF,UAApC,IAEF,IAAAH,QAAO,SAAC,G,IAAEwB,EAAO,UACf,OAAAG,QAAQH,IACRG,QAAQH,EAAQI,wBAChB,EAAKrC,cAAgB,EAAAC,cAAcY,QAFnC,IAIF,IAAAC,UAAS,SAAC,G,IAAEmB,EAAO,UAAO,WAAAlB,IACxB,EAAAjB,QAAQwC,gBAAgB,EAAAxC,QAAQyC,8BAA6B,IAAAnI,SAAQ6H,EAAS,qDAC9EjH,EAAsBiH,EAAUD,EAAehI,MAAMwE,SACrDnD,EAAiB,CAAC,EAAA6G,YAAYC,UAC9B/G,EAAgB,EAAK4E,YAAc,EAAAC,cAAc2B,UAJzB,GAT5B,CAgBJ,E,gCArFuB,GADxB,EAAArC,W,uBAI6BK,EAAwBJ,KAHzCgD,E,CAAb,GCPA,aAGE,WAAoBzC,EAAwBL,GAAxB,KAAAK,OAAAA,EAAwB,KAAAL,OAAAA,EAF5C,KAAAM,YAA6B,EAAAC,cAAcC,IAEmB,CAsBhE,OApBE,YAAAC,aAAA,WACE,OAAO,IAAAA,cACLxI,KAAK8K,gBAET,EAEA,sBAAY,8BAAe,C,IAA3B,sBACE,OAAO,SAAClC,EAASyB,GACf,OAAAzB,EAAQC,MACN,IAAAkC,QAAO/H,EAAkBiG,aACzB,IAAAH,QAAO,WAAM,SAAKT,cAAgB,EAAAC,cAAcY,QAAnC,IACb,IAAAC,UAAS,WAAM,SAAKf,OAAOpD,IAAsC,EAAK+C,OAAO2B,IAAIsB,mBAAmBnC,MAClG,IAAAM,UAAS,SAAC,G,IAAEa,EAAI,OAAqC,WAAAZ,IACnDnG,EAAkB+G,GAClB9G,IAFmD,IAIrD,IAAAgH,YAAW,SAAC5H,GAAoB,WAAA8G,IAAGnG,EAAkB,CAAC,GAAtB,GALnB,GAHjB,CAWJ,E,gCAxB2B,GAD5B,EAAA2E,W,uBAI6BK,EAAwBJ,KAHzCoD,E,CAAb,GCRErH,EAEE,EAAAuE,QAAO,eADTtE,EACE,EAAAsE,QAAO,eAOX,0BACE,KAAAE,YAA6B,EAAAC,cAAcC,IA6E7C,QA3EE,YAAAC,aAAA,WACE,OAAO,IAAAA,cACLxI,KAAKkL,eACLlL,KAAKmL,eAET,EAYA,sBAAY,6BAAc,C,IAA1B,WACE,OAAO,SAACvC,EAAkCyB,GACxC,OAAAzB,EAAQC,MACN,IAAAkC,QAAOnH,EAAeqF,aACtB,IAAAE,UAAS,eACCiC,EAAoBf,EAAehI,MAAK,eAgBhD,OAfiB,EAAAgJ,SAASC,cACjBC,cAAc,CACrBpE,GAAI,eACJqE,OAAQ,IACRC,OAAQ,IACRC,OAAQ,IACRC,OAAQ,IACRC,OAAQ,CACNC,aAAc,GAEhBC,OAAQ,CACN9C,KAAM,EAAAqC,SAASU,aAAaC,YAC5BC,SAAS,IAAAxJ,SAAQ2I,EAAgB,SAAU,QAGxC,IAAAhC,KACT,IACA,IAAAc,YAAW,SAAC5H,GAAoB,WAAA8G,KAAA,GArBlC,CAuBJ,E,gCAEA,sBAAY,6BAAc,C,IAA1B,WACE,OAAO,SAACR,EAAkCyB,GACxC,OAAAzB,EAAQC,MACN,IAAAC,QAAO,SAACC,GAAW,OAAAA,EAAOC,OAASnF,EAAeoF,UAA/B,IACnB,IAAAE,UAAS,eACCtC,EAAawD,EAAehI,MAAK,QAqBzC,OApBiB,EAAAgJ,SAASC,cACjBY,YAAY,CACnB/E,GAAI,qBACJyE,OAAQ,CACNO,SAAU,KAEZC,OAAQ,WACRC,OAAQxF,EACLiC,OAAO,SAAC5B,GAAqD,OAACA,EAAIoF,aAAepF,EAAIqF,SAAxB,GAC7DC,IACC,SAACtF,GAA0B,OACzBuF,SAAU,WACV5J,KAAMqE,EAAIrE,KACV6J,IAAK,GACLC,SAAU,IACVC,OAAO,IAAAnK,SAAgByE,EAAK,qBAAsB,KAClD2F,OAAO,IAAApK,SAAgByE,EAAK,0CAA2C,IAN9C,MAU1B,IAAAkC,KACT,IACA,IAAAc,YAAW,SAAC5H,GAAoB,WAAA8G,KAAA,GA1BlC,CA4BJ,E,gCA7EwB,GADzB,EAAAxB,YACYkF,E,CAAb,GCPE,EACE,EAAA3E,QAAO,gBAKX,aACE,WACS4E,EACAC,EACAC,GAFA,KAAAF,aAAAA,EACA,KAAAC,iBAAAA,EACA,KAAAC,SAAAA,CACL,CAsCN,OApCE,YAAAzE,aAAA,WACE,OAAO,IAAAA,cACLxI,KAAKkN,mBAET,EAEA,sBAAY,iCAAkB,C,IAA9B,WACE,OAAO,SAACtE,GACN,OAAAA,EAAQC,MACN,IAAAkC,QAAO,EAAgB9B,aACvB,IAAAH,QAAO,SAACC,GAA+C,OAAAA,EAAOuB,UAAY,EAAAhC,cAAcC,IAAjC,IACvD,IAAAY,UAAS,WACP,IAAIJ,EAAQ0C,EAAS,IACrB,OAAQ,EAAApC,MAAM8D,eACZ,KAAK,EAAA3D,UAAUI,SACbb,EAAS,IACT0C,EAAS,WACT,MACF,KAAK,EAAAjC,UAAUK,OACb4B,EAAS,SAWb,OARA,EAAAJ,SAASC,cAAc8B,cAAc,CACnC5B,OAAQ,IAAKC,OAAM,EACnBC,OAAQ,iBACRC,OAAQ,qBACRC,OAAQ,CACNO,SAAUpD,KAGP,CACL/F,IAEJ,GAzBF,CA0BJ,E,gCAzCgB,GADjB,EAAA4E,W,uBAGwBiD,EACII,EACR6B,KAJRO,E,CAAb,GCfQvJ,EAAqB,EAAA4D,eAAc,iBAG3C,2B,8CAOA,C,MAAA,OAPkC,O,EAArB4F,EAEJ,EAAAC,mBAAP,SAA0BpG,GACxB,EAAaqG,SAAW,EAAaA,UAAY,EAAAC,eAAeC,SAASC,WAAW,EAAAC,eAAeN,cACnG,IAAMI,EAAgB,EAAaF,SACnC,OAAOE,EAAWA,EAASH,mBAAmB,EAAAhD,YAAYX,SAAUzC,EAAIuG,EAASG,QAAU1G,CAC7F,EALO,EAAAqG,SAAW,KADK,KADxB,EAAA5F,YACY0F,E,CAAb,CAAkCxJ,GCQ1BC,EAA6C,EAAA2D,eAAc,UAC7D,GADaoG,EAAkC,EAAApG,eAAc,+BAKjC,GAHhC,GAAiB,oBACjB,GAAkB,qBAClB,GAAqB,wBAIvB,eACE,WAAoBU,EAAgB2F,EAA0BC,EAAsBC,GAClF,QAAK,UAACF,IAAM,K,OADM,EAAA3F,OAAAA,EAA0C,EAAA4F,MAAAA,EAAsB,EAAAC,aAAAA,E,CAEpF,CA+BF,OAlC2B,OAKzB,sBAAI,sBAAO,C,IAAX,W,QACE,OAAO,IAAAC,iBAAgB,WAElB,EAAAC,SAASC,oBAAoBpO,KAAKiO,eAClC,EAAAE,SAASE,oBACT,EAAAF,SAASG,sBAA2B,CAEvClD,gBAAgB,IAAAmD,gBAAa,KAC3B,EAAC,IAAoB,SAACC,EAAO,GAAgD,OAAvC,WAAkDA,CAAX,E,GAC5E,CAAC,CAAC,IACL3H,SAAS,IAAA0H,gBAAa,KACpB,EAAC,IAAqB,SAACC,EAAO,GAAyC,OAAhC,WAA2CA,CAAX,EACvE,EAAC,IAAwB,SAACA,EAAO,GAAyC,OAAhC,WAA2CA,CAAX,E,GACzE,MAEP,E,gCASA,sBAAI,0BAAW,C,IAAf,WACE,OAAO,IAAAhG,cAAaxI,KAAKgO,MAAMf,SAASzE,eAAgBxI,KAAKgO,MAAMhB,iBAAiBxE,eAClFxI,KAAKgO,MAAMjB,aAAavE,eAAgBxI,KAAKgO,MAAMxF,gBAAgB,IAAI,EAAAiG,YAAajG,eACpF,IAAI,EAAAkG,kBAAkB1O,KAAKoI,OAAQ,8BAA8BI,gBAAgB,IAAI,EAAAmG,gBAAiBnG,eAC1G,E,gCAjCgB,GADjB,EAAAZ,W,uBAE6BK,EAAe,QAA0BoF,EAA6BC,KADvFsB,E,CAAb,CAA2B7K,GCjBnBC,GAAa,EAAA0D,eAAc,SAUnC,eAkBE,WAAYmH,GACV,QAAK,UAACA,IAAI,K,OACVC,EAAKpB,SAAW,E,CAClB,CACF,OAtB0B,OACjB,EAAAqB,cAAP,SAAqBhB,G,MACnB,OAAO,EAAP,IACG,EAAA5F,QAAQ6G,WAAW/F,YAAa,WAC/B8E,EAAMkB,SAAS,EAAA9G,QAAQtE,kBACvB,EAAAsE,QAAQwC,gBAAgB,EAAAxC,QAAQ+G,iBAClC,EACA,EAAC,EAAA/G,QAAQtE,eAAeoF,YAAa,WACnC,EAAAd,QAAQwC,gBAAgB,EAAAxC,QAAQtE,iBAClC,E,CAEJ,EAWF,EAtBA,CAA0BG,I,UCLpBC,GAAuC,SAAC,GAAD,IAgBrCkL,EAhBwC/D,EAAc,iBACtD,IAA0B,YAAe,GAAM,GAA9CgE,EAAQ,KAAEC,EAAW,KAgB5B,OAdA,YAAgB,WAEVD,GACF,EAAA/D,SAASC,cAAcY,YAAY,CACjC/E,GAAI,wBACJyE,OAAQ,CACNO,SAAU,KAEZmD,OAAQ,oCAGd,EAAG,CAACF,IAEED,EAAeC,EAAW,gBAAkB,cAC3C,2BAASG,UAAU,qDACxB,uBAAKA,UAAU,yCACb,uBAAKA,UAAU,oDACb,uBAAKA,UAAU,4CACb,qBAAGpI,GAAG,wBAAwBqI,KAAK,qBAAqBC,QAAS,WAAM,OAAAJ,GAAaD,EAAb,EAAsB,gBAAgB,kBAC3GG,UAAU,2HAA0H,gBACrHH,EAAUM,KAAK,UAC9B,wBAAMH,UAAU,0BAAyB,YAAW,SAAQ,cAAa,OAAM,cACjE,QAAO,gBAAC,GAAAI,iBAAgB,CAACxI,GAAIiI,EAAW,WAAa,YACnE,wBAAMG,UAAW,UAAGJ,EAAY,wCAAsC,cACxD,QACZ,wBAAMI,UAAW,4BAAqBJ,KACtC,wBAAMI,UAAW,4BAAqBJ,MAExC,uBAAKI,UAAU,0BACb,wBAAMA,UAAU,8BAA6B,gBAAC,GAAAI,iBAAgB,CAACxI,GAAG,sCAClE,wBAAMoI,UAAU,0CAA0CK,MAAO,CAAEC,QAAST,EAAW,YAAS1P,IAAa,gBAAC,GAAAiQ,iBAAgB,CAACxI,GAAG,+BAIxI,uBAAKA,GAAG,kBACNoI,UAAU,qGACVK,MAAO,CAAEC,QAAST,EAAW,QAAU,SACvC,uBAAKG,UAAU,mBAEXnE,EAAeoB,IAAI,SAAC,G,IAClBsD,EAAI,OACJC,EAAY,eACZC,EAAgB,mBAEhB,8BAAKT,UAAU,YACb,uBAAKA,UAAU,WAAU,cAAa,SACtC,uBAAKA,UAAU,mBACb,uBAAKA,UAAU,YAAYO,GAC3B,uBAAKF,MAAO,CAAEK,WAAY,WAAY,gBAAC,EAAAC,WAAWC,aAAY,CAAC9N,OAAO,IAAAI,SAAQsN,EAAc,QAAS,KAAM,gBAAC,GAAAJ,iBAAgB,CAACxI,GAAG,aAElI,gBAAC,EAAA+I,WAAW/L,QAAO,CAACiM,OAAQJ,GAC1B,uBAAKT,UAAU,UAAS,cAAa,SACrC,gBAAC,EAAAW,WAAW/L,QAAO,CAACiM,MAAM,IAAA3N,SAAQuN,EAAkB,eAAe,IACjE,uBAAKT,UAAU,WACb,uBAAKA,UAAU,aAAY,IAAA9M,SAAQuN,EAAkB,cAAe,KACpE,2BAAK,gBAAC,EAAAE,WAAWC,aAAY,CAAC9N,OAAO,IAAAI,SAAQuN,EAAkB,yBAA0B,KAAM,gBAAC,GAAAL,iBAAgB,CAACxI,GAAG,cAGxH,gBAAC,EAAA+I,WAAW/L,QAAO,CAACiM,MAAM,IAAA3N,SAAQuN,EAAkB,cAAc,IAChE,2BACE,gBAAC,GAAAK,cAAa,CAAChO,OAAO,IAAAI,SAAQuN,EAAkB,aAAc,IAAKM,OAAO,SAASC,SAAS,OACzF,SAACC,GAAe,uBAAC,GAAAb,iBAAgB,CAACxI,GAAG,mBAAmBsJ,OAAQ,CAAED,WAAU,IAA5D,MAjB3B,OA6BhB,EAEatM,IAAS,IAAAwM,SACpB,SAAC,GAAoC,OAAGtF,eAAvB,kBAAyD,GAArC,EADjB,CAEpBnH,IC/EAE,GAEE,EAAA+L,WAAU,QADZ9L,GACE,EAAA8L,WAAU,SAoBR,GAAuD,SAAC,GAAD,IAC3D/I,EAAE,KACFtE,EAAI,OACJ8N,EAAgB,mBAChBC,EAAS,YAMTtE,GADY,eACF,cAEVuE,EAAY,eACZC,EAAgB,mBAChBC,EAAc,iBACdC,EAAgB,mBAEVC,EAAkB,SAACpP,GACvBA,EAAEqP,kBACFrP,EAAEsP,iBACE7E,QACc5M,IAAdmC,EAAEuP,SAAuC,KAAdvP,EAAEuP,SAAgC,KAAdvP,EAAEuP,SACnDJ,EAAiBD,EAErB,EACM,IAAiC,YAAe,GAAM,GAArDM,EAAc,KAAEC,EAAY,KAC7BC,EAAgB,SAAC1P,QACFnC,IAAdmC,EAAEuP,SAAuC,KAAdvP,EAAEuP,UAAmBvP,EAAEnB,OAAO8Q,UAAUC,SAAS,iBAC/EH,GAAcD,EAElB,EAWA,OAVA,YAAgB,WACdK,EAAE,IAAMvK,GACLf,KAAK,iBACLuL,SAAS,oFACTC,KAAK,WAAY,KACjB1P,OACAyP,SAAS,gBACTE,WAAW,KAChB,GAEO,uBAAK1K,GAAIA,EAAIoI,UAAW,uDAAgDjD,EAAa,WAAa,KACvG,uBAAKiD,UAAU,wHACb,uBAAKA,UAAU,gBACb,wBAAMpI,GAAI,cAAOA,GAAMoI,UAAU,uCAAuCE,QAASwB,GAC/E,yBAAO9J,GAAI,cAAOA,GAAMtE,KAAK,kBAAkBiP,QAASxF,EAAYtD,KAAK,QAAO,kBAAkB,sBAAe7B,GAAI,mBAAoB,2BAAoBA,GAAI,eAAgBmF,EAAYiD,UAAU,iCACvM,wBAAMA,UAAU,+DAGpB,uBAAKA,UAAU,qBACb,uBAAKpI,GAAI,sBAAeA,GAAMoI,UAAU,6DAA6DE,QAASwB,GAC5G,sBAAI1B,UAAU,0DAA0D1M,IAE1E,uBAAK0M,UAAU,6CAA4C,cAAa,SACxE,uBAAKA,UAAU,iBAAgB,cAAa,SAC5C,uBAAKA,UAAU,6CAA4C,cAAa,SACxE,uBAAKA,UAAU,oBAAmB,cAAa,SAC/C,uBAAKA,UAAU,gDAAgDpI,GAAI,2BAAoBA,IACrF,uBAAKoI,UAAU,2BACb,sBAAIpI,GAAI,qBAAcA,GAAMoI,UAAW,gEAAyD8B,EAAiB,WAAa,IAAMU,QAASR,EAAe9B,QAAS8B,EAAeS,wBAAyB,CAAEC,OAAQtB,KACvN,sBAAIpB,UAAU,gCAAgCyC,wBAAyB,CAAEC,OAAQrB,KACjF,uBAAKrB,UAAU,cACb,uBAAKA,UAAU,2CACb,gBAACpL,GAAO,CAACiM,MAAM,IAAA3N,SAAQqO,EAAkB,cAAc,IACrD,wBAAMvB,UAAU,kEACd,gBAAC,GAAAc,cAAa,CAAChO,OAAO,IAAAI,SAAQqO,EAAkB,aAAc,IAAKR,OAAO,SAASC,SAAS,OAExF,SAACC,GAAe,uBAAC,GAAAb,iBAAgB,CAACxI,GAAG,8BAA8BsJ,OAAQ,CAAED,WAAU,IAAvE,KAKxB,gBAACrM,GAAO,CAACiM,MAAM,IAAA3N,SAAQqO,EAAkB,oBAAoB,IAC3D,wBAAMvB,UAAU,kEACd,gBAAC,GAAAI,iBAAgB,CAACxI,GAAG,0BAA0BsJ,OAAQ,CAAEyB,OAAQC,KAAKC,KAAI,IAAA3P,SAAQqO,EAAkB,sBAAuB,IAAKuB,UAAU,IAAA5P,SAAQqO,EAAkB,mBAAoB,QAG5L,gBAAC3M,GAAO,CAACiM,MAAM,IAAA3N,UAASqO,OAAkBpR,GAAW,IACnD,uBAAK6P,UAAU,0BAAyB,cAAa,UAEvD,uBAAKA,UAAU,8DACb,gBAACpL,GAAO,CAACiM,MAAM,IAAA3N,SAAQqO,OAAkBpR,GAAW,IAClD,gBAAC,GAAAiQ,iBAAgB,CAACxI,GAAG,Q,KAEvB,gBAAC/C,GAAQ,CAAC/B,OACwD,KAAhE,IAAAI,SAAQqO,EAAkB,2BAA2B,IACjD,IAAArO,SAAQoO,EAAc,QAAS,IAC/B,IAAApO,SAAQqO,EAAkB,yBAA0B,GACxDwB,SAAS,IACX,gBAACnO,GAAO,CAACiM,MAAM,IAAA3N,SAAQqO,OAAkBpR,GAAW,IAClD,qBAAG6P,UAAU,mDACX,gBAAC,GAAAI,iBAAgB,CAACxI,GAAG,gBAAgBsJ,QAAQ,IAAAhO,SAAQoO,OAAcnR,EAAW,CAAC,QAIrF,gBAACyE,GAAO,CAACiM,MAAM,IAAA3N,SAAQqO,OAAkBpR,GAAW,IAClD,qBAAG6P,UAAU,uDACV,IAAA9M,SAAQqO,EAAkB,eAAgB,gBAAC,GAAAnB,iBAAgB,CAACxI,GAAG,uCAUpF,EAGa9C,IAAU,IAAAqM,SACrB,SAAC,GAAqB,OAAG,CAAH,EACtB,SAAAzB,GAAY,OACV+B,iBAAkB,SAACjI,GAAmC,OAAAkG,EAAS7L,EAAuB2F,GAAhC,EAD5C,EAFS,CAKrB,IC9IWzE,GAAoC,WACzC,QAA0B,YAAe,GAAM,GAA9C8K,EAAQ,KAAEC,EAAW,KAW5B,OAVA,YAAgB,WACdD,GACQ,EAAA/D,SAASC,cAAcY,YAAY,CACjC/E,GAAI,kBACJyE,OAAQ,CACNO,SAAU,KAEZmD,OAAQ,eAEpB,EAAG,CAACF,IACG,uBAAKG,UAAU,+BAA+BpI,GAAG,YACtD,0BAAQA,GAAG,cAAcoI,UAAU,8GAA8GE,QAAS,WAAM,OAAAJ,GAAaD,EAAb,EAAsB,gBAAiBA,GACrM,wBAAMG,UAAW,oBAAaH,EAAW,kBAAoB,iBAAiB,cAAc,S,KAC5F,gBAAC,GAAAO,iBAAgB,CAACxI,GAAG,uBAEvB,uBAAKoI,UAAU,WAAU,cAAa,SACtC,gBAAC,EAAAW,WAAW/L,QAAO,CAACiM,KAAMhB,GACxB,uBAAKG,UAAU,wGACb,0BAAQpI,GAAG,iBAAiB6B,KAAK,SAASyG,QAAS,WAAM,OAAAJ,GAAY,EAAZ,EAAoBE,UAAU,2DAA0D,aAAY,SAC3J,wBAAMA,UAAU,yBAAwB,cAAa,UAEvD,gBAAC,EAAAgD,qBAAoB,CAACpL,GAAG,mBAIjC,ECrBM,GAAuC,SAAC,G,IAC5CN,EAAO,UACH,8BAAK0I,UAAU,uCAAuCG,KAAK,cAC/D,6BAEI,ynEA4DF7I,EAAQiC,OAAO,SAAA5B,GAAO,OAACA,EAAIqF,SAAL,GACnBiG,KACC,SAACnU,EAAGyB,GAAM,OACR,IAAA2C,SAAgBF,EAAsBlE,EAAEoU,iBAAkB,eAAgB,IAC5D,IAAAhQ,SAAgBF,EAAsBzC,EAAE2S,iBAAkB,eAAgB,EAFhF,GAKXjG,IACC,SAAAkG,GAAmB,uBAACrO,GAAO,KAAKqO,GAAb,GAGzB,gBAACpO,GAAM,MA1EH,EA8EOC,IAAU,IAAAmM,SACrB,SAAC,GAA6B,OAAG7J,QAAvB,UAAoB,EADT,CAErB,ICtFArC,GACE,EAAA0L,WAAU,iBAGZ,GAEE,EAAA/H,QAAO,aADT1D,GACE,EAAA0D,QAAO,qBAUX,4B,8CAiBA,QAjBwB,OACtB,YAAAwK,kBAAA,SAAkBC,GAChB5S,KAAK6S,MAAMC,mBAAmBF,EAChC,EAEA,YAAAG,kBAAA,WACE/S,KAAK6S,MAAMpO,qBAAqB,EAAA8F,YAAYX,SAC9C,EAEA,YAAAoJ,OAAA,WACE,OAAO,wBAAM7L,GAAG,eACd,gBAACjD,GAAM,MACP,uBAAKqL,UAAU,WAAU,cAAa,SACtC,gBAAChL,GAAO,MACR,gBAACC,GAAgB,CAAC2C,GAAG,+BAEzB,EACF,EAjBA,CAAwB,aAmBXzC,IAAc,IAAAgM,SACzB,SAAC,GAAsB,OAAK,CAAL,EACvB,SAACzB,GAAa,OACZ6D,mBAAoB,SAACxQ,GAAe,OAAA2M,EAAS,GAAa3M,GAAtB,EACpCmC,qBAAsB,WAAM,OAAAwK,EAASxK,KAAT,EAFhB,EAFW,CAMzB,IC5CAE,GACE,EAAAuL,WAAU,gBAEDtL,GAAM,WAAM,uBAACD,GAAe,KACvC,gBAACD,GAAW,MADW,ECIvBG,GAEE,EAAAsD,QAAO,eADT,GACE,EAAAA,QAAO,gBAGX,eACE,WAAoB4F,EAAsBkF,EAAmDlL,EAAwBc,GACnH,QAAK,YAAE,K,OADW,EAAAkF,MAAAA,EAAsB,EAAAkF,OAAAA,EAAmD,EAAAlL,OAAAA,EAAwB,EAAAc,KAAAA,E,CAErH,CAyCF,OA5C6C,OAW3C,YAAAqK,KAAA,WACElT,KAAK6I,KAAKsK,UAAUrE,GAAKC,cAAc/O,KAAK+N,QAC5C/N,KAAK+N,MAAMkB,SAASpK,GAAe7E,KAAK+H,SACxC/H,KAAK+N,MAAMkB,SAASpK,GAAe7E,KAAKiT,OAAOJ,QAC/C7S,KAAK+N,MAAMkB,SAAS,GAAgB,EAAA3G,cAAcC,MACpD,EAOA,YAAA6K,QAAA,WACEpT,KAAK6I,KAAKwK,cACVrT,KAAK+N,MAAMqF,SACb,EAUA,YAAAJ,OAAA,SAAO7U,GACG,IAAA4P,EAAU/N,KAAI,MACtB7B,EAAK6U,OACH,gBAAC,EAAAM,gBAAe,CAACjR,MAAO,CAAE0F,OAAQ/H,KAAK+H,SACrC,gBAAC,WAAa,CAAOgG,MAAK,GAAI,gBAACnJ,GAAG,QAGxC,EA3CkC,IADnC,IAAA2O,QAAO,CAAEC,UAAW,a,uBAEQ5E,GAAuB,EAAA6E,eAAmD5L,EAAsBiH,MADxG4E,E,CAArB,CAA6C,EAAAC,Y", "sources": ["omf-changepackage-internet:///webpack/universalModuleDefinition?", "omf-changepackage-internet:///webpack/bootstrap?", "omf-changepackage-internet:///./tslib/tslib.es6.mjs?", "omf-changepackage-internet:///../src/utils/Characteristics.ts?", "omf-changepackage-internet:///../src/store/Actions.ts?", "omf-changepackage-internet:///../src/Config.ts?", "omf-changepackage-internet:///../src/Client.ts?", "omf-changepackage-internet:///../src/store/Epics/Catalog.ts?", "omf-changepackage-internet:///../src/store/Epics/UserAccount.ts?", "omf-changepackage-internet:///../src/store/Epics/Omniture.ts?", "omf-changepackage-internet:///../src/store/Epics.ts?", "omf-changepackage-internet:///../src/Localization.ts?", "omf-changepackage-internet:///../src/store/Store.ts?", "omf-changepackage-internet:///../src/Pipe.ts?", "omf-changepackage-internet:///../src/views/header/index.tsx?", "omf-changepackage-internet:///../src/views/catalog/Package.tsx?", "omf-changepackage-internet:///../src/views/catalog/Legal.tsx?", "omf-changepackage-internet:///../src/views/catalog/index.tsx?", "omf-changepackage-internet:///../src/views/index.tsx?", "omf-changepackage-internet:///../src/App.tsx?", "omf-changepackage-internet:///../src/Widget.tsx?", "omf-changepackage-internet:///external umd {\"root\":\"bwtk\",\"commonjs2\":\"bwtk\",\"commonjs\":\"bwtk\",\"amd\":\"bwtk\"}?", "omf-changepackage-internet:///external umd {\"root\":\"rxjs\",\"commonjs2\":\"rxjs\",\"commonjs\":\"rxjs\",\"amd\":\"rxjs\"}?", "omf-changepackage-internet:///external umd {\"root\":\"ReactIntl\",\"commonjs2\":\"react-intl\",\"commonjs\":\"react-intl\",\"amd\":\"react-intl\"}?", "omf-changepackage-internet:///external umd {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"}?", "omf-changepackage-internet:///external umd {\"root\":\"OMFChangepackageComponents\",\"commonjs2\":\"omf-changepackage-components\",\"commonjs\":\"omf-changepackage-components\",\"amd\":\"omf-changepackage-components\"}?", "omf-changepackage-internet:///external umd {\"root\":\"ReduxActions\",\"commonjs2\":\"redux-actions\",\"commonjs\":\"redux-actions\",\"amd\":\"redux-actions\"}?", "omf-changepackage-internet:///external umd {\"root\":\"Redux\",\"commonjs2\":\"redux\",\"commonjs\":\"redux\",\"amd\":\"redux\"}?", "omf-changepackage-internet:///external umd {\"root\":\"ReduxObservable\",\"commonjs2\":\"redux-observable\",\"commonjs\":\"redux-observable\",\"amd\":\"redux-observable\"}?", "omf-changepackage-internet:///external umd {\"root\":\"ReactRedux\",\"commonjs2\":\"react-redux\",\"commonjs\":\"react-redux\",\"amd\":\"react-redux\"}?", "omf-changepackage-internet:///webpack/runtime/define property getters?", "omf-changepackage-internet:///webpack/runtime/hasOwnProperty shorthand?", "omf-changepackage-internet:///webpack/runtime/make namespace object?", "omf-changepackage-internet:///../src/mutators/index.ts?"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"react\"), require(\"react-redux\"), require(\"omf-changepackage-components\"), require(\"bwtk\"), require(\"redux\"), require(\"redux-actions\"), require(\"redux-observable\"), require(\"rxjs\"), require(\"react-intl\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"react\", \"react-redux\", \"omf-changepackage-components\", \"bwtk\", \"redux\", \"redux-actions\", \"redux-observable\", \"rxjs\", \"react-intl\"], factory);\n\telse {\n\t\tvar a = typeof exports === 'object' ? factory(require(\"react\"), require(\"react-redux\"), require(\"omf-changepackage-components\"), require(\"bwtk\"), require(\"redux\"), require(\"redux-actions\"), require(\"redux-observable\"), require(\"rxjs\"), require(\"react-intl\")) : factory(root[\"React\"], root[\"ReactRedux\"], root[\"OMFChangepackageComponents\"], root[\"bwtk\"], root[\"Redux\"], root[\"ReduxActions\"], root[\"ReduxObservable\"], root[\"rxjs\"], root[\"ReactIntl\"]);\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function(__WEBPACK_EXTERNAL_MODULE__442__, __WEBPACK_EXTERNAL_MODULE__999__, __WEBPACK_EXTERNAL_MODULE__446__, __WEBPACK_EXTERNAL_MODULE__102__, __WEBPACK_EXTERNAL_MODULE__750__, __WEBPACK_EXTERNAL_MODULE__541__, __WEBPACK_EXTERNAL_MODULE__769__, __WEBPACK_EXTERNAL_MODULE__418__, __WEBPACK_EXTERNAL_MODULE__419__) {\nreturn ", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "import { Volt, ValueOf } from \"omf-changepackage-components\";\r\n\r\nexport function toCharacteristicsJSON(charactgerstics: Array<Volt.ICharacteristic>): { [key: string]: any } {\r\n  return ValueOf<Array<Volt.ICharacteristic>>(charactgerstics, undefined, []).reduce(\r\n    (json, charactgerstic) => {\r\n      if (charactgerstic.name) {\r\n        json[charactgerstic.name] =  charactgerstic.value;\r\n      }\r\n      return json;\r\n    }, {} as any\r\n  );\r\n}\r\n", "import { createAction, Action } from \"redux-actions\";\r\nimport { Volt } from \"omf-changepackage-components\";\r\nimport { serviceAccountMutatorFn, catalogMutatorFn, orderMutatorFn } from \"../mutators\";\r\nimport { IPackage, IAccountDetails, IServiceAccountAPI } from \"../models\";\r\n\r\n// Widget actions\r\nexport const getAccountDetails = createAction(\"GET_ACCOUNT_DETAILS\");\r\nexport const setAccountDetails = createAction<Array<IAccountDetails>>(\"SET_ACCOUNT_DETAILS\", serviceAccountMutatorFn as any) as (response: IServiceAccountAPI) => Action<Array<IAccountDetails>>;\r\nexport const getInternetCatalog = createAction(\"GET_INTERNET_CATALOG\");\r\nexport const setInternetCatalog = createAction<Array<IPackage>>(\"SET_INTERNET_CATALOG\", catalogMutatorFn as any) as (response: Volt.IAPIResponse) => Action<Array<IPackage>>;\r\n\r\nexport const togglePackageSelection = createAction<Volt.IHypermediaAction>(\"TOGGLE_INTERNET_PACKAGE\");\r\nexport const updateInternetCatalog = createAction<Array<IPackage>>(\"UPDATE_INTERNET_CATALOG\", orderMutatorFn as any) as (response: Volt.IAPIResponse, catalog: Array<IPackage>) => Action<Array<IPackage>>;\r\n\r\n// Piped actions\r\n\r\n", "import { Injectable, CommonFeatures } from \"bwtk\";\r\nimport { Models } from \"omf-changepackage-components\";\r\n\r\nconst { BaseConfig, configProperty } = CommonFeatures;\r\n\r\ninterface IAppConfig extends Models.IBaseConfig {\r\n}\r\n\r\ninterface IAppAPI extends Models.IBaseWidgetAPI {\r\n  catalogAPI: string;\r\n  bundleCatalogAPI: string;\r\n  serviceAccountAPI: string;\r\n}\r\n\r\n/**\r\n * Widget configuration provider\r\n * Allows the external immutable\r\n * config setting\r\n * @export\r\n * @class Config\r\n * @extends {BaseConfig<IAppConfig>}\r\n */\r\n@Injectable\r\nexport class Config extends BaseConfig<IAppConfig> {\r\n  @configProperty(\"\") flowType: string;\r\n  @configProperty({}) environmentVariables: any;\r\n  @configProperty({}) mockdata: any;\r\n  @configProperty({}) headers: any;\r\n  @configProperty({base: \"http://127.0.0.1:8881\"}) api: IAppAPI;\r\n}\r\n", "import { Injectable, AjaxServices } from \"bwtk\";\r\nimport { BaseClient } from \"omf-changepackage-components\";\r\n\r\nimport { Config } from \"./Config\";\r\n\r\n/**\r\n * Base client implementation\r\n * for AJAX calls\r\n * @export\r\n * @class Client\r\n * @extends {BaseClient}\r\n */\r\n@Injectable\r\nexport class Client extends BaseClient {\r\n  constructor(ajaxClient: AjaxServices, config: Config) {\r\n    super(ajaxClient, config);\r\n  }\r\n}\r\n", "import { Injectable } from \"bwtk\";\r\nimport { Epic, combineEpics } from \"redux-observable\";\r\nimport { EWidgetStatus, Actions, Models, AjaxResponse, Volt, FilterRestrictionObservable, EWidgetName, Utils, EFlowType, ValueOf } from \"omf-changepackage-components\";\r\nimport { filter, mergeMap, catchError , concat, of, Observable } from 'rxjs';\r\n\r\nimport { Client } from \"../../Client\";\r\nimport {\r\n  IStoreState\r\n} from \"../../models\";\r\nimport {\r\n  getInternetCatalog,\r\n  setInternetCatalog,\r\n  togglePackageSelection,\r\n  updateInternetCatalog\r\n} from \"../Actions\";\r\nimport { Config } from \"../../Config\";\r\n\r\nconst {\r\n  errorOccured,\r\n  setWidgetStatus,\r\n  clearCachedState,\r\n  finalizeRestriction\r\n} = Actions;\r\n\r\n@Injectable\r\nexport class CatalogEpics {\r\n  widgetState: EWidgetStatus = EWidgetStatus.INIT;\r\n\r\n  constructor(private client: Client, private config: Config) { }\r\n\r\n  combineEpics() {\r\n    return combineEpics(\r\n      this.requestCatalogEpic,\r\n      this.togglePlanSelectionEpic,\r\n      this.finalizeRestrictionEpic\r\n    );\r\n  }\r\n\r\n  private get requestCatalogEpic(): CatalogEpic {\r\n    return (action$: Observable<InputAction>) =>\r\n      action$.pipe(\r\n        filter((action): action is InputAction => action.type === getInternetCatalog.toString()),\r\n        filter(() => this.widgetState !== EWidgetStatus.UPDATING),\r\n        mergeMap(() => concat(\r\n          of(setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING)),\r\n          this.client.get<AjaxResponse<Volt.IAPIResponse>>(Utils.appendRefreshOnce(\r\n            Utils.getURLByFlowType({\r\n              [EFlowType.TV]: this.config.api.catalogAPI,\r\n              [EFlowType.INTERNET]: this.config.api.catalogAPI,\r\n              [EFlowType.BUNDLE]: this.config.api.bundleCatalogAPI\r\n            })\r\n          )).pipe(\r\n            mergeMap((response: AjaxResponse<Volt.IAPIResponse>) => \r\n              FilterRestrictionObservable(response, [\r\n                setInternetCatalog(response.data),\r\n                Actions.omniPageLoaded(),\r\n                setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED)\r\n              ])\r\n            )\r\n          )\r\n        )),\r\n        catchError((error: Response) => of(\r\n          errorOccured(new Models.ErrorHandler(\"getInternetCatalog\", error))\r\n        ))\r\n      ) as Observable<OutputAction>;\r\n  }\r\n\r\n  private get togglePlanSelectionEpic(): CatalogEpic {\r\n    return (action$: Observable<InputAction>, state$) =>\r\n      action$.pipe(\r\n        filter((action): action is ReduxActions.Action<Volt.IHypermediaAction> => \r\n          action.type === togglePackageSelection.toString()\r\n        ),\r\n        filter(() => this.widgetState !== EWidgetStatus.UPDATING),\r\n        mergeMap(({ payload }) =>\r\n          concat(\r\n            of(setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING)),\r\n            this.client.action<AjaxResponse<Volt.IAPIResponse>>(payload).pipe(\r\n              mergeMap((response: AjaxResponse<Volt.IAPIResponse>) => \r\n                FilterRestrictionObservable(response, [\r\n                  updateInternetCatalog(response.data, (state$ as any).value.catalog),\r\n                  clearCachedState([EWidgetName.PREVIEW]),\r\n                  setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED)\r\n                ])\r\n              )\r\n            )\r\n          )\r\n        ),\r\n        catchError((error: Response) => of(errorOccured(new Models.ErrorHandler(\"togglePackageSelection\", error))))\r\n      ) as Observable<OutputAction>;\r\n  }\r\n\r\n  private get finalizeRestrictionEpic(): CatalogEpic {\r\n    return (action$: Observable<InputAction>, state$) =>\r\n      action$.pipe(\r\n        filter((action): action is ReduxActions.Action<Volt.IAPIResponse> => \r\n          action.type === finalizeRestriction.toString()\r\n        ),\r\n        filter(({ payload }) => \r\n          Boolean(payload) && \r\n          Boolean(payload.productOfferingDetail) && \r\n          this.widgetState !== EWidgetStatus.UPDATING\r\n        ),\r\n        mergeMap(({ payload }) => of(\r\n          Actions.broadcastUpdate(Actions.setProductConfigurationTotal(ValueOf(payload, \"productOfferingDetail.productConfigurationTotal\"))),\r\n          updateInternetCatalog(payload, (state$ as any).value.catalog),\r\n          clearCachedState([EWidgetName.PREVIEW]),\r\n          setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED)\r\n        ))\r\n      ) as Observable<OutputAction>;\r\n  }\r\n}\r\n\r\ntype InputAction = ReduxActions.Action<any>;\r\ntype OutputAction = ReduxActions.Action<any>;\r\ntype CatalogEpic = Epic<InputAction, OutputAction, void, IStoreState>;\r\n", "import { Injectable } from \"bwtk\";\r\nimport { Epic, combineEpics, ofType } from \"redux-observable\";\r\nimport { EWidgetStatus, AjaxResponse } from \"omf-changepackage-components\";\r\nimport { filter, mergeMap, catchError , of } from 'rxjs';\r\n\r\nimport { Client } from \"../../Client\";\r\nimport {\r\n  IStoreState, IServiceAccountAPI,\r\n} from \"../../models\";\r\nimport {\r\n  getAccountDetails,\r\n  setAccountDetails,\r\n  getInternetCatalog,\r\n} from \"../Actions\";\r\nimport { Config } from \"../../Config\";\r\nimport { Action } from \"redux-actions\";\r\n\r\n@Injectable\r\nexport class UserAccountEpics {\r\n  widgetState: EWidgetStatus = EWidgetStatus.INIT;\r\n\r\n  constructor(private client: Client, private config: Config) { }\r\n\r\n  combineEpics() {\r\n    return combineEpics(\r\n      this.requestDataEpic,\r\n    );\r\n  }\r\n\r\n  private get requestDataEpic(): Epic<Action<any>, Action<any>, any, IStoreState> {\r\n    return (action$, state$) =>\r\n      action$.pipe(\r\n        ofType(getAccountDetails.toString()),\r\n        filter(() => this.widgetState !== EWidgetStatus.UPDATING),\r\n        mergeMap(() => this.client.get<AjaxResponse<IServiceAccountAPI>>(this.config.api.serviceAccountAPI).pipe(\r\n          mergeMap(({ data }: { data: IServiceAccountAPI }) => of(\r\n            setAccountDetails(data),\r\n            getInternetCatalog()\r\n          )),\r\n          catchError((error: Response) => of(setAccountDetails({} as IServiceAccountAPI)))\r\n        ))\r\n      );\r\n  }\r\n}\r\n", "import { Injectable } from \"bwtk\";\r\nimport { Epic, combineEpics, ofType } from \"redux-observable\";\r\nimport { EWidgetStatus, Omniture, Actions, ValueOf } from \"omf-changepackage-components\";\r\nimport { filter, mergeMap, catchError , of, Observable } from 'rxjs';\r\n\r\nimport {\r\n  IStoreState\r\n} from \"../../models\";\r\n\r\nconst {\r\n  omniPageLoaded,\r\n  omniPageSubmit\r\n} = Actions;\r\n\r\ntype InputAction = ReduxActions.Action<any>;\r\ntype OutputAction = ReduxActions.Action<any>;\r\ntype OmnitureEpic = Epic<InputAction, OutputAction, void, IStoreState>;\r\n\r\n@Injectable\r\nexport class OmnitureEpics {\r\n  widgetState: EWidgetStatus = EWidgetStatus.INIT;\r\n\r\n  combineEpics() {\r\n    return combineEpics(\r\n      this.pageLoadedEpic,\r\n      this.pageSubmitEpic\r\n    );\r\n  }\r\n\r\n  /**\r\n     * The only Omniture dependecy for pageload\r\n     * on Internet changepackage page is account details\r\n     * so we wait for those to come from API and then\r\n     * fire the beakon\r\n     * @readonly\r\n     * @private\r\n     * @type {UserAccountEpic}\r\n     * @memberof OmnitureEpics\r\n     */\r\n  private get pageLoadedEpic(): OmnitureEpic {\r\n    return (action$: Observable<InputAction>, state$) =>\r\n      action$.pipe(\r\n        ofType(omniPageLoaded.toString()),\r\n        mergeMap(() => {\r\n          const { accountDetails } = (state$ as any).value;\r\n          const omniture = Omniture.useOmniture();\r\n          omniture.trackFragment({\r\n            id: \"InternetPage\",\r\n            s_oSS1: \"~\",\r\n            s_oSS2: \"~\",\r\n            s_oSS3: \"~\",\r\n            s_oPGN: \"~\",\r\n            s_oAPT: {\r\n              actionresult: 1\r\n            },\r\n            s_oPLE: {\r\n              type: Omniture.EMessageType.Information,\r\n              content: ValueOf(accountDetails, \"0.Name\", \"\")\r\n            }\r\n          });\r\n          return of();\r\n        }),\r\n        catchError((error: Response) => of())\r\n      ) as Observable<OutputAction>;\r\n  }\r\n\r\n  private get pageSubmitEpic(): OmnitureEpic {\r\n    return (action$: Observable<InputAction>, state$) =>\r\n      action$.pipe(\r\n        filter((action) => action.type === omniPageSubmit.toString()),\r\n        mergeMap(() => {\r\n          const { catalog } = (state$ as any).value;\r\n          const omniture = Omniture.useOmniture();\r\n          omniture.trackAction({\r\n            id: \"internetPageSubmit\",\r\n            s_oAPT: {\r\n              actionId: 647\r\n            },\r\n            s_oBTN: \"Continue\",\r\n            s_oPRD: catalog\r\n              .filter((pkg: { isSelected: boolean; isCurrent: boolean }) => (pkg.isSelected && !pkg.isCurrent))\r\n              .map(\r\n                (pkg: { name: string }) => ({\r\n                  category: \"Internet\",\r\n                  name: pkg.name,\r\n                  sku: \"\",\r\n                  quantity: \"1\",\r\n                  price: ValueOf<string>(pkg, \"regularPrice.price\", \"0\"),\r\n                  promo: ValueOf<string>(pkg, \"promotionDetails.promotionalPrice.price\", \"\")\r\n                })\r\n              )\r\n          });\r\n          return of();\r\n        }),\r\n        catchError((error: Response) => of())\r\n      ) as Observable<OutputAction>;\r\n  }\r\n}\r\n", "import { Injectable } from \"bwtk\";\r\nimport { Epic, combineEpics, ofType } from \"redux-observable\";\r\nimport { filter, mergeMap } from \"rxjs\";\r\nimport { EWidgetStatus, Actions, Omniture, Utils, EFlowType } from \"omf-changepackage-components\";\r\n\r\nimport { IStoreState } from \"../models\";\r\nimport { getAccountDetails } from \"./Actions\";\r\nimport { CatalogEpics } from \"./Epics/Catalog\";\r\nimport { UserAccountEpics } from \"./Epics/UserAccount\";\r\nimport { OmnitureEpics } from \"./Epics/Omniture\";\r\n\r\nconst {\r\n  setWidgetStatus\r\n} = Actions;\r\n\r\n// const { concat } = ActionsObservable;\r\n\r\n@Injectable\r\nexport class Epics {\r\n  constructor(\r\n    public catalogEpics: CatalogEpics,\r\n    public userAccountEpics: UserAccountEpics,\r\n    public omniture: OmnitureEpics\r\n  ) { }\r\n\r\n  combineEpics() {\r\n    return combineEpics(\r\n      this.onWidgetStatusEpic,\r\n    );\r\n  }\r\n\r\n  private get onWidgetStatusEpic(): GeneralEpic {\r\n    return (action$) =>\r\n      action$.pipe(\r\n        ofType(setWidgetStatus.toString()),\r\n        filter((action: ReduxActions.Action<EWidgetStatus>) => action.payload === EWidgetStatus.INIT),\r\n        mergeMap(() => {\r\n          let action, s_oSS2 = \"~\";\r\n          switch (Utils.getFlowType()) {\r\n            case EFlowType.INTERNET:\r\n              action = 523;\r\n              s_oSS2 = \"Internet\";\r\n              break;\r\n            case EFlowType.BUNDLE:\r\n              s_oSS2 = \"Bundle\";\r\n              break;\r\n          }\r\n          Omniture.useOmniture().updateContext({\r\n            s_oSS1: \"~\", s_oSS2,\r\n            s_oSS3: \"Change package\",\r\n            s_oPGN: \"Setup your service\",\r\n            s_oAPT: {\r\n              actionId: action\r\n            }\r\n          });\r\n          return [\r\n            getAccountDetails()\r\n          ];\r\n        }));\r\n  }\r\n\r\n}\r\n\r\ntype GeneralEpic = Epic<ReduxActions.Action<any>, ReduxActions.Action<any>, void, IStoreState>;\r\n", "import { Injectable, CommonFeatures, ServiceLocator, CommonServices } from \"bwtk\";\r\nimport { EWidgetName } from \"omf-changepackage-components\";\r\n\r\nconst { BaseLocalization } = CommonFeatures;\r\n\r\n@Injectable\r\nexport class Localization extends BaseLocalization {\r\n  static Instance = null;\r\n  static getLocalizedString(id: string): string {\r\n    Localization.Instance = Localization.Instance || ServiceLocator.instance.getService(CommonServices.Localization);\r\n    const instance: any = Localization.Instance;\r\n    return instance ? instance.getLocalizedString(EWidgetName.INTERNET, id, instance.locale) : id;\r\n  }\r\n}\r\n", "import { combineReducers } from \"redux\";\r\nimport { Action, handleActions } from \"redux-actions\";\r\nimport { combineEpics } from \"redux-observable\";\r\nimport { Reducers, LifecycleEpics, RestricitonsEpics, ModalEpics } from \"omf-changepackage-components\";\r\n\r\nimport { Store as BwtkStore, Injectable, CommonFeatures } from \"bwtk\";\r\n\r\nimport * as actions from \"./Actions\";\r\n\r\nimport { IStoreState, IAccountDetails, IPackage } from \"../models\";\r\nimport { Epics } from \"./Epics\";\r\nimport { Localization } from \"../Localization\";\r\nimport { Client } from \"../Client\";\r\n\r\nconst { BaseStore, actionsToComputedPropertyName } = CommonFeatures;\r\nconst {\r\n  setAccountDetails,\r\n  setInternetCatalog,\r\n  updateInternetCatalog\r\n} = actionsToComputedPropertyName(actions);\r\n\r\n@Injectable\r\nexport class Store extends BaseStore<IStoreState> {\r\n  constructor(private client: Client, store: BwtkStore, private epics: Epics, private localization: Localization) {\r\n    super(store);\r\n  }\r\n\r\n  get reducer() {\r\n    return combineReducers({\r\n      // =========== Widget lifecycle methods =============\r\n      ...Reducers.WidgetBaseLifecycle(this.localization) as any,\r\n      ...Reducers.WidgetLightboxes() as any,\r\n      ...Reducers.WidgetRestrictions() as any,\r\n      // =========== Widget data ===============\r\n      accountDetails: handleActions<Array<IAccountDetails>>({\r\n        [setAccountDetails]: (state, { payload }: Action<Array<IAccountDetails>>) => payload || state,\r\n      }, [{}] as Array<IAccountDetails>),\r\n      catalog: handleActions<Array<IPackage>>({\r\n        [setInternetCatalog]: (state, { payload }: Action<Array<IPackage>>) => payload || state,\r\n        [updateInternetCatalog]: (state, { payload }: Action<Array<IPackage>>) => payload || state,\r\n      }, []),\r\n    }) as any;\r\n  }\r\n\r\n  /**\r\n   * Middlewares are collected bottom-to-top\r\n   * so, the bottom-most epic will receive the\r\n   * action first, while the top-most -- last\r\n   * @readonly\r\n   * @memberof Store\r\n   */\r\n  get middlewares(): any {\r\n    return combineEpics(this.epics.omniture.combineEpics(), this.epics.userAccountEpics.combineEpics(),\r\n      this.epics.catalogEpics.combineEpics(), this.epics.combineEpics(), new ModalEpics().combineEpics(),\r\n      new RestricitonsEpics(this.client, \"INTERNET_RESTRICTION_MODAL\").combineEpics(), new LifecycleEpics().combineEpics());\r\n  }\r\n}\r\n", "import { CommonFeatures } from \"bwtk\";\r\n// import { Action } from \"redux-actions\";\r\nimport { Actions } from \"omf-changepackage-components\";\r\nimport { Store } from \"./store\";\r\n\r\nconst { BasePipe } = CommonFeatures;\r\n\r\n/**\r\n * rxjs pipe provider\r\n * this fascilitates the direct connection\r\n * between widgets through rxjs Observable\r\n * @export\r\n * @class Pipe\r\n * @extends {BasePipe}\r\n */\r\nexport class Pipe extends BasePipe {\r\n  static Subscriptions(store: Store) {\r\n    return {\r\n      [Actions.onContinue.toString()]: () => {\r\n        store.dispatch(Actions.omniPageSubmit());\r\n        Actions.broadcastUpdate(Actions.historyForward());\r\n      },\r\n      [Actions.omniPageSubmit.toString()]: () => {\r\n        Actions.broadcastUpdate(Actions.omniPageSubmit());\r\n      }\r\n    };\r\n  }\r\n  /**\r\n     *Creates a static instance of Pipe.\r\n     * @param {*} arg\r\n     * @memberof Pipe\r\n     */\r\n  static instance: Pipe;\r\n  constructor(arg: any) {\r\n    super(arg);\r\n    Pipe.instance = this;\r\n  }\r\n}\r\n", "import * as React from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport { FormattedMessage, FormattedDate } from \"react-intl\";\r\nimport { ValueOf, Components, Omniture } from \"omf-changepackage-components\";\r\nimport { IStoreState, IAccountDetails } from \"../../models\";\r\n\r\ninterface IComponentProps {\r\n  accountDetails: Array<IAccountDetails>;\r\n}\r\n\r\nconst Component: React.FC<IComponentProps> = ({ accountDetails }) => {\r\n  const [expanded, toggleState] = React.useState(false);\r\n  // Omniture tracking for expand Interent package\r\n  React.useEffect(() => {\r\n    // we only care about when the menu expands\r\n    if (expanded) {\r\n      Omniture.useOmniture().trackAction({\r\n        id: \"myCurrentPackageClick\",\r\n        s_oAPT: {\r\n          actionId: 648\r\n        },\r\n        s_oEPN: \"My current Home Internet package\"\r\n      });\r\n    }\r\n  }, [expanded]);\r\n  // ---\r\n  const collapseIcon = expanded ? \"icon-Collapse\" : \"icon-Expand\";\r\n  return <section className=\"bgVirginGradiant accss-focus-outline-override-pad\">\r\n    <div className=\"container liquid-container sans-serif\">\r\n      <div className=\"accordion-group internet-current-package flexCol\">\r\n        <div className=\"accordion-heading col-xs-12 noPaddingImp\">\r\n          <a id=\"accordion_expand_link\" href=\"javascript:void(0)\" onClick={() => toggleState(!expanded)} aria-controls=\"div1-accessible\"\r\n            className=\"accordion-accessible-toggle txtSize18 txtDecorationNoneHover txtWhite flexRow align-items-center accss-width-fit-content\"\r\n            aria-expanded={expanded} role=\"button\">\r\n            <span className=\"sr-only accordion-label\" aria-live=\"polite\" aria-atomic=\"true\"\r\n              aria-hidden=\"true\"><FormattedMessage id={expanded ? \"Collapse\" : \"Expand\"} /></span>\r\n            <span className={`${collapseIcon} virgin-icon txtSize24 virginRedIcon`}\r\n              aria-hidden=\"true\">\r\n              <span className={`virgin-icon path1 ${collapseIcon}`} />\r\n              <span className={`virgin-icon path2 ${collapseIcon}`} />\r\n            </span>\r\n            <div className=\"margin-15-left flexCol\">\r\n              <span className=\"txtWhite txtBold txtSize18\"><FormattedMessage id=\"My current Home Internet package\" /></span>\r\n              <span className=\"expand txtWhite txtSize12 no-margin-top\" style={{ display: expanded ? \"none\" : undefined }}><FormattedMessage id=\"Expand to view details\" /></span>\r\n            </div>\r\n          </a>\r\n        </div>\r\n        <div id=\"div1-accessible\"\r\n          className=\"collapse-accordion-accessible-toggle accordion-body txtWhite col-xs-12 margin-5-top margin-40-left\"\r\n          style={{ display: expanded ? \"block\" : \"none\" }}>\r\n          <div className=\"accordion-inner\">\r\n            {\r\n              accountDetails.map(({\r\n                Name,\r\n                RegularPrice,\r\n                PromotionDetails\r\n              }) =>\r\n                <div className=\"col-sm-5\">\r\n                  <div className=\"spacer10\" aria-hidden=\"true\" />\r\n                  <div className=\"flexRow flexEnd\">\r\n                    <div className=\"flexGrow\">{Name}</div>\r\n                    <div style={{ whiteSpace: \"nowrap\" }}><Components.BellCurrency value={ValueOf(RegularPrice, \"Price\", 0)} /><FormattedMessage id=\"PER_MO\" /></div>\r\n                  </div>\r\n                  <Components.Visible when={!!PromotionDetails}>\r\n                    <div className=\"spacer5\" aria-hidden=\"true\" />\r\n                    <Components.Visible when={ValueOf(PromotionDetails, \"Description\", false)}>\r\n                      <div className=\"flexRow\">\r\n                        <div className=\"flexGrow\">{ValueOf(PromotionDetails, \"Description\", \"\")}</div>\r\n                        <div><Components.BellCurrency value={ValueOf(PromotionDetails, \"PromotionalPrice.Price\", 0)} /><FormattedMessage id=\"PER_MO\" /></div>\r\n                      </div>\r\n                    </Components.Visible>\r\n                    <Components.Visible when={ValueOf(PromotionDetails, \"ExpiryDate\", false)}>\r\n                      <div>\r\n                        <FormattedDate value={ValueOf(PromotionDetails, \"ExpiryDate\", \"\")} format=\"yMMMMd\" timeZone=\"UTC\">\r\n                          {(expiryDate) => <FormattedMessage id=\"PromotionExpires\" values={{ expiryDate }} />}\r\n                        </FormattedDate>\r\n                      </div>\r\n                    </Components.Visible>\r\n                  </Components.Visible>\r\n                </div>)\r\n            }\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>;\r\n};\r\n\r\nexport const Header = connect<IComponentProps>(\r\n  ({ accountDetails }: IStoreState) => ({ accountDetails: accountDetails || [] })\r\n)(Component);\r\n", "import { Components, ValueOf, Volt } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedDate, FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { IPackage, IStoreState } from \"../../models\";\r\nimport { togglePackageSelection } from \"../../store\";\r\n// import { Localization } from \"../../Localization\";\r\n\r\ndeclare function $(q: any): any;\r\n\r\nconst {\r\n  Visible,\r\n  Currency\r\n} = Components;\r\n\r\n// interface IParseCharacteristics {\r\n//     download: Volt.ICharacteristic | undefined;\r\n//     upload: Volt.ICharacteristic | undefined;\r\n//     usage: Volt.ICharacteristic | undefined;\r\n// }\r\n\r\n// function ParseCharacteristics(characteristics: Array<Volt.ICharacteristic>): IParseCharacteristics {\r\n//     return {\r\n//         download: characteristics.find(chr => chr.characteristicName === \"Download\"),\r\n//         upload: characteristics.find(chr => chr.characteristicName === \"Upload\"),\r\n//         usage: characteristics.find(chr => chr.characteristicName === \"Usage\"),\r\n//     };\r\n// }\r\n\r\ninterface IComponentDispatches {\r\n  onPackageClicked: (action: Volt.IHypermediaAction) => void;\r\n}\r\n\r\nconst Component: React.FC<IPackage & IComponentDispatches> = ({\r\n  id,\r\n  name,\r\n  shortDescription,\r\n  usagePlan,\r\n  // state,\r\n  // type,\r\n  // isSelectable,\r\n  // isCurrent,\r\n  isSelectable,\r\n  isSelected,\r\n  // characteristics,\r\n  regularPrice,\r\n  promotionDetails,\r\n  offeringAction,\r\n  onPackageClicked\r\n}) => {\r\n  const onPackageAction = (e: any) => {\r\n    e.stopPropagation();\r\n    e.preventDefault();\r\n    if (isSelected) return;\r\n    if (e.keyCode === undefined || e.keyCode === 32 || e.keyCode === 13) {\r\n      onPackageClicked(offeringAction);\r\n    }\r\n  };\r\n  const [uploadExpanded, ExpandUpload] = React.useState(false);\r\n  const onUploadClick = (e: any) => {\r\n    if ((e.keyCode === undefined || e.keyCode === 13) && e.target.classList.contains(\"txtUnderline\")) {\r\n      ExpandUpload(!uploadExpanded);\r\n    }\r\n  };\r\n  React.useEffect(() => {\r\n    $(\"#\" + id)\r\n      .find(\"[data-toggle]\")\r\n      .addClass(\"txtUnderline txtBlue pointer accss-text-blue-on-bg-white accss-width-fit-content\")\r\n      .attr(\"tabindex\", \"0\")\r\n      .next()\r\n      .addClass(\"downloadTray\")\r\n      .removeAttr(\"id\");\r\n  });\r\n  // const parsedCharacteristics = ParseCharacteristics(ValueOf(characteristics, undefined, []));\r\n  return <div id={id} className={`virgin-internet-box txtGray margin-15-bottom ${isSelected ? \"selected\" : \"\"}`}>\r\n    <div className=\"flexRow bgWhite border-radius-3 virgin-title-block pad-30 pad-15-left-right-sm accss-focus-outline-override-white-bg\">\r\n      <div className=\"package_ctrl\">\r\n        <span id={`CTA_${id}`} className=\"graphical_ctrl ctrl_radioBtn pointer\" onClick={onPackageAction}>\r\n          <input id={`OPT_${id}`} name=\"internetpackage\" checked={isSelected} type=\"radio\" aria-labelledby={`PACKAGE_CTA_${id}`} aria-describedby={`PACKAGE_CTA_DESC_${id}`} aria-checked={isSelected} className=\"radioBtn-active data-feature\" />\r\n          <span className=\"ctrl_element pointer data-addon-active data-addon-border\" />\r\n        </span>\r\n      </div>\r\n      <div className=\"package-desc fill\">\r\n        <div id={`PACKAGE_CTA_${id}`} className=\"fill pad-15-left content-width valign-top pad-0-xs pointer\" onClick={onPackageAction}>\r\n          <h2 className=\"virginUltraReg txtSize16 floatL txtUppercase no-margin\">{name}</h2>\r\n        </div>\r\n        <div className=\"spacer10 d-none d-sm-block d-md-none clear\" aria-hidden=\"true\" />\r\n        <div className=\"spacer15 clear\" aria-hidden=\"true\" />\r\n        <div className=\"spacer1 bgGrayLight6 clear margin-30-right\" aria-hidden=\"true\" />\r\n        <div className=\"spacer15 hidden-m\" aria-hidden=\"true\" />\r\n        <div className=\"pkg-pull-left neg-margin-left-40-sm flexBlock\" id={`PACKAGE_CTA_DESC_${id}`}>\r\n          <div className=\"flexRow fill flexCol-xs\">\r\n            <ul id={`UPLOAD_CTA_${id}`} className={`speed-box1 flexRow flexCol-xs mb-0 pl-0 list-unstyled ${uploadExpanded ? \"expanded\" : \"\"}`} onKeyUp={onUploadClick} onClick={onUploadClick} dangerouslySetInnerHTML={{ __html: shortDescription }} />\r\n            <ul className=\"speed-box2 mb-0 list-unstyled\" dangerouslySetInnerHTML={{ __html: usagePlan }} />\r\n            <div className=\"speed-box3\">\r\n              <div className=\"pad-30-left no-pad-xs margin-10-left-xs\">\r\n                <Visible when={ValueOf(promotionDetails, \"expiryDate\", false)}>\r\n                  <span className=\"txtSize12 txtBlack block txtBold bgGray2 pad-5 border-radius-3\">\r\n                    <FormattedDate value={ValueOf(promotionDetails, \"expiryDate\", \"\")} format=\"yMMMMd\" timeZone=\"UTC\">\r\n                      {\r\n                        (expiryDate) => <FormattedMessage id=\"Your monthly credit expires\" values={{ expiryDate }} />\r\n                      }\r\n                    </FormattedDate>\r\n                  </span>\r\n                </Visible>\r\n                <Visible when={ValueOf(promotionDetails, \"discountDuration\", false)}>\r\n                  <span className=\"txtSize12 txtBlack block txtBold bgGray2 pad-5 border-radius-3\">\r\n                    <FormattedMessage id=\"Get a credit for months\" values={{ credit: Math.abs(ValueOf(promotionDetails, \"discountPrice.price\", 0)), duration: ValueOf(promotionDetails, \"discountDuration\", 0) }} />\r\n                  </span>\r\n                </Visible>\r\n                <Visible when={ValueOf(!promotionDetails, undefined, false)}>\r\n                  <div className=\"spacer15 clear hidden-m\" aria-hidden=\"true\" />\r\n                </Visible>\r\n                <div className=\"price virginUltraReg txtSize40 line-height-1 margin-10-top\">\r\n                  <Visible when={ValueOf(promotionDetails, undefined, false)}>\r\n                    <FormattedMessage id=\"Now\" />&nbsp;\r\n                  </Visible>\r\n                  <Currency value={\r\n                    ValueOf(promotionDetails, \"?promotionalPrice.price\", false) === false\r\n                      ? ValueOf(regularPrice, \"price\", 0)\r\n                      : ValueOf(promotionDetails, \"promotionalPrice.price\", 0)\r\n                  } monthly={true} />\r\n                  <Visible when={ValueOf(promotionDetails, undefined, false)}>\r\n                    <p className=\"txtSize12 txtBlack txtBold sans-serif no-margin\">\r\n                      <FormattedMessage id=\"Current Price\" values={ValueOf(regularPrice, undefined, {}) as any} />\r\n                    </p>\r\n                  </Visible>\r\n                </div>\r\n                <Visible when={ValueOf(promotionDetails, undefined, false)}>\r\n                  <p className=\"txtSize12 txtBlack sans-serif no-margin pad-10-top\">\r\n                    {ValueOf(promotionDetails, \"legalMessage\", <FormattedMessage id=\"Prices may increase legal\" />)}\r\n                  </p>\r\n                </Visible>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>;\r\n};\r\n\r\n\r\nexport const Package = connect<{}, IComponentDispatches, IPackage>(\r\n  ({ }: IStoreState) => ({}),\r\n  dispatch => ({\r\n    onPackageClicked: (action: Volt.IHypermediaAction) => dispatch(togglePackageSelection(action))\r\n  })\r\n)(Component);\r\n", "import { Components, FormattedHTMLMessage, Omniture } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\n\r\ninterface IComponentProps { }\r\n\r\nexport const Footer: React.FC<IComponentProps> = () => {\r\n  const [expanded, toggleState] = React.useState(false);\r\n  React.useEffect(() => {\r\n    expanded &&\r\n            Omniture.useOmniture().trackAction({\r\n              id: \"ligalStuffClick\",\r\n              s_oAPT: {\r\n                actionId: 648\r\n              },\r\n              s_oEPN: \"Legal Stuff\"\r\n            });\r\n  }, [expanded]);\r\n  return <div className=\"virginUltraReg margin-15-top\" id=\"moreInfo\">\r\n    <button id=\"Legal_stuff\" className=\"btn btn-link noUnderlineAll noPaddingImp links-blue-on-bg-gray accss-focus-outline-override-grey-bg-element\" onClick={() => toggleState(!expanded)} aria-expanded={expanded}>\r\n      <span className={`volt-icon ${expanded ? \"icon-collapse_m\" : \"icon-expand_m\"}`} aria-hidden=\"true\" />&nbsp;&nbsp;\r\n      <FormattedMessage id=\"Legal stuff label\" />\r\n    </button>\r\n    <div className=\"spacer30\" aria-hidden=\"true\" />\r\n    <Components.Visible when={expanded}>\r\n      <div className=\"moreInfoBox bgWhite pad30 margin-30-bottom accss-link-override accss-focus-outline-override-white-bg\">\r\n        <button id=\"LEGALBOX_CLOSE\" type=\"button\" onClick={() => toggleState(false)} className=\"close moreInfoLink x-inner txtDarkGrey txtSize18 txtBold\" aria-label=\"close\">\r\n          <span className=\"virgin-icon icon-big_X\" aria-hidden=\"true\" />\r\n        </button>\r\n        <FormattedHTMLMessage id=\"GOOD TO KNOW\" />\r\n      </div>\r\n    </Components.Visible>\r\n  </div>;\r\n};\r\n", "import * as React from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport { ValueOf } from \"omf-changepackage-components\";\r\nimport { IStoreState, IPackage } from \"../../models\";\r\nimport { Package } from \"./Package\";\r\nimport { Footer } from \"./Legal\";\r\nimport { toCharacteristicsJSON } from \"../../utils/Characteristics\";\r\n\r\ninterface IComponentProps {\r\n  catalog: Array<IPackage>;\r\n}\r\n\r\nconst Component: React.FC<IComponentProps> = ({\r\n  catalog\r\n}) => <div className=\"container liquid-container noSpacing\" role=\"radiogroup\">\r\n  <style>\r\n    {\r\n      `.icon-upload-ico:before {\r\n                    content: \"\\\\e99d\";\r\n                }\r\n                .icon-download-ico:before {\r\n                    content: \"\\\\e929\";\r\n                }\r\n                .package-desc li {\r\n                    display: block;\r\n                    list-style: none;\r\n                    position: relative;\r\n                    width: calc(100% / 2);\r\n                }\r\n                    .package-desc li:not(:last-of-type) {\r\n                        padding-right: 15px;\r\n                    }\r\n                    .package-desc li .volt-icon {\r\n                        position: absolute;\r\n                        display: block;\r\n                        color: #cc0000;\r\n                        font-size: 32px;\r\n                        width: 42px;\r\n                        height: 42px;\r\n                        left: 0;\r\n                    }\r\n                    .package-desc li span {\r\n                        display: block;\r\n                        font-size: 12px;\r\n                    }\r\n                    .package-desc .speed-box2 span:first-of-type,\r\n                    .package-desc li span.speed {\r\n                        font-size: 22px;\r\n                        color: black;\r\n                        text-transform: uppercase;\r\n                        font-family: \"VMUltramagneticNormalRegular\", Helvetica, Arial, sans-serif;\r\n                }\r\n                .package-desc .speed-box2 span.usage {\r\n                    white-space: nowrap;\r\n                }\r\n                .speed-box1 li {\r\n                    margin-top: 10px;\r\n                    padding-left: 42px;\r\n                }\r\n                .package-desc li span.downloadTray {\r\n                    display: none;\r\n                }\r\n                .package-desc .speed-box1.expanded li span.downloadTray {\r\n                    display: block;\r\n                }\r\n                @media (max-width: 991.98px) {\r\n                    .package-desc li {\r\n                        width: 100%;\r\n                    }\r\n                  .pkg-pull-left {\r\n                      margin-left: -40px;\r\n                  }\r\n                }`\r\n    }\r\n  </style>\r\n  {\r\n    // We do not want to show current packages\r\n    catalog.filter(pkg => !pkg.isCurrent)\r\n      .sort(\r\n        (a, b) => (\r\n          ValueOf<number>(toCharacteristicsJSON(a.characteristics), \"sortPriority\", 0) -\r\n                        ValueOf<number>(toCharacteristicsJSON(b.characteristics), \"sortPriority\", 0)\r\n        )\r\n      )\r\n      .map(\r\n        internetPackage => <Package {...internetPackage} />\r\n      )\r\n  }\r\n  <Footer />\r\n</div>;\r\n\r\n\r\nexport const Catalog = connect<IComponentProps>(\r\n  ({ catalog }: IStoreState) => ({ catalog })\r\n)(Component);\r\n", "import * as React from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport { Actions, Components, EWidgetName } from \"omf-changepackage-components\";\r\nimport { IStoreState } from \"../models\";\r\nimport { Header } from \"./header\";\r\nimport { Catalog } from \"./catalog\";\r\n\r\nconst {\r\n  RestrictionModal\r\n} = Components;\r\n\r\nconst {\r\n  errorOccured,\r\n  widgetRenderComplete\r\n} = Actions;\r\n\r\ninterface IComponentProps {\r\n}\r\n\r\ninterface IComponentDispatches {\r\n  onErrorEncountered: Function;\r\n  widgetRenderComplete: Function;\r\n}\r\n\r\nclass Component extends React.Component<IComponentProps & IComponentDispatches> {\r\n  componentDidCatch(err: any) {\r\n    this.props.onErrorEncountered(err);\r\n  }\r\n\r\n  componentDidMount() {\r\n    this.props.widgetRenderComplete(EWidgetName.INTERNET);\r\n  }\r\n\r\n  render() {\r\n    return <main id=\"mainContent\">\r\n      <Header />\r\n      <div className=\"spacer30\" aria-hidden=\"true\" />\r\n      <Catalog />\r\n      <RestrictionModal id=\"INTERNET_RESTRICTION_MODAL\" />\r\n    </main>;\r\n  }\r\n}\r\n\r\nexport const Application = connect<IComponentProps, IComponentDispatches>(\r\n  ({  }: IStoreState) => ({  }),\r\n  (dispatch) => ({\r\n    onErrorEncountered: (error: any) => dispatch(errorOccured(error)),\r\n    widgetRenderComplete: () => dispatch(widgetRenderComplete())\r\n  })\r\n)(Component);\r\n", "import * as React from \"react\";\r\nimport { Components } from \"omf-changepackage-components\";\r\nimport { Application } from \"./views\";\r\n\r\nconst {\r\n  ApplicationRoot\r\n} = Components;\r\n\r\nexport const App = () => <ApplicationRoot>\r\n  <Application />\r\n</ApplicationRoot>;\r\n\r\n", "import * as React from \"react\";\r\nimport { Provider as StoreProvider } from \"react-redux\";\r\nimport { EWidgetStatus, Actions, ContextProvider } from \"omf-changepackage-components\";\r\nimport { ViewWidget, Widget, ParamsProvider } from \"bwtk\";\r\nimport { Store } from \"./store\";\r\nimport { IWidgetProps } from \"./models\";\r\nimport { Pipe } from \"./Pipe\";\r\nimport { Config } from \"./Config\";\r\nimport { App } from \"./App\";\r\nimport { Root } from \"react-dom/client\";\r\n\r\nconst {\r\n  setWidgetProps,\r\n  setWidgetStatus\r\n} = Actions;\r\n\r\n@Widget({ namespace: \"Ordering\" })\r\nexport default class WidgetContainer extends ViewWidget {\r\n  constructor(private store: Store, private params: ParamsProvider<IWidgetProps, any>, private config: Config, private pipe: Pipe) {\r\n    super();\r\n  }\r\n\r\n  /**\r\n   * Initialize widget flow\r\n   * please do not place any startup login in here\r\n   * all logic should reside in Epics.onWidgetStatusEpic\r\n   * @memberof WidgetContainer\r\n   */\r\n  init() {\r\n    this.pipe.subscribe(Pipe.Subscriptions(this.store));\r\n    this.store.dispatch(setWidgetProps(this.config));\r\n    this.store.dispatch(setWidgetProps(this.params.props));\r\n    this.store.dispatch(setWidgetStatus(EWidgetStatus.INIT));\r\n  }\r\n\r\n  /**\r\n   * Deinitialize widget flow\r\n   * Destroy all listeneres and connections\r\n   * @memberof WidgetContainer\r\n   */\r\n  destroy() {\r\n    this.pipe.unsubscribe();\r\n    this.store.destroy();\r\n  }\r\n\r\n  /**\r\n   * Render widget\r\n   * Set all contextual providers:\r\n   * * ContextProvider: top-most wrapper used to propagate all *immutable* state params\r\n   * * StoreProvider: redux store wrapper used to propagate all *mutable* state params\r\n   * @param {Element} root\r\n   * @memberof WidgetContainer\r\n   */\r\n  render(root: Root) {\r\n    const { store } = this;\r\n    root.render(\r\n      <ContextProvider value={{ config: this.config }}>\r\n        <StoreProvider {...{ store }}><App /></StoreProvider>\r\n      </ContextProvider>\r\n    );\r\n  }\r\n}\r\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__102__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__418__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__419__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__442__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__446__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__541__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__750__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__769__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__999__;", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { IServiceAccountAPI, IAccountDetails, IPackage } from \"../models\";\r\nimport { ValueOf, Volt } from \"omf-changepackage-components\";\r\n\r\nexport function serviceAccountMutatorFn(response: IServiceAccountAPI): Array<IAccountDetails> {\r\n  return ValueOf<Array<IAccountDetails>>(response, \"ProductOfferings\", [{ Unavailable: true }]);\r\n}\r\n\r\nexport function catalogMutatorFn(response: Volt.IAPIResponse): Array<IPackage> {\r\n  const productOfferingGroup: Volt.IProductOfferingGroup =\r\n        ValueOf(response, \"productOfferingDetail.productOfferingGroups\", [])\r\n          .find((group: Volt.IProductOfferingGroup) => group.lineOfBusiness === Volt.ELineOfBusiness.Internet &&\r\n                group.productOfferingGroupType === Volt.EProductOfferingGroupType.Default);\r\n  return ValueOf(productOfferingGroup, \"productOfferings\", []);\r\n}\r\n\r\nexport function orderMutatorFn(response: Volt.IAPIResponse, catalog: Array<IPackage>): Array<IPackage> {\r\n  const productOfferingGroup: Volt.IProductOfferingGroup =\r\n        ValueOf(response, \"productOfferingDetail.productOfferingGroups\", [])\r\n          .find((group: Volt.IProductOfferingGroup) => group.lineOfBusiness === Volt.ELineOfBusiness.Internet &&\r\n                group.productOfferingGroupType === Volt.EProductOfferingGroupType.Delta);\r\n  const productOfferings: Array<Volt.IProductOffering> = ValueOf(productOfferingGroup, \"productOfferings\", []);\r\n  productOfferings.forEach(product => {\r\n    const initial: IPackage = catalog.find(pkg => pkg.id === product.id) || {} as IPackage;\r\n    Object.assign(initial, product);\r\n  });\r\n  return [...catalog] as Array<IPackage>;\r\n}\r\n"], "names": ["root", "factory", "a", "i", "exports", "module", "require", "define", "amd", "self", "__WEBPACK_EXTERNAL_MODULE__442__", "__WEBPACK_EXTERNAL_MODULE__999__", "__WEBPACK_EXTERNAL_MODULE__446__", "__WEBPACK_EXTERNAL_MODULE__102__", "__WEBPACK_EXTERNAL_MODULE__750__", "__WEBPACK_EXTERNAL_MODULE__541__", "__WEBPACK_EXTERNAL_MODULE__769__", "__WEBPACK_EXTERNAL_MODULE__418__", "__WEBPACK_EXTERNAL_MODULE__419__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_module_cache__", "undefined", "__webpack_modules__", "__extends", "d", "b", "__", "this", "constructor", "TypeError", "String", "extendStatics", "prototype", "Object", "create", "__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "getOwnPropertyDescriptor", "Reflect", "decorate", "defineProperty", "__metadata", "metadataKey", "metadataValue", "metadata", "__read", "o", "n", "ar", "e", "m", "Symbol", "iterator", "call", "next", "done", "push", "value", "error", "toCharacteristicsJSON", "charact<PERSON><PERSON>", "ValueOf", "reduce", "json", "charact<PERSON>tic", "name", "__assign", "ownKeys", "getAccountDetails", "setAccountDetails", "getInternetCatalog", "setInternetCatalog", "togglePackageSelection", "updateInternetCatalog", "BaseConfig", "configProperty", "errorOccured", "setWidgetStatus", "clearCachedState", "finalizeRestriction", "omniPageLoaded", "omniPageSubmit", "BaseLocalization", "BaseStore", "BasePipe", "Component", "Header", "Visible", "<PERSON><PERSON><PERSON><PERSON>", "Package", "Footer", "Catalog", "RestrictionModal", "widgetRenderComplete", "Application", "ApplicationRoot", "App", "setWidgetProps", "definition", "enumerable", "get", "obj", "prop", "hasOwnProperty", "toStringTag", "setPrototypeOf", "__proto__", "Array", "p", "assign", "t", "s", "apply", "getOwnPropertyNames", "k", "SuppressedError", "createAction", "response", "Unavailable", "productOfferingGroup", "find", "group", "lineOfBusiness", "Volt", "ELineOfBusiness", "Internet", "productOfferingGroupType", "EProductOfferingGroupType", "<PERSON><PERSON><PERSON>", "catalog", "Delta", "for<PERSON>ach", "product", "initial", "pkg", "id", "to", "from", "pack", "l", "slice", "concat", "CommonFeatures", "base", "Injectable", "Config", "ajaxClient", "config", "AjaxServices", "Client", "BaseClient", "Actions", "client", "widgetState", "EWidgetStatus", "INIT", "combineEpics", "requestCatalogEpic", "togglePlanSelectionEpic", "finalizeRestrictionEpic", "action$", "pipe", "filter", "action", "type", "toString", "UPDATING", "mergeMap", "of", "Utils", "appendRefreshOnce", "getURLByFlowType", "EFlowType", "TV", "api", "catalogAPI", "INTERNET", "BUNDLE", "bundleCatalogAPI", "FilterRestrictionObservable", "data", "RENDERED", "catchError", "Models", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state$", "payload", "EWidgetName", "PREVIEW", "Boolean", "productOfferingDetail", "broadcastUpdate", "setProductConfigurationTotal", "CatalogEpics", "requestDataEpic", "ofType", "serviceAccountAPI", "UserAccountEpics", "pageLoadedEpic", "pageSubmitEpic", "accountDetails", "Omniture", "useOmniture", "trackFragment", "s_oSS1", "s_oSS2", "s_oSS3", "s_oPGN", "s_oAPT", "actionresult", "s_oPLE", "EMessageType", "Information", "content", "trackAction", "actionId", "s_oBTN", "s_oPRD", "isSelected", "isCurrent", "map", "category", "sku", "quantity", "price", "promo", "OmnitureEpics", "catalogEpics", "userAccountEpics", "omniture", "onWidgetStatusEpic", "getFlowType", "updateContext", "Epics", "Localization", "getLocalizedString", "Instance", "ServiceLocator", "instance", "getService", "CommonServices", "locale", "actionsToComputedPropertyName", "store", "epics", "localization", "combineReducers", "Reducers", "WidgetBaseLifecycle", "WidgetLightboxes", "WidgetRestrictions", "handleActions", "state", "ModalEpics", "RestricitonsEpics", "LifecycleEpics", "Store", "arg", "<PERSON><PERSON>", "Subscriptions", "onContinue", "dispatch", "historyForward", "collapseIcon", "expanded", "toggleState", "s_oEPN", "className", "href", "onClick", "role", "FormattedMessage", "style", "display", "Name", "RegularPrice", "PromotionDetails", "whiteSpace", "Components", "BellCurrency", "when", "FormattedDate", "format", "timeZone", "expiryDate", "values", "connect", "shortDescription", "usagePlan", "regularPrice", "promotionDetails", "offeringAction", "onPackageClicked", "onPackageAction", "stopPropagation", "preventDefault", "keyCode", "uploadExpanded", "ExpandUpload", "onUploadClick", "classList", "contains", "$", "addClass", "attr", "removeAttr", "checked", "onKeyUp", "dangerouslySetInnerHTML", "__html", "credit", "Math", "abs", "duration", "monthly", "FormattedHTMLMessage", "sort", "characteristics", "internetPackage", "componentDidCatch", "err", "props", "onErrorEncountered", "componentDidMount", "render", "params", "init", "subscribe", "destroy", "unsubscribe", "ContextProvider", "Widget", "namespace", "ParamsProvider", "WidgetContainer", "ViewWidget"], "sourceRoot": ""}