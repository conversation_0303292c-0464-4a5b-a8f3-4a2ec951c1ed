import * as React from "react";
import { Volt, Components } from "omf-changepackage-components";
import { FormattedMessage } from "react-intl";

import Combo from "../Components/Combo";
import { filterLanguage, sortOfferings } from "../../utils/Characteristics";
import { OmniturePage } from "../Components/Omniture";

interface IComponentConnectedProps {
  languages: Array<string>;
  combos: Array<Volt.IProductOffering>;
}

export const Combos: React.FC<IComponentConnectedProps> = ({
  languages,
  combos
}) => <OmniturePage name="International Combos">
  <div className="section-bell-tv-international-channels-combo">
    <div className="spacer10" />
    <h3 id={Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS} className="virginUltraReg noMargin txtBlack text-uppercase txtSize20"><FormattedMessage id="International Combos page" /></h3>
    <div className="spacer15" />
    {
      languages.map(langauge => {
        const _combos = filterLanguage(combos, langauge);
        return <Components.Visible key={langauge} when={_combos.length > 0}>
          <h4 className="txtSize18 txtBlack txtBold"><FormattedMessage id={langauge} /></h4>
          {sortOfferings(_combos).map(combo => <Combo key={combo.id} {...combo} />)}
        </Components.Visible>;
      })
    }
  </div>
</OmniturePage>;
