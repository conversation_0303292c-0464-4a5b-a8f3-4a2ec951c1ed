import { Components, FormattedHTMLMessage, ValueOf, Volt } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { IStoreState, ITVChannel } from "../../models";
import Filter from "../Components/Filter";
import { Footer } from "../Components/Legal";
import { OmniturePage } from "../Components/Omniture";

interface IComponentConnectedProps {
  channels: Array<ITVChannel>;
  refresh: any;
}

interface IComponentDispatches {}

export const Component: React.FC<IComponentConnectedProps &
  IComponentDispatches> = ({ channels, refresh }) => (
  <OmniturePage name="Browse all Channels">
    <div className="flexRow flex-justify-space-between">
      <div className="margin-xs">
        <h2
          id={`group-BrowseAll`}
          className="virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"
        >
          <FormattedMessage id="All channels page" />
        </h2>
        <FormattedHTMLMessage id="All channels page description">
          {(__html: any) => (
            <Components.Visible when={Boolean(__html)}>
              <div className="spacer5"></div>
              <p className="noMargintxtSize14">{__html}</p>
            </Components.Visible>
          )}
        </FormattedHTMLMessage>
      </div>
    </div>
    <div className="spacer15" aria-hidden="true" />
    <Filter
      groupName={`group-BrowseAll`}
      channels={channels}
      allowSelection={true}
      forceSelectable={true}
      allowMultipleWaysToAdd={true}
    />
    <Footer
      pageName={Volt.EDIsplayGroupKey.TV_BROWSE_ALL}
      label={`LEGAL_LABEL_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`}
      content={`LEGAL_COPY_${Volt.EDIsplayGroupKey.TV_BROWSE_ALL}`}
    />
  </OmniturePage>
);

export default connect<IComponentConnectedProps, IComponentDispatches>(
  ({ catalog }: IStoreState) => ({
    channels: ValueOf(catalog, "channels", []),
    refresh: Math.random() * 1000
  })
)(Component);
