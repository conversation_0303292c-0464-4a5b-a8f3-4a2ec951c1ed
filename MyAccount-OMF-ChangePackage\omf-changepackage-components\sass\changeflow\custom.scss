@import "mixins";
.wrapper {
    overflow-x: hidden;
    font-family: "Helvetica", Arial, sans-serif;
    color: $virginGray;
}

img {
    max-width: 100%;
}

.noPointer {
    cursor: default;
}

.no-pad {
    padding: 0px !important;
}

.no-margin {
    margin: 0px !important;
}

.no-pad-top {
    padding-top: 0px !important;
}

.no-margin-bottom {
    margin-bottom: 0px !important;
}

.pad-2-top {
    padding-top: 2px;
}


/*colors*/

.txtGray {
    color: $virginGray;
}

.txtLightGray {
    color: $colorGray;
}

.txtOrange {
    color: $virginOrange;
}

.virginRedIcon {
    .path1 {
        &:before {
            color: $virginOrange;
        }
    }
}

.icon-blue {
    color: $virginIconBlue;
}


/*background*/

.bgGrey {
    background-color: $virginGrayLight;
}

.bgOrange {
    background-color: $virginOrange;
}

.bgGrey2 {
    background: $virginGrayLigh2;
}

.borderBlack {
    border-color: $virginBlack;
}

.borderOrange {
    border-color: $virginOrange;
}

.borderGreyStyle {
    border: 1px solid $virginCustomGray1;
}

.noborderBottom {
    border-bottom: none;
}

.noborderBottom {
    border-bottom: none;
}

.noBorder {
    border: none !important;
}

.txtUppercase {
    text-transform: uppercase;
}

.txtCapitalize {
    text-transform: capitalize;
}

.height80 {
    height: 80px;
}

.lineHeight18,
.line-height-1 {
    line-height: 1;
}

.letterSpacing04 {
    letter-spacing: 0.4px;
}

.displayTable {
    display: table;
}

.icon-exapnd-outline-circled:before {
    content: "\e911";
}

.col {
    flex: 1;
}

.align-center {
    align-items: center;
}

.widthAuto {
    width: auto;
}

.container-fluid.nopad {
    padding: 0px;
}

.channel-item {
    width: 20%;
    display: flex;
    @media #{$media-mobile} {
        width: 25%;
    }
}

.banner img {
    width: 100%;
}

.blockData ul {
    list-style-type: none;
}

.flex-container {
    padding: 0;
    margin: 0;
    list-style: none;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-item {
    padding: 5px;
    margin: 10px 30px;
}

.vigin-tv-package-separator {
    width: 1px;
    background-color: $colorGray;
    margin: 30px 0;
}

//For Home Internet flow
.simplified-header {
    .page-heading {
        @media #{$media-mobile} {
            width: 85%;
        }
        margin-left: auto;
        margin-right: auto;
        padding-left: 15px;
        padding-right: 15px;
    }
}

// .simplified-header {
//   background: $virginOrange none repeat scroll 0 0;
//   box-shadow: 0 10px 39px 0 rgba(0, 0, 0, 0.2);
//   height: 75px;
//   letter-spacing: 0.5px;
//   position: relative;
//   text-align: center;
//   z-index: 50;
// }
// // .simplified-header {
//     // .responsive-simplified-header-back {
//     //     .icon-sort-descend {
//     //         transform: rotate(90deg);
//     //     }
//     // }
//   .btn-primary-white {
//     background: none;
//     border: 2px solid $colorWhite;
//     border-radius: 2px;
//     font-size: 16px;
//     font-weight: bold;
//     padding: 11px 26px;
//   }
// // }
.virgin-internet-box {
    box-sizing: border-box;
    min-height: 166px;
    border: 1px solid $colorGray;
    border-radius: 3px;
    background-color: $colorWhite;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.17);
    &.selected {
        border: 2px solid $virginBlack !important;
    }
    .speed-box-1,
    .speed-box-2,
    .speed-box-3 {
        align-items: center;
        display: flex;
        align-items: initial;
        padding: 15px 0 0 0;
    }
    .speed-box-1 {
        width: 45.3%;
        flex-basis: 45.3%;
        @extend %border-right;
    }
    .current-pack-tag {
        height: 35px;
        border-radius: 0 3px 3px 0;
        padding: 8px 26px;
        width: 174px;
        float: left;
        margin-left: -45px;
        margin-top: -10px;
    }
    .speed-box-2 {
        width: 23%;
        flex-basis: 23%;
        justify-content: center;
        @extend %border-right;
    }
    .speed-box-3 {
        width: 36.7%;
        flex-basis: 36.7%;
        letter-spacing: 0.4px;
    }
}

.more-info {
    display: flex;
    flex-direction: column;
    align-items: baseline;
    .moreInfoIcon {
        display: inline-block;
        padding-bottom: 50px;
        cursor: pointer;
        background: url(../../html/VOLT/img/accordion-open.png) no-repeat top left;
        height: 35px;
        line-height: 20px;
        padding-left: 35px;
        &.closeMoreInfo {
            background: url(../../html/VOLT/img/accordion-close.png) no-repeat top left;
        }
    }
    .moreInfoBox {
        background-color: $colorWhite;
        padding: 30px;
        position: relative;
        margin-bottom: 30px;
        text-align: left;
        line-height: 1.2;
    }
}

.internet-current-package {
    padding-top: 15px;
    padding-bottom: 15px;
}

.v-align-middle {
    display: table-cell;
    vertical-align: middle;
}

.internet-usage-div {
    border-left: 1px solid $virginCustomGray1;
    border-right: 1px solid $virginCustomGray1;
}

// flex Css
.flexGrow1 {
    flex-grow: 1;
}

.flexGrow2 {
    flex-grow: 2;
}

.flexGrow3 {
    flex-grow: 3;
}

.current-package {
    color: $colorWhite !important;
    background: linear-gradient(0deg, #2f2f2f 0%, $virginBlack 100%);
}

// .tierContinue {
//   padding: 10px 40px !important;
//   background-color: $virginGrayLight;
//   opacity: 1 !important;
// }
.virgin-dockbar {
    border-top: 1px solid $colorGray;
    .dockbar-buttons {
        @media #{$media-mobile} {
            flex-direction: row !important;
            .virgin-dockbar-row {
                width: 50% !important;
                button {
                    width: 100%;
                }
                &:first-child {
                    padding-right: 7.5px;
                }
                &:last-child {
                    padding-left: 7.5px;
                }
            }
        }
        .dockbar-cancel {
            padding-left: 20px;
            padding-right: 20px;
        }
    }
    .preview-btn {
        button {
            padding: 0px;
            margin-left: 10px;
        }
        @media #{$media-mobile} {
            // padding-bottom: 0 !important;
        }
    }
}

.tooltip {
    .tooltiptext {
        visibility: hidden;
        width: 120px;
        background-color: $colorWhite;
        color: $virginBlack;
        text-align: center;
        border-radius: 6px;
        padding: 5px 0;
        position: absolute;
        z-index: 1;
        bottom: 100%;
        left: 50%;
        margin-left: -60px;
    }
    &:hover {
        .tooltiptext {
            visibility: visible;
        }
    }
}

.panel-default {
    >.panel-heading {
        color: $colorWhite !important;
        background-color: $virginBlack !important;
        border-color: $virginBlack !important;
        color: $colorWhite !important;
    }
}

.promo-label {
    border-radius: 5px;
    padding: 4px 10px;
}

.termsConditions-height {
    height: 80px;
}

.virgin-modal-height {
    max-height: 90vh;
}

// #accordion-scrollSample {
//   height: 140px;
//   overflow-y: scroll;
// }
@media #{$media-mobile} {
    .txtSize26-xs {
        font-size: 26px !important;
    }
    .no-pad-xs {
        padding: 0px !important;
    }
    .no-margin-xs {
        margin: 0px !important;
    }
    .padding-25-xs {
        padding-left: 25px;
        padding-right: 25px;
    }
    .margin-25left-xs {
        margin-left: -25px;
    }
    .margin-25right-xs {
        margin-right: -25px;
    }
    .floatL-xs {
        float: left;
    }
    .block-xs {
        display: block;
    }
    .review-label {
        margin-top: -4px;
    }
    .font35-xs {
        font-size: 35px;
    }
    .padding-20-top-xs {
        padding-top: 20px;
    }
    .no-float-xs {
        float: none !important;
    }
    .noBorder-xs {
        border: none !important;
    }
}

@media #{$media-tablet} {
    .neg-marginLeft-40-sm {
        margin-left: -40px;
    }
}

.scrollTop {
    box-shadow: none !important;
    .txtCenter {
        height: 31px;
        a {
            width: 62px;
            display: inline-block;
            height: inherit;
            border-radius: 35px 35px 0 0;
        }
    }
}

.connector-active-lob li.active a {
    font-weight: bold;
}

.connector-active-lob ul a {
    font-size: 18px;
    color: $colorWhite !important;
}

.brf3-panel {
    background-color: $colorWhite;
    border-radius: 3px;
}

.border-radius-bottom {
    border-radius: 0 0 3px 3px;
}

.border-radius-top {
    border-radius: 3px 3px 0 0;
}

.modal-backdrop.in {
    opacity: 1 !important;
}

#change-programming-modal .modal-dialog.modal-md {
    width: 962px;
}

.totalLeft {
    position: relative;
}

.totalLeft::after {
    position: absolute;
    content: "";
    background: #f7f7f7;
    height: 100%;
    width: 1px;
    left: -1px;
    top: 0;
}

#accordion-terms-service {
    height: 140px !important;
    overflow: scroll;
    overflow-x: hidden;
}

.lineHeight1-3 {
    line-height: 1.3;
}

.tooltip-wrapper {
    display: inline-block;
    /* display: block works as well */
    margin: 50px;
    /* make some space so the tooltip is visible */
}

.tooltip-wrapper .btn[disabled] {
    /* don't let button block mouse events from reaching wrapper */
    pointer-events: none;
}

.tooltip-wrapper.disabled {
    /* OPTIONAL pointer-events setting above blocks cursor setting, so set it here */
    cursor: not-allowed;
}

.back-arrow {
    width: 12px;
    margin-right: 7px;
}

// .bell-tv-navigator .bell-tv-navigator-tab.active > a {
//   border-bottom: 1px solid $virginGray;
// }
.select-account-international {
    padding-top: 18px;
    padding-bottom: 16px;
    padding-left: 23px;
}

#internationalTab {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 48px;
    padding: 0px 20px;
    width: 465px;
    color: #909090;
}


/* For IE10 */

#internationalTab::-ms-expand {
    display: none;
}

.icon-custom-select {
    position: absolute;
    top: 17px;
    right: 40px;
    margin-right: 15px;
}

.internationalBlocks {
    padding: 25px 0 20px 33px;
}

#bell-international {
    display: none;
}

.channel-image {
    height: 94px;
    width: 98px;
    border: 1px solid #b2b2b2;
    padding: 15px;
}

.letterSpacing0 {
    letter-spacing: 0px !important;
}

.sans-serif {
    font-family: "Helvetica", Arial, sans-serif !important;
    letter-spacing: 0;
}

.selectchannelBlock {
    border: 2px solid #000;
    width: 23%;
    border-radius: 3px;
    margin-bottom: 15px;
    .close-channel,
    .close {
        opacity: 1;
    }
    .close {
        position: relative;
        top: -5px;
        right: -2px;
        ;
    }
    &:nth-child(4n) {
        margin-right: 0;
    }
    .channelImage {
        min-height: 55px;
        max-width: 55px;
        display: flex;
        flex-direction: column;
    }
    .channelName {
        height: 25px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

#order-volt-modal .modal-dialog.modal-md {
    width: 978px;
    max-width: 100%;
}

.virgin-title-block .ctrl_element {
    top: -2px;
    left: 0;
}

.virgin-title-block label {
    // width: 24px;
    height: 24px;
    padding-left: 40px;
}

.margin-4-top {
    margin-top: -4px;
}

.vSpacer45 {
    width: 45px;
}

.current-tag {
    margin-top: 10px !important;
    margin-left: 20px !important;
}

@media #{$media-mobile} {
    .current-tag {
        margin-top: 0px !important;
        margin-left: 0px !important;
    }
    .align-items-xs {
        align-items: flex-end;
    }
    .logOut-xs {
        background: transparent;
        color: $virginBlack;
        border: 2px solid;
        width: 153px !important;
        height: 42px;
        margin: 0 auto;
    }
    .speed-box1,
    .speed-box2,
    .speed-box3 {
        width: 100% !important;
        flex-direction: column;
    }
    .speed-box2 {
        justify-content: normal !important;
    }
    .ctrl_radioBtn {
        .ctrl_element {
            left: 0;
            top: -3px;
        }
    }
}

.vSpacer1 {
    width: 1px;
}

.speed-box1 {
    width: 60%;
    flex-basis: 60%;
    padding-right: 15px;
    padding-top: 20px;
}

.speed-box2 {
    width: 24%;
    flex-basis: 24%;
    padding: 30px 30px 40px 30px;
}

.speed-box3 {
    width: 35%;
    flex-basis: 35%;
    border-right: none !important;
    @media #{$media-mobile} {
        border-bottom: none !important;
        padding-bottom: 0 !important;
    }
}

.speed-box1,
.speed-box2,
.speed-box3 {
    align-items: center;
    display: flex;
    align-items: initial;
    border-right: 1px solid $bgGrey;
    @media #{$media-mobile} {
        border-bottom: 1px solid $bgGrey;
        border-right: none;
        padding: 30px 0;
        align-items: start !important;
    }
}

.upload-break-word {
    width: 105px;
    word-wrap: break-word;
}

.txtInverse {
    color: #000;
    background: #fff;
    border-radius: 50%;
    border: 1px solid #fff;
}

.heightFitContent {
    height: fit-content;
}

// Overrides to framework
@media (max-width: 991.98px) and (min-width: 320px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
}

.minHeight45 {
    min-height: 45px;
}

.bell-tv-package {
    border: none !important;
}

.bell-tv-package.selected {
    border: 2px solid #000 !important;
}

.dockbar-notification {
    position: absolute;
    top: -11px;
    right: -11px;
    height: 18px !important;
    width: 18px;
    border-radius: 50%;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.38);
}

// For accessibility
.btn-link.focus,
.btn-link:focus {
    background-color: transparent;
}

.radioBtn-active:focus~span {
    outline: 1px solid #4d90fe !important;
}

a:focus {
    outline: 1px solid #4d90fe !important;
}

.btn-search-submit:focus {
    outline: 1px solid #4d90fe !important;
}

a:hover .txtDecoration_hover,
a:focus .txtDecoration_hover {
    text-decoration: underline
}

.noUnderlineAll.btn-link:hover span {
    text-decoration: underline;
}

.noUnderlineAll.btn-link:hover,
.noUnderlineAll.btn-link:hover .volt-icon {
    text-decoration: none;
}

.bell-tv-package-controls .current-flag {
    top: 15px;
    left: 0px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

@media screen and (min-width: 768px) {
    .bell-tv-package-controls .current-flag {
        top: -20px;
        left: -30px;
    }
}

// .btn-primary:active in virgin.css is not working bz it don't have !important
.btn-primary:active {
    background: $virginOrange !important;
}

.bell-tv-package-filters-tray {
    .bell-tv-package-filters-tray-actions {
        margin-top: -80px !important;
    }
}

.bell-tv-package-body {
    .virgin-channel-block {
        margin: -15px 0 0 -5px;
        @media #{$media-tab-mobile} {
            margin: -15px 0 0 -15px;
        }
        li {
            height: 35px;
            width: 35px;
            border: none;
            margin-left: 15px;
            padding: 0px;
            &:first-child {
                margin-left: 0px;
            }
        }
    }
}

.graphical_ctrl_checkbox {
    .ctrl_element {
        &:after {
            left: 7px;
            top: 3px;
        }
    }
}

.virginRedIcon {
    .path2 {
        &:before {
            color: $virginBlack !important
        }
    }
}

.docbar-spacer {
    border-right: 1px solid #F7F7F7;
    padding: 2.5rem 0rem;
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
}

.side-navigation {
    @media #{$media-tab-mobile} {
        z-index: 1025;
        height: 100%;
        width: 0;
        position: fixed !important;
        top: 0;
        left: 0;
        overflow-x: hidden;
        transition: 0.5s;
        background: $virginGray;
    }
}

.omf-changepackage-review {
    .btn[disabled] {
        color: $colorWhite;
        background-color: $bgGrey;
        border: 2px solid $bgGrey;
    }
}

.dockbar-content {
    @media #{$media-tab-mobile} {
        position: absolute;
        // bottom: 0;
        width: 100%;
    }
}

.noBorderRadius {
    border-radius: 0 !important;
}

.floating-div {
    height: 85px;
    width: 85px;
    min-width: 85px;
    border-radius: 50%;
    background-color: #000000;
    box-shadow: 0 2px 20px 0 rgba(0, 0, 0, 0.5);
    margin-top: -5px;
}

.fix-floating-notification {
    position: fixed;
    top: 20px;
    z-index: 1000;
}

.panel-shadow {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.17);
    border-radius: 3px;
}

.outline {
    outline: #2672cb auto 5px;
}

.scrollAdjust {
    max-height: 60vh;
}

.alaCarteList {
    margin-bottom: 10px;
    &:before {
        content: '';
        margin-right: 15px;
        left: 1px;
        width: 5px;
        height: 5px;
        background-color: #000;
        border-radius: 80%;
        vertical-align: middle;
    }
}

ul.timeItem {
    display: block;
    li {
        margin-bottom: 10px !important;
        &:not(.selected) {
            display: block;
        }
        &.selected {
            border: none !important;
            button {
                border: 1px solid $virginOrange !important;
            }
        }
    }
}