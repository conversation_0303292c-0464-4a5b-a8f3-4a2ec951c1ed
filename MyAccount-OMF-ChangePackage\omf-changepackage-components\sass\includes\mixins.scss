// Wars
$media-all: "all";
$media-print: "print";

$media-large-desktop: "screen and (min-width: 1240px)";
$media-desktop: "screen and (min-width: 992px)";
$media-desktop-tab: "screen and (min-width: 768px)";
$media-tablet: "screen and (max-width: 992px) and (min-width: 768px)";
$media-tab-mobile: "screen and (max-width: 991px)";
$media-mobile: "screen and (max-width: 767px)";

/* IE10+ CSS styles go here */

$media-IE: "(-ms-high-contrast:none)";
// Colors
$virginGray: #333333; // virgin gray
$virginBlack: #000000; // virgin Black
$virginOrange: #e10a0a; // virgin Orange
$virginGrayLight: #e5e5e5; // virgin gray light
$virginCustomGray: #9c9c9c;
$virginCustomGray1: #dddddd;
$colorWhite: #fff;
$bellBlack: #111111; // Bell Black
$colorGray: #d4d4d4;
$bgGrey: #d7d7d7;
$colorLightGray: #e1e1e1; // Light Gray
$virginGrayLigh1: #f6f6f6;
$virginGrayLigh2: #f1f1f1;
$virginGrayLigh3: #eeeeee;
$virginIconBlue: #2390b9;
$virginYellow: #e99e00;
$virginBGGradiant: linear-gradient(0deg, #2f2f2f 0%, #000000 100%);
@mixin pack-content($horizontaly: center, $verticaly: center) {
  // Center the footer text vertically
  /* Internet Explorer 10 */
  display: -ms-flexbox;
  -ms-flex-pack: $horizontaly;
  -ms-flex-align: $verticaly;
  /* Firefox */
  display: -moz-box;
  -moz-box-pack: $horizontaly;
  -moz-box-align: $verticaly;
  /* Safari, Opera, and Chrome */
  display: -webkit-box;
  -webkit-box-pack: $horizontaly;
  -webkit-box-align: $verticaly;
  /* W3C */
  display: box;
  box-pack: $horizontaly;
  box-align: $verticaly;
}

@mixin display-flex($direction: row, $wrap: nowrap) {
  display: -webkit-box;
  /* OLD: Safari,  iOS, Android browser, older WebKit browsers.  */
  display: -moz-box;
  /* OLD: Firefox (buggy) */
  display: -ms-flexbox;
  /* MID: IE 10 */
  display: -webkit-flex;
  /* NEW, Chrome 21?28, Safari 6.1+ */
  display: flex;
  @if $direction {
    -webkit-flex-direction: $direction;
    -moz-flex-direction: $direction;
    -ms-flex-direction: $direction;
    flex-direction: $direction;
  }
  @if $wrap {
    -webkit-flex-wrap: $wrap;
    -moz-flex-wrap: $wrap;
    -ms-flex-wrap: $wrap;
    flex-wrap: $wrap;
  }
}

@mixin flex($shrink: 0, $grow: 0, $basis: auto) {
  @if $shrink {
    -webkit-flex-shrink: $shrink;
    -moz-flex-shrink: $shrink;
    -ms-flex-shrink: $shrink;
    flex-shrink: $shrink;
  }
  @if $grow {
    -webkit-flex-grow: $grow;
    -moz-flex-grow: $grow;
    -ms-flex-grow: $grow;
    flex-grow: $grow;
  }
  @if $basis {
    -webkit-flex-basis: $basis;
    -moz-flex-basis: $basis;
    -ms-flex-basis: $basis;
    flex-basis: $basis;
  }
}

@mixin expandable() {
  max-height: 0;
  overflow: hidden;
  transition: max-height 150ms;
  &.expanded {
    max-height: 99999px;
  }
}

%border-right {
  border-right: 1px solid $virginCustomGray1;
}

%brf3-border {
  border: 1px solid $virginCustomGray1;
}

%brf3-container {
  @media #{$media-large-desktop} {
    margin-left: 105px;
    margin-right: 105px;
  }
  margin-left: 0;
  margin-right: 0;
}
