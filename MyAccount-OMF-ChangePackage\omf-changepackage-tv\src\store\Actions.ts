import { Volt } from "omf-changepackage-components";
import { Action, createAction } from "redux-actions";
import { INavigationItem, IProductOffering, IServiceAccountAPI, ITVCatalog } from "../models";
import { catalogMutatorFn, navigationMutatorFn, orderMutatorFn, serviceAccountMutatorFn } from "../mutators";

// Widget actions
export const getAccountDetails = createAction("GET_ACCOUNT_DETAILS");
export const setAccountDetails = createAction<Array<IProductOffering>>("SET_ACCOUNT_DETAILS", serviceAccountMutatorFn as any) as (response: IServiceAccountAPI) => Action<Array<IProductOffering>>;
export const getCatalog = createAction("GET_TV_CATALOG");
export const setCatalog = createAction<ITVCatalog>("SET_TV_CATALOG", catalogMutatorFn as any) as (response: Volt.IAPIResponse) => Action<ITVCatalog>;
export const setNavigation = createAction<Array<INavigationItem>>("SET_TV_NAVIGATION", navigationMutatorFn as any) as (response: Volt.IAPIResponse) => Action<Array<INavigationItem>>;

export const toggleSelection = createAction<Volt.IHypermediaAction>("TOGGLE_TV_SELECTION");
export const updateCatalog = createAction<ITVCatalog>("UPDATE_TV_CATALOG", orderMutatorFn as any) as (response: Volt.IAPIResponse, catalog: ITVCatalog) => Action<ITVCatalog>;

// Piped actions
