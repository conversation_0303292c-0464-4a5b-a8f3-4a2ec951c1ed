build:
	git clone https://gitlab.int.bell.ca/uxp/omf-changepackage-components.git
	cd omf-changepackage-components;git checkout ${CI_COMMIT_REF_NAME} || git checkout Release; rm package-lock.json; ls; npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true 
	git clone https://gitlab.int.bell.ca/uxp/omf-changepackage-navigation.git
	cd omf-changepackage-navigation; git checkout ${CI_COMMIT_REF_NAME} || git checkout Release; rm package-lock.json; npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true
	git clone https://gitlab.int.bell.ca/uxp/omf-changepackage-internet.git
	cd omf-changepackage-internet; git checkout ${CI_COMMIT_REF_NAME} || git checkout Release;rm package-lock.json;  npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true
	git clone https://gitlab.int.bell.ca/uxp/omf-changepackage-tv.git
	cd omf-changepackage-tv; git checkout ${CI_COMMIT_REF_NAME} || git checkout Release;rm package-lock.json;  npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true
	git clone https://gitlab.int.bell.ca/uxp/omf-changepackage-appointment.git
	cd omf-changepackage-appointment;git checkout ${CI_COMMIT_REF_NAME} || git checkout Release;rm package-lock.json;  npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true
	git clone https://gitlab.int.bell.ca/uxp/omf-changepackage-review.git
	cd omf-changepackage-review; git checkout ${CI_COMMIT_REF_NAME} || git checkout Release;rm package-lock.json;  npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true
	git clone https://gitlab.int.bell.ca/uxp/myaccount-omf-changepackage.git
	cd myaccount-omf-changepackage; git checkout ${CI_COMMIT_REF_NAME} || git checkout Release;rm package-lock.json;  npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true; npm install --package-lock-only --legacy-peer-deps
