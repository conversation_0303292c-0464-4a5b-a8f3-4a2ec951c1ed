import { Actions, Components, FormattedHTMLMessage, ValueOf, Volt } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { INavigationItem, IStoreState, ITVChannel } from "../../models";
import { manageFloater } from "../../utils/Floater";
import Filter from "../Components/Filter";
import { Footer } from "../Components/Legal";
import { OmniturePage } from "../Components/Omniture";
import SelectedChannels, { ModalId as SelectedChannelsModal } from "../Modals/SelectedChannels";


interface IComponentConnectedProps {
  channels: Array<ITVChannel>;
  navigation: Volt.IDisplayGroupOffering;
}

interface IComponentDispatches {
  openLightbox: (data: any) => void;
}

export const Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({
  channels,
  navigation,
  openLightbox
}) => {
  const floater$: HTMLDivElement | any = React.useRef(null);
  const [floater, setIsFloating] = React.useState({ isFloating: false, leftPos: "auto" });
  const channelCount = ValueOf(navigation, "count", 0);
  const totalPrice = ValueOf(navigation, "subTotalPrice.price", 0);
  const groupName = "Alacarte";

  React.useEffect(() => {
    const scrollSpy = () => {
      manageFloater(floater$, setIsFloating);
    };
    window.addEventListener("scroll", scrollSpy);
    return () => {
      window.removeEventListener("scroll", scrollSpy);
    };
  }, []);

  return <OmniturePage name="Alacarte">
    <div className="flexRow flex-justify-space-between">
      <div className="margin-xs">
        <h2 id={`group-${groupName}`} className="virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs">
          <FormattedMessage id="A la carte page" />
        </h2>
        <FormattedHTMLMessage id="A la carte page Description">
          {
            (__html: any) => <Components.Visible when={Boolean(__html)}>
              <div className="spacer5"></div>
              <p className="noMargintxtSize14">{__html}</p>
            </Components.Visible>
          }
        </FormattedHTMLMessage>
      </div>
      <div
        id="wrap"
        ref={floater$}
        tabIndex={0}
        role="button"
        onClick={(e) => channelCount > 0 && openLightbox("wrap")}
        onKeyDown={(e) => (e.key === "Enter" || e.keyCode === 32 || e.type !== "keydown") && channelCount > 0 && openLightbox("wrap")}
        className={`floating-div line-height-1 txtWhite txtSize12 txtCenter sans-serif pointer ${floater.isFloating ? "fix-floating-notification" : ""}`}
        style={{ left: floater.leftPos }}>
        <div className=" txtSize26 virginUltraReg pad-10-top">{channelCount}</div>
        <span className=""><FormattedMessage id="CHANNELS_SELECTED" /></span>
        <div className="txtBold"><Components.BellCurrency value={totalPrice} /></div>
      </div>
    </div>
    <div className="spacer15" aria-hidden="true" />
    <Filter
      groupName={groupName}
      channels={channels}
      allowSelection={true} />
    <Footer pageName={Volt.EDIsplayGroupKey.ALACARTE}
      label={`LEGAL_LABEL_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`}
      content={`LEGAL_COPY_${Volt.EDIsplayGroupKey.ALACARTE}`} />
    <SelectedChannels
      channels={channels.filter(channel => channel.isSelected)}
      totalPrice={totalPrice}
    />
  </OmniturePage>;
};

export default connect<IComponentConnectedProps, IComponentDispatches>(
  ({ catalog, navigation }: IStoreState) => ({
    channels: ValueOf(catalog, "offerings." + Volt.EDIsplayGroupKey.ALACARTE, []),
    navigation: ValueOf<Array<INavigationItem>>(navigation, undefined, []).find(data => data.key === Volt.EDIsplayGroupKey.ALACARTE) as Volt.IDisplayGroupOffering
  }),
  (dispatch) => ({
    openLightbox: (id: any) => dispatch(Actions.openLightbox({ lightboxId: SelectedChannelsModal, data: { relativeId: id } }))
  })
)(Component);
