// import { Injectable } from "bwtk";
// import { Epic, combineEpics } from "redux-observable";
// import { EWidgetStatus } from "omf-changepackage-components";
// import {
//     IStoreState
// } from "../../models";


// @Injectable
// export class OmnitureEpics {
//     widgetState: EWidgetStatus = EWidgetStatus.INIT;

//     combineEpics() {
//         return combineEpics(

//         );
//     }

// }

// type UserAccountEpic = Epic<ReduxActions.Action<any>, IStoreState>;