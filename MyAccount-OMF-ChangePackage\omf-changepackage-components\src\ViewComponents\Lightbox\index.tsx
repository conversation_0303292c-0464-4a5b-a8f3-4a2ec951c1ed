import * as React from "react";
import { connect } from "react-redux";
import { Actions } from "../../Actions";
import { Models } from "../../Models";
import { Utils } from "../../Utils";
import { VisibleComponent } from "../VisibilityContainer";

declare const $: (t: string) => any;
enum EModalEvent {
  SHOW = "show.bs.modal",
  SHOWN = "shown.bs.modal",
  HIDE = "hide.bs.modal",
  HIDDEN = "hidden.bs.modal"
}

export interface ILightboxContainerProps extends Models.IBaseComponentProps {
  title: any;
  modalId: string;
  flexDisplay?: boolean;
  size?: string;
  permanent?: boolean;
  containerClass?: Array<string>;
  onShow?: () => void;
  onShown?: () => void;
  onHide?: () => void;
  onHidden?: () => void;
  onClose?: Function | undefined;
  onDismiss?: () => void;
  children?: React.ReactNode;
}

export interface ILightboxConnectedProps {
  lightboxData?: any;
}

export interface ILightboxContainerDispatches {
  onCloseLightbox: (modalId: string) => void;
  clearLightboxData: () => void;
}

export class Component extends React.Component<ILightboxContainerProps & ILightboxConnectedProps & ILightboxContainerDispatches> {
  static defaultProps = {
    className: "",
    size: "md"
  };
  componentDidMount() {
    this.props.onShow &&
      $(`#${this.props.id || this.props.modalId}`).on(EModalEvent.SHOW, this.props.onShow);
    this.props.onShown &&
      $(`#${this.props.id || this.props.modalId}`).on(EModalEvent.SHOWN, this.props.onShown);
    this.props.onHide &&
      $(`#${this.props.id || this.props.modalId}`).on(EModalEvent.HIDE, this.props.onHide);
    this.props.onHidden &&
      $(`#${this.props.id || this.props.modalId}`).on(EModalEvent.HIDDEN, this.props.onHidden);
    this.onClose = this.onClose.bind(this);
    // Move focus back to open button element
    $(`#${this.props.id || this.props.modalId}`).on(EModalEvent.HIDDEN, () => {
      const domElem = this.props.lightboxData && this.props.lightboxData.relativeId && document.getElementById(this.props.lightboxData.relativeId);
      domElem && domElem.focus();
      this.props.clearLightboxData();
    }
    );
  }

  onClose() {
    if (Utils.isLightboxOpen(this.props.id || this.props.modalId)) {
      this.props.onCloseLightbox(this.props.id || this.props.modalId);
      this.props.onClose !== undefined && this.props.onClose(this.props.id || this.props.modalId);
    }
  }

  render() {
    const {
      id, className = "", size,
      title, containerClass = [],
      modalId, children, onDismiss,
      permanent
    } = this.props;
    return <div
      id={id || modalId}
      className={`modal modal-vm fade ${className}`}
      role="dialog"
      tabIndex={-1}
      data-backdrop="static"
      data-keyboard="false"
      aria-modal="true"
      aria-labelledby={`${id || modalId}_label`}
      // aria-describedby={`${id || modalId}_desc`}
      aria-hidden="true"
    >
      <span className="sr-only">dialog</span>
      <div className={`modal-dialog modal-md modal-bg modal-${size} bell-modal-${size}`} role="document">
        <div className="modal-content noBorderRadius noBorder-xs">
          <div className="modal-header bgGrayLight2 pad-30-left pad-30-right pad-25-top pad-25-bottom pad-15-left-right-xs align-items-center noBorderRadius accss-focus-outline-override-grey-bg">
            <h2 id={`${id || modalId}_label`} className="virginUltra txtBlack txtSize24 overflow-ellipsis txtSize18-xs txtUppercase sans-serif-xs lineHeight1_5 margin-b-0">{title}</h2>
            <VisibleComponent when={!permanent}>
              <button onClick={onDismiss} id={`close_${id || modalId}`} type="button" className="no-pad close" data-dismiss="modal" aria-label="Close Dialog" aria-describedby={`${id || modalId}_label`} autoFocus={true}><span className="volt-icon icon-big_X"></span></button>
            </VisibleComponent>
          </div>
          <div id={`${id || modalId}_desc`} className={`modal-body pad-0 ${containerClass.join(" ")}`}>
            {children}
          </div>
        </div>
      </div>
    </div>;
  }
  componentWillUnmount() {
    this.props.onShow &&
      $(`#${this.props.id || this.props.modalId}`).off(EModalEvent.SHOW, this.props.onShow);
    this.props.onShown &&
      $(`#${this.props.id || this.props.modalId}`).off(EModalEvent.SHOWN, this.props.onShown);
    this.props.onHide &&
      $(`#${this.props.id || this.props.modalId}`).off(EModalEvent.HIDE, this.props.onHide);
    this.props.onHidden &&
      $(`#${this.props.id || this.props.modalId}`).off(EModalEvent.HIDDEN, this.props.onHidden);
  }
}

export const LightboxContainer = connect<{}, ILightboxContainerDispatches, ILightboxContainerProps>(
  ({ lightboxData }: any) => ({ lightboxData }),
  (dispatch) => ({
    onCloseLightbox: (modalId: string) => dispatch(Actions.closeLightbox(modalId)),
    clearLightboxData: () => dispatch(Actions.setlightboxData(""))
  })
)(Component);
