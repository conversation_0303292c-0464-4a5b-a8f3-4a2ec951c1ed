import { Actions, Components, EFlowType, EWidgetRoute, WidgetContext,  } from "omf-changepackage-components";
import * as React from "react";
import { connect } from "react-redux";
import { BrowserRouter, Redirect, Route, Switch, useLocation } from "react-router-dom";
import { IStoreState } from "../models";
import { setHistoryProvider } from "../utils/History";
import { Footer } from "./footer";
import { Header } from "./header";
import { ApplicationExitLightbox } from "./modals/ApplicationExit";
import { ApplicationLogoutLightbox } from "./modals/ApplicationLogout";
import { ApplicationResetLightbox } from "./modals/ApplicationReset";
import { PreviewLightbox } from "./modals/Summary";
import { Appointment } from "./pages/Appointment";
import { Confirmation } from "./pages/Confirmation";
import { Internet } from "./pages/Internet";
import { Review } from "./pages/Review";
import { TV } from "./pages/TV";
import { SummaryPanel } from "./summary";

const {
  RestrictionModal
} = Components;

const {
  errorOccured,
  widgetRenderComplete
} = Actions;

interface IComponentProps {
  defaultRoute: EWidgetRoute;
  flowType: EFlowType;    
}

interface IComponentDispatches {
  onErrorEncountered: Function;
  widgetRenderComplete: Function;
}

const AppRouter: React.FC<IComponentProps> = (props) => {
  const location = useLocation();
  const { config: {environmentVariables} }: any = React.useContext(WidgetContext);
  return <React.Fragment>
    <style>
      {`
                .brf .modal.fade.do-not-center-in .modal-dialog {
                    transform: none!important;
                    top: auto;
                }
                .brf .modal.fade.do-not-center-in .modal-content {
                    min-height: 460px;
                }
            `}
    </style>
    <Header {...props} location={location} />
    {/* Generic catch-all route */}
    <Route path="*" component={({ history }: any) => {
      // Set history object for navigation from components
      setHistoryProvider(history);
      // Should the device be missing from config, jump back to step one
      return null;
    }} />
    <Switch>
      <Route path={[
        "/Changepackage/Internet/Appointment",
        "/Bundle/Internet/Appointment"
      ]}>
        <Appointment title="Appointment" />
      </Route>
      <Route path={[
        "/Changepackage/Internet/Review",
        "/Changepackage/TV/Review",
        "/Add/TV/Review",
        "/Bundle/Review"
      ]}>
        <Review title="Review" />
      </Route>
      <Route path={[
        "/Changepackage/Internet/Confirmation",
        "/Changepackage/TV/Confirmation",
        "/Add/TV/Confirmation",
        "/Bundle/Confirmation"
      ]}>
        <Confirmation title="Confirmation" />
      </Route>
      {/* Order is very important!!!
                Internet + TV routes MUST be last */}
      <Route path={[
        "/Changepackage/Internet",
        "/Bundle/Internet"
      ]}>
        <Internet title="Internet" />
      </Route>
      <Route path={[
        "/Changepackage/TV",
        "/Bundle/TV",
        "/Add/TV"
      ]}>
        <TV title= {environmentVariables.language === "fr" ? "Configurez vos service - TV" : "Set up your service - TV"} />
      </Route>
      {/* Every unknown path should redirect to base route */}
      <Route path="*">
        <Redirect to={props.defaultRoute} />
      </Route>
    </Switch>
    <SummaryPanel {...props} location={location} />
    <Footer />
    <PreviewLightbox />
    <RestrictionModal id="NAVIGATION_RESTRICTION_MODAL" />
    <ApplicationResetLightbox />
    <ApplicationExitLightbox />
    <ApplicationLogoutLightbox />
    <div className="spacer20 hidden-xs hidden-m" aria-hidden="true" />
    <div className="spacer60 hidden-xs hidden-m" aria-hidden="true" />
  </React.Fragment>;
};

class Component extends React.Component<IComponentProps & IComponentDispatches> {
  componentDidCatch(err: any) {
    this.props.onErrorEncountered(err);
  }

  componentDidMount() {
    this.props.widgetRenderComplete("omf-changepackage-navigation");
  }

  render() {
    return (<BrowserRouter basename="/Ordering"><AppRouter {...this.props} /></BrowserRouter>);
  }
}

export const Application = connect<IComponentProps, IComponentDispatches>(
  ({ defaultRoute, flowType }: IStoreState) => ({ defaultRoute, flowType }),
  (dispatch) => ({
    onErrorEncountered: (error: any) => dispatch(errorOccured(error)),
    widgetRenderComplete: () => dispatch(widgetRenderComplete())
  })
)(Component);
