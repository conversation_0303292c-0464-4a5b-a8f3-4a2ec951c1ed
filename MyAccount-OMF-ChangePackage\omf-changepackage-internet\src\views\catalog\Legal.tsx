import { Components, FormattedHTMLMessage, Omniture } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";

interface IComponentProps { }

export const Footer: React.FC<IComponentProps> = () => {
  const [expanded, toggleState] = React.useState(false);
  React.useEffect(() => {
    expanded &&
            Omniture.useOmniture().trackAction({
              id: "ligalStuffClick",
              s_oAPT: {
                actionId: 648
              },
              s_oEPN: "Legal Stuff"
            });
  }, [expanded]);
  return <div className="virginUltraReg margin-15-top" id="moreInfo">
    <button id="Legal_stuff" className="btn btn-link noUnderlineAll noPaddingImp links-blue-on-bg-gray accss-focus-outline-override-grey-bg-element" onClick={() => toggleState(!expanded)} aria-expanded={expanded}>
      <span className={`volt-icon ${expanded ? "icon-collapse_m" : "icon-expand_m"}`} aria-hidden="true" />&nbsp;&nbsp;
      <FormattedMessage id="Legal stuff label" />
    </button>
    <div className="spacer30" aria-hidden="true" />
    <Components.Visible when={expanded}>
      <div className="moreInfoBox bgWhite pad30 margin-30-bottom accss-link-override accss-focus-outline-override-white-bg">
        <button id="LEGALBOX_CLOSE" type="button" onClick={() => toggleState(false)} className="close moreInfoLink x-inner txtDarkGrey txtSize18 txtBold" aria-label="close">
          <span className="virgin-icon icon-big_X" aria-hidden="true" />
        </button>
        <FormattedHTMLMessage id="GOOD TO KNOW" />
      </div>
    </Components.Visible>
  </div>;
};
