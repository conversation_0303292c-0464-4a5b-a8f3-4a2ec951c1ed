@import "mixins";

/* Bell package module
 * 
 * Package is a base view with radio
 * button control. It's only used on
 * Your TV Package page
*/

.bell-tv-base-pack {
    .bell-tv-package-header {
        padding: 10px;
        background-color: #00549a;
        color: #fff;
    }
    .bell-tv-package-main {
        .bell-tv-package-controls {
            // width: 181px;
            width: 27%;
            flex-basis: 27%;
            margin: 40px 30px;
            // display: inline-table;
            .package-name {
                line-height: 22px;
            }
            @media #{$media-tab-mobile} {
                width: 33%;
                flex-basis: 33%;
            }
            @media #{$media-mobile} {
                width: auto;
                padding: 15px;
                padding-top: 30px;
                .txtCurrency {
                    margin-top: -5px;
                }
            }
        }
        .bell-tv-package-separator {
            width: 1px;
            background-color: #D4D4D4;
            margin: 40px 0;
            @media #{$media-mobile} {
                width: auto;
                height: 1px;
                margin: 0 15px;
            }
        }
        .bell-tv-package-description {
            // This is for IE11 compatibility
            width: 100%;
            padding: 40px 30px;
            @media #{$media-mobile} {
                padding: 20px 15px;
            }
            @media #{$media-mobile} {
                width: auto;
                padding: 15px;
                padding-bottom: 30px;
            } // .bell-tv-package-icons {
            //     img {
            //         flex-basis: 25%;
            //         width: 25%;
            //         max-width: unset;
            //         height: auto;
            //         max-height: unset;
            //         margin-right: unset;
            //         align-self: center;
            //         padding-right: 15px;
            //         margin-bottom: 15px;
            //         /*- Override -*/
            //         @media #{$media-tablet} {
            //             flex-basis: 16.66%;
            //             padding: 7.5px;
            //             width: 16.66%;
            //             margin-right: 0;
            //             max-width: unset;
            //         }
            //         @media #{$media-mobile} {
            //             padding:7.5px;
            //             margin-bottom:0;
            //         }
            //     }
            // }
            .bell-tv-package-icons-container {
                @media #{$media-mobile} {
                    flex-basis: 100%;
                }
                .bell-tv-package-icons {
                    img {
                        width: 49px;
                        max-width: none;
                        height: auto;
                        max-height: none;
                        align-self: center;
                        margin-right: 15px;
                        margin-bottom: 15px;
                        /*- Override -*/
                        @media #{$media-tablet} {
                            margin: 7.5px;
                            max-width: none;
                        }
                        @media #{$media-mobile} {
                            margin: 7.5px;
                        }
                    }
                    .channel-item {
                        @media #{$media-mobile} {
                            justify-content: center;
                            flex-basis: 25%;
                            text-align: center;
                            align-items: center;
                            flex-direction: column;
                        }
                        display: flex;
                        span {
                            display: flex;
                        }
                    }
                }
            }
            .bell-tv-package-channels-numbers {
                width: 120px;
                padding: 0 30px 0 0;
                color: $bellBlack;
                @media #{$media-mobile} {
                    order: 1;
                    padding: 0 20px 0 0px;
                }
                .package-channels-numbers {
                    width: 100%;
                    height: 57px;
                    >img {
                        height: 100%
                    }
                    >span {
                        margin-top: -4px;
                    }
                }
            }
            .bell-tv-package-channels-detail {
                @media #{$media-mobile} {
                    flex: 1;
                }
            }
            .show-more {
                @media #{$media-tab-mobile} {
                    padding-left: 7.5px;
                }
            }
        }
    }
}


/*-- Genesis --*/

.section-bell-tv-genesis, .section-bell-tv-qcp {
    .bell-tv-base-pack .bell-tv-package-main .bell-tv-package-controls {
        width: 155px;
        @media #{$media-mobile} {
            width: auto;
        }
    }
}