import { Actions, Components, FormattedHTMLMessage, Omniture } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { Localization } from "../../Localization";
import { IStoreState } from "../../models";

const {
  Modal
} = Components;

interface IComponentProps {
}

interface IComponentDispatches {
  onContinueClick: () => void;
  closeLightbox: () => void;
}

export const ModalId: string = "APPLICATION_EXIT";

const Component: React.FC<IComponentProps & IComponentDispatches> = ({
  onContinueClick,
  closeLightbox
}) => <Modal
  modalId={ModalId}
  onShown={() => {
    Omniture.useOmniture().trackFragment({
      id: "exitLightbox",
      s_oAPT: {
        actionId: 104
      },
      s_oPRM: Localization.getLocalizedString("APPLICATION_EXIT_TITLE"),
      s_oLBC: Localization.getLocalizedString("APPLICATION_EXIT_TEXT")
    });
  }}
  title={<FormattedMessage id="APPLICATION_EXIT_TITLE" />}>
  <div id="APPLICATION_EXIT_TEXT" className="pad-30 pad-15-left-right-xs">
    <FormattedHTMLMessage id="APPLICATION_EXIT_TEXT" />
  </div>
  <div className="bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg">
    <button id="APP_EXIT_CLOSE" className="btn btn-primary fill-xs" onClick={closeLightbox}><FormattedMessage id="APPLICATION_EXIT_CLOSE" /></button>
    <div className="vSpacer15" aria-hidden="true"></div>
    <button id="APP_EXIT_CONTINUE" className="btn btn-default fill-xs" onClick={onContinueClick}><FormattedMessage id="APPLICATION_EXIT_CONTINUE" /></button>
  </div>
</Modal>;

export const ApplicationExitLightbox = connect<IComponentProps, IComponentDispatches>(
  ({ }: IStoreState) => ({}),
  (dispatch) => ({
    onContinueClick: () => {
      Omniture.useOmniture().trackAction({
        id: "exitLightbox",
        s_oAPT: {
          actionId: 647
        },
        s_oBTN: {
          ref: "APP_EXIT_CONTINUE"
        }
      });
      dispatch(Actions.applicationExit());
    },
    closeLightbox: () => {
      Omniture.useOmniture().trackAction({
        id: "exitLightbox",
        s_oAPT: {
          actionId: 647
        },
        s_oBTN: {
          ref: "APP_EXIT_CLOSE"
        }
      });
      dispatch(Actions.closeLightbox(ModalId));
    },
  })
)(Component);
