/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("react"), require("react-redux"), require("omf-changepackage-components"), require("bwtk"), require("redux"), require("redux-actions"), require("redux-observable"), require("rxjs"), require("react-intl"));
	else if(typeof define === 'function' && define.amd)
		define(["react", "react-redux", "omf-changepackage-components", "bwtk", "redux", "redux-actions", "redux-observable", "rxjs", "react-intl"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("react"), require("react-redux"), require("omf-changepackage-components"), require("bwtk"), require("redux"), require("redux-actions"), require("redux-observable"), require("rxjs"), require("react-intl")) : factory(root["React"], root["ReactRedux"], root["OMFChangepackageComponents"], root["bwtk"], root["Redux"], root["ReduxActions"], root["ReduxObservable"], root["rxjs"], root["ReactIntl"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, function(__WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_redux__, __WEBPACK_EXTERNAL_MODULE_omf_changepackage_components__, __WEBPACK_EXTERNAL_MODULE_bwtk__, __WEBPACK_EXTERNAL_MODULE_redux__, __WEBPACK_EXTERNAL_MODULE_redux_actions__, __WEBPACK_EXTERNAL_MODULE_redux_observable__, __WEBPACK_EXTERNAL_MODULE_rxjs__, __WEBPACK_EXTERNAL_MODULE_react_intl__) {
return /******/ (function() { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "../src/Widget.tsx":
/*!**************************************!*\
  !*** ../src/Widget.tsx + 40 modules ***!
  \**************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("{// ESM COMPAT FLAG\n__webpack_require__.r(__webpack_exports__);\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ Widget; }\n});\n\n// NAMESPACE OBJECT: ../src/store/Actions.ts\nvar Actions_namespaceObject = {};\n__webpack_require__.r(Actions_namespaceObject);\n__webpack_require__.d(Actions_namespaceObject, {\n  contactInformation: function() { return contactInformation; },\n  getAppointment: function() { return getAppointment; },\n  getOderDetails: function() { return getOderDetails; },\n  initSlickSlider: function() { return initSlickSlider; },\n  setAdditionalDetails: function() { return setAdditionalDetails; },\n  setAppointment: function() { return setAppointment; },\n  setAvailableDates: function() { return setAvailableDates; },\n  setDuration: function() { return setDuration; },\n  setForErrors: function() { return setForErrors; },\n  setInstallationAddress: function() { return setInstallationAddress; },\n  setIsInstallationRequired: function() { return setIsInstallationRequired; }\n});\n\n;// ./tslib/tslib.es6.mjs\n/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nfunction __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nvar __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nfunction __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nfunction __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nfunction __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nfunction __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nfunction __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nfunction __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nfunction __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nfunction __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nfunction __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nvar __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nfunction __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nfunction __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nfunction __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nfunction __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nfunction __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nfunction __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nfunction __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nfunction __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nfunction __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nfunction __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nfunction __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nfunction __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nfunction __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nfunction __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nfunction __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\n/* harmony default export */ var tslib_es6 = ({\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n});\n\n// EXTERNAL MODULE: external {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"}\nvar external_root_React_commonjs2_react_commonjs_react_amd_react_ = __webpack_require__(\"react\");\n// EXTERNAL MODULE: external {\"root\":\"ReactRedux\",\"commonjs2\":\"react-redux\",\"commonjs\":\"react-redux\",\"amd\":\"react-redux\"}\nvar external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_ = __webpack_require__(\"react-redux\");\n// EXTERNAL MODULE: external {\"root\":\"OMFChangepackageComponents\",\"commonjs2\":\"omf-changepackage-components\",\"commonjs\":\"omf-changepackage-components\",\"amd\":\"omf-changepackage-components\"}\nvar external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_ = __webpack_require__(\"omf-changepackage-components\");\n// EXTERNAL MODULE: external {\"root\":\"bwtk\",\"commonjs2\":\"bwtk\",\"commonjs\":\"bwtk\",\"amd\":\"bwtk\"}\nvar external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_ = __webpack_require__(\"bwtk\");\n// EXTERNAL MODULE: external {\"root\":\"Redux\",\"commonjs2\":\"redux\",\"commonjs\":\"redux\",\"amd\":\"redux\"}\nvar external_root_Redux_commonjs2_redux_commonjs_redux_amd_redux_ = __webpack_require__(\"redux\");\n// EXTERNAL MODULE: external {\"root\":\"ReduxActions\",\"commonjs2\":\"redux-actions\",\"commonjs\":\"redux-actions\",\"amd\":\"redux-actions\"}\nvar external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_ = __webpack_require__(\"redux-actions\");\n// EXTERNAL MODULE: external {\"root\":\"ReduxObservable\",\"commonjs2\":\"redux-observable\",\"commonjs\":\"redux-observable\",\"amd\":\"redux-observable\"}\nvar external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_ = __webpack_require__(\"redux-observable\");\n;// ../src/store/Actions.ts\n\nvar getOderDetails = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"GET_ORDER_DETAILS\");\nvar getAppointment = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"GET_APPOINTMENT\");\nvar setAppointment = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_APPOINTMENT\");\nvar setAvailableDates = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_AVAIALBLE_DATES\");\nvar contactInformation = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_CONTACT_INFO\");\nvar setDuration = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_DURATION\");\nvar setInstallationAddress = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_INSTALLATION_ADDRESS\");\nvar setAdditionalDetails = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_ADDITIONAL_DETAILS\");\nvar setIsInstallationRequired = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_INSTALLATION_REQUIRED\");\nvar setForErrors = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_FORM_ERRORS\");\nvar initSlickSlider = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"INIT_SLICK_SLIDER\");\n\n// EXTERNAL MODULE: external {\"root\":\"rxjs\",\"commonjs2\":\"rxjs\",\"commonjs\":\"rxjs\",\"amd\":\"rxjs\"}\nvar external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_ = __webpack_require__(\"rxjs\");\n;// ../src/Config.ts\n\n\nvar BaseConfig = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseConfig, configProperty = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.configProperty;\nvar Config = (function (_super) {\n    __extends(Config, _super);\n    function Config() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"headers\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"environmentVariables\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"mockdata\", void 0);\n    __decorate([\n        configProperty({ base: \"http://127.0.0.1:8881\", orderDetailsAPI: \"/\", appointmentAPI: \"/\", orderSubmitAPI: \"/\" }),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"api\", void 0);\n    Config = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], Config);\n    return Config;\n}(BaseConfig));\n\n\n;// ../src/Client.ts\n\n\n\n\nvar Client = (function (_super) {\n    __extends(Client, _super);\n    function Client(ajaxClient, config) {\n        return _super.call(this, ajaxClient, config) || this;\n    }\n    Client = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.AjaxServices, Config])\n    ], Client);\n    return Client;\n}(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.BaseClient));\n\n\n;// ../src/models/Store.ts\n\n\n;// ../src/models/App.ts\n\n\n;// ../src/models/Widget.ts\n\n\n;// ../src/models/Appointment.ts\nvar Request = {\n    availableDates: null,\n    duration: \"\",\n    installationAddress: {\n        address1: \"\",\n        address2: \"\",\n        city: \"\",\n        province: \"\",\n        postalCode: \"\",\n        apartmentType: \"\",\n        apartmentNumber: \"\"\n    },\n    contactInformation: {\n        preferredContactMethod: \"\",\n        primaryPhone: {\n            phoneNumber: \"\",\n            phoneExtension: \"\"\n        },\n        mobileNumber: null,\n        additionalPhone: {\n            phoneNumber: \"\",\n            phoneExtension: \"\"\n        },\n        textMessage: \"\",\n        email: \"\"\n    },\n    additionalDetails: {\n        apartment: \"\",\n        entryCode: \"\",\n        specialInstructions: \"\",\n        superintendantName: \"\",\n        superintendantPhone: \"\",\n        informedSuperintendant: null\n    },\n    isInstallationRequired: null\n};\nvar MapRequestData = (function () {\n    function MapRequestData() {\n    }\n    MapRequestData.create = function (payload, request, store) {\n        request.installationAddress.address1 = store.installationAddress && store.installationAddress.address1 ? store.installationAddress.address1 : \"\";\n        request.installationAddress.address2 = store.installationAddress && store.installationAddress.address2 ? store.installationAddress.address2 : \"\";\n        request.installationAddress.city = store.installationAddress && store.installationAddress.city ? store.installationAddress.city : \"\";\n        request.installationAddress.postalCode = store.installationAddress && store.installationAddress.postalCode ? store.installationAddress.postalCode : \"\";\n        request.installationAddress.province = store.installationAddress && store.installationAddress.province ? store.installationAddress.province : \"\";\n        request.installationAddress.apartmentType = store.installationAddress && store.installationAddress.province ? store.installationAddress.apartmentType : \"\";\n        request.installationAddress.apartmentNumber = store.installationAddress && store.installationAddress.province ? store.installationAddress.apartmentNumber : \"\";\n        request.isInstallationRequired = store.isInstallationRequired;\n        request.duration = store.duration;\n        request.contactInformation.primaryPhone.phoneNumber = payload.Phone_LABEL ? payload.Phone_LABEL : \"\";\n        request.contactInformation.primaryPhone.phoneExtension = payload.Phone_EXT ? payload.Phone_EXT : \"\";\n        request.contactInformation.additionalPhone.phoneNumber = payload.ADDITIONAL_PHONE_NUMBER;\n        request.contactInformation.additionalPhone.phoneExtension = payload.ADDITIONAL_PHONE_EXT;\n        request.contactInformation.preferredContactMethod = payload.PREFERED_METHOD_OF_CONTACT;\n        request.contactInformation.email = payload.Email_LABEL ? payload.Email_LABEL : \"\";\n        request.contactInformation.textMessage = payload.TextMessage_LABEL ? payload.TextMessage_LABEL : \"\";\n        request.availableDates = updateAvailableDates(store.availableDates, JSON.parse(payload.dateAndTime));\n        request.additionalDetails.apartment = payload.APPARTMENT;\n        request.additionalDetails.entryCode = payload.ENTRY_CODE;\n        request.additionalDetails.informedSuperintendant = payload.INFORMED_SUPERINTENDANT;\n        request.additionalDetails.specialInstructions = payload.SPECIAL_INSTRUCTIONS;\n        request.additionalDetails.superintendantName = payload.SUPERINTENDANT_NAME;\n        request.additionalDetails.superintendantPhone = payload.SUPERINTENDANT_PHONE;\n        return request;\n    };\n    return MapRequestData;\n}());\n\nfunction updateAvailableDates(dates, selectedDate) {\n    dates === null || dates === void 0 ? void 0 : dates.forEach(function (date) {\n        date.timeSlots.forEach(function (time) { return time.isSelected = false; });\n    });\n    dates === null || dates === void 0 ? void 0 : dates.forEach(function (date) { return (date.timeSlots.forEach(function (time) { return time.isSelected = (date.date === selectedDate.date && selectedDate.timeSlots.map(function (selectedTime) { return selectedTime.intervalType === time.intervalType; })) ? true : false; })); });\n    return dates;\n}\n\n;// ../src/models/index.ts\n\n\n\n\n\n;// ../src/store/Epics/Appointment.ts\n\n\n\n\n\n\n\n\n\nvar errorOccured = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.errorOccured, setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus, setProductConfigurationTotal = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setProductConfigurationTotal, broadcastUpdate = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate, historyGo = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyGo, clearCachedState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.clearCachedState, omniPageLoaded = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageLoaded;\nvar AppointmentEpics = (function () {\n    function AppointmentEpics(client, config) {\n        this.client = client;\n        this.config = config;\n        this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT;\n    }\n    AppointmentEpics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.appointmentEpic, this.submitAppointmentEpic);\n    };\n    Object.defineProperty(AppointmentEpics.prototype, \"appointmentEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(getAppointment.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function () { return _this.widgetState !== external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING; }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.concat)((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING)), _this.client.get(_this.config.api.appointmentAPI).pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {\n                        var data = _a.data;\n                        return [\n                            setAvailableDates(data.appointment.availableDates),\n                            setDuration(data.appointment.duration),\n                            setInstallationAddress(data.appointment.installationAddress),\n                            contactInformation(data.appointment.contactInformation),\n                            setAdditionalDetails(data.appointment.additionalDetails),\n                            setIsInstallationRequired(data.appointment.isInstallationRequired),\n                            broadcastUpdate(setProductConfigurationTotal((0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(data, \"productConfigurationTotal\"))),\n                            setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.RENDERED),\n                            omniPageLoaded()\n                        ];\n                    })));\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(errorOccured(new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Models.ErrorHandler(\"getAppointment\", error))); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AppointmentEpics.prototype, \"submitAppointmentEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$, store) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(setAppointment.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function () { return _this.widgetState !== external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING; }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {\n                    var payload = _a.payload;\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.concat)((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING)), _this.client.put(_this.config.api.appointmentAPI, MapRequestData.create(payload, Request, store.getState())).pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () { return [\n                        broadcastUpdate(historyGo(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.REVIEW)),\n                        clearCachedState([external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.REVIEW])\n                    ]; })));\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(errorOccured(new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Models.ErrorHandler(\"getAppointment\", error))); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AppointmentEpics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [Client, Config])\n    ], AppointmentEpics);\n    return AppointmentEpics;\n}());\n\n\n;// ../src/store/Epics/Omniture.ts\n\n\n\n\n\nvar Omniture_omniPageLoaded = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageLoaded;\nvar OmnitureEpics = (function () {\n    function OmnitureEpics() {\n        this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT;\n    }\n    OmnitureEpics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.pageLoadedEpic);\n    };\n    Object.defineProperty(OmnitureEpics.prototype, \"pageLoadedEpic\", {\n        get: function () {\n            return function (action$, store) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(Omniture_omniPageLoaded.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    var omniture = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture();\n                    var currentFlowType = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getFlowType();\n                    var s_oSS3, s_oSS2;\n                    switch (currentFlowType) {\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.INTERNET:\n                            s_oSS2 = \"Internet\";\n                            s_oSS3 = \"Change package\";\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.TV:\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.ADDTV:\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.BUNDLE:\n                            s_oSS2 = \"Bundle\";\n                            s_oSS3 = \"Add Tv\";\n                            break;\n                    }\n                    omniture.trackPage({\n                        id: \"AppointmentPage\",\n                        s_oSS1: \"~\",\n                        s_oSS2: s_oSS2 ? s_oSS2 : \"~\",\n                        s_oSS3: s_oSS3 ? s_oSS3 : \"Change package\",\n                        s_oPGN: \"Installation\",\n                        s_oPLE: {\n                            type: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.EMessageType.Warning,\n                            content: {\n                                ref: \"IstallationMessageBanner\"\n                            }\n                        }\n                    });\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)([]);\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)([]); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    OmnitureEpics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], OmnitureEpics);\n    return OmnitureEpics;\n}());\n\n\n;// ../src/store/Epics.ts\n\n\n\n\n\n\n\n\nvar Epics_setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus, Epics_broadcastUpdate = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate, setAppointmentVisited = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setAppointmentVisited;\nvar Epics = (function () {\n    function Epics(omnitureEpics, appointmentEpics) {\n        this.omnitureEpics = omnitureEpics;\n        this.appointmentEpics = appointmentEpics;\n    }\n    Epics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.onWidgetStatusEpic);\n    };\n    Object.defineProperty(Epics.prototype, \"onWidgetStatusEpic\", {\n        get: function () {\n            return function (action$) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(Epics_setWidgetStatus.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (_a) {\n                    var payload = _a.payload;\n                    return payload === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT;\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(Epics_broadcastUpdate(setAppointmentVisited()), getAppointment()); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Epics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [OmnitureEpics,\n            AppointmentEpics])\n    ], Epics);\n    return Epics;\n}());\n\n\n;// ../src/Localization.ts\n\n\nvar BaseLocalization = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseLocalization;\nvar SOURCE_WIDGET_ID = \"omf-changepackage-appointment\";\nvar Localization = (function (_super) {\n    __extends(Localization, _super);\n    function Localization() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Localization_1 = Localization;\n    Localization.getLocalizedString = function (id) {\n        Localization_1.Instance = Localization_1.Instance ||\n            external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ServiceLocator\n                .instance\n                .getService(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonServices.Localization);\n        var instance = Localization_1.Instance;\n        return instance ? instance.getLocalizedString(SOURCE_WIDGET_ID, id, instance.locale) : id;\n    };\n    var Localization_1;\n    Localization.Instance = null;\n    Localization = Localization_1 = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], Localization);\n    return Localization;\n}(BaseLocalization));\n\n\n;// ../src/store/Store.ts\n\n\n\n\n\n\n\n\n\n\nvar BaseStore = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseStore, actionsToComputedPropertyName = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.actionsToComputedPropertyName;\nvar _a = actionsToComputedPropertyName(Actions_namespaceObject), Store_setAvailableDates = _a.setAvailableDates, Store_setDuration = _a.setDuration, Store_setInstallationAddress = _a.setInstallationAddress, Store_contactInformation = _a.contactInformation, Store_setAdditionalDetails = _a.setAdditionalDetails, Store_setIsInstallationRequired = _a.setIsInstallationRequired;\nvar Store = (function (_super) {\n    __extends(Store, _super);\n    function Store(client, store, epics, localization) {\n        var _this = _super.call(this, store) || this;\n        _this.client = client;\n        _this.epics = epics;\n        _this.localization = localization;\n        return _this;\n    }\n    Object.defineProperty(Store.prototype, \"reducer\", {\n        get: function () {\n            var _a, _b, _c, _d, _e, _f;\n            return (0,external_root_Redux_commonjs2_redux_commonjs_redux_amd_redux_.combineReducers)(__assign(__assign(__assign(__assign({}, external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetBaseLifecycle(this.localization)), external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetLightboxes()), external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetRestrictions()), { availableDates: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_a = {},\n                    _a[Store_setAvailableDates] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _a), null), duration: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_b = {},\n                    _b[Store_setDuration] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _b), null), installationAddress: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_c = {},\n                    _c[Store_setInstallationAddress] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _c), null), contactInformation: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_d = {},\n                    _d[Store_contactInformation] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _d), null), additionalDetails: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_e = {},\n                    _e[Store_setAdditionalDetails] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _e), null), isInstallationRequired: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_f = {},\n                    _f[Store_setIsInstallationRequired] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _f), false) }));\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Store.prototype, \"middlewares\", {\n        get: function () {\n            return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.epics.omnitureEpics.combineEpics(), this.epics.appointmentEpics.combineEpics(), this.epics.combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ModalEpics().combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.RestricitonsEpics(this.client, \"APPOINTMENT_RESTRICTION_MODAL\").combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.LifecycleEpics().combineEpics());\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Store = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [Client, external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Store, Epics, Localization])\n    ], Store);\n    return Store;\n}(BaseStore));\n\n\n;// ../src/store/index.ts\n\n\n\n;// ./react-hook-form/dist/react-hook-form.es.js\n\n\n\nconst VALIDATION_MODE = {\r\n    onBlur: 'onBlur',\r\n    onChange: 'onChange',\r\n    onSubmit: 'onSubmit',\r\n};\r\nconst RADIO_INPUT = 'radio';\r\nconst FILE_INPUT = 'file';\r\nconst VALUE = 'value';\r\nconst UNDEFINED = 'undefined';\r\nconst EVENTS = {\r\n    BLUR: 'blur',\r\n    CHANGE: 'change',\r\n    INPUT: 'input',\r\n};\r\nconst INPUT_VALIDATION_RULES = {\r\n    max: 'max',\r\n    min: 'min',\r\n    maxLength: 'maxLength',\r\n    minLength: 'minLength',\r\n    pattern: 'pattern',\r\n    required: 'required',\r\n    validate: 'validate',\r\n};\r\nconst REGEX_IS_DEEP_PROP = /\\.|\\[(?:[^[\\]]*|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/;\r\nconst REGEX_IS_PLAIN_PROP = /^\\w*$/;\r\nconst REGEX_PROP_NAME = /[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g;\r\nconst REGEX_ESCAPE_CHAR = /\\\\(\\\\)?/g;\n\nfunction attachEventListeners({ field, handleChange, isRadioOrCheckbox, }) {\r\n    const { ref } = field;\r\n    if (ref.addEventListener) {\r\n        ref.addEventListener(isRadioOrCheckbox ? EVENTS.CHANGE : EVENTS.INPUT, handleChange);\r\n        ref.addEventListener(EVENTS.BLUR, handleChange);\r\n    }\r\n}\n\nvar isUndefined = (val) => val === undefined;\n\nvar isNullOrUndefined = (value) => value === null || isUndefined(value);\n\nvar isArray = (value) => Array.isArray(value);\n\nconst isObjectType = (value) => typeof value === 'object';\r\nvar isObject = (value) => !isNullOrUndefined(value) && !isArray(value) && isObjectType(value);\n\nconst isKey = (value) => !isArray(value) &&\r\n    (REGEX_IS_PLAIN_PROP.test(value) || !REGEX_IS_DEEP_PROP.test(value));\r\nconst stringToPath = (string) => {\r\n    const result = [];\r\n    string.replace(REGEX_PROP_NAME, (match, number, quote, string) => {\r\n        result.push(quote ? string.replace(REGEX_ESCAPE_CHAR, '$1') : number || match);\r\n    });\r\n    return result;\r\n};\r\nfunction set(object, path, value) {\r\n    let index = -1;\r\n    const tempPath = isKey(path) ? [path] : stringToPath(path);\r\n    const length = tempPath.length;\r\n    const lastIndex = length - 1;\r\n    while (++index < length) {\r\n        const key = tempPath[index];\r\n        let newValue = value;\r\n        if (index !== lastIndex) {\r\n            const objValue = object[key];\r\n            newValue =\r\n                isObject(objValue) || isArray(objValue)\r\n                    ? objValue\r\n                    : !isNaN(tempPath[index + 1])\r\n                        ? []\r\n                        : {};\r\n        }\r\n        object[key] = newValue;\r\n        object = object[key];\r\n    }\r\n    return object;\r\n}\n\nvar transformToNestObject = (data) => Object.entries(data).reduce((previous, [key, value]) => {\r\n    if (REGEX_IS_DEEP_PROP.test(key)) {\r\n        set(previous, key, value);\r\n        return previous;\r\n    }\r\n    return Object.assign(Object.assign({}, previous), { [key]: value });\r\n}, {});\n\nvar removeAllEventListeners = (ref, validateWithStateUpdate) => {\r\n    if (ref.removeEventListener) {\r\n        ref.removeEventListener(EVENTS.INPUT, validateWithStateUpdate);\r\n        ref.removeEventListener(EVENTS.CHANGE, validateWithStateUpdate);\r\n        ref.removeEventListener(EVENTS.BLUR, validateWithStateUpdate);\r\n    }\r\n};\n\nvar isRadioInput = (type) => type === RADIO_INPUT;\n\nvar isCheckBoxInput = (type) => type === 'checkbox';\n\nfunction isDetached(element) {\r\n    if (!element) {\r\n        return true;\r\n    }\r\n    if (!(element instanceof HTMLElement) ||\r\n        element.nodeType === Node.DOCUMENT_NODE) {\r\n        return false;\r\n    }\r\n    return isDetached(element.parentNode);\r\n}\n\nfunction findRemovedFieldAndRemoveListener(fields, handleChange, field, forceDelete) {\r\n    if (!field) {\r\n        return;\r\n    }\r\n    const { ref, ref: { name, type }, mutationWatcher, } = field;\r\n    if (!type) {\r\n        return;\r\n    }\r\n    const fieldValue = fields[name];\r\n    if ((isRadioInput(type) || isCheckBoxInput(type)) && fieldValue) {\r\n        const { options } = fieldValue;\r\n        if (isArray(options) && options.length) {\r\n            options.forEach(({ ref }, index) => {\r\n                if ((ref && isDetached(ref)) || forceDelete) {\r\n                    const mutationWatcher = ref.mutationWatcher;\r\n                    removeAllEventListeners(ref, handleChange);\r\n                    if (mutationWatcher) {\r\n                        mutationWatcher.disconnect();\r\n                    }\r\n                    options.splice(index, 1);\r\n                }\r\n            });\r\n            if (options && !options.length) {\r\n                delete fields[name];\r\n            }\r\n        }\r\n        else {\r\n            delete fields[name];\r\n        }\r\n    }\r\n    else if (isDetached(ref) || forceDelete) {\r\n        removeAllEventListeners(ref, handleChange);\r\n        if (mutationWatcher) {\r\n            mutationWatcher.disconnect();\r\n        }\r\n        delete fields[name];\r\n    }\r\n}\n\nconst defaultReturn = {\r\n    isValid: false,\r\n    value: '',\r\n};\r\nvar getRadioValue = (options) => isArray(options)\r\n    ? options.reduce((previous, { ref: { checked, value } }) => checked\r\n        ? {\r\n            isValid: true,\r\n            value,\r\n        }\r\n        : previous, defaultReturn)\r\n    : defaultReturn;\n\nvar getMultipleSelectValue = (options) => [...options]\r\n    .filter(({ selected }) => selected)\r\n    .map(({ value }) => value);\n\nvar isFileInput = (type) => type === FILE_INPUT;\n\nvar isMultipleSelect = (type) => type === 'select-multiple';\n\nvar isEmptyString = (value) => value === '';\n\nconst defaultResult = {\r\n    value: false,\r\n    isValid: false,\r\n};\r\nconst validResult = { value: true, isValid: true };\r\nvar getCheckboxValue = (options) => {\r\n    if (isArray(options)) {\r\n        if (options.length > 1) {\r\n            const values = options\r\n                .filter(({ ref: { checked } }) => checked)\r\n                .map(({ ref: { value } }) => value);\r\n            return { value: values, isValid: !!values.length };\r\n        }\r\n        const { checked, value, attributes } = options[0].ref;\r\n        return checked\r\n            ? attributes && !isUndefined(attributes.value)\r\n                ? isUndefined(value) || isEmptyString(value)\r\n                    ? validResult\r\n                    : { value: value, isValid: true }\r\n                : validResult\r\n            : defaultResult;\r\n    }\r\n    return defaultResult;\r\n};\n\nfunction getFieldValue(fields, ref) {\r\n    const { type, name, options, value, files } = ref;\r\n    const field = fields[name];\r\n    if (isFileInput(type)) {\r\n        return files;\r\n    }\r\n    if (isRadioInput(type)) {\r\n        return field ? getRadioValue(field.options).value : '';\r\n    }\r\n    if (isMultipleSelect(type)) {\r\n        return getMultipleSelectValue(options);\r\n    }\r\n    if (isCheckBoxInput(type)) {\r\n        return field ? getCheckboxValue(field.options).value : false;\r\n    }\r\n    return value;\r\n}\n\nvar getFieldsValues = (fields) => Object.values(fields).reduce((previous, { ref, ref: { name } }) => (Object.assign(Object.assign({}, previous), { [name]: getFieldValue(fields, ref) })), {});\n\nvar isEmptyObject = (value) => isObject(value) && !Object.keys(value).length;\n\nvar isSameError = (error, type, message) => isObject(error) && error.type === type && error.message === message;\n\nvar get = (obj, path, defaultValue) => {\r\n    const result = path\r\n        .split(/[,[\\].]+?/)\r\n        .filter(Boolean)\r\n        .reduce((result, key) => (isNullOrUndefined(result) ? result : result[key]), obj);\r\n    return isUndefined(result) || result === obj\r\n        ? obj[path] || defaultValue\r\n        : result;\r\n};\n\nfunction shouldUpdateWithError({ errors, name, error, validFields, fieldsWithValidation, }) {\r\n    const isFieldValid = isEmptyObject(error);\r\n    const isFormValid = isEmptyObject(errors);\r\n    const currentFieldError = get(error, name);\r\n    const existFieldError = get(errors, name);\r\n    if ((isFieldValid && validFields.has(name)) ||\r\n        (existFieldError && existFieldError.isManual)) {\r\n        return false;\r\n    }\r\n    if (isFormValid !== isFieldValid ||\r\n        (!isFormValid && !existFieldError) ||\r\n        (isFieldValid && fieldsWithValidation.has(name) && !validFields.has(name))) {\r\n        return true;\r\n    }\r\n    return (currentFieldError &&\r\n        !isSameError(existFieldError, currentFieldError.type, currentFieldError.message));\r\n}\n\nvar isRegex = (value) => value instanceof RegExp;\n\nvar getValueAndMessage = (validationData) => {\r\n    const isPureObject = isObject(validationData) && !isRegex(validationData);\r\n    return {\r\n        value: isPureObject\r\n            ? validationData.value\r\n            : validationData,\r\n        message: isPureObject\r\n            ? validationData.message\r\n            : '',\r\n    };\r\n};\n\nvar isString = (value) => typeof value === 'string';\n\nvar isFunction = (value) => typeof value === 'function';\n\nvar isBoolean = (value) => typeof value === 'boolean';\n\nfunction getValidateError(result, ref, type = 'validate') {\r\n    const isStringValue = isString(result);\r\n    if (isStringValue || (isBoolean(result) && !result)) {\r\n        const message = isStringValue ? result : '';\r\n        return {\r\n            type,\r\n            message,\r\n            ref,\r\n        };\r\n    }\r\n}\n\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => {\r\n    if (!validateAllFieldCriteria) {\r\n        return {};\r\n    }\r\n    const error = errors[name];\r\n    return Object.assign(Object.assign({}, error), { types: Object.assign(Object.assign({}, (error && error.types ? error.types : {})), { [type]: message || true }) });\r\n};\n\nvar validateField = async (fieldsRef, validateAllFieldCriteria, { ref, ref: { type, value, name, valueAsNumber, valueAsDate }, options, required, maxLength, minLength, min, max, pattern, validate, }) => {\r\n    const fields = fieldsRef.current;\r\n    const error = {};\r\n    const isRadio = isRadioInput(type);\r\n    const isCheckBox = isCheckBoxInput(type);\r\n    const isRadioOrCheckbox = isRadio || isCheckBox;\r\n    const isEmpty = isEmptyString(value);\r\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\r\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\r\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\r\n        error[name] = Object.assign({ type: exceedMax ? maxType : minType, message,\r\n            ref }, (exceedMax\r\n            ? appendErrorsCurry(maxType, message)\r\n            : appendErrorsCurry(minType, message)));\r\n        if (!validateAllFieldCriteria) {\r\n            return error;\r\n        }\r\n    };\r\n    if (required &&\r\n        ((!isRadio && !isCheckBox && (isEmpty || isNullOrUndefined(value))) ||\r\n            (isBoolean(value) && !value) ||\r\n            (isCheckBox && !getCheckboxValue(options).isValid) ||\r\n            (isRadio && !getRadioValue(options).isValid))) {\r\n        const message = isString(required)\r\n            ? required\r\n            : getValueAndMessage(required).message;\r\n        error[name] = Object.assign({ type: INPUT_VALIDATION_RULES.required, message, ref: isRadioOrCheckbox ? fields[name].options[0].ref : ref }, appendErrorsCurry(INPUT_VALIDATION_RULES.required, message));\r\n        if (!validateAllFieldCriteria) {\r\n            return error;\r\n        }\r\n    }\r\n    if (!isNullOrUndefined(min) || !isNullOrUndefined(max)) {\r\n        let exceedMax;\r\n        let exceedMin;\r\n        const { value: maxValue, message: maxMessage } = getValueAndMessage(max);\r\n        const { value: minValue, message: minMessage } = getValueAndMessage(min);\r\n        if (type === 'number' || (!type && !isNaN(value))) {\r\n            const valueNumber = valueAsNumber || parseFloat(value);\r\n            if (!isNullOrUndefined(maxValue)) {\r\n                exceedMax = valueNumber > maxValue;\r\n            }\r\n            if (!isNullOrUndefined(minValue)) {\r\n                exceedMin = valueNumber < minValue;\r\n            }\r\n        }\r\n        else {\r\n            const valueDate = valueAsDate || new Date(value);\r\n            if (isString(maxValue)) {\r\n                exceedMax = valueDate > new Date(maxValue);\r\n            }\r\n            if (isString(minValue)) {\r\n                exceedMin = valueDate < new Date(minValue);\r\n            }\r\n        }\r\n        if (exceedMax || exceedMin) {\r\n            getMinMaxMessage(!!exceedMax, maxMessage, minMessage, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\r\n            if (!validateAllFieldCriteria) {\r\n                return error;\r\n            }\r\n        }\r\n    }\r\n    if (isString(value) && !isEmpty && (maxLength || minLength)) {\r\n        const { value: maxLengthValue, message: maxLengthMessage, } = getValueAndMessage(maxLength);\r\n        const { value: minLengthValue, message: minLengthMessage, } = getValueAndMessage(minLength);\r\n        const inputLength = value.toString().length;\r\n        const exceedMax = maxLength && inputLength > maxLengthValue;\r\n        const exceedMin = minLength && inputLength < minLengthValue;\r\n        if (exceedMax || exceedMin) {\r\n            getMinMaxMessage(!!exceedMax, maxLengthMessage, minLengthMessage);\r\n            if (!validateAllFieldCriteria) {\r\n                return error;\r\n            }\r\n        }\r\n    }\r\n    if (pattern && !isEmpty) {\r\n        const { value: patternValue, message: patternMessage } = getValueAndMessage(pattern);\r\n        if (isRegex(patternValue) && !patternValue.test(value)) {\r\n            error[name] = Object.assign({ type: INPUT_VALIDATION_RULES.pattern, message: patternMessage, ref }, appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, patternMessage));\r\n            if (!validateAllFieldCriteria) {\r\n                return error;\r\n            }\r\n        }\r\n    }\r\n    if (validate) {\r\n        const fieldValue = getFieldValue(fields, ref);\r\n        const validateRef = isRadioOrCheckbox && options ? options[0].ref : ref;\r\n        if (isFunction(validate)) {\r\n            const result = await validate(fieldValue);\r\n            const validateError = getValidateError(result, validateRef);\r\n            if (validateError) {\r\n                error[name] = Object.assign(Object.assign({}, validateError), appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message));\r\n                if (!validateAllFieldCriteria) {\r\n                    return error;\r\n                }\r\n            }\r\n        }\r\n        else if (isObject(validate)) {\r\n            const validateFunctions = Object.entries(validate);\r\n            const validationResult = await new Promise((resolve) => {\r\n                validateFunctions.reduce(async (previous, [key, validate], index) => {\r\n                    if ((!isEmptyObject(await previous) && !validateAllFieldCriteria) ||\r\n                        !isFunction(validate)) {\r\n                        return resolve(previous);\r\n                    }\r\n                    let result;\r\n                    const validateResult = await validate(fieldValue);\r\n                    const validateError = getValidateError(validateResult, validateRef, key);\r\n                    if (validateError) {\r\n                        result = Object.assign(Object.assign({}, validateError), appendErrorsCurry(key, validateError.message));\r\n                        if (validateAllFieldCriteria) {\r\n                            error[name] = result;\r\n                        }\r\n                    }\r\n                    else {\r\n                        result = previous;\r\n                    }\r\n                    return validateFunctions.length - 1 === index\r\n                        ? resolve(result)\r\n                        : result;\r\n                }, {});\r\n            });\r\n            if (!isEmptyObject(validationResult)) {\r\n                error[name] = Object.assign({ ref: validateRef }, validationResult);\r\n                if (!validateAllFieldCriteria) {\r\n                    return error;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    return error;\r\n};\n\nconst parseErrorSchema = (error, validateAllFieldCriteria) => isArray(error.inner)\r\n    ? error.inner.reduce((previous, { path, message, type }) => (Object.assign(Object.assign({}, previous), (previous[path] && validateAllFieldCriteria\r\n        ? {\r\n            [path]: appendErrors(path, validateAllFieldCriteria, previous, type, message),\r\n        }\r\n        : {\r\n            [path]: previous[path] || Object.assign({ message,\r\n                type }, (validateAllFieldCriteria\r\n                ? {\r\n                    types: { [type]: message || true },\r\n                }\r\n                : {})),\r\n        }))), {})\r\n    : {\r\n        [error.path]: { message: error.message, type: error.type },\r\n    };\r\nasync function validateWithSchema(validationSchema, validateAllFieldCriteria, data) {\r\n    try {\r\n        return {\r\n            values: await validationSchema.validate(data, { abortEarly: false }),\r\n            errors: {},\r\n        };\r\n    }\r\n    catch (e) {\r\n        return {\r\n            values: {},\r\n            errors: transformToNestObject(parseErrorSchema(e, validateAllFieldCriteria)),\r\n        };\r\n    }\r\n}\n\nvar getDefaultValue = (defaultValues, name, defaultValue) => isUndefined(defaultValues[name])\r\n    ? get(defaultValues, name, defaultValue)\r\n    : defaultValues[name];\n\nfunction flatArray(list) {\r\n    return list.reduce((a, b) => a.concat(isArray(b) ? flatArray(b) : b), []);\r\n}\n\nvar isPrimitive = (value) => isNullOrUndefined(value) || !isObjectType(value);\n\nconst getPath = (path, values) => {\r\n    const getInnerPath = (value, key, isObject) => {\r\n        const pathWithIndex = isObject ? `${path}.${key}` : `${path}[${key}]`;\r\n        return isPrimitive(value) ? pathWithIndex : getPath(pathWithIndex, value);\r\n    };\r\n    return isArray(values)\r\n        ? values.map((value, key) => getInnerPath(value, key))\r\n        : Object.entries(values).map(([key, value]) => getInnerPath(value, key, true));\r\n};\r\nvar getPath$1 = (parentPath, value) => flatArray(getPath(parentPath, value));\n\nvar assignWatchFields = (fieldValues, fieldName, watchFields, combinedDefaultValues) => {\r\n    let value;\r\n    if (isEmptyObject(fieldValues)) {\r\n        value = undefined;\r\n    }\r\n    else if (!isUndefined(fieldValues[fieldName])) {\r\n        watchFields.add(fieldName);\r\n        value = fieldValues[fieldName];\r\n    }\r\n    else {\r\n        value = get(transformToNestObject(fieldValues), fieldName);\r\n        if (!isUndefined(value)) {\r\n            getPath$1(fieldName, value).forEach(name => watchFields.add(name));\r\n        }\r\n    }\r\n    return isUndefined(value)\r\n        ? isObject(combinedDefaultValues)\r\n            ? getDefaultValue(combinedDefaultValues, fieldName)\r\n            : combinedDefaultValues\r\n        : value;\r\n};\n\nvar skipValidation = ({ hasError, isBlurEvent, isOnSubmit, isReValidateOnSubmit, isOnBlur, isReValidateOnBlur, isSubmitted, }) => (isOnSubmit && isReValidateOnSubmit) ||\r\n    (isOnSubmit && !isSubmitted) ||\r\n    (isOnBlur && !isBlurEvent && !hasError) ||\r\n    (isReValidateOnBlur && !isBlurEvent && hasError) ||\r\n    (isReValidateOnSubmit && isSubmitted);\n\nfunction getIsFieldsDifferent(referenceArray, differenceArray) {\r\n    let isMatch = false;\r\n    if (!isArray(referenceArray) ||\r\n        !isArray(differenceArray) ||\r\n        referenceArray.length !== differenceArray.length) {\r\n        return true;\r\n    }\r\n    for (let i = 0; i < referenceArray.length; i++) {\r\n        if (isMatch) {\r\n            break;\r\n        }\r\n        const dataA = referenceArray[i];\r\n        const dataB = differenceArray[i];\r\n        if (!dataB || Object.keys(dataA).length !== Object.keys(dataB).length) {\r\n            isMatch = true;\r\n            break;\r\n        }\r\n        for (const key in dataA) {\r\n            if (!dataB[key] || dataA[key] !== dataB[key]) {\r\n                isMatch = true;\r\n                break;\r\n            }\r\n        }\r\n    }\r\n    return isMatch;\r\n}\n\nconst isMatchFieldArrayName = (name, searchName) => name.startsWith(`${searchName}[`);\r\nvar isNameInFieldArray = (names, name) => [...names].reduce((prev, current) => (isMatchFieldArrayName(name, current) ? true : prev), false);\n\nfunction onDomRemove(element, onDetachCallback) {\r\n    const observer = new MutationObserver(() => {\r\n        if (isDetached(element)) {\r\n            observer.disconnect();\r\n            onDetachCallback();\r\n        }\r\n    });\r\n    observer.observe(window.document, {\r\n        childList: true,\r\n        subtree: true,\r\n    });\r\n    return observer;\r\n}\n\nconst unsetObject = (target) => {\r\n    for (const key in target) {\r\n        const data = target[key];\r\n        const isArrayObject = isArray(data);\r\n        if ((isObject(data) || isArrayObject) && !data.ref) {\r\n            unsetObject(data);\r\n        }\r\n        if (isUndefined(data) ||\r\n            isEmptyObject(data) ||\r\n            (isArrayObject && !target[key].filter(Boolean).length)) {\r\n            delete target[key];\r\n        }\r\n    }\r\n    return target;\r\n};\r\nconst unset = (target, paths) => {\r\n    paths.forEach(path => {\r\n        set(target, path, undefined);\r\n    });\r\n    return unsetObject(target);\r\n};\n\nvar modeChecker = (mode) => ({\r\n    isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\r\n    isOnBlur: mode === VALIDATION_MODE.onBlur,\r\n    isOnChange: mode === VALIDATION_MODE.onChange,\r\n});\n\nconst { useRef, useState, useCallback, useEffect } = external_root_React_commonjs2_react_commonjs_react_amd_react_;\r\nfunction useForm({ mode = VALIDATION_MODE.onSubmit, reValidateMode = VALIDATION_MODE.onChange, validationSchema, defaultValues = {}, submitFocusError = true, validateCriteriaMode, } = {}) {\r\n    const fieldsRef = useRef({});\r\n    const validateAllFieldCriteria = validateCriteriaMode === 'all';\r\n    const errorsRef = useRef({});\r\n    const touchedFieldsRef = useRef({});\r\n    const watchFieldsRef = useRef(new Set());\r\n    const dirtyFieldsRef = useRef(new Set());\r\n    const fieldsWithValidationRef = useRef(new Set());\r\n    const validFieldsRef = useRef(new Set());\r\n    const isValidRef = useRef(true);\r\n    const defaultRenderValuesRef = useRef({});\r\n    const defaultValuesRef = useRef(defaultValues);\r\n    const isUnMount = useRef(false);\r\n    const isWatchAllRef = useRef(false);\r\n    const isSubmittedRef = useRef(false);\r\n    const isDirtyRef = useRef(false);\r\n    const submitCountRef = useRef(0);\r\n    const isSubmittingRef = useRef(false);\r\n    const handleChangeRef = useRef();\r\n    const resetFieldArrayFunctionRef = useRef({});\r\n    const fieldArrayNamesRef = useRef(new Set());\r\n    const [, render] = useState();\r\n    const { isOnBlur, isOnSubmit } = useRef(modeChecker(mode)).current;\r\n    const isWindowUndefined = typeof window === UNDEFINED;\r\n    const isWeb = typeof document !== UNDEFINED &&\r\n        !isWindowUndefined &&\r\n        !isUndefined(window.HTMLElement);\r\n    const isProxyEnabled = isWeb && 'Proxy' in window;\r\n    const readFormStateRef = useRef({\r\n        dirty: !isProxyEnabled,\r\n        isSubmitted: isOnSubmit,\r\n        submitCount: !isProxyEnabled,\r\n        touched: !isProxyEnabled,\r\n        isSubmitting: !isProxyEnabled,\r\n        isValid: !isProxyEnabled,\r\n    });\r\n    const { isOnBlur: isReValidateOnBlur, isOnSubmit: isReValidateOnSubmit, } = useRef(modeChecker(reValidateMode)).current;\r\n    defaultValuesRef.current = defaultValuesRef.current\r\n        ? defaultValuesRef.current\r\n        : defaultValues;\r\n    const reRender = useCallback(() => {\r\n        if (!isUnMount.current) {\r\n            render({});\r\n        }\r\n    }, []);\r\n    const validateFieldCurry = useCallback(validateField.bind(null, fieldsRef, validateAllFieldCriteria), []);\r\n    const validateFieldsSchemaCurry = useCallback(validateWithSchema.bind(null, validationSchema, validateAllFieldCriteria), [validationSchema]);\r\n    const renderBaseOnError = useCallback((name, error, shouldRender, skipReRender) => {\r\n        let shouldReRender = shouldRender ||\r\n            shouldUpdateWithError({\r\n                errors: errorsRef.current,\r\n                error,\r\n                name,\r\n                validFields: validFieldsRef.current,\r\n                fieldsWithValidation: fieldsWithValidationRef.current,\r\n            });\r\n        if (isEmptyObject(error)) {\r\n            if (fieldsWithValidationRef.current.has(name) || validationSchema) {\r\n                validFieldsRef.current.add(name);\r\n                shouldReRender = shouldReRender || get(errorsRef.current, name);\r\n            }\r\n            errorsRef.current = unset(errorsRef.current, [name]);\r\n        }\r\n        else {\r\n            validFieldsRef.current.delete(name);\r\n            shouldReRender = shouldReRender || !get(errorsRef.current, name);\r\n            set(errorsRef.current, name, error[name]);\r\n        }\r\n        if (shouldReRender && !skipReRender) {\r\n            reRender();\r\n            return true;\r\n        }\r\n    }, [reRender, validationSchema]);\r\n    const setFieldValue = useCallback((name, rawValue) => {\r\n        const field = fieldsRef.current[name];\r\n        if (!field) {\r\n            return false;\r\n        }\r\n        const ref = field.ref;\r\n        const { type } = ref;\r\n        const options = field.options;\r\n        const value = isWeb &&\r\n            ref instanceof window.HTMLElement &&\r\n            isNullOrUndefined(rawValue)\r\n            ? ''\r\n            : rawValue;\r\n        if (isRadioInput(type) && options) {\r\n            options.forEach(({ ref: radioRef }) => (radioRef.checked = radioRef.value === value));\r\n        }\r\n        else if (isFileInput(type)) {\r\n            if (value instanceof FileList || value === '') {\r\n                ref.files = value;\r\n            }\r\n            else {\r\n                ref.value = value;\r\n            }\r\n        }\r\n        else if (isMultipleSelect(type)) {\r\n            [...ref.options].forEach(selectRef => (selectRef.selected = value.includes(selectRef.value)));\r\n        }\r\n        else if (isCheckBoxInput(type) && options) {\r\n            options.length > 1\r\n                ? options.forEach(({ ref: checkboxRef }) => (checkboxRef.checked = value.includes(checkboxRef.value)))\r\n                : (options[0].ref.checked = !!value);\r\n        }\r\n        else {\r\n            ref.value = value;\r\n        }\r\n        return type;\r\n    }, [isWeb]);\r\n    const setDirty = (name) => {\r\n        if (!fieldsRef.current[name] || !readFormStateRef.current.dirty) {\r\n            return false;\r\n        }\r\n        const isFieldArray = isNameInFieldArray(fieldArrayNamesRef.current, name);\r\n        let isDirty = defaultRenderValuesRef.current[name] !==\r\n            getFieldValue(fieldsRef.current, fieldsRef.current[name].ref);\r\n        if (isFieldArray) {\r\n            console.log(defaultValuesRef.current);\r\n            const fieldArrayName = name.substring(0, name.indexOf('['));\r\n            isDirty = getIsFieldsDifferent(transformToNestObject(getFieldsValues(fieldsRef.current))[fieldArrayName], get(defaultValuesRef.current, fieldArrayName));\r\n        }\r\n        const isDirtyChanged = isFieldArray\r\n            ? isDirtyRef.current !== isDirty\r\n            : dirtyFieldsRef.current.has(name) !== isDirty;\r\n        if (isDirty) {\r\n            dirtyFieldsRef.current.add(name);\r\n        }\r\n        else {\r\n            dirtyFieldsRef.current.delete(name);\r\n        }\r\n        isDirtyRef.current = isFieldArray ? isDirty : !!dirtyFieldsRef.current.size;\r\n        return isDirtyChanged;\r\n    };\r\n    const setInternalValue = useCallback((name, value) => {\r\n        setFieldValue(name, value);\r\n        if (setDirty(name) ||\r\n            (!get(touchedFieldsRef.current, name) &&\r\n                readFormStateRef.current.touched)) {\r\n            return !!set(touchedFieldsRef.current, name, true);\r\n        }\r\n    }, [setFieldValue]);\r\n    const executeValidation = useCallback(async (name, shouldRender, skipReRender) => {\r\n        const field = fieldsRef.current[name];\r\n        if (!field) {\r\n            return false;\r\n        }\r\n        if (shouldRender) {\r\n            reRender();\r\n        }\r\n        const error = await validateField(fieldsRef, validateAllFieldCriteria, field);\r\n        renderBaseOnError(name, error, false, skipReRender);\r\n        return isEmptyObject(error);\r\n    }, [reRender, renderBaseOnError, validateAllFieldCriteria]);\r\n    const executeSchemaValidation = useCallback(async (payload, shouldRender) => {\r\n        const { errors } = await validateWithSchema(validationSchema, validateAllFieldCriteria, transformToNestObject(getFieldsValues(fieldsRef.current)));\r\n        const previousFormIsValid = isValidRef.current;\r\n        isValidRef.current = isEmptyObject(errors);\r\n        if (isArray(payload)) {\r\n            payload.forEach(name => {\r\n                if (errors[name]) {\r\n                    set(errorsRef.current, name, errors[name]);\r\n                }\r\n                else {\r\n                    unset(errorsRef.current, [name]);\r\n                }\r\n            });\r\n            reRender();\r\n        }\r\n        else {\r\n            const fieldName = payload;\r\n            const error = (get(errors, fieldName)\r\n                ? { [fieldName]: get(errors, fieldName) }\r\n                : {});\r\n            renderBaseOnError(fieldName, error, shouldRender || previousFormIsValid !== isValidRef.current);\r\n        }\r\n        return isEmptyObject(errorsRef.current);\r\n    }, [reRender, renderBaseOnError, validateAllFieldCriteria, validationSchema]);\r\n    const triggerValidation = useCallback(async (payload, shouldRender) => {\r\n        const fields = payload || Object.keys(fieldsRef.current);\r\n        if (validationSchema) {\r\n            return executeSchemaValidation(fields, shouldRender);\r\n        }\r\n        if (isArray(fields)) {\r\n            const result = await Promise.all(fields.map(async (data) => await executeValidation(data, false, true)));\r\n            reRender();\r\n            return result.every(Boolean);\r\n        }\r\n        return await executeValidation(fields, shouldRender);\r\n    }, [executeSchemaValidation, executeValidation, reRender, validationSchema]);\r\n    const setValue = useCallback((name, value, shouldValidate) => {\r\n        const shouldRender = setInternalValue(name, value) ||\r\n            isWatchAllRef.current ||\r\n            watchFieldsRef.current.has(name);\r\n        if (shouldValidate) {\r\n            return triggerValidation(name, shouldRender);\r\n        }\r\n        if (shouldRender) {\r\n            reRender();\r\n        }\r\n        return;\r\n    }, [reRender, setInternalValue, triggerValidation]);\r\n    handleChangeRef.current = handleChangeRef.current\r\n        ? handleChangeRef.current\r\n        : async ({ type, target }) => {\r\n            const name = target ? target.name : '';\r\n            const fields = fieldsRef.current;\r\n            const errors = errorsRef.current;\r\n            const field = fields[name];\r\n            const currentError = get(errors, name);\r\n            let error;\r\n            if (!field) {\r\n                return;\r\n            }\r\n            const isBlurEvent = type === EVENTS.BLUR;\r\n            const shouldSkipValidation = skipValidation({\r\n                hasError: !!currentError,\r\n                isBlurEvent,\r\n                isOnSubmit,\r\n                isReValidateOnSubmit,\r\n                isOnBlur,\r\n                isReValidateOnBlur,\r\n                isSubmitted: isSubmittedRef.current,\r\n            });\r\n            const shouldUpdateDirty = setDirty(name);\r\n            let shouldUpdateState = isWatchAllRef.current ||\r\n                watchFieldsRef.current.has(name) ||\r\n                shouldUpdateDirty;\r\n            if (isBlurEvent &&\r\n                !get(touchedFieldsRef.current, name) &&\r\n                readFormStateRef.current.touched) {\r\n                set(touchedFieldsRef.current, name, true);\r\n                shouldUpdateState = true;\r\n            }\r\n            if (shouldSkipValidation) {\r\n                return shouldUpdateState && reRender();\r\n            }\r\n            if (validationSchema) {\r\n                const { errors } = await validateWithSchema(validationSchema, validateAllFieldCriteria, transformToNestObject(getFieldsValues(fields)));\r\n                const validForm = isEmptyObject(errors);\r\n                error = (get(errors, name)\r\n                    ? { [name]: get(errors, name) }\r\n                    : {});\r\n                if (isValidRef.current !== validForm) {\r\n                    shouldUpdateState = true;\r\n                }\r\n                isValidRef.current = validForm;\r\n            }\r\n            else {\r\n                error = await validateField(fieldsRef, validateAllFieldCriteria, field);\r\n            }\r\n            if (!renderBaseOnError(name, error) && shouldUpdateState) {\r\n                reRender();\r\n            }\r\n        };\r\n    const validateSchemaIsValid = useCallback(() => {\r\n        const fieldValues = isEmptyObject(defaultValuesRef.current)\r\n            ? getFieldsValues(fieldsRef.current)\r\n            : defaultValuesRef.current;\r\n        validateFieldsSchemaCurry(transformToNestObject(fieldValues)).then(({ errors }) => {\r\n            const previousFormIsValid = isValidRef.current;\r\n            isValidRef.current = isEmptyObject(errors);\r\n            if (previousFormIsValid && previousFormIsValid !== isValidRef.current) {\r\n                reRender();\r\n            }\r\n        });\r\n    }, [reRender, validateFieldsSchemaCurry]);\r\n    const resetFieldRef = useCallback((name) => {\r\n        errorsRef.current = unset(errorsRef.current, [name]);\r\n        touchedFieldsRef.current = unset(touchedFieldsRef.current, [name]);\r\n        defaultRenderValuesRef.current = unset(defaultRenderValuesRef.current, [\r\n            name,\r\n        ]);\r\n        [\r\n            dirtyFieldsRef,\r\n            fieldsWithValidationRef,\r\n            validFieldsRef,\r\n            watchFieldsRef,\r\n        ].forEach(data => data.current.delete(name));\r\n        if (readFormStateRef.current.isValid ||\r\n            readFormStateRef.current.touched) {\r\n            reRender();\r\n        }\r\n        if (validationSchema) {\r\n            validateSchemaIsValid();\r\n        }\r\n    }, [reRender]);\r\n    const removeEventListenerAndRef = useCallback((field, forceDelete) => {\r\n        if (!field) {\r\n            return;\r\n        }\r\n        if (!isUndefined(handleChangeRef.current)) {\r\n            findRemovedFieldAndRemoveListener(fieldsRef.current, handleChangeRef.current, field, forceDelete);\r\n        }\r\n        resetFieldRef(field.ref.name);\r\n    }, [resetFieldRef]);\r\n    function clearError(name) {\r\n        if (isUndefined(name)) {\r\n            errorsRef.current = {};\r\n        }\r\n        else {\r\n            unset(errorsRef.current, isArray(name) ? name : [name]);\r\n        }\r\n        reRender();\r\n    }\r\n    const setInternalError = ({ name, type, types, message, preventRender, }) => {\r\n        const field = fieldsRef.current[name];\r\n        if (!isSameError(errorsRef.current[name], type, message)) {\r\n            set(errorsRef.current, name, {\r\n                type,\r\n                types,\r\n                message,\r\n                ref: field ? field.ref : {},\r\n                isManual: true,\r\n            });\r\n            if (!preventRender) {\r\n                reRender();\r\n            }\r\n        }\r\n    };\r\n    function setError(name, type = '', message) {\r\n        if (isString(name)) {\r\n            setInternalError(Object.assign({ name }, (isObject(type)\r\n                ? {\r\n                    types: type,\r\n                    type: '',\r\n                }\r\n                : {\r\n                    type,\r\n                    message,\r\n                })));\r\n        }\r\n        else if (isArray(name)) {\r\n            name.forEach(error => setInternalError(Object.assign(Object.assign({}, error), { preventRender: true })));\r\n            reRender();\r\n        }\r\n    }\r\n    function watch(fieldNames, defaultValue) {\r\n        const combinedDefaultValues = isUndefined(defaultValue)\r\n            ? isUndefined(defaultValuesRef.current)\r\n                ? {}\r\n                : defaultValuesRef.current\r\n            : defaultValue;\r\n        const fieldValues = getFieldsValues(fieldsRef.current);\r\n        const watchFields = watchFieldsRef.current;\r\n        if (isProxyEnabled) {\r\n            readFormStateRef.current.dirty = true;\r\n        }\r\n        if (isString(fieldNames)) {\r\n            return assignWatchFields(fieldValues, fieldNames, watchFields, combinedDefaultValues);\r\n        }\r\n        if (isArray(fieldNames)) {\r\n            return fieldNames.reduce((previous, name) => {\r\n                let value;\r\n                if (isEmptyObject(fieldsRef.current) &&\r\n                    isObject(combinedDefaultValues)) {\r\n                    value = getDefaultValue(combinedDefaultValues, name);\r\n                }\r\n                else {\r\n                    value = assignWatchFields(fieldValues, name, watchFields, combinedDefaultValues);\r\n                }\r\n                return Object.assign(Object.assign({}, previous), { [name]: value });\r\n            }, {});\r\n        }\r\n        isWatchAllRef.current = true;\r\n        const result = (!isEmptyObject(fieldValues) && fieldValues) ||\r\n            defaultValue ||\r\n            defaultValuesRef.current;\r\n        return fieldNames && fieldNames.nest\r\n            ? transformToNestObject(result)\r\n            : result;\r\n    }\r\n    function unregister(names) {\r\n        if (!isEmptyObject(fieldsRef.current)) {\r\n            (isArray(names) ? names : [names]).forEach(fieldName => removeEventListenerAndRef(fieldsRef.current[fieldName], true));\r\n        }\r\n    }\r\n    function registerFieldsRef(ref, validateOptions = {}) {\r\n        if (!ref.name) {\r\n            return console.warn('Missing name @', ref);\r\n        }\r\n        const { name, type, value } = ref;\r\n        const fieldAttributes = Object.assign({ ref }, validateOptions);\r\n        const fields = fieldsRef.current;\r\n        const isRadioOrCheckbox = isRadioInput(type) || isCheckBoxInput(type);\r\n        let currentField = fields[name];\r\n        let isEmptyDefaultValue = true;\r\n        let isFieldArray = false;\r\n        let defaultValue;\r\n        if (isRadioOrCheckbox\r\n            ? currentField &&\r\n                isArray(currentField.options) &&\r\n                currentField.options.find(({ ref }) => value === ref.value)\r\n            : currentField) {\r\n            fields[name] = Object.assign(Object.assign({}, currentField), validateOptions);\r\n            return;\r\n        }\r\n        if (type) {\r\n            const mutationWatcher = onDomRemove(ref, () => removeEventListenerAndRef(fieldAttributes));\r\n            if (isRadioOrCheckbox) {\r\n                currentField = Object.assign({ options: [\r\n                        ...((currentField && currentField.options) || []),\r\n                        {\r\n                            ref,\r\n                            mutationWatcher,\r\n                        },\r\n                    ], ref: { type, name } }, validateOptions);\r\n            }\r\n            else {\r\n                currentField = Object.assign(Object.assign({}, fieldAttributes), { mutationWatcher });\r\n            }\r\n        }\r\n        else {\r\n            currentField = fieldAttributes;\r\n        }\r\n        fields[name] = currentField;\r\n        if (!isEmptyObject(defaultValuesRef.current)) {\r\n            defaultValue = getDefaultValue(defaultValuesRef.current, name);\r\n            isEmptyDefaultValue = isUndefined(defaultValue);\r\n            isFieldArray = isNameInFieldArray(fieldArrayNamesRef.current, name);\r\n            if (!isEmptyDefaultValue && !isFieldArray) {\r\n                setFieldValue(name, defaultValue);\r\n            }\r\n        }\r\n        if (validationSchema && readFormStateRef.current.isValid) {\r\n            validateSchemaIsValid();\r\n        }\r\n        else if (!isEmptyObject(validateOptions)) {\r\n            fieldsWithValidationRef.current.add(name);\r\n            if (!isOnSubmit && readFormStateRef.current.isValid) {\r\n                validateFieldCurry(currentField).then(error => {\r\n                    const previousFormIsValid = isValidRef.current;\r\n                    if (isEmptyObject(error)) {\r\n                        validFieldsRef.current.add(name);\r\n                    }\r\n                    else {\r\n                        isValidRef.current = false;\r\n                    }\r\n                    if (previousFormIsValid !== isValidRef.current) {\r\n                        reRender();\r\n                    }\r\n                });\r\n            }\r\n        }\r\n        if (!defaultRenderValuesRef.current[name] &&\r\n            !(isFieldArray && isEmptyDefaultValue)) {\r\n            defaultRenderValuesRef.current[name] = isEmptyDefaultValue\r\n                ? getFieldValue(fields, currentField.ref)\r\n                : defaultValue;\r\n        }\r\n        if (!type) {\r\n            return;\r\n        }\r\n        const fieldToAttachListener = isRadioOrCheckbox && currentField.options\r\n            ? currentField.options[currentField.options.length - 1]\r\n            : currentField;\r\n        attachEventListeners({\r\n            field: fieldToAttachListener,\r\n            isRadioOrCheckbox,\r\n            handleChange: handleChangeRef.current,\r\n        });\r\n    }\r\n    function register(refOrValidationOptions, validationOptions) {\r\n        if (isWindowUndefined || !refOrValidationOptions) {\r\n            return;\r\n        }\r\n        if (isString(refOrValidationOptions)) {\r\n            registerFieldsRef({ name: refOrValidationOptions }, validationOptions);\r\n            return;\r\n        }\r\n        if (isObject(refOrValidationOptions) && 'name' in refOrValidationOptions) {\r\n            registerFieldsRef(refOrValidationOptions, validationOptions);\r\n            return;\r\n        }\r\n        return (ref) => ref && registerFieldsRef(ref, refOrValidationOptions);\r\n    }\r\n    const handleSubmit = useCallback((callback) => async (e) => {\r\n        if (e) {\r\n            e.preventDefault();\r\n            e.persist();\r\n        }\r\n        let fieldErrors;\r\n        let fieldValues;\r\n        const fields = fieldsRef.current;\r\n        if (readFormStateRef.current.isSubmitting) {\r\n            isSubmittingRef.current = true;\r\n            reRender();\r\n        }\r\n        try {\r\n            if (validationSchema) {\r\n                fieldValues = getFieldsValues(fields);\r\n                const { errors, values } = await validateFieldsSchemaCurry(transformToNestObject(fieldValues));\r\n                errorsRef.current = errors;\r\n                fieldErrors = errors;\r\n                fieldValues = values;\r\n            }\r\n            else {\r\n                const { errors, values, } = await Object.values(fields).reduce(async (previous, field) => {\r\n                    if (!field) {\r\n                        return previous;\r\n                    }\r\n                    const resolvedPrevious = await previous;\r\n                    const { ref, ref: { name }, } = field;\r\n                    if (!fields[name]) {\r\n                        return Promise.resolve(resolvedPrevious);\r\n                    }\r\n                    const fieldError = await validateFieldCurry(field);\r\n                    if (fieldError[name]) {\r\n                        set(resolvedPrevious.errors, name, fieldError[name]);\r\n                        validFieldsRef.current.delete(name);\r\n                        return Promise.resolve(resolvedPrevious);\r\n                    }\r\n                    if (fieldsWithValidationRef.current.has(name)) {\r\n                        validFieldsRef.current.add(name);\r\n                    }\r\n                    resolvedPrevious.values[name] = getFieldValue(fields, ref);\r\n                    return Promise.resolve(resolvedPrevious);\r\n                }, Promise.resolve({\r\n                    errors: {},\r\n                    values: {},\r\n                }));\r\n                fieldErrors = errors;\r\n                fieldValues = values;\r\n            }\r\n            if (isEmptyObject(fieldErrors)) {\r\n                errorsRef.current = {};\r\n                await callback(transformToNestObject(fieldValues), e);\r\n            }\r\n            else {\r\n                if (submitFocusError) {\r\n                    for (const key in fieldsRef.current) {\r\n                        if (get(fieldErrors, key)) {\r\n                            const field = fieldsRef.current[key];\r\n                            if (field) {\r\n                                if (field.ref.focus) {\r\n                                    field.ref.focus();\r\n                                    break;\r\n                                }\r\n                                else if (field.options) {\r\n                                    field.options[0].ref.focus();\r\n                                    break;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                errorsRef.current = fieldErrors;\r\n            }\r\n        }\r\n        finally {\r\n            isSubmittedRef.current = true;\r\n            isSubmittingRef.current = false;\r\n            submitCountRef.current = submitCountRef.current + 1;\r\n            reRender();\r\n        }\r\n    }, [\r\n        reRender,\r\n        submitFocusError,\r\n        validateFieldCurry,\r\n        validateFieldsSchemaCurry,\r\n        validationSchema,\r\n    ]);\r\n    const resetRefs = () => {\r\n        errorsRef.current = {};\r\n        fieldsRef.current = {};\r\n        touchedFieldsRef.current = {};\r\n        validFieldsRef.current = new Set();\r\n        fieldsWithValidationRef.current = new Set();\r\n        defaultRenderValuesRef.current = {};\r\n        watchFieldsRef.current = new Set();\r\n        dirtyFieldsRef.current = new Set();\r\n        isWatchAllRef.current = false;\r\n        isSubmittedRef.current = false;\r\n        isDirtyRef.current = false;\r\n        isValidRef.current = true;\r\n        submitCountRef.current = 0;\r\n    };\r\n    const reset = (values) => {\r\n        for (const value of Object.values(fieldsRef.current)) {\r\n            if (value && value.ref && value.ref.closest) {\r\n                try {\r\n                    value.ref.closest('form').reset();\r\n                    break;\r\n                }\r\n                catch (_a) { }\r\n            }\r\n        }\r\n        if (values) {\r\n            defaultValuesRef.current = values;\r\n        }\r\n        Object.values(resetFieldArrayFunctionRef.current).forEach(resetFieldArray => isFunction(resetFieldArray) && resetFieldArray(values));\r\n        resetRefs();\r\n        reRender();\r\n    };\r\n    const getValues = (payload) => {\r\n        const fieldValues = getFieldsValues(fieldsRef.current);\r\n        const outputValues = isEmptyObject(fieldValues)\r\n            ? defaultValuesRef.current\r\n            : fieldValues;\r\n        return payload && payload.nest\r\n            ? transformToNestObject(outputValues)\r\n            : outputValues;\r\n    };\r\n    useEffect(() => () => {\r\n        isUnMount.current = true;\r\n        fieldsRef.current &&\r\n            Object.values(fieldsRef.current).forEach((field) => removeEventListenerAndRef(field, true));\r\n    }, [removeEventListenerAndRef]);\r\n    if (!validationSchema) {\r\n        isValidRef.current =\r\n            validFieldsRef.current.size >= fieldsWithValidationRef.current.size &&\r\n                isEmptyObject(errorsRef.current);\r\n    }\r\n    const formState = {\r\n        dirty: isDirtyRef.current,\r\n        isSubmitted: isSubmittedRef.current,\r\n        submitCount: submitCountRef.current,\r\n        touched: touchedFieldsRef.current,\r\n        isSubmitting: isSubmittingRef.current,\r\n        isValid: isOnSubmit\r\n            ? isSubmittedRef.current && isEmptyObject(errorsRef.current)\r\n            : isEmptyObject(fieldsRef.current) || isValidRef.current,\r\n    };\r\n    const control = {\r\n        register,\r\n        unregister,\r\n        setValue,\r\n        triggerValidation,\r\n        formState,\r\n        mode: {\r\n            isOnBlur,\r\n            isOnSubmit,\r\n        },\r\n        reValidateMode: {\r\n            isReValidateOnBlur,\r\n            isReValidateOnSubmit,\r\n        },\r\n        errors: errorsRef.current,\r\n        fieldsRef,\r\n        resetFieldArrayFunctionRef,\r\n        fieldArrayNamesRef,\r\n        isDirtyRef,\r\n        readFormStateRef,\r\n        defaultValuesRef,\r\n    };\r\n    return {\r\n        watch,\r\n        control,\r\n        handleSubmit,\r\n        setValue,\r\n        triggerValidation,\r\n        getValues: useCallback(getValues, []),\r\n        reset: useCallback(reset, [reRender]),\r\n        register: useCallback(register, [defaultValuesRef.current]),\r\n        unregister: useCallback(unregister, [removeEventListenerAndRef]),\r\n        clearError: useCallback(clearError, []),\r\n        setError: useCallback(setError, []),\r\n        errors: errorsRef.current,\r\n        formState: isProxyEnabled\r\n            ? new Proxy(formState, {\r\n                get: (obj, prop) => {\r\n                    if (prop in obj) {\r\n                        readFormStateRef.current[prop] = true;\r\n                        return obj[prop];\r\n                    }\r\n                    return {};\r\n                },\r\n            })\r\n            : formState,\r\n    };\r\n}\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n\r\nfunction react_hook_form_es_rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\n\nconst FormGlobalContext = (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.createContext)(null);\r\nfunction useFormContext() {\r\n    return (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useContext)(FormGlobalContext);\r\n}\r\nfunction FormContext(_a) {\r\n    var { children, formState, errors } = _a, restMethods = react_hook_form_es_rest(_a, [\"children\", \"formState\", \"errors\"]);\r\n    return ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement)(FormGlobalContext.Provider, { value: Object.assign(Object.assign({}, restMethods), { formState, errors }) }, children));\r\n}\n\nvar generateId = () => {\r\n    const d = typeof performance === UNDEFINED ? Date.now() : performance.now() * 1000;\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\r\n        const r = (Math.random() * 16 + d) % 16 | 0;\r\n        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\r\n    });\r\n};\n\nconst appendId = (value) => (Object.assign(Object.assign({}, value), { id: generateId() }));\r\nconst mapIds = (data) => (isArray(data) ? data : []).map(value => appendId(value));\n\nvar removeArrayAt = (data, index) => !isUndefined(index) && isArray(data)\r\n    ? [...data.slice(0, index), ...data.slice(index + 1)]\r\n    : [];\n\nvar moveArrayAt = (data, from, to) => isArray(data) && data.splice(to, 0, data.splice(from, 1)[0]);\n\nvar swapArrayAt = (fields, indexA, indexB) => isArray(fields) &&\r\n    ([fields[indexA], fields[indexB]] = [fields[indexB], fields[indexA]]);\n\nfunction useFieldArray({ control, name }) {\r\n    const methods = useFormContext();\r\n    const { resetFieldArrayFunctionRef, fieldArrayNamesRef, fieldsRef, defaultValuesRef, unregister, isDirtyRef, readFormStateRef, } = control || methods.control;\r\n    const memoizedDefaultValues = (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useRef)(get(defaultValuesRef.current, name, []));\r\n    const [fields, setField] = (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useState)(mapIds(memoizedDefaultValues.current));\r\n    const getFieldValuesByName = (fields, name) => transformToNestObject(getFieldsValues(fields))[name];\r\n    const resetFields = (flagOrFields) => {\r\n        if (readFormStateRef.current.dirty) {\r\n            isDirtyRef.current = isUndefined(flagOrFields)\r\n                ? true\r\n                : getIsFieldsDifferent(flagOrFields, memoizedDefaultValues.current);\r\n        }\r\n        for (const key in fieldsRef.current) {\r\n            if (isMatchFieldArrayName(key, name)) {\r\n                unregister(key);\r\n            }\r\n        }\r\n    };\r\n    const append = (value) => {\r\n        if (readFormStateRef.current.dirty) {\r\n            isDirtyRef.current = true;\r\n        }\r\n        setField([...fields, appendId(value)]);\r\n    };\r\n    const prepend = (value) => {\r\n        resetFields();\r\n        setField(mapIds([appendId(value), ...fields]));\r\n    };\r\n    const remove = (index) => {\r\n        const updatedFields = removeArrayAt(getFieldValuesByName(fieldsRef.current, name), index);\r\n        resetFields(updatedFields);\r\n        setField(mapIds(updatedFields));\r\n    };\r\n    const insert = (index, value) => {\r\n        const fieldValues = getFieldValuesByName(fieldsRef.current, name);\r\n        resetFields();\r\n        setField(mapIds([\r\n            ...fieldValues.slice(0, index),\r\n            appendId(value),\r\n            ...fieldValues.slice(index),\r\n        ]));\r\n    };\r\n    const swap = (indexA, indexB) => {\r\n        const fieldValues = getFieldValuesByName(fieldsRef.current, name);\r\n        swapArrayAt(fieldValues, indexA, indexB);\r\n        resetFields(fieldValues);\r\n        setField(mapIds(fieldValues));\r\n    };\r\n    const move = (from, to) => {\r\n        const fieldValues = getFieldValuesByName(fieldsRef.current, name);\r\n        moveArrayAt(fieldValues, from, to);\r\n        resetFields(fieldValues);\r\n        setField(mapIds(fieldValues));\r\n    };\r\n    const reset = (values) => {\r\n        resetFields();\r\n        setField(mapIds(get(values, name)));\r\n        memoizedDefaultValues.current = get(defaultValuesRef.current, name, []);\r\n    };\r\n    (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect)(() => {\r\n        const resetFunctions = resetFieldArrayFunctionRef.current;\r\n        const fieldArrayNames = fieldArrayNamesRef.current;\r\n        fieldArrayNames.add(name);\r\n        resetFunctions[name] = reset;\r\n        return () => {\r\n            resetFields();\r\n            delete resetFunctions[name];\r\n            fieldArrayNames.delete(name);\r\n        };\r\n        // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [name]);\r\n    return {\r\n        swap,\r\n        move,\r\n        prepend,\r\n        append,\r\n        remove,\r\n        insert,\r\n        fields,\r\n    };\r\n}\n\nvar getInputValue = (target, isCheckbox) => {\r\n    if (isNullOrUndefined(target)) {\r\n        return target;\r\n    }\r\n    return isCheckbox\r\n        ? isUndefined(target.checked)\r\n            ? target\r\n            : target.checked\r\n        : isUndefined(target.value)\r\n            ? target\r\n            : target.value;\r\n};\n\nconst Controller = (_a) => {\r\n    var { name, rules, as: InnerComponent, onChange, onChangeName = VALIDATION_MODE.onChange, onBlurName = VALIDATION_MODE.onBlur, valueName, defaultValue, control } = _a, rest = react_hook_form_es_rest(_a, [\"name\", \"rules\", \"as\", \"onChange\", \"onChangeName\", \"onBlurName\", \"valueName\", \"defaultValue\", \"control\"]);\r\n    const methods = useFormContext();\r\n    const { defaultValuesRef, setValue, register, unregister, errors, triggerValidation, mode: { isOnSubmit, isOnBlur }, reValidateMode: { isReValidateOnBlur, isReValidateOnSubmit }, formState: { isSubmitted }, fieldsRef, fieldArrayNamesRef, } = control || methods.control;\r\n    const [value, setInputStateValue] = (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useState)(isUndefined(defaultValue)\r\n        ? get(defaultValuesRef.current, name)\r\n        : defaultValue);\r\n    const valueRef = (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useRef)(value);\r\n    const isCheckboxInput = isBoolean(value);\r\n    const shouldValidate = () => !skipValidation({\r\n        hasError: !!get(errors, name),\r\n        isOnBlur,\r\n        isOnSubmit,\r\n        isReValidateOnBlur,\r\n        isReValidateOnSubmit,\r\n        isSubmitted,\r\n    });\r\n    const commonTask = (target) => {\r\n        const data = getInputValue(target, isCheckboxInput);\r\n        setInputStateValue(data);\r\n        valueRef.current = data;\r\n        return data;\r\n    };\r\n    const eventWrapper = (event) => (...arg) => setValue(name, commonTask(event(arg)), shouldValidate());\r\n    const handleChange = (e) => {\r\n        const data = commonTask(e && e.target ? e.target : e);\r\n        setValue(name, data, shouldValidate());\r\n    };\r\n    const registerField = () => register(Object.defineProperty({\r\n        name,\r\n    }, VALUE, {\r\n        set(data) {\r\n            setInputStateValue(data);\r\n            valueRef.current = data;\r\n        },\r\n        get() {\r\n            return valueRef.current;\r\n        },\r\n    }), Object.assign({}, rules));\r\n    if (!fieldsRef.current[name]) {\r\n        registerField();\r\n    }\r\n    (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect)(() => {\r\n        const fieldArrayNames = fieldArrayNamesRef.current;\r\n        registerField();\r\n        return () => {\r\n            if (!isNameInFieldArray(fieldArrayNames, name)) {\r\n                unregister(name);\r\n            }\r\n        };\r\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    [name]);\r\n    const props = Object.assign(Object.assign(Object.assign(Object.assign({ name }, rest), (onChange\r\n        ? { [onChangeName]: eventWrapper(onChange) }\r\n        : { [onChangeName]: handleChange })), (isOnBlur || isReValidateOnBlur\r\n        ? { [onBlurName]: () => triggerValidation(name) }\r\n        : {})), { [valueName || (isCheckboxInput ? 'checked' : VALUE)]: value });\r\n    return (0,external_root_React_commonjs2_react_commonjs_react_amd_react_.isValidElement)(InnerComponent) ? ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.cloneElement)(InnerComponent, props)) : ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement)(InnerComponent, Object.assign({}, props)));\r\n};\n\nconst ErrorMessage = ({ as: InnerComponent, errors, name, children, }) => {\r\n    const methods = useFormContext();\r\n    const { message, types } = get(errors || methods.errors, name, {});\r\n    if (!message) {\r\n        return null;\r\n    }\r\n    const props = {\r\n        children: children ? children({ message, messages: types }) : message,\r\n    };\r\n    return InnerComponent ? ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.isValidElement)(InnerComponent) ? ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.cloneElement)(InnerComponent, props)) : ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement)(InnerComponent, Object.assign({}, props)))) : ((0,external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement)(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, Object.assign({}, props)));\r\n};\n\n\n\n;// ../src/models/Enums.ts\nvar EContactMethod;\n(function (EContactMethod) {\n    EContactMethod[\"PHONE\"] = \"Phone\";\n    EContactMethod[\"TEXT_MESSAGE\"] = \"TextMessage\";\n    EContactMethod[\"EMAIL\"] = \"Email\";\n})(EContactMethod || (EContactMethod = {}));\nvar EDuration;\n(function (EDuration) {\n    EDuration[\"AM\"] = \"AM\";\n    EDuration[\"PM\"] = \"PM\";\n    EDuration[\"Evening\"] = \"Evening\";\n    EDuration[\"AllDay\"] = \"AllDay\";\n    EDuration[\"Item0810\"] = \"Item0810\";\n    EDuration[\"Item1012\"] = \"Item1012\";\n    EDuration[\"Item1315\"] = \"Item1315\";\n    EDuration[\"Item1517\"] = \"Item1517\";\n    EDuration[\"Item1719\"] = \"Item1719\";\n    EDuration[\"Item1921\"] = \"Item1921\";\n})(EDuration || (EDuration = {}));\nvar EPreferredContactMethod;\n(function (EPreferredContactMethod) {\n    EPreferredContactMethod[\"EMAIL\"] = \"Email\";\n    EPreferredContactMethod[\"TEXT_MESSAGE\"] = \"TextMessage\";\n    EPreferredContactMethod[\"PHONE\"] = \"Phone\";\n})(EPreferredContactMethod || (EPreferredContactMethod = {}));\n\n;// ../src/utils/AppointmentUtils.ts\n\n\nfunction stripTimeBit(date) {\n    try {\n        var fragments = date.split(\"T\");\n        var newDate = new Date(fragments[0]);\n        newDate.setMinutes(new Date(date).getMinutes() + new Date(date).getTimezoneOffset());\n        newDate.setHours(0);\n        newDate.setMinutes(0);\n        return newDate;\n    }\n    catch (e) {\n        return date;\n    }\n}\nfunction mapEnum(enumerable, fn) {\n    var enumMembers = Object.keys(enumerable).map(function (key) { return enumerable[key]; });\n    return enumMembers.map(function (m) { return fn(m); });\n}\nvar noSpecialCharRegex = /^[a-zA-Z0-9]+$/i;\nvar emailRegex = /^[a-z0-9`!#\\$%&\\*\\+\\/=\\?\\^\\'\\-_]+((\\.)+[a-z0-9`!#\\$%&\\*\\+\\/=\\?\\^\\'\\-_]+)*@([a-z0-9]+([\\-][a-z0-9])*)+([\\.]([a-z0-9]+([\\-][a-z0-9])*)+)+$/i;\nvar formattedPhoneRegex = /^[0-9]\\d{2}-\\d{3}-\\d{4}$/i;\nvar getMessagesList = function (errors) {\n    var errorslist = [];\n    var action;\n    if (external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getFlowType() === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.INTERNET)\n        action = 523;\n    if (external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getFlowType() === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.BUNDLE)\n        action = 508;\n    Object.keys(errors).map(function (k) {\n        var err = errors[k];\n        errorslist.push({\n            id: err.ref.name,\n            error: err.type\n        });\n    });\n    external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackError(errorslist.map(function (err) { return ({\n        code: err.id,\n        type: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.EErrorType.Validation,\n        layer: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.EApplicationLayer.Frontend,\n        description: err.error\n    }); }), action);\n    return errorslist;\n};\nfunction autoFormat(value) {\n    var newVal = filterNumbers(value);\n    newVal = newVal.substr(0, 10);\n    return newVal.length === 10 ? newVal.slice(0, 3) + \"-\" + newVal.slice(3, 6) + \"-\" + newVal.slice(6) : newVal;\n}\nfunction filterNumbers(value) {\n    var num = value.replace(/\\D/g, \"\");\n    return num;\n}\nfunction ValueOf(val) {\n    return val;\n}\nfunction getSelectedDate(dates) {\n    var selectedDates = dates.filter(function (date) { return date.timeSlots.find(function (time) { return time.isSelected === true; }); });\n    return selectedDates.length > 0 ? selectedDates : [dates[0]];\n}\nvar getPrimaryValue = function (method, value) {\n    var _a, _b;\n    switch (method) {\n        case EContactMethod.EMAIL:\n            return value === null || value === void 0 ? void 0 : value.email;\n        case EContactMethod.PHONE:\n            return ((_a = value === null || value === void 0 ? void 0 : value.primaryPhone) === null || _a === void 0 ? void 0 : _a.phoneNumber) && autoFormat((_b = value === null || value === void 0 ? void 0 : value.primaryPhone) === null || _b === void 0 ? void 0 : _b.phoneNumber);\n        case EContactMethod.TEXT_MESSAGE:\n            return (value === null || value === void 0 ? void 0 : value.textMessage) && autoFormat(value === null || value === void 0 ? void 0 : value.textMessage);\n        default:\n            return \"\";\n    }\n};\n\n// EXTERNAL MODULE: external {\"root\":\"ReactIntl\",\"commonjs2\":\"react-intl\",\"commonjs\":\"react-intl\",\"amd\":\"react-intl\"}\nvar external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_ = __webpack_require__(\"react-intl\");\n;// ../src/views/Componenets/FormElements/Checkbox.tsx\n\n\n\n\n\nvar Checkbox = function (props) {\n    var privateProps = __assign(__assign({}, defaultProps), props);\n    var label = privateProps.label, value = privateProps.value, handleChange = privateProps.handleChange, subLabel = privateProps.subLabel, checked = privateProps.checked;\n    var register = useFormContext().register;\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexBlock flexCol-xs margin-15-bottom\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { htmlFor: \"additionalPhoneNumber\", className: \"installation-form-label\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtBold block\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: label })),\n            subLabel ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtItalic block txtNormal\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: subLabel })) : null),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol margin-5-top\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { className: \"graphical_ctrl graphical_ctrl_checkbox txtNormal\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: value + \"_LABEL\" }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"input\", { type: \"checkbox\", ref: register, id: label, name: label, defaultChecked: checked, onChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"ctrl_element chk_radius\" })))));\n};\nvar defaultProps = {\n    checked: false\n};\n\n;// ../src/views/Componenets/FormElements/RadioBtn.tsx\n\n\n\n\nvar RadioBtn = function (props) {\n    var privateProps = __assign(__assign({}, RadioBtn_defaultProps), props);\n    var label = privateProps.label, value = privateProps.value, handleChange = privateProps.handleChange, checked = privateProps.checked, requiredInput = privateProps.requiredInput;\n    var register = useFormContext().register;\n    return label ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { className: \"graphical_ctrl pointer ctrl_radioBtn margin-10-bottom\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { className: \"txtBold block\", htmlFor: \"option_\".concat(value) },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: value })),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"input\", { type: \"radio\", id: \"option_\".concat(value), ref: register({ required: requiredInput }), name: label, value: value, checked: checked, onChange: function (e) { return handleChange(e); } }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"ctrl_element\" }),\n        value === \"OTHER\" && checked === true ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"topArrow text-left otherOption\", \"aria-hidden\": \"true\" }) : null) : null;\n};\nvar RadioBtn_defaultProps = {\n    checked: false,\n    requiredInput: false\n};\n\n;// ../src/views/Componenets/FormElements/TextArea.tsx\n\n\n\n\n\nvar TextArea = function (props) {\n    var privateProps = __assign(__assign({}, TextArea_defaultProps), props);\n    var label = privateProps.label, required = privateProps.required, value = privateProps.value, subLabel = privateProps.subLabel, handleChange = privateProps.handleChange, requiredInput = privateProps.requiredInput, maxLength = privateProps.maxLength;\n    var register = useFormContext().register;\n    var _a = __read(external_root_React_commonjs2_react_commonjs_react_amd_react_.useState((maxLength || 0) - (value || \"\").length), 2), crCount = _a[0], setCount = _a[1];\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexBlock flexCol-xs margin-15-bottom\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { htmlFor: label, className: \"installation-form-label\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtBold\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: label })),\n            required ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtNormal\" }, \"(optional)\") : \"\",\n            subLabel ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtItalic block txtNormal\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: subLabel })) : null),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"textarea\", { ref: register({ required: requiredInput }), id: label, name: label, defaultValue: value, maxLength: maxLength, className: \"brf3-textarea form-control\", onChange: function (e) {\n                    setCount((maxLength || 0) - (e.currentTarget.value || \"\").length);\n                    handleChange(e);\n                } }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", null,\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: label + \"_DESCRIPTION\", values: { max: maxLength, count: crCount } })))));\n};\nvar TextArea_defaultProps = {\n    required: false,\n    requiredInput: false,\n    value: \"\",\n    subLabel: \"\"\n};\n\n;// ../src/views/Componenets/FormElements/TextInput.tsx\n\n\n\n\n\n\nvar TextInput = function (props) {\n    var privateProps = __assign(__assign({}, TextInput_defaultProps), props);\n    var label = privateProps.label, subLabel = privateProps.subLabel, handleChange = privateProps.handleChange, containerClass = privateProps.containerClass, extention = privateProps.extention, optionalExtenstion = privateProps.optionalExtenstion, requiredInput = privateProps.requiredInput, requiredPattern = privateProps.requiredPattern, value = privateProps.value, subValue = privateProps.subValue;\n    var _a = useFormContext(), register = _a.register, errors = _a.errors;\n    var getAriaLabel = function (label) {\n        switch (label) {\n            case \"TELEPHONE_FORMAT\":\n            case \"PREFERED_PHONE_FORMAT\":\n            case \"PREFERED_TEXT_MESSAGE_FORMAT\":\n            case \"Phone_FORMAT\":\n            case \"TextMessage_FORMAT\":\n                return Localization.getLocalizedString(\"TELEPHONE_FORMAT_ARIA\");\n            default:\n                return Localization.getLocalizedString(label);\n        }\n    };\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexBlock flexCol-xs margin-15-bottom flexWrap \".concat(containerClass) },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { htmlFor: \"additionalPhoneNumber\", className: \"installation-form-label \".concat(requiredInput ? \"form-required\" : \"\", \" \").concat(errors && errors[label] ? \"error\" : \"\") },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtBold block\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: label })),\n            subLabel ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtItalic block txtNormal\", \"aria-label\": getAriaLabel(subLabel) },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: subLabel })) : null),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol relative \".concat(errors && errors[label] ? \"has-error\" : \"\") },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"topArrow text-left hide\", \"aria-hidden\": \"true\" }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"input\", { type: \"text\", ref: register({ required: requiredInput, pattern: requiredPattern }), className: \"brf3-virgin-form-input form-control\", id: label, name: label, title: label, defaultValue: value, onBlur: handleChange, onChange: function (e) { return handleChange(e); } }),\n            errors && errors[label] ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"error margin-5-top\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virgin-icon icon-warning margin-15-right\", \"aria-hidden\": true },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"volt-icon path1\" }),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"volt-icon path2\" })),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtSize12\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: errors[label].type !== \"pattern\" ? \"INLINE_ERROR_required\" : \"INLINE_ERROR_\".concat(label, \"_pattern\") }))) : null),\n        extention ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol brf3-virgin-form-subInput fill-sm\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexBlock flexCol-xs\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { htmlFor: \"extension\", className: \"installation-form-label\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtBold block\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: extention })),\n                    optionalExtenstion ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtItalic block txtNormal\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"OPTIONAL_LABEL\" })) : null),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"input\", { type: \"text\", ref: register, className: \"brf3-virgin-form-input form-control\", id: extention, name: extention, title: extention, maxLength: 10, defaultValue: subValue, onBlur: handleChange, onChange: function (e) { return handleChange(e); } })))) : null));\n};\nvar TextInput_defaultProps = {\n    requiredInput: false,\n    requiredPattern: /.*/i,\n    containerClass: \"\",\n    value: \"\",\n    subValue: \"\"\n};\n\n;// ../src/views/Componenets/FormElements/Fieldset.tsx\n\n\nvar Legend = function (_a) {\n    var legend = _a.legend, required = _a.required, accessibleLegend = _a.accessibleLegend, legendAdditionalClass = _a.legendAdditionalClass;\n    return legend ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"legend\", { className: \"installation-form-label \".concat(required ? \"form-required\" : \"\", \" \").concat(accessibleLegend ? \"sr-only\" : \"\", \" \").concat(legendAdditionalClass) },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: legend })) : null;\n};\nvar Fieldset = function (_a) {\n    var className = _a.className, children = _a.children, legend = _a.legend, accessibleLegend = _a.accessibleLegend, legendAdditionalClass = _a.legendAdditionalClass, required = _a.required, additionalClass = _a.additionalClass;\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"fieldset\", { className: \"margin-15-bottom \".concat(className) }, accessibleLegend ?\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Legend, { legend: legend, required: required, accessibleLegend: accessibleLegend, legendAdditionalClass: legendAdditionalClass }),\n            children) :\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexBlock flexCol-xs \".concat(additionalClass) },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Legend, { legend: legend, required: required, accessibleLegend: accessibleLegend, legendAdditionalClass: legendAdditionalClass }),\n            children));\n};\n\n;// ../src/views/Componenets/FormElements/index.ts\n\n\n\n\n\n\n;// ../src/views/Componenets/Header/Banner.tsx\n\n\n\n\nvar EBannerIcons;\n(function (EBannerIcons) {\n    EBannerIcons[\"ERROR\"] = \"icon-warning\";\n    EBannerIcons[\"NOTE\"] = \"icon-info\";\n    EBannerIcons[\"VALIDATION\"] = \"icon-Big_check_confirm\";\n    EBannerIcons[\"INFO\"] = \"icon-BIG_WARNING\";\n})(EBannerIcons || (EBannerIcons = {}));\nvar Banner = function (props) {\n    var privateProps = __assign(__assign({}, Banner_defaultProps), props);\n    var iconType = privateProps.iconType, heading = privateProps.heading, message = privateProps.message, messages = privateProps.messages, iconSizeCSS = privateProps.iconSizeCSS;\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Container, null,\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Panel, { className: \"flexBlock pad-20-left pad-20-right pad-25-top pad-25-bottom margin-15-bottom txtBlack\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virgin-icon \".concat(iconType, \" \").concat(iconSizeCSS, \" txtSize36\"), \"aria-hidden\": true },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virgin-icon path1\" }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virgin-icon path2\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { id: \"IstallationMessageBanner\", className: \"flexCol pad-15-left content-width valign-top pad-0-xs\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"h4\", { className: \"virginUltraReg txtSize18 txtDarkGrey1 no-margin-top txtUppercase\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: heading })),\n                message ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"txtSize14 txtGray4A sans-serif no-margin\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: message })) : null,\n                messages ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"ul\", null, messages && messages.map(function (message) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"li\", { className: \"error\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"a\", { id: \"message_\".concat(message.id), href: \"#\".concat(message.id), className: \"txtRed txtBold txtUnderline\", title: message.id },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: message.id })),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtDarkGrey\" },\n                        \"\\u00A0-\\u00A0\",\n                        message.error === \"required\" ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"INLINE_ERROR_required\" }) : external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"INLINE_ERROR_\" + message.id + \"_\" + message.error }))); })) : null))));\n};\nvar Banner_defaultProps = {\n    iconType: EBannerIcons.INFO,\n    iconSizeCSS: \"txtSize36\"\n};\n\n;// ../src/views/Componenets/Header/Heading.tsx\n\n\n\n\nvar HeadingTags;\n(function (HeadingTags) {\n    HeadingTags[\"H1\"] = \"h1\";\n    HeadingTags[\"H2\"] = \"h2\";\n    HeadingTags[\"H3\"] = \"h3\";\n    HeadingTags[\"H4\"] = \"h4\";\n    HeadingTags[\"H5\"] = \"h5\";\n    HeadingTags[\"H6\"] = \"h6\";\n})(HeadingTags || (HeadingTags = {}));\nvar Heading = function (props) {\n    var privateProps = __assign(__assign({}, Heading_defaultProps), props);\n    var tag = privateProps.tag, additionalClass = privateProps.additionalClass, content = privateProps.content, description = privateProps.description;\n    var Tag = tag || \"h2\";\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Tag, { className: \"virginUltra txtBlack txtCapital noMargin \".concat(additionalClass) },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: content })),\n        description ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"spacer10 col-xs-12 clear\" }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"noMargin\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: description }))) : null));\n};\nvar Heading_defaultProps = {\n    additionalClass: \"\",\n    description: \"\"\n};\n\n;// ../src/views/Componenets/Header/Header.tsx\n\n\n\n\n\n\nvar Header = (function (_super) {\n    __extends(Header, _super);\n    function Header(props) {\n        var _this = _super.call(this, props) || this;\n        _this.headingProps = {\n            tag: HeadingTags.H2,\n            classNames: \"txtSize28 txtSize24-xs\",\n            content: \"INSTALLATION_PAGE_HEADING\",\n        };\n        return _this;\n    }\n    Header.prototype.render = function () {\n        return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Container, null,\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.BRF3Container, null,\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"spacer5 flex col-12\" }),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Heading, __assign({}, this.headingProps)),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"spacer25 flex col-12 clear\" }))),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Banner, { iconType: EBannerIcons.INFO, heading: \"INSTALLATION_HEADING\", message: \"INSTALLATION_MESSAGE\" }),\n            Object.keys(this.props.errors).length ? (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Banner, { iconType: EBannerIcons.ERROR, heading: \"ERRORS_HEADING\", messages: getMessagesList(this.props.errors) })) : null));\n    };\n    return Header;\n}(external_root_React_commonjs2_react_commonjs_react_amd_react_.PureComponent));\n\n\n;// ../src/views/Componenets/Header/index.ts\n\n\n\n\n;// ../src/views/Componenets/ContactInformation/ContactInformation.tsx\n\n\n\n\n\n\n\n\nvar ContactInformation = function () {\n    var _a, _b;\n    var contactInformation = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.useSelector)(function (state) { return state === null || state === void 0 ? void 0 : state.contactInformation; });\n    var additionalDetails = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.useSelector)(function (state) { return state === null || state === void 0 ? void 0 : state.additionalDetails; });\n    var _c = __read(external_root_React_commonjs2_react_commonjs_react_amd_react_.useState(EContactMethod.PHONE), 2), contactMethod = _c[0], setContactMethod = _c[1];\n    var setValue = useFormContext().setValue;\n    var handleChange = function (e) {\n        var _a = e.target, value = _a.value, name = _a.name;\n        switch (value) {\n            case EContactMethod.PHONE:\n            case EContactMethod.EMAIL:\n            case EContactMethod.TEXT_MESSAGE:\n                setContactMethod(value);\n                break;\n            default:\n                break;\n        }\n        switch (name) {\n            case EContactMethod.PHONE + \"_LABEL\":\n            case EContactMethod.TEXT_MESSAGE + \"_LABEL\":\n            case \"ADDITIONAL_PHONE_NUMBER\":\n                setValue(name, autoFormat(value), true);\n                break;\n            case EContactMethod.PHONE + \"_EXT\":\n            case \"ADDITIONAL_PHONE_EXT\":\n                setValue(name, filterNumbers(value), true);\n                break;\n            case \"SUPERINTENDANT_PHONE\":\n                setValue(name, autoFormat(value), true);\n                break;\n            case EContactMethod.EMAIL + \"_LABEL\":\n                setValue(name, value, true);\n                break;\n            default:\n                break;\n        }\n    };\n    external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect(function () {\n        setContactMethod((contactInformation === null || contactInformation === void 0 ? void 0 : contactInformation.preferredContactMethod) ? contactInformation.preferredContactMethod : EContactMethod.PHONE);\n    }, [contactInformation]);\n    var headingProps = {\n        tag: HeadingTags.H2,\n        additionalClass: \"txtSize22 txtSize24-xs\",\n        content: \"CONTACT_INFORMATION\"\n    };\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"margin-30-bottom\", id: \"section2\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Heading, __assign({}, headingProps)),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"spacer10 visible-xs\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"pad-25-top no-pad-xs\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Fieldset, { legend: \"PREFERED_METHOD_OF_CONTACT\", required: true, additionalClass: \"flexWrap\", accessibleLegend: false },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol lineHeight18\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer15 visible-xs\" }),\n                    mapEnum(EContactMethod, function (item) {\n                        return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(RadioBtn, { label: \"PREFERED_METHOD_OF_CONTACT\", value: item, handleChange: handleChange, checked: item === contactMethod });\n                    })),\n                mapEnum(EContactMethod, function (item) {\n                    var _a;\n                    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextInput, { requiredInput: contactMethod === item, label: item + \"_LABEL\", containerClass: \"sub-option flex-wrap \".concat(item === contactMethod ? \"show\" : \"hide\"), subLabel: item + \"_FORMAT\", extention: item === EContactMethod.PHONE ? item + \"_EXT\" : false, optionalExtenstion: true, requiredPattern: item === EContactMethod.EMAIL ? emailRegex : formattedPhoneRegex, value: getPrimaryValue(item, contactInformation), subValue: (_a = contactInformation === null || contactInformation === void 0 ? void 0 : contactInformation.primaryPhone) === null || _a === void 0 ? void 0 : _a.phoneExtension, handleChange: function (e) { return handleChange(e); } });\n                })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Fieldset, { legend: \"ADDITIONAL_PHONE_NUMBER\", required: false, accessibleLegend: true },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextInput, { requiredInput: false, label: \"ADDITIONAL_PHONE_NUMBER\", subLabel: \"TELEPHONE_FORMAT\", extention: \"ADDITIONAL_PHONE_EXT\", optionalExtenstion: true, requiredPattern: formattedPhoneRegex, value: (_a = contactInformation === null || contactInformation === void 0 ? void 0 : contactInformation.additionalPhone) === null || _a === void 0 ? void 0 : _a.phoneNumber, subValue: (_b = contactInformation === null || contactInformation === void 0 ? void 0 : contactInformation.additionalPhone) === null || _b === void 0 ? void 0 : _b.phoneExtension, handleChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextInput, { label: \"APPARTMENT\", value: additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.apartment, handleChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextInput, { label: \"ENTRY_CODE\", value: additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.entryCode, handleChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextInput, { label: \"SUPERINTENDANT_NAME\", value: additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.superintendantName, handleChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextInput, { label: \"SUPERINTENDANT_PHONE\", requiredPattern: formattedPhoneRegex, value: additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.superintendantPhone, handleChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Checkbox, { label: \"INFORMED_SUPERINTENDANT\", value: \"YES\", checked: additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.informedSuperintendant, handleChange: function (e) { return handleChange(e); } }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TextArea, { label: \"SPECIAL_INSTRUCTIONS\", subLabel: \"SPECIAL_INSTRUCTIONS_SUBLABEL\", value: additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.specialInstructions, maxLength: 200, handleChange: function (e) { return handleChange(e); } })))));\n};\n\n;// ../src/views/Componenets/ContactInformation/index.ts\n\n\n;// ../src/views/Componenets/Installation/DateAndTime/DateAndTime.tsx\n\n\n\nvar DateAndTime = external_root_React_commonjs2_react_commonjs_react_amd_react_.memo(function (props) {\n    var _a;\n    var handleChange = props.handleChange, preferredDate = props.preferredDate, checked = props.checked, register = useFormContext().register;\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { id: \"dateAndTime\" + preferredDate.date, className: \"graphical_ctrl pointer ctrl_radioBtn margin-10-bottom\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"input\", { type: \"radio\", ref: register({ required: true }), id: \"timeOption\" + preferredDate.date, name: \"dateAndTime\", value: JSON.stringify(preferredDate), onChange: function (e) { return handleChange(e); }, checked: checked.date === preferredDate.date }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { className: \"block no-margin\", htmlFor: \"timeOption\" + preferredDate.date }, Boolean(preferredDate.date) ?\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedDate, { value: preferredDate.date, year: \"numeric\", weekday: \"long\", month: \"long\", day: \"2-digit\", timeZone: \"UTC\" }) :\n                \"No Appointment Details\"),\n            Boolean(preferredDate.timeSlots.length) ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtNormal block\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: (_a = preferredDate.timeSlots.find(function (item) { return item.isAvailable; })) === null || _a === void 0 ? void 0 : _a.intervalType })) : null,\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"ctrl_element\" })));\n});\n\n;// ../src/views/Componenets/Installation/DateAndTime/TimeSlots.tsx\n\n\n\n\n\n\nvar TimeSlots = (function (_super) {\n    __extends(TimeSlots, _super);\n    function TimeSlots(props) {\n        return _super.call(this, props) || this;\n    }\n    TimeSlots.prototype.componentDidMount = function () {\n        this.props.initSlickSlider();\n    };\n    TimeSlots.prototype.render = function () {\n        var _a = this.props, availableDates = _a.availableDates, selectDate = _a.selectDate, selectedDateTime = _a.selectedDateTime;\n        return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexBlock margin-15-bottom sub-option relative timeslot-picker\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"select-timeslot fill\" }, availableDates && availableDates.map(function (day, dayIndex) {\n                return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: day.timeSlots[0].intervalType === EDuration.AllDay ? \"allDayContainer\" : \"day-container\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"label\", { htmlFor: \"dayIndex_\" + dayIndex, className: \"virginUltra sans-serif-xs txtBold-xs txtSize16 txtBlack lineHeight1-3 margin-15-bottom\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedDate, { value: stripTimeBit(day.date), weekday: \"long\", timeZone: \"UTC\" }),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"br\", { className: \"hidden-m\" }),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"d-sm-none d-md-none d-lg-none d-xl-none\" }, \", \"),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedDate, { value: stripTimeBit(day.date), year: \"numeric\", month: \"short\", day: \"2-digit\", timeZone: \"UTC\" })),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"ul\", { className: \"noMargin list-unstyled timeItem\", \"aria-labelledby\": \"mondayList\" }, day.timeSlots.map(function (timeSlot) {\n                            var selectedInterval = selectedDateTime.timeSlots[0].intervalType === timeSlot.intervalType && selectedDateTime.date === day.date;\n                            return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"li\", { className: \"txtBlue \".concat(selectedInterval ? \"selected\" : \"\") },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"slot_\".concat(timeSlot.intervalType), onClick: function (e) { return selectDate(e, day.date, timeSlot); }, className: \"btn btn-link \".concat(timeSlot.intervalType === EDuration.AllDay ? \"flexCol flexJustify\" : \"\", \" \").concat(timeSlot.isAvailable ? \"\" : \"disabled\", \" \").concat(timeSlot.isSelected ? \"selected\" : \"\"), tabIndex: 0 },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: timeSlot.intervalType })));\n                        }))));\n            })));\n    };\n    TimeSlots.displayName = \"TimeSlots\";\n    return TimeSlots;\n}(external_root_React_commonjs2_react_commonjs_react_amd_react_.Component));\n\n\n;// ../src/views/Componenets/Installation/DateAndTime/index.ts\n\n\n\n;// ../src/views/Componenets/Installation/Installation.tsx\n\n\n\n\n\n\n\n\nvar Visible = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible;\nvar Component = (function (_super) {\n    __extends(Component, _super);\n    function Component(props) {\n        var _this = _super.call(this, props) || this;\n        _this.handleChange = function (e) {\n            var value = e.target.value;\n            switch (value) {\n                case \"OTHER\":\n                    _this.setState({\n                        showTimeSlots: true,\n                    });\n                    break;\n                default:\n                    _this.setState({\n                        showTimeSlots: false,\n                        selectedDateTime: JSON.parse(value)\n                    });\n                    break;\n            }\n        };\n        _this.selectDate = function (e, day, interval) {\n            e.preventDefault();\n            var newPreferedDates = __spreadArray([], __read(_this.state.preferredDates), false);\n            if (_this.state.preferredDates[0].date === day &&\n                _this.state.preferredDates[0].timeSlots[0].intervalType === interval.intervalType) {\n                _this.setState({\n                    preferredDates: _this.state.preferredDates,\n                    selectedDateTime: _this.state.preferredDates[0],\n                    showTimeSlots: false,\n                    showOther: false\n                });\n            }\n            else {\n                newPreferedDates[1] = { date: day, timeSlots: [__assign(__assign({}, interval), { isSelected: true })] };\n                _this.setState({\n                    preferredDates: newPreferedDates,\n                    selectedDateTime: newPreferedDates[1],\n                    showTimeSlots: false,\n                    showOther: false\n                });\n            }\n        };\n        _this.changeBtn = function (e) {\n            e.preventDefault();\n            _this.setState({\n                showOther: true,\n                showTimeSlots: true,\n                preferredDates: [_this.state.preferredDates[0]]\n            });\n        };\n        _this.state = {\n            showTimeSlots: false,\n            selectedDateTime: null,\n            preferredDates: [],\n            showOther: true\n        };\n        _this.handleChange.bind(_this);\n        _this.changeBtn.bind(_this);\n        return _this;\n    }\n    Component.prototype.componentDidUpdate = function (props) {\n        if (this.props.availableDates && this.props.availableDates.length && JSON.stringify(this.props.availableDates) !== JSON.stringify(props.availableDates)) {\n            var selectedDate = getSelectedDate(this.props.availableDates);\n            this.setState({\n                preferredDates: selectedDate,\n                selectedDateTime: selectedDate[0].date ? selectedDate[0] : null,\n                showOther: selectedDate.length > 1 ? false : true\n            });\n        }\n    };\n    Component.prototype.render = function () {\n        var _this = this;\n        var _a = this.props, installationAddress = _a.installationAddress, availableDates = _a.availableDates, initSlickSlider = _a.initSlickSlider;\n        var _b = this.state, showTimeSlots = _b.showTimeSlots, selectedDateTime = _b.selectedDateTime, showOther = _b.showOther, preferredDates = _b.preferredDates;\n        var headingProps = {\n            tag: HeadingTags.H2,\n            additionalClass: \"txtSize22 txtSize24-xs\",\n            content: \"INSTALLATION_DETAILS\",\n            description: \"INSTALLATION_DETAILS_DESC\"\n        };\n        return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"margin-30-bottom\", id: \"section1\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Heading, __assign({}, headingProps)),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"spacer10 flex col-12 clear\" }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"noMargin txtItalic\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"REQUIRED_INFO_FLAG\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"pad-15-top\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Fieldset, { legend: \"DATE_AND_TIME_LABEL\", required: true, accessibleLegend: false, additionalClass: \"flex-wrap\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer10 visible-xs\" }),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol lineHeight18\" },\n                        preferredDates && preferredDates.length && preferredDates.map(function (date) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(DateAndTime, { handleChange: _this.handleChange, preferredDate: date, checked: showTimeSlots || selectedDateTime }); }),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: showOther, placeholder: external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"pad-35-left relative changeBtn\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"CHANGE_BTN\", className: \"btn btn-link pad-0 txtSize14 txtUnderline txtVirginBlue\", onClick: function (e) { return _this.changeBtn(e); } }, \"Change\")) },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(RadioBtn, { handleChange: this.handleChange, requiredInput: true, checked: showTimeSlots, label: \"dateAndTime\", value: \"OTHER\" }))),\n                    showTimeSlots ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TimeSlots, { selectDate: this.selectDate, availableDates: availableDates, initSlickSlider: initSlickSlider, selectedDateTime: selectedDateTime }) : null),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: Boolean(selectedDateTime) }, selectedDateTime && selectedDateTime !== \"OTHER\" ?\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Fieldset, { legend: \"ESTIMATED_DURATION\", required: false, accessibleLegend: false },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"block\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: selectedDateTime.timeSlots[0].duration })),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"block\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"ARRIVAL_OF_TECHNICIAN\" })))) : null),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: Boolean(installationAddress) },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Fieldset, { legend: \"SHIPPING_INSTALLATION_ADDRESS\", required: false, accessibleLegend: false },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexCol\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"block\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"apartmentNumber\", false) },\n                                    (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"apartmentNumber\", \"\"),\n                                    \"\\u00A0-\\u00A0\"),\n                                (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"address1\", \"\"),\n                                \"\\u00A0\",\n                                (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"address2\", \"\"),\n                                \"\\u00A0\",\n                                (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"streetType\", \"\"),\n                                \",\\u00A0\",\n                                (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"city\", \"\"),\n                                \",\\u00A0\",\n                                (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"province\", \"\"),\n                                \",\\u00A0\",\n                                (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(installationAddress, \"postalCode\", \"\")),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"margin-10-top\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: \"CONTACT_US_NOTE\" }))))))));\n    };\n    return Component;\n}(external_root_React_commonjs2_react_commonjs_react_amd_react_.Component));\n\n\n;// ../src/views/Componenets/Installation/index.ts\n\n\n\nvar Installation = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {\n    var installationAddress = _a.installationAddress, availableDates = _a.availableDates, duration = _a.duration;\n    return ({ installationAddress: installationAddress, availableDates: availableDates, duration: duration });\n}, function (dispatch) { return ({\n    initSlickSlider: function () { return dispatch(initSlickSlider()); }\n}); })(Component);\n\n;// ../src/views/Componenets/index.ts\n\n\n\n\n\n;// ../src/views/Form.tsx\n\n\n\n\n\n\nvar _submitForm = null;\nvar Form = function (props) {\n    var submitRef = external_root_React_commonjs2_react_commonjs_react_amd_react_.useRef(null);\n    var handleSubmit = useFormContext().handleSubmit;\n    var dispatch = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.useDispatch)();\n    _submitForm = function () {\n        submitRef.current.click();\n    };\n    var customSubmit = function (e) { return __awaiter(void 0, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            e.preventDefault();\n            handleSubmit(function (data) {\n                dispatch(setAppointment(data));\n            })(e);\n            return [2];\n        });\n    }); };\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"form\", { id: \"AppointmentForm\", onSubmit: customSubmit },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer45 hidden-m\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer20 d-block d-sm-none\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Installation, null),\n        \" \",\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(ContactInformation, null),\n        \" \",\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { ref: submitRef, type: \"submit\", \"aria-hidden\": \"true\", style: { display: \"none\" } })));\n};\nForm.useSubmitRef = function () { return _submitForm; };\n/* harmony default export */ var views_Form = (Form);\n\n;// ../src/Pipe.ts\n\n\n\n\nvar BasePipe = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BasePipe;\nvar Pipe = (function (_super) {\n    __extends(Pipe, _super);\n    function Pipe(arg) {\n        var _this = _super.call(this, arg) || this;\n        Pipe.instance = _this;\n        return _this;\n    }\n    Pipe.Subscriptions = function (store) {\n        var _a;\n        return _a = {},\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.onContinue.toString()] = function (_a) {\n                var ref = views_Form.useSubmitRef();\n                ref && ref();\n                store.dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.RENDERED));\n            },\n            _a;\n    };\n    return Pipe;\n}(BasePipe));\n\n\n;// ../src/views/index.tsx\n\n\n\n\n\n\nvar RestrictionModal = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.RestrictionModal;\nvar widgetRenderComplete = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.widgetRenderComplete;\nvar Application = function (props) {\n    var dispatch = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.useDispatch)();\n    var errors = useFormContext().errors;\n    external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect(function () {\n        dispatch(widgetRenderComplete(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.APPOINTMENT));\n    }, []);\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"main\", { id: \"mainContent\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"flex spacer30 col-12\", \"aria-hidden\": \"true\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(RestrictionModal, { id: \"APPOINTMENT_RESTRICTION_MODAL\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Header, { errors: errors }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Container, null,\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Panel, { className: \"pad-25-left pad-25-right clearfix\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(views_Form, null))));\n};\n\n;// ../src/App.tsx\n\n\n\n\n\nvar ApplicationRoot = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.ApplicationRoot;\nvar App = function (props) {\n    var methods = useForm();\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(ApplicationRoot, null,\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(FormContext, __assign({}, methods),\n            \" \",\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Application, null))));\n};\n\n;// ../src/Widget.tsx\n\n\n\n\n\n\n\n\n\nvar setWidgetProps = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetProps, Widget_setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus;\nvar StoreProvider = external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.Provider;\nvar WidgetContainer = (function (_super) {\n    __extends(WidgetContainer, _super);\n    function WidgetContainer(store, params, config, pipe) {\n        var _this = _super.call(this) || this;\n        _this.store = store;\n        _this.params = params;\n        _this.config = config;\n        _this.pipe = pipe;\n        return _this;\n    }\n    WidgetContainer.prototype.init = function () {\n        this.pipe.subscribe(Pipe.Subscriptions(this.store));\n        this.store.dispatch(setWidgetProps(this.config));\n        this.store.dispatch(setWidgetProps(this.params.props));\n        this.store.dispatch(Widget_setWidgetStatus(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT));\n    };\n    WidgetContainer.prototype.destroy = function () {\n        this.pipe.unsubscribe();\n        this.store.destroy();\n    };\n    WidgetContainer.prototype.render = function (root) {\n        var store = this.store;\n        root.render(external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ContextProvider, { value: { config: this.config } },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(StoreProvider, { store: store },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(App, null))));\n    };\n    WidgetContainer = __decorate([\n        (0,external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Widget)({ namespace: \"Ordering\" }),\n        __metadata(\"design:paramtypes\", [Store, external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ParamsProvider, Config, Pipe])\n    ], WidgetContainer);\n    return WidgetContainer;\n}(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ViewWidget));\n/* harmony default export */ var Widget = (WidgetContainer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vc3JjL1dpZGdldC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxTQUFTLGdCQUFnQixzQ0FBc0Msa0JBQWtCO0FBQ2pGLHdCQUF3QjtBQUN4QjtBQUNBOztBQUVPO0FBQ1A7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7O0FBRU87QUFDUDtBQUNBLCtDQUErQyxPQUFPO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsY0FBYztBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVPO0FBQ1A7QUFDQTtBQUNBLDJDQUEyQyxRQUFRO0FBQ25EO0FBQ0E7O0FBRU87QUFDUCxrQ0FBa0M7QUFDbEM7O0FBRU87QUFDUCx1QkFBdUIsdUZBQXVGO0FBQzlHO0FBQ0E7QUFDQSx5R0FBeUc7QUFDekc7QUFDQSxzQ0FBc0MsUUFBUTtBQUM5QztBQUNBLGdFQUFnRTtBQUNoRTtBQUNBLDhDQUE4Qyx5RkFBeUY7QUFDdkksOERBQThELDJDQUEyQztBQUN6RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRU87QUFDUDtBQUNBLGtCQUFrQix5QkFBeUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7O0FBRU87QUFDUDtBQUNBOztBQUVPO0FBQ1A7QUFDQSw0Q0FBNEMseUVBQXlFO0FBQ3JIOztBQUVPO0FBQ1A7QUFDQTs7QUFFTztBQUNQLDBCQUEwQiwrREFBK0QsaUJBQWlCO0FBQzFHO0FBQ0Esa0NBQWtDLE1BQU0sK0JBQStCLFlBQVk7QUFDbkYsaUNBQWlDLE1BQU0sbUNBQW1DLFlBQVk7QUFDdEYsOEJBQThCO0FBQzlCO0FBQ0EsR0FBRztBQUNIOztBQUVPO0FBQ1AsWUFBWSw2QkFBNkIsMEJBQTBCLGNBQWMscUJBQXFCO0FBQ3RHLDJJQUEySSxjQUFjO0FBQ3pKLHFCQUFxQixzQkFBc0I7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDO0FBQ3RDLGlDQUFpQyxTQUFTO0FBQzFDLGlDQUFpQyxXQUFXLFVBQVU7QUFDdEQsd0NBQXdDLGNBQWM7QUFDdEQ7QUFDQSw0R0FBNEcsT0FBTztBQUNuSCwrRUFBK0UsaUJBQWlCO0FBQ2hHLHVEQUF1RCxnQkFBZ0IsUUFBUTtBQUMvRSw2Q0FBNkMsZ0JBQWdCLGdCQUFnQjtBQUM3RTtBQUNBLGdDQUFnQztBQUNoQztBQUNBO0FBQ0EsUUFBUSxZQUFZLGFBQWEsU0FBUyxVQUFVO0FBQ3BELGtDQUFrQyxTQUFTO0FBQzNDO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxlQUFlLG9DQUFvQztBQUNuRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDOztBQUVNO0FBQ1A7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsTUFBTTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7O0FBRUE7QUFDTztBQUNQLDJCQUEyQixzQkFBc0I7QUFDakQ7QUFDQTtBQUNBOztBQUVBO0FBQ087QUFDUCxnREFBZ0QsUUFBUTtBQUN4RCx1Q0FBdUMsUUFBUTtBQUMvQyx1REFBdUQsUUFBUTtBQUMvRDtBQUNBO0FBQ0E7O0FBRU87QUFDUCwyRUFBMkUsT0FBTztBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0Esd01BQXdNLGNBQWM7QUFDdE4sNEJBQTRCLHNCQUFzQjtBQUNsRCx3QkFBd0IsWUFBWSxzQkFBc0IscUNBQXFDLDJDQUEyQyxNQUFNO0FBQ2hKLDBCQUEwQixNQUFNLGlCQUFpQixZQUFZO0FBQzdELHFCQUFxQjtBQUNyQiw0QkFBNEI7QUFDNUIsMkJBQTJCO0FBQzNCLDBCQUEwQjtBQUMxQjs7QUFFTztBQUNQO0FBQ0EsZUFBZSw2Q0FBNkMsVUFBVSxzREFBc0QsY0FBYztBQUMxSSx3QkFBd0IsNkJBQTZCLG9CQUFvQix1Q0FBdUMsa0JBQWtCO0FBQ2xJOztBQUVPO0FBQ1A7QUFDQTtBQUNBLHlHQUF5Ryx1RkFBdUYsY0FBYztBQUM5TSxxQkFBcUIsOEJBQThCLGdEQUFnRCx3REFBd0Q7QUFDM0osMkNBQTJDLHNDQUFzQyxVQUFVLG1CQUFtQixJQUFJO0FBQ2xIOztBQUVPO0FBQ1AsK0JBQStCLHVDQUF1QyxZQUFZLEtBQUssT0FBTztBQUM5RjtBQUNBOztBQUVBO0FBQ0Esd0NBQXdDLDRCQUE0QjtBQUNwRSxDQUFDO0FBQ0Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVPO0FBQ1A7QUFDQTtBQUNBLHFEQUFxRCxjQUFjO0FBQ25FO0FBQ0E7QUFDQTs7QUFFTztBQUNQLDJDQUEyQztBQUMzQzs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBOztBQUVPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLE1BQU0sb0JBQW9CLFlBQVk7QUFDNUUscUJBQXFCLDhDQUE4QztBQUNuRTtBQUNBO0FBQ0EscUJBQXFCLGFBQWE7QUFDbEM7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVGQUF1RixTQUFTLGdCQUFnQjtBQUNoSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBOztBQUVBLDhDQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUNoWm1EO0FBSzlDLElBQU0sY0FBYyxHQUFHLDZHQUFZLENBQUMsbUJBQW1CLENBQUMsQ0FBQztBQUN6RCxJQUFNLGNBQWMsR0FBRyw2R0FBWSxDQUFDLGlCQUFpQixDQUFDLENBQUM7QUFDdkQsSUFBTSxjQUFjLEdBQUcsNkdBQVksQ0FBTSxpQkFBaUIsQ0FBa0MsQ0FBQztBQUM3RixJQUFNLGlCQUFpQixHQUFHLDZHQUFZLENBQU0scUJBQXFCLENBQWtDLENBQUM7QUFFcEcsSUFBTSxrQkFBa0IsR0FBRyw2R0FBWSxDQUFNLGtCQUFrQixDQUFrQyxDQUFDO0FBQ2xHLElBQU0sV0FBVyxHQUFHLDZHQUFZLENBQU0sY0FBYyxDQUFrQyxDQUFDO0FBQ3ZGLElBQU0sc0JBQXNCLEdBQUcsNkdBQVksQ0FBTSwwQkFBMEIsQ0FBa0MsQ0FBQztBQUM5RyxJQUFNLG9CQUFvQixHQUFHLDZHQUFZLENBQU0sd0JBQXdCLENBQWtDLENBQUM7QUFDMUcsSUFBTSx5QkFBeUIsR0FBRyw2R0FBWSxDQUFNLDJCQUEyQixDQUFrQyxDQUFDO0FBQ2xILElBQU0sWUFBWSxHQUFHLDZHQUFZLENBQU0saUJBQWlCLENBQWtDLENBQUM7QUFPM0YsSUFBTSxlQUFlLEdBQUcsNkdBQVksQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDOzs7Ozs7QUN0QmY7QUFHMUMsY0FBVSxHQUFxQix3RUFBYyxXQUFuQyxFQUFFLGNBQWMsR0FBSyx3RUFBYyxlQUFuQixDQUFvQjtBQW9CdEQ7SUFBNEIsMEJBQXNCO0lBQWxEOztJQUtBLENBQUM7SUFKcUI7UUFBbkIsY0FBYyxDQUFDLEVBQUUsQ0FBQzs7MkNBQWM7SUFDYjtRQUFuQixjQUFjLENBQUMsRUFBRSxDQUFDOzt3REFBMkI7SUFDMUI7UUFBbkIsY0FBYyxDQUFDLEVBQUUsQ0FBQzs7NENBQWU7SUFDK0U7UUFBaEgsY0FBYyxDQUFDLEVBQUMsSUFBSSxFQUFFLHVCQUF1QixFQUFFLGVBQWUsRUFBRSxHQUFHLEVBQUUsY0FBYyxFQUFFLEdBQUcsRUFBRSxjQUFjLEVBQUUsR0FBRyxFQUFDLENBQUM7O3VDQUFjO0lBSm5ILE1BQU07UUFEbEIsb0VBQVU7T0FDRSxNQUFNLENBS2xCO0lBQUQsYUFBQztDQUFBLENBTDJCLFVBQVUsR0FLckM7QUFMa0I7Ozs7QUN2QjZCO0FBQ1U7QUFFeEI7QUFVbEM7SUFBNEIsMEJBQVU7SUFDcEMsZ0JBQVksVUFBd0IsRUFBRSxNQUFjO1FBQ2xELGFBQUssWUFBQyxVQUFVLEVBQUUsTUFBTSxDQUFDLFNBQUM7SUFDNUIsQ0FBQztJQUhVLE1BQU07UUFEbEIsb0VBQVU7eUNBRWUsc0VBQVksRUFBVSxNQUFNO09BRHpDLE1BQU0sQ0FJbEI7SUFBRCxhQUFDO0NBQUEsQ0FKMkIsa0tBQVUsR0FJckM7QUFKa0I7Ozs7Ozs7Ozs7OztBSU9aLElBQU0sT0FBTyxHQUFHO0lBQ3JCLGNBQWMsRUFBRSxJQUFJO0lBQ3BCLFFBQVEsRUFBRSxFQUFFO0lBQ1osbUJBQW1CLEVBQUU7UUFDbkIsUUFBUSxFQUFFLEVBQUU7UUFDWixRQUFRLEVBQUUsRUFBRTtRQUNaLElBQUksRUFBRSxFQUFFO1FBQ1IsUUFBUSxFQUFFLEVBQUU7UUFDWixVQUFVLEVBQUUsRUFBRTtRQUNkLGFBQWEsRUFBRSxFQUFFO1FBQ2pCLGVBQWUsRUFBRSxFQUFFO0tBQ3BCO0lBQ0Qsa0JBQWtCLEVBQUU7UUFDbEIsc0JBQXNCLEVBQUUsRUFBRTtRQUMxQixZQUFZLEVBQUU7WUFDWixXQUFXLEVBQUUsRUFBRTtZQUNmLGNBQWMsRUFBRSxFQUFFO1NBQ25CO1FBQ0QsWUFBWSxFQUFFLElBQUk7UUFDbEIsZUFBZSxFQUFFO1lBQ2YsV0FBVyxFQUFFLEVBQUU7WUFDZixjQUFjLEVBQUUsRUFBRTtTQUNuQjtRQUNELFdBQVcsRUFBRSxFQUFFO1FBQ2YsS0FBSyxFQUFFLEVBQUU7S0FDVjtJQUNELGlCQUFpQixFQUFFO1FBQ2pCLFNBQVMsRUFBRSxFQUFFO1FBQ2IsU0FBUyxFQUFFLEVBQUU7UUFDYixtQkFBbUIsRUFBRSxFQUFFO1FBQ3ZCLGtCQUFrQixFQUFFLEVBQUU7UUFDdEIsbUJBQW1CLEVBQUUsRUFBRTtRQUN2QixzQkFBc0IsRUFBRSxJQUFJO0tBQzdCO0lBQ0Qsc0JBQXNCLEVBQUUsSUFBSTtDQUM3QixDQUFDO0FBRUY7SUFBQTtJQTJDQSxDQUFDO0lBMUNlLHFCQUFNLEdBQXBCLFVBQXFCLE9BQXdCLEVBQUUsT0FBdUIsRUFBRSxLQUFrQjtRQU14RixPQUFPLENBQUMsbUJBQW1CLENBQUMsUUFBUSxHQUFHLEtBQUssQ0FBQyxtQkFBbUIsSUFBSSxLQUFLLENBQUMsbUJBQW1CLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsbUJBQW1CLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUM7UUFDakosT0FBTyxDQUFDLG1CQUFtQixDQUFDLFFBQVEsR0FBRyxLQUFLLENBQUMsbUJBQW1CLElBQUksS0FBSyxDQUFDLG1CQUFtQixDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLG1CQUFtQixDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDO1FBQ2pKLE9BQU8sQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLEdBQUcsS0FBSyxDQUFDLG1CQUFtQixJQUFJLEtBQUssQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztRQUNySSxPQUFPLENBQUMsbUJBQW1CLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQyxtQkFBbUIsSUFBSSxLQUFLLENBQUMsbUJBQW1CLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsbUJBQW1CLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUM7UUFDdkosT0FBTyxDQUFDLG1CQUFtQixDQUFDLFFBQVEsR0FBRyxLQUFLLENBQUMsbUJBQW1CLElBQUksS0FBSyxDQUFDLG1CQUFtQixDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLG1CQUFtQixDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDO1FBQ2pKLE9BQU8sQ0FBQyxtQkFBbUIsQ0FBQyxhQUFhLEdBQUcsS0FBSyxDQUFDLG1CQUFtQixJQUFJLEtBQUssQ0FBQyxtQkFBbUIsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxtQkFBbUIsQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztRQUMzSixPQUFPLENBQUMsbUJBQW1CLENBQUMsZUFBZSxHQUFHLEtBQUssQ0FBQyxtQkFBbUIsSUFBSSxLQUFLLENBQUMsbUJBQW1CLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsbUJBQW1CLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUM7UUFFL0osT0FBTyxDQUFDLHNCQUFzQixHQUFHLEtBQUssQ0FBQyxzQkFBNkIsQ0FBQztRQUdyRSxPQUFPLENBQUMsUUFBUSxHQUFHLEtBQUssQ0FBQyxRQUFrQixDQUFDO1FBRzVDLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBQyxZQUFZLENBQUMsV0FBVyxHQUFHLE9BQU8sQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztRQUNyRyxPQUFPLENBQUMsa0JBQWtCLENBQUMsWUFBWSxDQUFDLGNBQWMsR0FBRyxPQUFPLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUM7UUFDcEcsT0FBTyxDQUFDLGtCQUFrQixDQUFDLGVBQWUsQ0FBQyxXQUFXLEdBQUcsT0FBTyxDQUFDLHVCQUF1QixDQUFDO1FBQ3pGLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBQyxlQUFlLENBQUMsY0FBYyxHQUFHLE9BQU8sQ0FBQyxvQkFBb0IsQ0FBQztRQUN6RixPQUFPLENBQUMsa0JBQWtCLENBQUMsc0JBQXNCLEdBQUcsT0FBTyxDQUFDLDBCQUEwQixDQUFDO1FBQ3ZGLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBQyxLQUFLLEdBQUcsT0FBTyxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDO1FBQ2xGLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBQyxXQUFXLEdBQUcsT0FBTyxDQUFDLGlCQUFpQixDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsaUJBQXdCLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztRQUczRyxPQUFPLENBQUMsY0FBYyxHQUFHLG9CQUFvQixDQUFDLEtBQUssQ0FBQyxjQUFjLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsV0FBVyxDQUFDLENBQVEsQ0FBQztRQUc1RyxPQUFPLENBQUMsaUJBQWlCLENBQUMsU0FBUyxHQUFHLE9BQU8sQ0FBQyxVQUFVLENBQUM7UUFDekQsT0FBTyxDQUFDLGlCQUFpQixDQUFDLFNBQVMsR0FBRyxPQUFPLENBQUMsVUFBVSxDQUFDO1FBQ3pELE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxzQkFBc0IsR0FBRyxPQUFPLENBQUMsdUJBQThCLENBQUM7UUFDMUYsT0FBTyxDQUFDLGlCQUFpQixDQUFDLG1CQUFtQixHQUFHLE9BQU8sQ0FBQyxvQkFBb0IsQ0FBQztRQUM3RSxPQUFPLENBQUMsaUJBQWlCLENBQUMsa0JBQWtCLEdBQUcsT0FBTyxDQUFDLG1CQUFtQixDQUFDO1FBQzNFLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxtQkFBbUIsR0FBRyxPQUFPLENBQUMsb0JBQW9CLENBQUM7UUFHN0UsT0FBTyxPQUFPLENBQUM7SUFDakIsQ0FBQztJQUNILHFCQUFDO0FBQUQsQ0FBQzs7QUFFRCxTQUFTLG9CQUFvQixDQUFDLEtBQXlDLEVBQUUsWUFBNkI7SUFFcEcsS0FBSyxhQUFMLEtBQUssdUJBQUwsS0FBSyxDQUFFLE9BQU8sQ0FBQyxjQUFJO1FBRWpCLElBQUksQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLGNBQUksSUFBSSxXQUFJLENBQUMsVUFBVSxHQUFHLEtBQUssRUFBdkIsQ0FBdUIsQ0FBQyxDQUFDO0lBQzFELENBQUMsQ0FBQyxDQUFDO0lBRUgsS0FBSyxhQUFMLEtBQUssdUJBQUwsS0FBSyxDQUFFLE9BQU8sQ0FBQyxjQUFJLElBQUksUUFFckIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsY0FBSSxJQUFJLFdBQUksQ0FBQyxVQUFVLEdBQUcsQ0FBQyxJQUFJLENBQUMsSUFBSSxLQUFLLFlBQVksQ0FBQyxJQUFJLElBQUksWUFBWSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsc0JBQVksSUFBSSxtQkFBWSxDQUFDLFlBQVksS0FBSyxJQUFJLENBQUMsWUFBWSxFQUEvQyxDQUErQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxLQUFLLEVBQWpLLENBQWlLLENBQUMsQ0FDbE0sRUFIc0IsQ0FHdEIsQ0FBQyxDQUFDO0lBRUgsT0FBTyxLQUFLLENBQUM7QUFDZixDQUFDOzs7QUNuSHVCO0FBQ0Y7QUFDRztBQUNLOzs7O0FDSEk7QUFDNkI7QUFDaUU7QUFDMUY7QUFHaEI7QUFXRjtBQUNrQjtBQUMyQjtBQU0vRCxnQkFBWSxHQU9WLCtKQUFPLGFBUEcsRUFDWixlQUFlLEdBTWIsK0pBQU8sZ0JBTk0sRUFDZiw0QkFBNEIsR0FLMUIsK0pBQU8sNkJBTG1CLEVBQzVCLGVBQWUsR0FJYiwrSkFBTyxnQkFKTSxFQUNmLFNBQVMsR0FHUCwrSkFBTyxVQUhBLEVBQ1QsZ0JBQWdCLEdBRWQsK0pBQU8saUJBRk8sRUFDaEIsY0FBYyxHQUNaLCtKQUFPLGVBREssQ0FDSjtBQUdaO0lBR0UsMEJBQW9CLE1BQWMsRUFBVSxNQUFjO1FBQXRDLFdBQU0sR0FBTixNQUFNLENBQVE7UUFBVSxXQUFNLEdBQU4sTUFBTSxDQUFRO1FBRjFELGdCQUFXLEdBQWtCLHFLQUFhLENBQUMsSUFBSSxDQUFDO0lBRWMsQ0FBQztJQUUvRCx1Q0FBWSxHQUFaO1FBQ0UsT0FBTyx5SEFBWSxDQUNqQixJQUFJLENBQUMsZUFBZSxFQUNwQixJQUFJLENBQUMscUJBQXFCLENBQzNCLENBQUM7SUFDSixDQUFDO0lBRUQsc0JBQVksNkNBQWU7YUFBM0I7WUFBQSxpQkE0QkM7WUEzQkMsT0FBTyxVQUFDLE9BQVk7Z0JBQ2xCLGNBQU8sQ0FBQyxJQUFJLENBQ1YsbUhBQU0sQ0FBQyxjQUFjLENBQUMsUUFBUSxFQUFFLENBQUMsRUFDakMsb0VBQU0sQ0FBQyxjQUFNLFlBQUksQ0FBQyxXQUFXLEtBQUsscUtBQWEsQ0FBQyxRQUFRLEVBQTNDLENBQTJDLENBQUMsRUFDekQsc0VBQVEsQ0FBQztvQkFDUCwyRUFBTSxDQUNKLGdFQUFFLENBQUMsZUFBZSxDQUFDLEtBQUksQ0FBQyxXQUFXLEdBQUcscUtBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxFQUM5RCxLQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBd0MsS0FBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsY0FBYyxDQUFDLENBQUMsSUFBSSxDQUN6RixzRUFBUSxDQUFDLFVBQUMsRUFBUTs0QkFBTixJQUFJO3dCQUFPOzRCQUNyQixpQkFBaUIsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLGNBQWMsQ0FBQzs0QkFFbEQsV0FBVyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsUUFBUSxDQUFDOzRCQUN0QyxzQkFBc0IsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLG1CQUFtQixDQUFDOzRCQUM1RCxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLGtCQUFrQixDQUFDOzRCQUN2RCxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLGlCQUFpQixDQUFDOzRCQUN4RCx5QkFBeUIsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLHNCQUFzQixDQUFDOzRCQUNsRSxlQUFlLENBQUMsNEJBQTRCLENBQUMsbUtBQU8sQ0FBQyxJQUFJLEVBQUUsMkJBQTJCLENBQUMsQ0FBQyxDQUFDOzRCQUN6RixlQUFlLENBQUMsS0FBSSxDQUFDLFdBQVcsR0FBRyxxS0FBYSxDQUFDLFFBQVEsQ0FBQzs0QkFDMUQsY0FBYyxFQUFFO3lCQUNqQjtvQkFYc0IsQ0FXdEIsQ0FBQyxDQUNILENBQ0Y7Z0JBaEJELENBZ0JDLENBQ0YsRUFDRCx3RUFBVSxDQUFDLFVBQUMsS0FBZSxJQUFLLHVFQUFFLENBQ2hDLFlBQVksQ0FBQyxJQUFJLDhKQUFNLENBQUMsWUFBWSxDQUFDLGdCQUFnQixFQUFFLEtBQUssQ0FBQyxDQUFDLENBQy9ELEVBRitCLENBRS9CLENBQUMsQ0FDSDtZQXpCRCxDQXlCQyxDQUFDO1FBQ04sQ0FBQzs7O09BQUE7SUFFRCxzQkFBWSxtREFBcUI7YUFBakM7WUFBQSxpQkFvQkM7WUFuQkMsT0FBTyxVQUFDLE9BQVksRUFBRSxLQUFVO2dCQUM5QixjQUFPLENBQUMsSUFBSSxDQUNWLG1IQUFNLENBQUMsY0FBYyxDQUFDLFFBQVEsRUFBRSxDQUFDLEVBQ2pDLG9FQUFNLENBQUMsY0FBTSxZQUFJLENBQUMsV0FBVyxLQUFLLHFLQUFhLENBQUMsUUFBUSxFQUEzQyxDQUEyQyxDQUFDLEVBQ3pELHNFQUFRLENBQUMsVUFBQyxFQUFnQjt3QkFBZCxPQUFPO29CQUNqQiwyRUFBTSxDQUNKLGdFQUFFLENBQUMsZUFBZSxDQUFDLEtBQUksQ0FBQyxXQUFXLEdBQUcscUtBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxFQUM5RCxLQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBTSxLQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxjQUFjLEVBQUUsY0FBYyxDQUFDLE1BQU0sQ0FBQyxPQUFPLEVBQUUsT0FBTyxFQUFFLEtBQUssQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUNsSCxzRUFBUSxDQUFDLGNBQU07d0JBQ2IsZUFBZSxDQUFDLFNBQVMsQ0FBQyxvS0FBWSxDQUFDLE1BQU0sQ0FBQyxDQUFDO3dCQUMvQyxnQkFBZ0IsQ0FBQyxDQUFDLG1LQUFXLENBQUMsTUFBTSxDQUFDLENBQUM7cUJBQ3ZDLEVBSGMsQ0FHZCxDQUFDLENBQ0gsQ0FDRjtnQkFSRCxDQVFDLENBQ0YsRUFDRCx3RUFBVSxDQUFDLFVBQUMsS0FBZSxJQUFLLHVFQUFFLENBQ2hDLFlBQVksQ0FBQyxJQUFJLDhKQUFNLENBQUMsWUFBWSxDQUFDLGdCQUFnQixFQUFFLEtBQUssQ0FBQyxDQUFDLENBQy9ELEVBRitCLENBRS9CLENBQUMsQ0FDSDtZQWpCRCxDQWlCQyxDQUFDO1FBQ04sQ0FBQzs7O09BQUE7SUE5RFUsZ0JBQWdCO1FBRDVCLG9FQUFVO3lDQUltQixNQUFNLEVBQWtCLE1BQU07T0FIL0MsZ0JBQWdCLENBK0Q1QjtJQUFELHVCQUFDO0NBQUE7QUEvRDRCOzs7O0FDbkNLO0FBQ2dFO0FBQ3BDO0FBQ2I7QUFJL0MsMkJBQWMsR0FDWiwrSkFBTyxlQURLLENBQ0o7QUFHWjtJQUFBO1FBQ0UsZ0JBQVcsR0FBa0IscUtBQWEsQ0FBQyxJQUFJLENBQUM7SUFnRGxELENBQUM7SUE5Q0Msb0NBQVksR0FBWjtRQUNFLE9BQU8seUhBQVksQ0FDakIsSUFBSSxDQUFDLGNBQWMsQ0FDcEIsQ0FBQztJQUNKLENBQUM7SUFFRCxzQkFBWSx5Q0FBYzthQUExQjtZQUNFLE9BQU8sVUFBQyxPQUFZLEVBQUUsS0FBSztnQkFDekIsY0FBTyxDQUFDLElBQUksQ0FDVixtSEFBTSxDQUFDLHVCQUFjLENBQUMsUUFBUSxFQUFFLENBQUMsRUFDakMsc0VBQVEsQ0FBQztvQkFDUCxJQUFNLFFBQVEsR0FBRyxnS0FBUSxDQUFDLFdBQVcsRUFBRSxDQUFDO29CQUN4QyxJQUFNLGVBQWUsR0FBRyw2SkFBSyxDQUFDLFdBQVcsRUFBRSxDQUFDO29CQUM1QyxJQUFJLE1BQU0sRUFBRSxNQUFNLENBQUM7b0JBQ25CLFFBQVEsZUFBZSxFQUFFLENBQUM7d0JBQ3hCLEtBQUssaUtBQVMsQ0FBQyxRQUFROzRCQUNyQixNQUFNLEdBQUcsVUFBVSxDQUFDOzRCQUNwQixNQUFNLEdBQUcsZ0JBQWdCLENBQUM7NEJBQzFCLE1BQU07d0JBQ1IsS0FBSyxpS0FBUyxDQUFDLEVBQUU7NEJBQ2YsTUFBTTt3QkFDUixLQUFLLGlLQUFTLENBQUMsS0FBSzs0QkFDbEIsTUFBTTt3QkFDUixLQUFLLGlLQUFTLENBQUMsTUFBTTs0QkFDbkIsTUFBTSxHQUFHLFFBQVEsQ0FBQzs0QkFDbEIsTUFBTSxHQUFHLFFBQVEsQ0FBQzs0QkFDbEIsTUFBTTtvQkFDVixDQUFDO29CQUNELFFBQVEsQ0FBQyxTQUFTLENBQUM7d0JBQ2pCLEVBQUUsRUFBRSxpQkFBaUI7d0JBQ3JCLE1BQU0sRUFBRSxHQUFHO3dCQUNYLE1BQU0sRUFBRSxNQUFNLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsR0FBRzt3QkFDN0IsTUFBTSxFQUFFLE1BQU0sQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxnQkFBZ0I7d0JBQzFDLE1BQU0sRUFBRSxjQUFjO3dCQUN0QixNQUFNLEVBQUU7NEJBQ04sSUFBSSxFQUFFLGdLQUFRLENBQUMsWUFBWSxDQUFDLE9BQU87NEJBQ25DLE9BQU8sRUFBRTtnQ0FDUCxHQUFHLEVBQUUsMEJBQTBCOzZCQUNoQzt5QkFDRjtxQkFDRixDQUFDLENBQUM7b0JBQ0gsT0FBTyxnRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDO2dCQUNoQixDQUFDLENBQUMsRUFDRix3RUFBVSxDQUFDLFVBQUMsS0FBZSxJQUFLLHVFQUFFLENBQUMsRUFBRSxDQUFDLEVBQU4sQ0FBTSxDQUFDLENBQ3hDO1lBcENELENBb0NDLENBQUM7UUFDTixDQUFDOzs7T0FBQTtJQWhEVSxhQUFhO1FBRHpCLG9FQUFVO09BQ0UsYUFBYSxDQWlEekI7SUFBRCxvQkFBQztDQUFBO0FBakR5Qjs7OztBQ1hRO0FBQzRCO0FBQ1E7QUFDekI7QUFFRjtBQUNZO0FBQ047QUFHL0MseUJBQWUsR0FHYiwrSkFBTyxnQkFITSxFQUNmLHFCQUFlLEdBRWIsK0pBQU8sZ0JBRk0sRUFDZixxQkFBcUIsR0FDbkIsK0pBQU8sc0JBRFksQ0FDWDtBQUdaO0lBQ0UsZUFDUyxhQUE0QixFQUM1QixnQkFBa0M7UUFEbEMsa0JBQWEsR0FBYixhQUFhLENBQWU7UUFDNUIscUJBQWdCLEdBQWhCLGdCQUFnQixDQUFrQjtJQUN4QyxDQUFDO0lBRUosNEJBQVksR0FBWjtRQUNFLE9BQU8seUhBQVksQ0FDakIsSUFBSSxDQUFDLGtCQUFrQixDQUN4QixDQUFDO0lBQ0osQ0FBQztJQUVELHNCQUFZLHFDQUFrQjthQUE5QjtZQUNFLE9BQU8sVUFBQyxPQUFZO2dCQUNsQixjQUFPLENBQUMsSUFBSSxDQUNWLG1IQUFNLENBQUMscUJBQWUsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxFQUNsQyxvRUFBTSxDQUFDLFVBQUMsRUFBK0M7d0JBQTdDLE9BQU87b0JBQTJDLGNBQU8sS0FBSyxxS0FBYSxDQUFDLElBQUk7Z0JBQTlCLENBQThCLENBQUMsRUFDM0Ysc0VBQVEsQ0FBQyxjQUFNLHVFQUFFLENBQ2YscUJBQWUsQ0FBQyxxQkFBcUIsRUFBRSxDQUFDLEVBQ3hDLGNBQWMsRUFBRSxDQUNqQixFQUhjLENBR2QsQ0FBQyxDQUNIO1lBUEQsQ0FPQyxDQUFDO1FBQ04sQ0FBQzs7O09BQUE7SUF0QlUsS0FBSztRQURqQixvRUFBVTt5Q0FHZSxhQUFhO1lBQ1YsZ0JBQWdCO09BSGhDLEtBQUssQ0F3QmpCO0lBQUQsWUFBQztDQUFBO0FBeEJpQjs7OztBQ2hCZ0U7QUFFMUUsb0JBQWdCLEdBQUssd0VBQWMsaUJBQW5CLENBQW9CO0FBRTVDLElBQU0sZ0JBQWdCLEdBQUcsK0JBQStCLENBQUM7QUFFekQ7SUFBa0MsZ0NBQWdCO0lBQWxEOztJQVVBLENBQUM7cUJBVlksWUFBWTtJQUVoQiwrQkFBa0IsR0FBekIsVUFBMEIsRUFBVTtRQUNsQyxjQUFZLENBQUMsUUFBUSxHQUFHLGNBQVksQ0FBQyxRQUFRO1lBQzNDLHdFQUFjO2lCQUNYLFFBQVE7aUJBQ1IsVUFBVSxDQUFDLHdFQUFjLENBQUMsWUFBWSxDQUFDLENBQUM7UUFDN0MsSUFBTSxRQUFRLEdBQVEsY0FBWSxDQUFDLFFBQVEsQ0FBQztRQUM1QyxPQUFPLFFBQVEsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLGtCQUFrQixDQUFDLGdCQUFnQixFQUFFLEVBQUUsRUFBRSxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztJQUM1RixDQUFDOztJQVJNLHFCQUFRLEdBQUcsSUFBSSxDQUFDO0lBRFosWUFBWTtRQUR4QixvRUFBVTtPQUNFLFlBQVksQ0FVeEI7SUFBRCxtQkFBQztDQUFBLENBVmlDLGdCQUFnQixHQVVqRDtBQVZ3Qjs7OztBQ05lO0FBQ2M7QUFDTjtBQUN1RDtBQUVqQztBQUdqQztBQUdMO0FBQ2U7QUFFWjtBQUUzQixhQUFTLEdBQW9DLHdFQUFjLFVBQWxELEVBQUUsNkJBQTZCLEdBQUssd0VBQWMsOEJBQW5CLENBQW9CO0FBQzlELFNBUUYsNkJBQTZCLENBQUMsdUJBQU8sQ0FBQyxFQVB4Qyx1QkFBaUIseUJBRWpCLGlCQUFXLG1CQUNYLDRCQUFzQiw4QkFDdEIsd0JBQWtCLDBCQUNsQiwwQkFBb0IsNEJBQ3BCLCtCQUF5QiwrQkFDZSxDQUFDO0FBRzNDO0lBQTJCLHlCQUFzQjtJQUMvQyxlQUFvQixNQUFjLEVBQUUsS0FBZ0IsRUFBVSxLQUFZLEVBQVUsWUFBMEI7UUFDNUcsa0JBQUssWUFBQyxLQUFLLENBQUMsU0FBQztRQURLLFlBQU0sR0FBTixNQUFNLENBQVE7UUFBNEIsV0FBSyxHQUFMLEtBQUssQ0FBTztRQUFVLGtCQUFZLEdBQVosWUFBWSxDQUFjOztJQUU5RyxDQUFDO0lBRUQsc0JBQUksMEJBQU87YUFBWDs7WUFDRSxPQUFPLGlGQUFlLHlDQUVqQixnS0FBUSxDQUFDLG1CQUFtQixDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsR0FDL0MsZ0tBQVEsQ0FBQyxnQkFBZ0IsRUFBRSxHQUMzQixnS0FBUSxDQUFDLGtCQUFrQixFQUFFLEtBRWhDLGNBQWMsRUFBRSw4R0FBYTtvQkFDM0IsR0FBQyx1QkFBaUIsSUFBRyxVQUFDLEtBQUssRUFBRSxFQUFvQzs0QkFBbEMsT0FBTzt3QkFBZ0MsY0FBTyxJQUFJLEtBQUs7b0JBQWhCLENBQWdCO3lCQUNyRixJQUFJLENBQUMsRUFJUixRQUFRLEVBQUUsOEdBQWE7b0JBQ3JCLEdBQUMsaUJBQVcsSUFBRyxVQUFDLEtBQUssRUFBRSxFQUE4Qjs0QkFBNUIsT0FBTzt3QkFBMEIsY0FBTyxJQUFJLEtBQUs7b0JBQWhCLENBQWdCO3lCQUN6RSxJQUFJLENBQUMsRUFDUixtQkFBbUIsRUFBRSw4R0FBYTtvQkFDaEMsR0FBQyw0QkFBc0IsSUFBRyxVQUFDLEtBQUssRUFBRSxFQUF5Qzs0QkFBdkMsT0FBTzt3QkFBcUMsY0FBTyxJQUFJLEtBQUs7b0JBQWhCLENBQWdCO3lCQUMvRixJQUFJLENBQUMsRUFDUixrQkFBa0IsRUFBRSw4R0FBYTtvQkFDL0IsR0FBQyx3QkFBa0IsSUFBRyxVQUFDLEtBQUssRUFBRSxFQUF3Qzs0QkFBdEMsT0FBTzt3QkFBb0MsY0FBTyxJQUFJLEtBQUs7b0JBQWhCLENBQWdCO3lCQUMxRixJQUFJLENBQUMsRUFDUixpQkFBaUIsRUFBRSw4R0FBYTtvQkFDOUIsR0FBQywwQkFBb0IsSUFBRyxVQUFDLEtBQUssRUFBRSxFQUF1Qzs0QkFBckMsT0FBTzt3QkFBbUMsY0FBTyxJQUFJLEtBQUs7b0JBQWhCLENBQWdCO3lCQUMzRixJQUFJLENBQUMsRUFDUixzQkFBc0IsRUFBRSw4R0FBYTtvQkFDbkMsR0FBQywrQkFBeUIsSUFBRyxVQUFDLEtBQUssRUFBRSxFQUE0Qjs0QkFBMUIsT0FBTzt3QkFBd0IsY0FBTyxJQUFJLEtBQUs7b0JBQWhCLENBQWdCO3lCQUNyRixLQUFLLENBQUMsSUFDVCxDQUFDO1FBQ0wsQ0FBQzs7O09BQUE7SUFTRCxzQkFBSSw4QkFBVzthQUFmO1lBQ0UsT0FBTyx5SEFBWSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsYUFBYSxDQUFDLFlBQVksRUFBRSxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsZ0JBQWdCLENBQUMsWUFBWSxFQUFFLEVBQ3JHLElBQUksQ0FBQyxLQUFLLENBQUMsWUFBWSxFQUFFLEVBQUUsSUFBSSxrS0FBVSxFQUFFLENBQUMsWUFBWSxFQUFFLEVBQUUsSUFBSSx5S0FBaUIsQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLCtCQUErQixDQUFDLENBQUMsWUFBWSxFQUFFLEVBQzlJLElBQUksc0tBQWMsRUFBRSxDQUFDLFlBQVksRUFBRSxDQUFDLENBQUM7UUFDekMsQ0FBQzs7O09BQUE7SUEvQ1UsS0FBSztRQURqQixvRUFBVTt5Q0FFbUIsTUFBTSxFQUFTLCtEQUFTLEVBQWlCLEtBQUssRUFBd0IsWUFBWTtPQURuRyxLQUFLLENBZ0RqQjtJQUFELFlBQUM7Q0FBQSxDQWhEMEIsU0FBUyxHQWdEbkM7QUFoRGlCOzs7QUM1Qk07QUFDRTs7O0FDREs7QUFDZ0o7O0FBRS9LO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxnQ0FBZ0MseUNBQXlDO0FBQ3pFLFlBQVksTUFBTTtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLGVBQWUsY0FBYztBQUN0RSxDQUFDLElBQUk7O0FBRUw7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFlBQVksWUFBWSxxQkFBcUI7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixVQUFVO0FBQzFCO0FBQ0EsK0JBQStCLEtBQUs7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLE9BQU8sa0JBQWtCO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGVBQWUsVUFBVTtBQUN6QixZQUFZLE9BQU87O0FBRW5COztBQUVBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLE9BQU8sV0FBVztBQUM3Qyx3QkFBd0IsT0FBTyxTQUFTO0FBQ3hDLHFCQUFxQjtBQUNyQjtBQUNBLGdCQUFnQiw2QkFBNkI7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVksb0NBQW9DO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSw0RUFBNEUsWUFBWSxRQUFRLG9DQUFvQyxlQUFlLG9DQUFvQyxNQUFNOztBQUU3TDs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUNBQWlDLHlEQUF5RDtBQUMxRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QyxZQUFZLHFDQUFxQywwQ0FBMEMsTUFBTSx5QkFBeUIsR0FBRztBQUN0Szs7QUFFQSxrRUFBa0UsWUFBWSwrQ0FBK0MseUVBQXlFO0FBQ3RNO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQztBQUN0QyxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyw0R0FBNEc7QUFDbEo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsdUNBQXVDO0FBQ3ZELGdCQUFnQix1Q0FBdUM7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixvREFBb0Q7QUFDcEUsZ0JBQWdCLG9EQUFvRDtBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLCtDQUErQztBQUMvRDtBQUNBLDBDQUEwQyxvRUFBb0U7QUFDOUc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNERBQTREO0FBQzVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0Q7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsSUFBSTtBQUNyQixhQUFhO0FBQ2I7QUFDQSw4Q0FBOEMsa0JBQWtCO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxzQ0FBc0MscUJBQXFCLG9DQUFvQztBQUMvRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRDtBQUN0RCxzQkFBc0I7QUFDdEI7QUFDQSw2QkFBNkIseUJBQXlCO0FBQ3REO0FBQ0Esb0JBQW9CO0FBQ3BCLFNBQVMsT0FBTztBQUNoQjtBQUNBLHdCQUF3QiwwQ0FBMEM7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0REFBNEQsbUJBQW1CO0FBQy9FLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQSw0Q0FBNEMsS0FBSyxHQUFHLElBQUksT0FBTyxLQUFLLEdBQUcsSUFBSTtBQUMzRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsd0JBQXdCLHFHQUFxRztBQUM3SDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQiwyQkFBMkI7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHVFQUF1RSxXQUFXO0FBQ2xGOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQsUUFBUSwyQ0FBMkMsRUFBRSw2REFBSztBQUMxRCxtQkFBbUIsZ0hBQWdILG1EQUFtRCxJQUFJO0FBQzFMLCtCQUErQjtBQUMvQjtBQUNBLCtCQUErQjtBQUMvQixzQ0FBc0M7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdEO0FBQ2hEO0FBQ0E7QUFDQSxZQUFZLHVCQUF1QjtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWSxrRUFBa0U7QUFDOUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsT0FBTztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixlQUFlO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsa0JBQWtCO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsZ0JBQWdCLFNBQVM7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQixvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsbUJBQW1CLGNBQWM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsU0FBUztBQUNqQztBQUNBO0FBQ0Esd0JBQXdCO0FBQ3hCLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhFQUE4RSxRQUFRO0FBQ3RGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLDRDQUE0QztBQUM1RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkM7QUFDM0M7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsTUFBTTtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0EsaUZBQWlGLFlBQVkscUJBQXFCO0FBQ2xIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRCxlQUFlLGVBQWU7QUFDbkYsYUFBYSxJQUFJO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3REFBd0Q7QUFDeEQ7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLG9CQUFvQjtBQUNwQyxnREFBZ0QsS0FBSztBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsS0FBSztBQUNsRDtBQUNBLHlEQUF5RDtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0NBQStDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLDhCQUE4QixjQUFjO0FBQzVDO0FBQ0E7QUFDQSw2REFBNkQsc0JBQXNCLGlCQUFpQjtBQUNwRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLDhCQUE4QjtBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGlCQUFpQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGtCQUFrQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixZQUFZLE1BQU0sSUFBSTtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLDhCQUE4QjtBQUM5Qiw4QkFBOEI7QUFDOUIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsZ0VBQWdFO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsdUJBQU07QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZEQUE2RCxjQUFjO0FBQzNFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsMEJBQTBCLCtFQUFhO0FBQ3ZDO0FBQ0EsV0FBVyw0RUFBVTtBQUNyQjtBQUNBO0FBQ0EsVUFBVSw4QkFBOEIsb0JBQW9CLHVCQUFNO0FBQ2xFLFlBQVksK0VBQWEsK0JBQStCLHFDQUFxQyxrQkFBa0IsbUJBQW1CLEdBQUc7QUFDckk7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQSwyREFBMkQsWUFBWSxrQkFBa0I7QUFDekY7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUEseUJBQXlCLGVBQWU7QUFDeEM7QUFDQSxZQUFZLHlIQUF5SDtBQUNySSxrQ0FBa0Msd0VBQVE7QUFDMUMsK0JBQStCLDBFQUFVO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksMkVBQVc7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxVQUFVLDRKQUE0SixhQUFhLHVCQUFNO0FBQ3pMO0FBQ0EsWUFBWSxxRkFBcUYsc0JBQXNCLG9CQUFvQiwwQ0FBMEMsZUFBZSxhQUFhLG1DQUFtQztBQUNwUCx3Q0FBd0MsMEVBQVU7QUFDbEQ7QUFDQTtBQUNBLHFCQUFxQix3RUFBUTtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUssbUJBQW1CO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBLElBQUksMkVBQVc7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLDRFQUE0RSxNQUFNO0FBQ2xGLFlBQVk7QUFDWixZQUFZLDhCQUE4QjtBQUMxQyxZQUFZO0FBQ1osWUFBWSxNQUFNLDZEQUE2RDtBQUMvRSxXQUFXLGdGQUFjLG9CQUFvQiw4RUFBWSw0QkFBNEIsK0VBQWEsaUNBQWlDO0FBQ25JOztBQUVBLHdCQUF3Qiw2Q0FBNkM7QUFDckU7QUFDQSxZQUFZLGlCQUFpQix3Q0FBd0M7QUFDckU7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsMEJBQTBCO0FBQ2xFO0FBQ0EsNkJBQTZCLGdGQUFjLG9CQUFvQiw4RUFBWSw0QkFBNEIsK0VBQWEsaUNBQWlDLGVBQWUsK0VBQWEsQ0FBQyxzRUFBUSxrQkFBa0I7QUFDNU07O0FBRXlGOzs7QUM5N0N6RixJQUFZLGNBSVg7QUFKRCxXQUFZLGNBQWM7SUFDeEIsaUNBQWU7SUFDZiw4Q0FBNEI7SUFDNUIsaUNBQWU7QUFDakIsQ0FBQyxFQUpXLGNBQWMsS0FBZCxjQUFjLFFBSXpCO0FBRUQsSUFBWSxTQVdYO0FBWEQsV0FBWSxTQUFTO0lBQ25CLHNCQUFTO0lBQ1Qsc0JBQVM7SUFDVCxnQ0FBbUI7SUFDbkIsOEJBQWlCO0lBQ2pCLGtDQUFxQjtJQUNyQixrQ0FBcUI7SUFDckIsa0NBQXFCO0lBQ3JCLGtDQUFxQjtJQUNyQixrQ0FBcUI7SUFDckIsa0NBQXFCO0FBQ3ZCLENBQUMsRUFYVyxTQUFTLEtBQVQsU0FBUyxRQVdwQjtBQUVELElBQVksdUJBSVg7QUFKRCxXQUFZLHVCQUF1QjtJQUNqQywwQ0FBZTtJQUNmLHVEQUE0QjtJQUM1QiwwQ0FBZTtBQUNqQixDQUFDLEVBSlcsdUJBQXVCLEtBQXZCLHVCQUF1QixRQUlsQzs7O0FDdkJ5RTtBQUV6QjtBQUUxQyxTQUFTLFlBQVksQ0FBQyxJQUFZO0lBQ3ZDLElBQUksQ0FBQztRQUNILElBQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDbEMsSUFBTSxPQUFPLEdBQUcsSUFBSSxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDdkMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxVQUFVLEVBQUUsR0FBRyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDLENBQUM7UUFDckYsT0FBTyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNwQixPQUFPLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3RCLE9BQU8sT0FBTyxDQUFDO0lBQ2pCLENBQUM7SUFBQyxPQUFPLENBQUMsRUFBRSxDQUFDO1FBQ1gsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0FBQ0gsQ0FBQztBQUlNLFNBQVMsT0FBTyxDQUFDLFVBQW9CLEVBQUUsRUFBWTtJQUV4RCxJQUFNLFdBQVcsR0FBVSxNQUFNLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxVQUFDLEdBQVEsSUFBSyxpQkFBVSxDQUFDLEdBQUcsQ0FBQyxFQUFmLENBQWUsQ0FBQyxDQUFDO0lBR3RGLE9BQU8sV0FBVyxDQUFDLEdBQUcsQ0FBQyxXQUFDLElBQUksU0FBRSxDQUFDLENBQUMsQ0FBQyxFQUFMLENBQUssQ0FBQyxDQUFDO0FBQ3JDLENBQUM7QUFFTSxJQUFNLGtCQUFrQixHQUFHLGlCQUFpQixDQUFDO0FBRTdDLElBQU0sVUFBVSxHQUFHLDJJQUEySSxDQUFDO0FBRS9KLElBQU0sbUJBQW1CLEdBQUcsMkJBQTJCLENBQUM7QUFHeEQsSUFBTSxlQUFlLEdBQUcsVUFBQyxNQUFvQztJQUNsRSxJQUFNLFVBQVUsR0FBdUIsRUFBRSxDQUFDO0lBQzFDLElBQUksTUFBTSxDQUFDO0lBQ1gsSUFBSSw2SkFBSyxDQUFDLFdBQVcsRUFBRSxLQUFLLGlLQUFTLENBQUMsUUFBUTtRQUM1QyxNQUFNLEdBQUcsR0FBRyxDQUFDO0lBRWYsSUFBSSw2SkFBSyxDQUFDLFdBQVcsRUFBRSxLQUFLLGlLQUFTLENBQUMsTUFBTTtRQUMxQyxNQUFNLEdBQUcsR0FBRyxDQUFDO0lBRWYsTUFBTSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQyxHQUFHLENBQUMsV0FBQztRQUN2QixJQUFNLEdBQUcsR0FBUSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDM0IsVUFBVSxDQUFDLElBQUksQ0FBQztZQUNkLEVBQUUsRUFBRSxHQUFHLENBQUMsR0FBRyxDQUFDLElBQUk7WUFDaEIsS0FBSyxFQUFFLEdBQUcsQ0FBQyxJQUFJO1NBQ2hCLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsZ0tBQVEsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxVQUFVLENBQy9CLFVBQVUsQ0FBQyxHQUFHLENBQUMsVUFBQyxHQUFHLElBQUssUUFBQztRQUN2QixJQUFJLEVBQUUsR0FBRyxDQUFDLEVBQUU7UUFDWixJQUFJLEVBQUUsZ0tBQVEsQ0FBQyxVQUFVLENBQUMsVUFBVTtRQUNwQyxLQUFLLEVBQUUsZ0tBQVEsQ0FBQyxpQkFBaUIsQ0FBQyxRQUFRO1FBQzFDLFdBQVcsRUFBRSxHQUFHLENBQUMsS0FBSztLQUN2QixDQUFDLEVBTHNCLENBS3RCLENBQUMsRUFBRSxNQUFNLENBQUMsQ0FBQztJQUVmLE9BQU8sVUFBVSxDQUFDO0FBQ3BCLENBQUMsQ0FBQztBQVNLLFNBQVMsVUFBVSxDQUFDLEtBQWE7SUFDdEMsSUFBSSxNQUFNLEdBQUcsYUFBYSxDQUFDLEtBQUssQ0FBQyxDQUFDO0lBQ2xDLE1BQU0sR0FBRyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FBQztJQUM5QixPQUFPLE1BQU0sQ0FBQyxNQUFNLEtBQUssRUFBRSxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsR0FBRyxHQUFHLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQUcsR0FBRyxHQUFHLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQztBQUMvRyxDQUFDO0FBRU0sU0FBUyxhQUFhLENBQUMsS0FBYTtJQUN6QyxJQUFNLEdBQUcsR0FBRyxLQUFLLENBQUMsT0FBTyxDQUFDLEtBQUssRUFBRSxFQUFFLENBQUMsQ0FBQztJQUNyQyxPQUFPLEdBQUcsQ0FBQztBQUNiLENBQUM7QUFFTSxTQUFTLE9BQU8sQ0FBQyxHQUFRO0lBQzlCLE9BQU8sR0FBRyxDQUFDO0FBQ2IsQ0FBQztBQU1NLFNBQVMsZUFBZSxDQUFDLEtBQTZCO0lBQzNELElBQU0sYUFBYSxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsY0FBSSxJQUFJLFdBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLGNBQUksSUFBSSxXQUFJLENBQUMsVUFBVSxLQUFLLElBQUksRUFBeEIsQ0FBd0IsQ0FBQyxFQUFyRCxDQUFxRCxDQUFDLENBQUM7SUFDbEcsT0FBTyxhQUFhLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO0FBQy9ELENBQUM7QUFFTSxJQUFNLGVBQWUsR0FBRyxVQUFDLE1BQXNCLEVBQUUsS0FBc0M7O0lBQzVGLFFBQVEsTUFBTSxFQUFFLENBQUM7UUFDZixLQUFLLGNBQWMsQ0FBQyxLQUFLO1lBQ3ZCLE9BQU8sS0FBSyxhQUFMLEtBQUssdUJBQUwsS0FBSyxDQUFFLEtBQUssQ0FBQztRQUN0QixLQUFLLGNBQWMsQ0FBQyxLQUFLO1lBQ3ZCLE9BQU8sWUFBSyxhQUFMLEtBQUssdUJBQUwsS0FBSyxDQUFFLFlBQVksMENBQUUsV0FBVyxLQUFJLFVBQVUsQ0FBQyxXQUFLLGFBQUwsS0FBSyx1QkFBTCxLQUFLLENBQUUsWUFBWSwwQ0FBRSxXQUFXLENBQUMsQ0FBQztRQUMxRixLQUFLLGNBQWMsQ0FBQyxZQUFZO1lBQzlCLE9BQU8sTUFBSyxhQUFMLEtBQUssdUJBQUwsS0FBSyxDQUFFLFdBQVcsS0FBSSxVQUFVLENBQUMsS0FBSyxhQUFMLEtBQUssdUJBQUwsS0FBSyxDQUFFLFdBQVcsQ0FBQyxDQUFDO1FBQzlEO1lBQ0UsT0FBTyxFQUFFLENBQUM7SUFDZCxDQUFDO0FBQ0gsQ0FBQyxDQUFDOzs7Ozs7QUN4RzZCO0FBQ2U7QUFDRztBQUNtQjtBQVU3RCxJQUFNLFFBQVEsR0FBRyxVQUFDLEtBQXFCO0lBQzVDLElBQU0sWUFBWSxHQUFHLHNCQUFLLFlBQVksR0FBSyxLQUFLLENBQUMsQ0FBQztJQUMxQyxTQUFLLEdBQTZDLFlBQVksTUFBekQsRUFBRSxLQUFLLEdBQXNDLFlBQVksTUFBbEQsRUFBRSxZQUFZLEdBQXdCLFlBQVksYUFBcEMsRUFBRSxRQUFRLEdBQWMsWUFBWSxTQUExQixFQUFFLE9BQU8sR0FBSyxZQUFZLFFBQWpCLENBQWtCO0lBQy9ELFlBQVEsR0FBVSxjQUFjLEVBQUUsU0FBMUIsQ0FBMkI7SUFFM0MsT0FBTyxDQUNMLHFGQUFLLFNBQVMsRUFBQyx1Q0FBdUM7UUFDcEQsdUZBQU8sT0FBTyxFQUFDLHVCQUF1QixFQUFDLFNBQVMsRUFBQyx5QkFBeUI7WUFDeEUsc0ZBQU0sU0FBUyxFQUFDLGVBQWU7Z0JBQUMsNEVBQUMsaUdBQWdCLElBQUMsRUFBRSxFQUFFLEtBQUssR0FBSSxDQUFPO1lBQ3JFLFFBQVEsQ0FBQyxDQUFDLENBQUMsc0ZBQU0sU0FBUyxFQUFDLDJCQUEyQjtnQkFBQyw0RUFBQyw0S0FBb0IsSUFBQyxFQUFFLEVBQUUsUUFBUSxHQUFJLENBQU8sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUN0RztRQUNSLHFGQUFLLFNBQVMsRUFBQyxzQkFBc0I7WUFDbkMsdUZBQU8sU0FBUyxFQUFDLGtEQUFrRDtnQkFDakUsNEVBQUMsNEtBQW9CLElBQUMsRUFBRSxFQUFFLEtBQUssR0FBRyxRQUFRLEdBQUk7Z0JBQzlDLHVGQUFPLElBQUksRUFBQyxVQUFVLEVBQUMsR0FBRyxFQUFFLFFBQVEsRUFBRSxFQUFFLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRSxLQUFLLEVBQUUsY0FBYyxFQUFFLE9BQU8sRUFBRSxRQUFRLEVBQUUsVUFBQyxDQUFDLElBQUssbUJBQVksQ0FBQyxDQUFDLENBQUMsRUFBZixDQUFlLEdBQUk7Z0JBQzNILHNGQUFNLFNBQVMsRUFBQyx5QkFBeUIsR0FBUSxDQUMzQyxDQUNKLENBQ0YsQ0FDUCxDQUFDO0FBQ0osQ0FBQyxDQUFDO0FBRUYsSUFBTSxZQUFZLEdBQUc7SUFDbkIsT0FBTyxFQUFFLEtBQUs7Q0FDZixDQUFDOzs7O0FDckM2QjtBQUNlO0FBQ0c7QUFVMUMsSUFBTSxRQUFRLEdBQUcsVUFBQyxLQUFxQjtJQUM1QyxJQUFNLFlBQVkseUJBQXdCLHFCQUFZLEdBQUssS0FBSyxDQUFDLENBQUM7SUFDMUQsU0FBSyxHQUFrRCxZQUFZLE1BQTlELEVBQUUsS0FBSyxHQUEyQyxZQUFZLE1BQXZELEVBQUUsWUFBWSxHQUE2QixZQUFZLGFBQXpDLEVBQUUsT0FBTyxHQUFvQixZQUFZLFFBQWhDLEVBQUUsYUFBYSxHQUFLLFlBQVksY0FBakIsQ0FBa0I7SUFDcEUsWUFBUSxHQUFVLGNBQWMsRUFBRSxTQUExQixDQUEyQjtJQUUzQyxPQUFRLEtBQUssQ0FBQyxDQUFDLENBQUMsdUZBQU8sU0FBUyxFQUFDLHVEQUF1RDtRQUN0Rix1RkFBTyxTQUFTLEVBQUMsZUFBZSxFQUFDLE9BQU8sRUFBRSxpQkFBVSxLQUFLLENBQUU7WUFBRSw0RUFBQyxpR0FBZ0IsSUFBQyxFQUFFLEVBQUUsS0FBSyxHQUFJLENBQVE7UUFDcEcsdUZBQ0UsSUFBSSxFQUFDLE9BQU8sRUFDWixFQUFFLEVBQUUsaUJBQVUsS0FBSyxDQUFFLEVBQ3JCLEdBQUcsRUFBRSxRQUFRLENBQUMsRUFBRSxRQUFRLEVBQUUsYUFBYSxFQUFDLENBQUMsRUFDekMsSUFBSSxFQUFFLEtBQUssRUFDWCxLQUFLLEVBQUUsS0FBSyxFQUNaLE9BQU8sRUFBRSxPQUFPLEVBQ2hCLFFBQVEsRUFBRSxVQUFDLENBQUMsSUFBSyxtQkFBWSxDQUFDLENBQUMsQ0FBQyxFQUFmLENBQWUsR0FDaEM7UUFDRixzRkFBTSxTQUFTLEVBQUMsY0FBYyxHQUFRO1FBR3BDLEtBQUssS0FBSyxPQUFPLElBQUksT0FBTyxLQUFLLElBQUksQ0FBQyxDQUFDLENBQUMsc0ZBQU0sU0FBUyxFQUFDLGdDQUFnQyxpQkFBYSxNQUFNLEdBQVEsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUN0SCxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUM7QUFDbEIsQ0FBQyxDQUFDO0FBRUYsSUFBTSxxQkFBWSxHQUFHO0lBQ25CLE9BQU8sRUFBRSxLQUFLO0lBQ2QsYUFBYSxFQUFFLEtBQUs7Q0FDckIsQ0FBQzs7OztBQ3RDNkI7QUFDZTtBQUNHO0FBQ21CO0FBWTdELElBQU0sUUFBUSxHQUFHLFVBQUMsS0FBcUI7SUFDNUMsSUFBTSxZQUFZLEdBQUcsc0JBQUsscUJBQVksR0FBSyxLQUFLLENBQUUsQ0FBQztJQUMzQyxTQUFLLEdBQXdFLFlBQVksTUFBcEYsRUFBRSxRQUFRLEdBQThELFlBQVksU0FBMUUsRUFBRSxLQUFLLEdBQXVELFlBQVksTUFBbkUsRUFBRSxRQUFRLEdBQTZDLFlBQVksU0FBekQsRUFBRSxZQUFZLEdBQStCLFlBQVksYUFBM0MsRUFBRSxhQUFhLEdBQWdCLFlBQVksY0FBNUIsRUFBRSxTQUFTLEdBQUssWUFBWSxVQUFqQixDQUFrQjtJQUMxRixZQUFRLEdBQVUsY0FBYyxFQUFFLFNBQTFCLENBQTJCO0lBQ3JDLGdCQUFzQixzRUFBYyxDQUN4QyxDQUFDLFNBQVMsSUFBSSxDQUFDLENBQUMsR0FBRyxDQUFDLEtBQUssSUFBSSxFQUFFLENBQUMsQ0FBQyxNQUFNLENBQ3hDLE1BRk0sT0FBTyxVQUFFLFFBQVEsUUFFdkIsQ0FBQztJQUVGLE9BQU8sQ0FDTCxxRkFBSyxTQUFTLEVBQUMsdUNBQXVDO1FBQ3BELHVGQUFPLE9BQU8sRUFBRSxLQUFLLEVBQUUsU0FBUyxFQUFDLHlCQUF5QjtZQUN4RCxzRkFBTSxTQUFTLEVBQUMsU0FBUztnQkFBQyw0RUFBQyxpR0FBZ0IsSUFBQyxFQUFFLEVBQUUsS0FBSyxHQUFJLENBQU87WUFDL0QsUUFBUSxDQUFDLENBQUMsQ0FBQyxzRkFBTSxTQUFTLEVBQUMsV0FBVyxpQkFBa0IsQ0FBQyxDQUFDLENBQUMsRUFBRTtZQUM3RCxRQUFRLENBQUMsQ0FBQyxDQUFDLHNGQUFNLFNBQVMsRUFBQywyQkFBMkI7Z0JBQUMsNEVBQUMsNEtBQW9CLElBQUMsRUFBRSxFQUFFLFFBQVEsR0FBSSxDQUFPLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FDdEc7UUFDUixxRkFBSyxTQUFTLEVBQUMsU0FBUztZQUN0QiwwRkFDRSxHQUFHLEVBQUUsUUFBUSxDQUFDLEVBQUUsUUFBUSxFQUFFLGFBQWEsRUFBRSxDQUFDLEVBQzFDLEVBQUUsRUFBRSxLQUFLLEVBQ1QsSUFBSSxFQUFFLEtBQUssRUFDWCxZQUFZLEVBQUUsS0FBSyxFQUNuQixTQUFTLEVBQUUsU0FBUyxFQUNwQixTQUFTLEVBQUMsNEJBQTRCLEVBQ3RDLFFBQVEsRUFBRSxVQUFDLENBQXlDO29CQUNsRCxRQUFRLENBQ04sQ0FBQyxTQUFTLElBQUksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsYUFBYSxDQUFDLEtBQUssSUFBSSxFQUFFLENBQUMsQ0FBQyxNQUFNLENBQ3hELENBQUM7b0JBQ0YsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO2dCQUNsQixDQUFDLEdBQ1E7WUFDWDtnQkFDRSw0RUFBQyxpR0FBZ0IsSUFBQyxFQUFFLEVBQUUsS0FBSyxHQUFHLGNBQWMsRUFBRSxNQUFNLEVBQUUsRUFBRSxHQUFHLEVBQUUsU0FBUyxFQUFFLEtBQUssRUFBRSxPQUFPLEVBQUUsR0FBSSxDQUMxRixDQUNBLENBQ0YsQ0FDUCxDQUFDO0FBQ0osQ0FBQyxDQUFDO0FBRUYsSUFBTSxxQkFBWSxHQUFHO0lBQ25CLFFBQVEsRUFBRSxLQUFLO0lBQ2YsYUFBYSxFQUFFLEtBQUs7SUFDcEIsS0FBSyxFQUFFLEVBQUU7SUFDVCxRQUFRLEVBQUUsRUFBRTtDQUNiLENBQUM7Ozs7QUMxRDZCO0FBQ2tCO0FBQ0g7QUFDTztBQUNlO0FBZTdELElBQU0sU0FBUyxHQUFRLFVBQUMsS0FBcUI7SUFDbEQsSUFBTSxZQUFZLEdBQUcsc0JBQUssc0JBQVksR0FBSyxLQUFLLENBQUUsQ0FBQztJQUMzQyxTQUFLLEdBQTZILFlBQVksTUFBekksRUFBRSxRQUFRLEdBQW1ILFlBQVksU0FBL0gsRUFBRSxZQUFZLEdBQXFHLFlBQVksYUFBakgsRUFBRSxjQUFjLEdBQXFGLFlBQVksZUFBakcsRUFBRSxTQUFTLEdBQTBFLFlBQVksVUFBdEYsRUFBRSxrQkFBa0IsR0FBc0QsWUFBWSxtQkFBbEUsRUFBRSxhQUFhLEdBQXVDLFlBQVksY0FBbkQsRUFBRSxlQUFlLEdBQXNCLFlBQVksZ0JBQWxDLEVBQUUsS0FBSyxHQUFlLFlBQVksTUFBM0IsRUFBRSxRQUFRLEdBQUssWUFBWSxTQUFqQixDQUFrQjtJQUNqSixTQUE0QixjQUFjLEVBQUUsRUFBMUMsUUFBUSxnQkFBRSxNQUFNLFlBQTBCLENBQUM7SUFFbkQsSUFBTSxZQUFZLEdBQUcsVUFBQyxLQUFhO1FBQ2pDLFFBQVEsS0FBSyxFQUFFLENBQUM7WUFDZCxLQUFLLGtCQUFrQixDQUFDO1lBQ3hCLEtBQUssdUJBQXVCLENBQUM7WUFDN0IsS0FBSyw4QkFBOEIsQ0FBQztZQUNwQyxLQUFLLGNBQWMsQ0FBQztZQUNwQixLQUFLLG9CQUFvQjtnQkFDdkIsT0FBTyxZQUFZLENBQUMsa0JBQWtCLENBQUMsdUJBQXVCLENBQUMsQ0FBQztZQUNsRTtnQkFDRSxPQUFPLFlBQVksQ0FBQyxrQkFBa0IsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNsRCxDQUFDO0lBQ0gsQ0FBQyxDQUFDO0lBRUYsT0FBTyxDQUNMLHFGQUFLLFNBQVMsRUFBRSx5REFBa0QsY0FBYyxDQUFFO1FBQ2hGLHVGQUFPLE9BQU8sRUFBQyx1QkFBdUIsRUFBQyxTQUFTLEVBQUUsa0NBQTJCLGFBQWEsQ0FBQyxDQUFDLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxFQUFFLGNBQUksTUFBTSxJQUFJLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUU7WUFDM0osc0ZBQU0sU0FBUyxFQUFDLGVBQWU7Z0JBQUMsNEVBQUMsaUdBQWdCLElBQUMsRUFBRSxFQUFFLEtBQUssR0FBSSxDQUFPO1lBQ3JFLFFBQVEsQ0FBQyxDQUFDLENBQUMsc0ZBQU0sU0FBUyxFQUFDLDJCQUEyQixnQkFBYSxZQUFZLENBQUMsUUFBUSxDQUFDO2dCQUFFLDRFQUFDLDRLQUFvQixJQUFDLEVBQUUsRUFBRSxRQUFRLEdBQUksQ0FBTyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQzFJO1FBQ1IscUZBQUssU0FBUyxFQUFFLDJCQUFvQixNQUFNLElBQUksTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBRTtZQUM5RSxzRkFBTSxTQUFTLEVBQUMseUJBQXlCLGlCQUFhLE1BQU0sR0FBUTtZQUNwRSx1RkFDRSxJQUFJLEVBQUMsTUFBTSxFQUNYLEdBQUcsRUFBRSxRQUFRLENBQUMsRUFBRSxRQUFRLEVBQUUsYUFBYSxFQUFFLE9BQU8sRUFBRSxlQUFlLEVBQUUsQ0FBQyxFQUNwRSxTQUFTLEVBQUMscUNBQXFDLEVBQy9DLEVBQUUsRUFBRSxLQUFLLEVBQ1QsSUFBSSxFQUFFLEtBQUssRUFDWCxLQUFLLEVBQUUsS0FBSyxFQUNaLFlBQVksRUFBRSxLQUFLLEVBQ25CLE1BQU0sRUFBRSxZQUFtQixFQUMzQixRQUFRLEVBQUUsVUFBQyxDQUFDLElBQUssbUJBQVksQ0FBQyxDQUFDLENBQUMsRUFBZixDQUFlLEdBQ2hDO1lBQ0QsTUFBTSxJQUFJLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsc0ZBQU0sU0FBUyxFQUFDLG9CQUFvQjtnQkFDN0Qsc0ZBQU0sU0FBUyxFQUFDLDBDQUEwQyxpQkFBYyxJQUFJO29CQUMxRSxzRkFBTSxTQUFTLEVBQUMsaUJBQWlCLEdBQVE7b0JBQUEsc0ZBQU0sU0FBUyxFQUFDLGlCQUFpQixHQUFRLENBQzdFO2dCQUNQLHNGQUFNLFNBQVMsRUFBQyxXQUFXO29CQUFDLDRFQUFDLDRLQUFvQixJQUFDLEVBQUUsRUFBRSxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxLQUFLLFNBQVMsQ0FBQyxDQUFDLENBQUMsdUJBQXVCLENBQUMsQ0FBQyxDQUFDLHVCQUFnQixLQUFLLGFBQVUsR0FBSSxDQUFPLENBQ3hKLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FDVjtRQUVKLFNBQVMsQ0FBQyxDQUFDLENBQUMscUZBQUssU0FBUyxFQUFDLDJDQUEyQztZQUNwRSxxRkFBSyxTQUFTLEVBQUMsc0JBQXNCO2dCQUNuQyx1RkFBTyxPQUFPLEVBQUMsV0FBVyxFQUFDLFNBQVMsRUFBQyx5QkFBeUI7b0JBQzVELHNGQUFNLFNBQVMsRUFBQyxlQUFlO3dCQUFDLDRFQUFDLGlHQUFnQixJQUFDLEVBQUUsRUFBRSxTQUFTLEdBQUksQ0FBTztvQkFDekUsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLHNGQUFNLFNBQVMsRUFBQywyQkFBMkI7d0JBQUMsNEVBQUMsaUdBQWdCLElBQUMsRUFBRSxFQUFDLGdCQUFnQixHQUFHLENBQU8sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUNsSDtnQkFDUixxRkFBSyxTQUFTLEVBQUMsU0FBUztvQkFDdEIsdUZBQ0UsSUFBSSxFQUFDLE1BQU0sRUFDWCxHQUFHLEVBQUUsUUFBUSxFQUNiLFNBQVMsRUFBQyxxQ0FBcUMsRUFDL0MsRUFBRSxFQUFFLFNBQVMsRUFDYixJQUFJLEVBQUUsU0FBUyxFQUNmLEtBQUssRUFBRSxTQUFTLEVBQ2hCLFNBQVMsRUFBRSxFQUFFLEVBQ2IsWUFBWSxFQUFFLFFBQVEsRUFDdEIsTUFBTSxFQUFFLFlBQW1CLEVBQzNCLFFBQVEsRUFBRSxVQUFDLENBQUMsSUFBSyxtQkFBWSxDQUFDLENBQUMsQ0FBQyxFQUFmLENBQWUsR0FDaEMsQ0FDRSxDQUNGLENBQ0YsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUVYLENBQ1AsQ0FBQztBQUNKLENBQUMsQ0FBQztBQUVGLElBQU0sc0JBQVksR0FBRztJQUNuQixhQUFhLEVBQUUsS0FBSztJQUNwQixlQUFlLEVBQUUsS0FBSztJQUN0QixjQUFjLEVBQUUsRUFBRTtJQUNsQixLQUFLLEVBQUUsRUFBRTtJQUNULFFBQVEsRUFBRSxFQUFFO0NBQ2IsQ0FBQzs7O0FDakc2QjtBQUVlO0FBYzlDLElBQU0sTUFBTSxHQUF1QixVQUFDLEVBQTZEO1FBQTNELE1BQU0sY0FBRSxRQUFRLGdCQUFFLGdCQUFnQix3QkFBRSxxQkFBcUI7SUFBTyxhQUFNLENBQUMsQ0FBQyxDQUFDLHdGQUFRLFNBQVMsRUFBRSxrQ0FBMkIsUUFBUSxDQUFDLENBQUMsQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDLEVBQUUsY0FBSSxnQkFBZ0IsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxFQUFFLGNBQUkscUJBQXFCLENBQUU7UUFDMVAsNEVBQUMsaUdBQWdCLElBQUMsRUFBRSxFQUFFLE1BQU0sR0FBSSxDQUN6QixDQUFDLENBQUMsQ0FBQyxJQUFJO0FBRnNGLENBRXRGLENBQUM7QUFFVixJQUFNLFFBQVEsR0FBdUIsVUFBQyxFQUU1QztRQURDLFNBQVMsaUJBQUUsUUFBUSxnQkFBRSxNQUFNLGNBQUUsZ0JBQWdCLHdCQUFFLHFCQUFxQiw2QkFBRSxRQUFRLGdCQUFFLGVBQWU7SUFDM0YsaUdBQVUsU0FBUyxFQUFFLDJCQUFvQixTQUFTLENBQUUsSUFDdkQsZ0JBQWdCLENBQUMsQ0FBQztRQUNqQjtZQUNFLDRFQUFDLE1BQU0sSUFBQyxNQUFNLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxRQUFRLEVBQUUsZ0JBQWdCLEVBQUUsZ0JBQWdCLEVBQUUscUJBQXFCLEVBQUUscUJBQXFCLEdBQUk7WUFDL0gsUUFBUSxDQUNSLENBQUMsQ0FBQztRQUNMLHFGQUFLLFNBQVMsRUFBRSwrQkFBd0IsZUFBZSxDQUFFO1lBQ3ZELDRFQUFDLE1BQU0sSUFBQyxNQUFNLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxRQUFRLEVBQUUsZ0JBQWdCLEVBQUUsZ0JBQWdCLEVBQUUscUJBQXFCLEVBQUUscUJBQXFCLEdBQUk7WUFDL0gsUUFBUSxDQUNMLENBQ0M7QUFWTCxDQVVLLENBQUM7OztBQ2hDZTtBQUNBO0FBQ0E7QUFDQztBQUNEOzs7O0FDSnNEO0FBQ2xEO0FBQ2U7QUFJOUMsSUFBWSxZQUtYO0FBTEQsV0FBWSxZQUFZO0lBQ3RCLHNDQUFzQjtJQUN0QixrQ0FBa0I7SUFDbEIscURBQXFDO0lBQ3JDLHlDQUF5QjtBQUMzQixDQUFDLEVBTFcsWUFBWSxLQUFaLFlBQVksUUFLdkI7QUFVTSxJQUFNLE1BQU0sR0FBRyxVQUFDLEtBQXFCO0lBQzFDLElBQU0sWUFBWSxHQUFHLHNCQUFLLG1CQUFZLEdBQUssS0FBSyxDQUFFLENBQUM7SUFDM0MsWUFBUSxHQUE4QyxZQUFZLFNBQTFELEVBQUUsT0FBTyxHQUFxQyxZQUFZLFFBQWpELEVBQUUsT0FBTyxHQUE0QixZQUFZLFFBQXhDLEVBQUUsUUFBUSxHQUFrQixZQUFZLFNBQTlCLEVBQUUsV0FBVyxHQUFLLFlBQVksWUFBakIsQ0FBa0I7SUFFM0UsT0FBTyxDQUNMLDRFQUFDLGtLQUFVLENBQUMsU0FBUztRQUNuQiw0RUFBQyxrS0FBVSxDQUFDLEtBQUssSUFBQyxTQUFTLEVBQUUsdUZBQXVGO1lBQ2xILHNGQUFNLFNBQVMsRUFBRSxzQkFBZSxRQUFRLGNBQUksV0FBVyxlQUFZLGlCQUFlLElBQUk7Z0JBQUUsc0ZBQU0sU0FBUyxFQUFFLG1CQUFtQixHQUFTO2dCQUFBLHNGQUFNLFNBQVMsRUFBQyxtQkFBbUIsR0FBUSxDQUFPO1lBQ3ZMLHFGQUFLLEVBQUUsRUFBQywwQkFBMEIsRUFBQyxTQUFTLEVBQUMsdURBQXVEO2dCQUNsRyxvRkFBSSxTQUFTLEVBQUMsa0VBQWtFO29CQUFDLDRFQUFDLDRLQUFvQixJQUFDLEVBQUUsRUFBRSxPQUFPLEdBQUksQ0FBSztnQkFFekgsT0FBTyxDQUFDLENBQUMsQ0FBQyxtRkFBRyxTQUFTLEVBQUMsMENBQTBDO29CQUFDLDRFQUFDLDRLQUFvQixJQUFDLEVBQUUsRUFBRSxPQUFPLEdBQUksQ0FBSSxDQUFDLENBQUMsQ0FBQyxJQUFJO2dCQUdsSCxRQUFRLENBQUMsQ0FBQyxDQUFDLHdGQUVQLFFBQVEsSUFBSSxRQUFRLENBQUMsR0FBRyxDQUFDLGlCQUFPLElBQUksMkZBQUksU0FBUyxFQUFDLE9BQU87b0JBQUMsbUZBQUcsRUFBRSxFQUFFLGtCQUFXLE9BQU8sQ0FBQyxFQUFFLENBQUUsRUFBRSxJQUFJLEVBQUUsV0FBSSxPQUFPLENBQUMsRUFBRSxDQUFFLEVBQUUsU0FBUyxFQUFDLDZCQUE2QixFQUFDLEtBQUssRUFBRSxPQUFPLENBQUMsRUFBRTt3QkFBRSw0RUFBQyxpR0FBZ0IsSUFBQyxFQUFFLEVBQUUsT0FBTyxDQUFDLEVBQUUsR0FBSSxDQUFJO29CQUNuTixzRkFBTSxTQUFTLEVBQUMsYUFBYTs7d0JBQWUsT0FBTyxDQUFDLEtBQUssS0FBSyxVQUFVLENBQUMsQ0FBQyxDQUFDLDRFQUFDLGlHQUFnQixJQUFDLEVBQUUsRUFBQyx1QkFBdUIsR0FBRyxDQUFDLENBQUMsQ0FBQyw0RUFBQyxpR0FBZ0IsSUFBQyxFQUFFLEVBQUUsZUFBZSxHQUFHLE9BQU8sQ0FBQyxFQUFFLEdBQUcsR0FBRyxHQUFHLE9BQU8sQ0FBQyxLQUFLLEdBQUksQ0FBUSxDQUM5TSxFQUYrQixDQUUvQixDQUFDLENBRUwsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUVWLENBQ1csQ0FDRSxDQUN4QixDQUFDO0FBQ0osQ0FBQyxDQUFDO0FBRUYsSUFBTSxtQkFBWSxHQUFHO0lBQ25CLFFBQVEsRUFBRSxZQUFZLENBQUMsSUFBSTtJQUMzQixXQUFXLEVBQUUsV0FBVztDQUN6QixDQUFDOzs7O0FDcEQ2QjtBQUNlO0FBQ3NCO0FBRXBFLElBQVksV0FPWDtBQVBELFdBQVksV0FBVztJQUNyQix3QkFBUztJQUNULHdCQUFTO0lBQ1Qsd0JBQVM7SUFDVCx3QkFBUztJQUNULHdCQUFTO0lBQ1Qsd0JBQVM7QUFDWCxDQUFDLEVBUFcsV0FBVyxLQUFYLFdBQVcsUUFPdEI7QUFTTSxJQUFNLE9BQU8sR0FBRyxVQUFDLEtBQXFCO0lBQzNDLElBQU0sWUFBWSxHQUFHLHNCQUFLLG9CQUFZLEdBQUssS0FBSyxDQUFDLENBQUM7SUFDMUMsT0FBRyxHQUE0QyxZQUFZLElBQXhELEVBQUUsZUFBZSxHQUEyQixZQUFZLGdCQUF2QyxFQUFFLE9BQU8sR0FBa0IsWUFBWSxRQUE5QixFQUFFLFdBQVcsR0FBSyxZQUFZLFlBQWpCLENBQWtCO0lBQ3BFLElBQU0sR0FBRyxHQUFHLEdBQUcsSUFBSSxJQUFJLENBQUM7SUFFeEIsT0FBTyxDQUNMO1FBQ0UsNEVBQUMsR0FBRyxJQUFDLFNBQVMsRUFBRSxtREFBNEMsZUFBZSxDQUFFO1lBQzNFLDRFQUFDLGlHQUFnQixJQUFDLEVBQUUsRUFBRSxPQUFPLEdBQUksQ0FDN0I7UUFFSixXQUFXLENBQUMsQ0FBQyxDQUFDO1lBQ1osc0ZBQU0sU0FBUyxFQUFDLDBCQUEwQixHQUFRO1lBQ2xELG1GQUFHLFNBQVMsRUFBQyxVQUFVO2dCQUFDLDRFQUFDLDRLQUFvQixJQUFDLEVBQUUsRUFBRSxXQUFXLEdBQUksQ0FBSSxDQUNwRSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBRVgsQ0FDSixDQUFDO0FBQ0osQ0FBQyxDQUFDO0FBRUYsSUFBTSxvQkFBWSxHQUFHO0lBQ25CLGVBQWUsRUFBRSxFQUFFO0lBQ25CLFdBQVcsRUFBRSxFQUFFO0NBQ2hCLENBQUM7Ozs7QUMzQzZCO0FBQzJCO0FBQ1Q7QUFDRDtBQUNrQjtBQVFsRTtJQUE0QiwwQkFBbUQ7SUFDN0UsZ0JBQVksS0FBVTtRQUNwQixrQkFBSyxZQUFDLEtBQUssQ0FBQyxTQUFDO1FBR1Asa0JBQVksR0FBRztZQUNyQixHQUFHLEVBQUUsV0FBVyxDQUFDLEVBQUU7WUFDbkIsVUFBVSxFQUFFLHdCQUF3QjtZQUNwQyxPQUFPLEVBQUUsMkJBQTJCO1NBQ3JDLENBQUM7O0lBTkYsQ0FBQztJQVFELHVCQUFNLEdBQU47UUFDRSxPQUFPLENBQ0w7WUFDRSw0RUFBQyxrS0FBVSxDQUFDLFNBQVM7Z0JBQ25CLDRFQUFDLGtLQUFVLENBQUMsYUFBYTtvQkFDdkIsc0ZBQU0sU0FBUyxFQUFDLHFCQUFxQixHQUFRO29CQUM3Qyw0RUFBQyxPQUFPLGVBQUssSUFBSSxDQUFDLFlBQVksRUFBSTtvQkFDbEMsc0ZBQU0sU0FBUyxFQUFDLDRCQUE0QixHQUFRLENBQzNCLENBQ047WUFDdkIsNEVBQUMsTUFBTSxJQUFDLFFBQVEsRUFBRSxZQUFZLENBQUMsSUFBSSxFQUFFLE9BQU8sRUFBRSxzQkFBc0IsRUFBRSxPQUFPLEVBQUUsc0JBQXNCLEdBQUk7WUFDeEcsTUFBTSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FDdkMsNEVBQUMsTUFBTSxJQUFDLFFBQVEsRUFBRSxZQUFZLENBQUMsS0FBSyxFQUFFLE9BQU8sRUFBRSxnQkFBZ0IsRUFBRSxRQUFRLEVBQUUsZUFBZSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLEdBQUksQ0FDbEgsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUNQLENBQ0osQ0FBQztJQUNKLENBQUM7SUFDSCxhQUFDO0FBQUQsQ0FBQyxDQTVCMkIsMkVBQW1CLEdBNEI5Qzs7OztBQ3hDd0I7QUFDQztBQUNEOzs7O0FDRk07QUFDa0I7QUFDUDtBQUVhO0FBQ2dGO0FBQ25EO0FBQ25DO0FBRTFDLElBQU0sa0JBQWtCLEdBQUc7O0lBQ2hDLElBQU0sa0JBQWtCLEdBQW9DLG9HQUFXLENBQUMsVUFBQyxLQUFrQixJQUFLLFlBQUssYUFBTCxLQUFLLHVCQUFMLEtBQUssQ0FBRSxrQkFBa0IsRUFBekIsQ0FBeUIsQ0FBQyxDQUFDO0lBQzNILElBQU0saUJBQWlCLEdBQW1DLG9HQUFXLENBQUMsVUFBQyxLQUFrQixJQUFLLFlBQUssYUFBTCxLQUFLLHVCQUFMLEtBQUssQ0FBRSxpQkFBaUIsRUFBeEIsQ0FBd0IsQ0FBQyxDQUFDO0lBQ2xILGdCQUFvQyxzRUFBYyxDQUFDLGNBQWMsQ0FBQyxLQUFLLENBQUMsTUFBdkUsYUFBYSxVQUFFLGdCQUFnQixRQUF3QyxDQUFDO0lBQ3ZFLFlBQVEsR0FBSyxjQUFjLEVBQUUsU0FBckIsQ0FBc0I7SUFFdEMsSUFBTSxZQUFZLEdBQUcsVUFBQyxDQUFzQztRQUNwRCxTQUFrQixDQUFDLENBQUMsTUFBTSxFQUF4QixLQUFLLGFBQUUsSUFBSSxVQUFhLENBQUM7UUFFakMsUUFBUSxLQUFLLEVBQUUsQ0FBQztZQUNkLEtBQUssY0FBYyxDQUFDLEtBQUssQ0FBQztZQUMxQixLQUFLLGNBQWMsQ0FBQyxLQUFLLENBQUM7WUFDMUIsS0FBSyxjQUFjLENBQUMsWUFBWTtnQkFDOUIsZ0JBQWdCLENBQUMsS0FBSyxDQUFDLENBQUM7Z0JBQ3hCLE1BQU07WUFDUjtnQkFDRSxNQUFNO1FBQ1YsQ0FBQztRQUdELFFBQVEsSUFBSSxFQUFFLENBQUM7WUFDYixLQUFLLGNBQWMsQ0FBQyxLQUFLLEdBQUcsUUFBUSxDQUFDO1lBQ3JDLEtBQUssY0FBYyxDQUFDLFlBQVksR0FBRyxRQUFRLENBQUM7WUFDNUMsS0FBSyx5QkFBeUI7Z0JBQzVCLFFBQVEsQ0FBQyxJQUFJLEVBQUUsVUFBVSxDQUFDLEtBQUssQ0FBQyxFQUFFLElBQUksQ0FBQyxDQUFDO2dCQUV4QyxNQUFNO1lBQ1IsS0FBSyxjQUFjLENBQUMsS0FBSyxHQUFHLE1BQU0sQ0FBQztZQUNuQyxLQUFLLHNCQUFzQjtnQkFDekIsUUFBUSxDQUFDLElBQUksRUFBRSxhQUFhLENBQUMsS0FBSyxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUM7Z0JBQzNDLE1BQU07WUFDUixLQUFLLHNCQUFzQjtnQkFDekIsUUFBUSxDQUFDLElBQUksRUFBRSxVQUFVLENBQUMsS0FBSyxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUM7Z0JBQ3hDLE1BQU07WUFDUixLQUFLLGNBQWMsQ0FBQyxLQUFLLEdBQUcsUUFBUTtnQkFDbEMsUUFBUSxDQUFDLElBQUksRUFBRSxLQUFLLEVBQUUsSUFBSSxDQUFDLENBQUM7Z0JBQzVCLE1BQU07WUFDUjtnQkFDRSxNQUFNO1FBQ1YsQ0FBQztJQUNILENBQUMsQ0FBQztJQUVGLHVFQUFlLENBQUM7UUFDZCxnQkFBZ0IsQ0FBQyxtQkFBa0IsYUFBbEIsa0JBQWtCLHVCQUFsQixrQkFBa0IsQ0FBRSxzQkFBc0IsRUFBQyxDQUFDLENBQUMsa0JBQWtCLENBQUMsc0JBQXNCLENBQUMsQ0FBQyxDQUFDLGNBQWMsQ0FBQyxLQUFZLENBQUMsQ0FBQztJQUN6SSxDQUFDLEVBQUUsQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLENBQUM7SUFFekIsSUFBTSxZQUFZLEdBQUc7UUFDbkIsR0FBRyxFQUFFLFdBQVcsQ0FBQyxFQUFFO1FBQ25CLGVBQWUsRUFBRSx3QkFBd0I7UUFDekMsT0FBTyxFQUFFLHFCQUFxQjtLQUMvQixDQUFDO0lBRUYsT0FBTyxDQUNMLHFGQUFLLFNBQVMsRUFBQyxrQkFBa0IsRUFBQyxFQUFFLEVBQUMsVUFBVTtRQUM3Qyw0RUFBQyxPQUFPLGVBQUssWUFBWSxFQUFJO1FBQzdCLHNGQUFNLFNBQVMsRUFBQyxxQkFBcUIsR0FBUTtRQUM3QyxxRkFBSyxTQUFTLEVBQUMsc0JBQXNCO1lBQ25DLDRFQUFDLFFBQVEsSUFDUCxNQUFNLEVBQUUsNEJBQTRCLEVBQ3BDLFFBQVEsRUFBRSxJQUFJLEVBQ2QsZUFBZSxFQUFFLFVBQVUsRUFDM0IsZ0JBQWdCLEVBQUUsS0FBSztnQkFFdkIscUZBQUssU0FBUyxFQUFDLHNCQUFzQjtvQkFDbkMscUZBQUssU0FBUyxFQUFDLHFCQUFxQixHQUFPO29CQUV6QyxPQUFPLENBQUMsY0FBYyxFQUFFLFVBQUMsSUFBb0I7d0JBQzNDLG1GQUFDLFFBQVEsSUFDUCxLQUFLLEVBQUUsNEJBQTRCLEVBQ25DLEtBQUssRUFBRSxJQUFJLEVBQ1gsWUFBWSxFQUFFLFlBQVksRUFDMUIsT0FBTyxFQUFFLElBQUksS0FBSyxhQUFhLEdBQy9CO29CQUxGLENBS0UsQ0FBQyxDQUVIO2dCQUVKLE9BQU8sQ0FBQyxjQUFjLEVBQUUsVUFBQyxJQUFvQjs7b0JBQUssbUZBQUMsU0FBUyxJQUMxRCxhQUFhLEVBQUUsYUFBYSxLQUFLLElBQUksRUFDckMsS0FBSyxFQUFFLElBQUksR0FBRyxRQUFRLEVBQ3RCLGNBQWMsRUFBRSwrQkFBd0IsSUFBSSxLQUFLLGFBQWEsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUUsRUFDbEYsUUFBUSxFQUFFLElBQUksR0FBRyxTQUFTLEVBQzFCLFNBQVMsRUFBRSxJQUFJLEtBQUssY0FBYyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsSUFBSSxHQUFHLE1BQU0sQ0FBQyxDQUFDLENBQUMsS0FBSyxFQUNoRSxrQkFBa0IsRUFBRSxJQUFJLEVBQ3hCLGVBQWUsRUFBRSxJQUFJLEtBQUssY0FBYyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxtQkFBbUIsRUFDakYsS0FBSyxFQUFFLGVBQWUsQ0FBQyxJQUFJLEVBQUUsa0JBQWtCLENBQUMsRUFDaEQsUUFBUSxFQUFFLHdCQUFrQixhQUFsQixrQkFBa0IsdUJBQWxCLGtCQUFrQixDQUFFLFlBQVksMENBQUUsY0FBYyxFQUMxRCxZQUFZLEVBQUUsVUFBQyxDQUFzQyxJQUFLLG1CQUFZLENBQUMsQ0FBQyxDQUFDLEVBQWYsQ0FBZSxHQUN6RTtpQkFBQSxDQUFDLENBRUk7WUFDWCw0RUFBQyxRQUFRLElBQ1AsTUFBTSxFQUFFLHlCQUF5QixFQUNqQyxRQUFRLEVBQUUsS0FBSyxFQUNmLGdCQUFnQixFQUFFLElBQUk7Z0JBRXRCLDRFQUFDLFNBQVMsSUFDUixhQUFhLEVBQUUsS0FBSyxFQUNwQixLQUFLLEVBQUUseUJBQXlCLEVBQ2hDLFFBQVEsRUFBRSxrQkFBa0IsRUFDNUIsU0FBUyxFQUFFLHNCQUFzQixFQUNqQyxrQkFBa0IsRUFBRSxJQUFJLEVBQ3hCLGVBQWUsRUFBRSxtQkFBbUIsRUFDcEMsS0FBSyxFQUFFLHdCQUFrQixhQUFsQixrQkFBa0IsdUJBQWxCLGtCQUFrQixDQUFFLGVBQWUsMENBQUUsV0FBVyxFQUN2RCxRQUFRLEVBQUUsd0JBQWtCLGFBQWxCLGtCQUFrQix1QkFBbEIsa0JBQWtCLENBQUUsZUFBZSwwQ0FBRSxjQUFjLEVBQzdELFlBQVksRUFBRSxVQUFDLENBQXNDLElBQUssbUJBQVksQ0FBQyxDQUFDLENBQUMsRUFBZixDQUFlLEdBQ3pFO2dCQUVGLDRFQUFDLFNBQVMsSUFDUixLQUFLLEVBQUUsWUFBWSxFQUNuQixLQUFLLEVBQUUsaUJBQWlCLGFBQWpCLGlCQUFpQix1QkFBakIsaUJBQWlCLENBQUUsU0FBUyxFQUNuQyxZQUFZLEVBQUUsVUFBQyxDQUFzQyxJQUFLLG1CQUFZLENBQUMsQ0FBQyxDQUFDLEVBQWYsQ0FBZSxHQUN6RTtnQkFFRiw0RUFBQyxTQUFTLElBQ1IsS0FBSyxFQUFFLFlBQVksRUFDbkIsS0FBSyxFQUFFLGlCQUFpQixhQUFqQixpQkFBaUIsdUJBQWpCLGlCQUFpQixDQUFFLFNBQVMsRUFDbkMsWUFBWSxFQUFFLFVBQUMsQ0FBc0MsSUFBSyxtQkFBWSxDQUFDLENBQUMsQ0FBQyxFQUFmLENBQWUsR0FDekU7Z0JBRUYsNEVBQUMsU0FBUyxJQUNSLEtBQUssRUFBRSxxQkFBcUIsRUFDNUIsS0FBSyxFQUFFLGlCQUFpQixhQUFqQixpQkFBaUIsdUJBQWpCLGlCQUFpQixDQUFFLGtCQUFrQixFQUM1QyxZQUFZLEVBQUUsVUFBQyxDQUFzQyxJQUFLLG1CQUFZLENBQUMsQ0FBQyxDQUFDLEVBQWYsQ0FBZSxHQUN6RTtnQkFFRiw0RUFBQyxTQUFTLElBQ1IsS0FBSyxFQUFFLHNCQUFzQixFQUM3QixlQUFlLEVBQUUsbUJBQW1CLEVBQ3BDLEtBQUssRUFBRSxpQkFBaUIsYUFBakIsaUJBQWlCLHVCQUFqQixpQkFBaUIsQ0FBRSxtQkFBbUIsRUFDN0MsWUFBWSxFQUFFLFVBQUMsQ0FBc0MsSUFBSyxtQkFBWSxDQUFDLENBQUMsQ0FBQyxFQUFmLENBQWUsR0FDekU7Z0JBRUYsNEVBQUMsUUFBUSxJQUNQLEtBQUssRUFBRSx5QkFBeUIsRUFDaEMsS0FBSyxFQUFFLEtBQUssRUFDWixPQUFPLEVBQUUsaUJBQWlCLGFBQWpCLGlCQUFpQix1QkFBakIsaUJBQWlCLENBQUUsc0JBQXNCLEVBQ2xELFlBQVksRUFBRSxVQUFDLENBQXNDLElBQUssbUJBQVksQ0FBQyxDQUFDLENBQUMsRUFBZixDQUFlLEdBQ3pFO2dCQUVGLDRFQUFDLFFBQVEsSUFDUCxLQUFLLEVBQUUsc0JBQXNCLEVBQzdCLFFBQVEsRUFBRSwrQkFBK0IsRUFDekMsS0FBSyxFQUFFLGlCQUFpQixhQUFqQixpQkFBaUIsdUJBQWpCLGlCQUFpQixDQUFFLG1CQUFtQixFQUM3QyxTQUFTLEVBQUUsR0FBRyxFQUNkLFlBQVksRUFBRSxVQUFDLENBQXNDLElBQUssbUJBQVksQ0FBQyxDQUFDLENBQUMsRUFBZixDQUFlLEdBQ3pFLENBQ08sQ0FDUCxDQUNGLENBQ1AsQ0FBQztBQUNKLENBQUMsQ0FBQzs7O0FDL0ptQzs7O0FDQU47QUFDOEI7QUFHWjtBQVExQyxJQUFNLFdBQVcsR0FBK0Msa0VBQVUsQ0FBQyxVQUFDLEtBQVU7O0lBQ25GLGdCQUFZLEdBQTZCLEtBQUssYUFBbEMsRUFBRSxhQUFhLEdBQWMsS0FBSyxjQUFuQixFQUFFLE9BQU8sR0FBSyxLQUFLLFFBQVYsRUFDeEMsUUFBUSxHQUFLLGNBQWMsRUFBRSxTQUFyQixDQUFzQjtJQUVsQyxPQUFPO1FBQ0wsdUZBQU8sRUFBRSxFQUFFLGFBQWEsR0FBRyxhQUFhLENBQUMsSUFBSSxFQUFFLFNBQVMsRUFBQyx1REFBdUQ7WUFDOUcsdUZBQ0UsSUFBSSxFQUFDLE9BQU8sRUFDWixHQUFHLEVBQUUsUUFBUSxDQUFDLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDLEVBQ2pDLEVBQUUsRUFBRSxZQUFZLEdBQUcsYUFBYSxDQUFDLElBQUksRUFDckMsSUFBSSxFQUFDLGFBQWEsRUFDbEIsS0FBSyxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLEVBQ3BDLFFBQVEsRUFBRSxVQUFDLENBQUMsSUFBSyxtQkFBWSxDQUFDLENBQUMsQ0FBQyxFQUFmLENBQWUsRUFDaEMsT0FBTyxFQUFFLE9BQU8sQ0FBQyxJQUFJLEtBQUssYUFBYSxDQUFDLElBQUksR0FDNUM7WUFDRix1RkFBTyxTQUFTLEVBQUMsaUJBQWlCLEVBQUMsT0FBTyxFQUFFLFlBQVksR0FBRyxhQUFhLENBQUMsSUFBSSxJQUMxRSxPQUFPLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7Z0JBQzVCLDRFQUFDLDhGQUFhLElBQUMsS0FBSyxFQUFFLGFBQWEsQ0FBQyxJQUFjLEVBQUUsSUFBSSxFQUFDLFNBQVMsRUFBQyxPQUFPLEVBQUMsTUFBTSxFQUFDLEtBQUssRUFBQyxNQUFNLEVBQUMsR0FBRyxFQUFDLFNBQVMsRUFBQyxRQUFRLEVBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQztnQkFDaEksd0JBQXdCLENBQ3BCO1lBQ1AsT0FBTyxDQUFDLGFBQWEsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLHNGQUFNLFNBQVMsRUFBQyxpQkFBaUI7Z0JBQUMsNEVBQUMsaUdBQWdCLElBQUMsRUFBRSxFQUFFLG1CQUFhLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxVQUFDLElBQVMsSUFBSyxXQUFJLENBQUMsV0FBVyxFQUFoQixDQUFnQixDQUFDLDBDQUFFLFlBQVksR0FBSSxDQUFPLENBQUMsQ0FBQyxDQUFDLElBQUk7WUFDaE0sc0ZBQU0sU0FBUyxFQUFDLGNBQWMsR0FBUSxDQUNoQyxDQUNQLENBQUM7QUFDTixDQUFDLENBQUMsQ0FBQzs7OztBQ3BDNEI7QUFDWTtBQUN1QjtBQUViO0FBQ2U7QUFTcEU7SUFBK0IsNkJBQW9DO0lBRWpFLG1CQUFZLEtBQXFCO1FBQy9CLGFBQUssWUFBQyxLQUFLLENBQUMsU0FBQztJQUNmLENBQUM7SUFFRCxxQ0FBaUIsR0FBakI7UUFDRSxJQUFJLENBQUMsS0FBSyxDQUFDLGVBQWUsRUFBRSxDQUFDO0lBQy9CLENBQUM7SUFFRCwwQkFBTSxHQUFOO1FBQ1EsU0FBbUQsSUFBSSxDQUFDLEtBQUssRUFBM0QsY0FBYyxzQkFBRSxVQUFVLGtCQUFFLGdCQUFnQixzQkFBZSxDQUFDO1FBRXBFLE9BQU8scUZBQUssU0FBUyxFQUFDLGdFQUFnRTtZQUNwRixxRkFBSyxTQUFTLEVBQUMsc0JBQXNCLElBRWxDLGNBQWMsSUFBSSxjQUFjLENBQUMsR0FBRyxDQUFDLFVBQUMsR0FBRyxFQUFFLFFBQVE7Z0JBQ2xELDRGQUFLLFNBQVMsRUFBQyxFQUFFO29CQUNmLHFGQUFLLFNBQVMsRUFBRSxHQUFHLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUFDLFlBQVksS0FBSyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLENBQUMsZUFBZTt3QkFDdEcsdUZBQU8sT0FBTyxFQUFFLFdBQVcsR0FBRyxRQUFRLEVBQUUsU0FBUyxFQUFDLHdGQUF3Rjs0QkFDeEksNEVBQUMsOEZBQWEsSUFBQyxLQUFLLEVBQUUsWUFBWSxDQUFDLEdBQUcsQ0FBQyxJQUFjLENBQUMsRUFBRSxPQUFPLEVBQUMsTUFBTSxFQUFDLFFBQVEsRUFBQyxLQUFLLEdBQUc7NEJBQ3hGLG9GQUFJLFNBQVMsRUFBRSxVQUFVLEdBQUk7NEJBQzdCLHNGQUFNLFNBQVMsRUFBQyx5Q0FBeUMsU0FBVTs0QkFDbkUsNEVBQUMsOEZBQWEsSUFBQyxLQUFLLEVBQUUsWUFBWSxDQUFDLEdBQUcsQ0FBQyxJQUFjLENBQUMsRUFBRSxJQUFJLEVBQUMsU0FBUyxFQUFDLEtBQUssRUFBQyxPQUFPLEVBQUMsR0FBRyxFQUFDLFNBQVMsRUFBQyxRQUFRLEVBQUMsS0FBSyxHQUFHLENBQzlHO3dCQUVSLG9GQUFJLFNBQVMsRUFBQyxpQ0FBaUMscUJBQWlCLFlBQVksSUFFeEUsR0FBRyxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsa0JBQVE7NEJBQ3hCLElBQU0sZ0JBQWdCLEdBQUcsZ0JBQWdCLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUFDLFlBQVksS0FBSyxRQUFRLENBQUMsWUFBWSxJQUFJLGdCQUFnQixDQUFDLElBQUksS0FBSyxHQUFHLENBQUMsSUFBSSxDQUFDOzRCQUNwSSxPQUFPLG9GQUFJLFNBQVMsRUFBRSxrQkFBVyxnQkFBZ0IsQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUU7Z0NBQ25FLHdGQUFRLEVBQUUsRUFBRSxlQUFRLFFBQVEsQ0FBQyxZQUFZLENBQUUsRUFBRSxPQUFPLEVBQUUsVUFBQyxDQUFDLElBQUssaUJBQVUsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLElBQUksRUFBRSxRQUFRLENBQUMsRUFBakMsQ0FBaUMsRUFBRSxTQUFTLEVBQUUsdUJBQWdCLFFBQVEsQ0FBQyxZQUFZLEtBQUssU0FBUyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMscUJBQXFCLENBQUMsQ0FBQyxDQUFDLEVBQUUsY0FBSSxRQUFRLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLFVBQVUsY0FBSSxRQUFRLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBRSxFQUFFLFFBQVEsRUFBRSxDQUFDO29DQUNsUyw0RUFBQyw0S0FBb0IsSUFBQyxFQUFFLEVBQUUsUUFBUSxDQUFDLFlBQVksR0FBSSxDQUM1QyxDQUNOLENBQUM7d0JBQ1IsQ0FBQyxDQUFDLENBRUQsQ0FDRCxDQUNGO1lBdEJOLENBc0JNLENBQ1AsQ0FFRyxDQUNGLENBQUM7SUFDVCxDQUFDO0lBM0NNLHFCQUFXLEdBQUcsV0FBVyxDQUFDO0lBNENuQyxnQkFBQztDQUFBLENBN0M4Qix1RUFBZSxHQTZDN0M7QUE3Q3FCOzs7QUNkUTtBQUNGOzs7O0FDREc7QUFDa0I7QUFDSDtBQUMyQztBQUNwQztBQUNFO0FBRVc7QUFFMUQsV0FBTyxHQUFLLGtLQUFVLFFBQWYsQ0FBZ0I7QUFvQi9CO0lBQStCLDZCQUFpRjtJQUM5RyxtQkFBWSxLQUFVO1FBQ3BCLGtCQUFLLFlBQUMsS0FBSyxDQUFDLFNBQUM7UUFXZixrQkFBWSxHQUFHLFVBQUMsQ0FBc0M7WUFDNUMsU0FBSyxHQUFLLENBQUMsQ0FBQyxNQUFNLE1BQWIsQ0FBYztZQUMzQixRQUFRLEtBQUssRUFBRSxDQUFDO2dCQUNkLEtBQUssT0FBTztvQkFFVixLQUFJLENBQUMsUUFBUSxDQUFDO3dCQUNaLGFBQWEsRUFBRSxJQUFJO3FCQUVwQixDQUFDLENBQUM7b0JBQ0gsTUFBTTtnQkFDUjtvQkFDRSxLQUFJLENBQUMsUUFBUSxDQUFDO3dCQUNaLGFBQWEsRUFBRSxLQUFLO3dCQUNwQixnQkFBZ0IsRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBUTtxQkFDM0MsQ0FBQyxDQUFDO29CQUNILE1BQU07WUFDVixDQUFDO1FBQ0gsQ0FBQyxDQUFDO1FBRUYsZ0JBQVUsR0FBRyxVQUFDLENBQU0sRUFBRSxHQUFXLEVBQUUsUUFBb0I7WUFDckQsQ0FBQyxDQUFDLGNBQWMsRUFBRSxDQUFDO1lBRW5CLElBQU0sZ0JBQWdCLEdBQUcseUJBQUksS0FBSSxDQUFDLEtBQUssQ0FBQyxjQUFjLFNBQUMsQ0FBQztZQUV4RCxJQUFJLEtBQUksQ0FBQyxLQUFLLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksS0FBSyxHQUFHO2dCQUMzQyxLQUFJLENBQUMsS0FBSyxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLENBQUMsWUFBWSxLQUFLLFFBQVEsQ0FBQyxZQUFZLEVBQUUsQ0FBQztnQkFFbkYsS0FBSSxDQUFDLFFBQVEsQ0FBQztvQkFDWixjQUFjLEVBQUUsS0FBSSxDQUFDLEtBQUssQ0FBQyxjQUFjO29CQUN6QyxnQkFBZ0IsRUFBRSxLQUFJLENBQUMsS0FBSyxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUM7b0JBQzlDLGFBQWEsRUFBRSxLQUFLO29CQUNwQixTQUFTLEVBQUUsS0FBSztpQkFDakIsQ0FBQyxDQUFDO1lBQ0wsQ0FBQztpQkFBTSxDQUFDO2dCQUNOLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUUsSUFBSSxFQUFFLEdBQUcsRUFBRSxTQUFTLEVBQUUsQ0FBQyxzQkFBSyxRQUFRLEtBQUUsVUFBVSxFQUFFLElBQUksSUFBRyxFQUFFLENBQUM7Z0JBQ3BGLEtBQUksQ0FBQyxRQUFRLENBQUM7b0JBQ1osY0FBYyxFQUFFLGdCQUFnQjtvQkFDaEMsZ0JBQWdCLEVBQUUsZ0JBQWdCLENBQUMsQ0FBQyxDQUFDO29CQUNyQyxhQUFhLEVBQUUsS0FBSztvQkFDcEIsU0FBUyxFQUFFLEtBQUs7aUJBQ2pCLENBQUMsQ0FBQztZQUNMLENBQUM7UUFDSCxDQUFDLENBQUM7UUFFRixlQUFTLEdBQUcsVUFBQyxDQUFzQztZQUNqRCxDQUFDLENBQUMsY0FBYyxFQUFFLENBQUM7WUFDbkIsS0FBSSxDQUFDLFFBQVEsQ0FBQztnQkFDWixTQUFTLEVBQUUsSUFBSTtnQkFDZixhQUFhLEVBQUUsSUFBSTtnQkFHbkIsY0FBYyxFQUFFLENBQUMsS0FBSSxDQUFDLEtBQUssQ0FBQyxjQUFjLENBQUMsQ0FBQyxDQUFDLENBQUM7YUFDL0MsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDO1FBL0RBLEtBQUksQ0FBQyxLQUFLLEdBQUc7WUFDWCxhQUFhLEVBQUUsS0FBSztZQUNwQixnQkFBZ0IsRUFBRSxJQUFJO1lBQ3RCLGNBQWMsRUFBRSxFQUFFO1lBQ2xCLFNBQVMsRUFBRSxJQUFJO1NBQ2hCLENBQUM7UUFDRixLQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxLQUFJLENBQUMsQ0FBQztRQUM3QixLQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxLQUFJLENBQUMsQ0FBQzs7SUFDNUIsQ0FBQztJQXlERCxzQ0FBa0IsR0FBbEIsVUFBbUIsS0FBeUI7UUFDMUMsSUFDRSxJQUFJLENBQUMsS0FBSyxDQUFDLGNBQWMsSUFBSSxJQUFJLENBQUMsS0FBSyxDQUFDLGNBQWMsQ0FBQyxNQUFNLElBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLGNBQWMsQ0FBQyxLQUFLLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLGNBQWMsQ0FBQyxFQUNuSixDQUFDO1lBRUQsSUFBTSxZQUFZLEdBQTJCLGVBQWUsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLGNBQWMsQ0FBQyxDQUFDO1lBRXhGLElBQUksQ0FBQyxRQUFRLENBQUM7Z0JBQ1osY0FBYyxFQUFFLFlBQVk7Z0JBQzVCLGdCQUFnQixFQUFFLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSTtnQkFDL0QsU0FBUyxFQUFFLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLElBQUk7YUFDbEQsQ0FBQyxDQUFDO1FBQ0wsQ0FBQztJQUNILENBQUM7SUFFRCwwQkFBTSxHQUFOO1FBQUEsaUJBK0VDO1FBOUVPLFNBQTJELElBQUksQ0FBQyxLQUFLLEVBQW5FLG1CQUFtQiwyQkFBRSxjQUFjLHNCQUFFLGVBQWUscUJBQWUsQ0FBQztRQUN0RSxTQUFpRSxJQUFJLENBQUMsS0FBSyxFQUF6RSxhQUFhLHFCQUFFLGdCQUFnQix3QkFBRSxTQUFTLGlCQUFFLGNBQWMsb0JBQWUsQ0FBQztRQUNsRixJQUFNLFlBQVksR0FBRztZQUNuQixHQUFHLEVBQUUsV0FBVyxDQUFDLEVBQUU7WUFDbkIsZUFBZSxFQUFFLHdCQUF3QjtZQUN6QyxPQUFPLEVBQUUsc0JBQXNCO1lBQy9CLFdBQVcsRUFBRSwyQkFBMkI7U0FDekMsQ0FBQztRQUVGLE9BQU8sQ0FDTCxxRkFBSyxTQUFTLEVBQUMsa0JBQWtCLEVBQUMsRUFBRSxFQUFDLFVBQVU7WUFDN0MsNEVBQUMsT0FBTyxlQUFLLFlBQVksRUFBSTtZQUM3QixzRkFBTSxTQUFTLEVBQUMsNEJBQTRCLEdBQVE7WUFDcEQsbUZBQUcsU0FBUyxFQUFDLG9CQUFvQjtnQkFBQyw0RUFBQyxpR0FBZ0IsSUFBQyxFQUFFLEVBQUMsb0JBQW9CLEdBQUcsQ0FBSTtZQUNsRixxRkFBSyxTQUFTLEVBQUMsWUFBWTtnQkFDekIsNEVBQUMsUUFBUSxJQUFDLE1BQU0sRUFBRSxxQkFBcUIsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLGdCQUFnQixFQUFFLEtBQUssRUFBRSxlQUFlLEVBQUUsV0FBVztvQkFDNUcscUZBQUssU0FBUyxFQUFDLHFCQUFxQixHQUFPO29CQUMzQyxxRkFBSyxTQUFTLEVBQUMsc0JBQXNCO3dCQUVqQyxjQUFjLElBQUksY0FBYyxDQUFDLE1BQU0sSUFBSSxjQUFjLENBQUMsR0FBRyxDQUFDLGNBQUksSUFBSSxtRkFBQyxXQUFXLElBQ2hGLFlBQVksRUFBRSxLQUFJLENBQUMsWUFBWSxFQUMvQixhQUFhLEVBQUUsSUFBSSxFQUNuQixPQUFPLEVBQUUsYUFBYSxJQUFLLGdCQUFvQyxHQUMvRCxFQUpvRSxDQUlwRSxDQUFDO3dCQUVMLDRFQUFDLE9BQU8sSUFBQyxJQUFJLEVBQUUsU0FBUyxFQUN0QixXQUFXLEVBRVQscUZBQUssU0FBUyxFQUFDLGdDQUFnQztnQ0FDN0Msd0ZBQVEsRUFBRSxFQUFDLFlBQVksRUFBQyxTQUFTLEVBQUMseURBQXlELEVBQUMsT0FBTyxFQUFFLFVBQUMsQ0FBQyxJQUFLLFlBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLEVBQWpCLENBQWlCLGFBQWlCLENBQzFJOzRCQUdSLDRFQUFDLFFBQVEsSUFDUCxZQUFZLEVBQUUsSUFBSSxDQUFDLFlBQVksRUFDL0IsYUFBYSxFQUFFLElBQUksRUFDbkIsT0FBTyxFQUFFLGFBQWEsRUFDdEIsS0FBSyxFQUFFLGFBQWEsRUFDcEIsS0FBSyxFQUFFLE9BQU8sR0FBSSxDQUNaLENBRU47b0JBRUosYUFBYSxDQUFDLENBQUMsQ0FBQyw0RUFBQyxTQUFTLElBQUMsVUFBVSxFQUFFLElBQUksQ0FBQyxVQUFVLEVBQUUsY0FBYyxFQUFFLGNBQWMsRUFBRSxlQUFlLEVBQUUsZUFBZSxFQUFFLGdCQUFnQixFQUFFLGdCQUFtQyxHQUFJLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FFbkw7Z0JBQ1gsNEVBQUMsT0FBTyxJQUFDLElBQUksRUFBRSxPQUFPLENBQUMsZ0JBQWdCLENBQUMsSUFFcEMsZ0JBQWdCLElBQUksZ0JBQWdCLEtBQUssT0FBTyxDQUFDLENBQUM7b0JBQ2hELDRFQUFDLFFBQVEsSUFBQyxNQUFNLEVBQUUsb0JBQW9CLEVBQUUsUUFBUSxFQUFFLEtBQUssRUFBRSxnQkFBZ0IsRUFBRSxLQUFLO3dCQUM5RSxxRkFBSyxTQUFTLEVBQUMsU0FBUzs0QkFDdEIsc0ZBQU0sU0FBUyxFQUFDLE9BQU87Z0NBQUMsNEVBQUMsaUdBQWdCLElBQUMsRUFBRSxFQUFFLGdCQUFnQixDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLEdBQUksQ0FBTzs0QkFDL0Ysc0ZBQU0sU0FBUyxFQUFDLE9BQU87Z0NBQUMsNEVBQUMsaUdBQWdCLElBQUMsRUFBRSxFQUFDLHVCQUF1QixHQUFHLENBQU8sQ0FDMUUsQ0FDRyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBRWQ7Z0JBQ1YsNEVBQUMsT0FBTyxJQUFDLElBQUksRUFBRSxPQUFPLENBQUMsbUJBQW1CLENBQUM7b0JBQ3pDLDRFQUFDLFFBQVEsSUFBQyxNQUFNLEVBQUUsK0JBQStCLEVBQUUsUUFBUSxFQUFFLEtBQUssRUFBRSxnQkFBZ0IsRUFBRSxLQUFLO3dCQUN6RixxRkFBSyxTQUFTLEVBQUMsU0FBUzs0QkFDdEIsc0ZBQU0sU0FBUyxFQUFDLE9BQU87Z0NBQ3JCLDRFQUFDLE9BQU8sSUFBQyxJQUFJLEVBQUUsbUtBQU8sQ0FBQyxtQkFBbUIsRUFBRSxpQkFBaUIsRUFBRSxLQUFLLENBQUM7b0NBQ2xFLG1LQUFPLENBQUMsbUJBQW1CLEVBQUUsaUJBQWlCLEVBQUUsRUFBRSxDQUFDO29EQUM1QztnQ0FDVCxtS0FBTyxDQUFDLG1CQUFtQixFQUFFLFVBQVUsRUFBRSxFQUFFLENBQUM7O2dDQUM1QyxtS0FBTyxDQUFDLG1CQUFtQixFQUFFLFVBQVUsRUFBRSxFQUFFLENBQUM7O2dDQUM1QyxtS0FBTyxDQUFDLG1CQUFtQixFQUFFLFlBQVksRUFBRSxFQUFFLENBQUM7O2dDQUM5QyxtS0FBTyxDQUFDLG1CQUFtQixFQUFFLE1BQU0sRUFBRSxFQUFFLENBQUM7O2dDQUN4QyxtS0FBTyxDQUFDLG1CQUFtQixFQUFFLFVBQVUsRUFBRSxFQUFFLENBQUM7O2dDQUM1QyxtS0FBTyxDQUFDLG1CQUFtQixFQUFFLFlBQVksRUFBRSxFQUFFLENBQUMsQ0FDMUM7NEJBQ1Asc0ZBQU0sU0FBUyxFQUFDLGVBQWU7Z0NBQUMsNEVBQUMsNEtBQW9CLElBQUMsRUFBRSxFQUFDLGlCQUFpQixHQUFHLENBQU8sQ0FDaEYsQ0FDRyxDQUNILENBQ04sQ0FDRixDQUNQLENBQUM7SUFDSixDQUFDO0lBQ0gsZ0JBQUM7QUFBRCxDQUFDLENBbks4Qix1RUFBZSxHQW1LN0M7Ozs7QUNoTXVGO0FBQ2xEO0FBRVc7QUFFMUMsSUFBTSxZQUFZLEdBQUcsZ0dBQU8sQ0FDakMsVUFBQyxFQUErRDtRQUE3RCxtQkFBbUIsMkJBQUUsY0FBYyxzQkFBRSxRQUFRO0lBQzlDLFFBQUMsRUFBRSxtQkFBbUIsdUJBQUUsY0FBYyxrQkFBRSxRQUFRLFlBQUUsQ0FBQztBQUFuRCxDQUFtRCxFQUNyRCxVQUFDLFFBQVEsSUFBSyxRQUFDO0lBQ2IsZUFBZSxFQUFFLGNBQU0sZUFBUSxDQUFDLGVBQWUsRUFBRSxDQUFDLEVBQTNCLENBQTJCO0NBQ25ELENBQUMsRUFGWSxDQUVaLENBQ0gsQ0FBQyxTQUFTLENBQUMsQ0FBQzs7O0FDWHdCO0FBQ047QUFDTjtBQUNNOzs7O0FDSEE7QUFDa0I7QUFDZ0I7QUFDdkI7QUFDQTtBQUUxQyxJQUFJLFdBQVcsR0FBUSxJQUFJLENBQUM7QUFLNUIsSUFBTSxJQUFJLEdBQVEsVUFBQyxLQUFxQjtJQUN0QyxJQUFNLFNBQVMsR0FBUSxvRUFBWSxDQUFDLElBQUksQ0FBQyxDQUFDO0lBR2xDLGdCQUFZLEdBQUssY0FBYyxFQUFFLGFBQXJCLENBQXNCO0lBQzFDLElBQU0sUUFBUSxHQUFHLG9HQUFXLEVBQUUsQ0FBQztJQUUvQixXQUFXLEdBQUc7UUFDWCxTQUFpQixDQUFDLE9BQU8sQ0FBQyxLQUFLLEVBQUUsQ0FBQztJQUNyQyxDQUFDLENBQUM7SUFFRixJQUFNLFlBQVksR0FBRyxVQUFPLENBQW1DOztZQUM3RCxDQUFDLENBQUMsY0FBYyxFQUFFLENBQUM7WUFDbkIsWUFBWSxDQUFDLFVBQUMsSUFBUztnQkFFckIsUUFBUSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDO1lBQ2pDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDOzs7U0FDUCxDQUFDO0lBRUYsT0FBTyxDQUNMLHNGQUFNLEVBQUUsRUFBQyxpQkFBaUIsRUFBQyxRQUFRLEVBQUUsWUFBWTtRQUMvQyxxRkFBSyxTQUFTLEVBQUMsbUJBQW1CLEdBQU87UUFDekMscUZBQUssU0FBUyxFQUFDLDRCQUE0QixHQUFPO1FBQ2xELDRFQUFDLFlBQVksT0FBRzs7UUFDaEIsNEVBQUMsa0JBQWtCLE9BQUc7O1FBQ3RCLHdGQUFRLEdBQUcsRUFBRSxTQUFTLEVBQUUsSUFBSSxFQUFDLFFBQVEsaUJBQWEsTUFBTSxFQUFDLEtBQUssRUFBRSxFQUFFLE9BQU8sRUFBRSxNQUFNLEVBQUUsR0FBSSxDQUNqRixDQUNULENBQUM7QUFDSixDQUFDLENBQUM7QUFFRixJQUFJLENBQUMsWUFBWSxHQUFHLGNBQVcsa0JBQVcsRUFBWCxDQUFXLENBQUM7QUFFM0MsK0NBQWUsSUFBSSxFQUFDOzs7O0FDM0NrQjtBQUVnQztBQUV0QztBQUV4QixZQUFRLEdBQUssd0VBQWMsU0FBbkIsQ0FBb0I7QUFVcEM7SUFBMEIsd0JBQVE7SUFnQmhDLGNBQVksR0FBUTtRQUNsQixrQkFBSyxZQUFDLEdBQUcsQ0FBQyxTQUFDO1FBQ1gsSUFBSSxDQUFDLFFBQVEsR0FBRyxLQUFJLENBQUM7O0lBQ3ZCLENBQUM7SUFsQk0sa0JBQWEsR0FBcEIsVUFBcUIsS0FBWTs7UUFDL0I7WUFDRSxHQUFDLCtKQUFPLENBQUMsVUFBVSxDQUFDLFFBQVEsRUFBRSxJQUFHLFVBQUMsRUFBbUI7Z0JBQ25ELElBQU0sR0FBRyxHQUFHLFVBQUksQ0FBQyxZQUFZLEVBQUUsQ0FBQztnQkFDaEMsR0FBRyxJQUFJLEdBQUcsRUFBRSxDQUFDO2dCQUNiLEtBQUssQ0FBQyxRQUFRLENBQUMsK0pBQU8sQ0FBQyxlQUFlLENBQUMscUtBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBQ2xFLENBQUM7ZUFDRDtJQUNKLENBQUM7SUFXSCxXQUFDO0FBQUQsQ0FBQyxDQXBCeUIsUUFBUSxHQW9CakM7Ozs7QUNwQzhCO0FBQ1c7QUFDc0M7QUFDL0I7QUFDVjtBQUNiO0FBR3hCLG9CQUFnQixHQUNkLGtLQUFVLGlCQURJLENBQ0g7QUFHYix3QkFBb0IsR0FDbEIsK0pBQU8scUJBRFcsQ0FDVjtBQVdMLElBQU0sV0FBVyxHQUFHLFVBQUMsS0FBc0I7SUFDaEQsSUFBTSxRQUFRLEdBQUcsb0dBQVcsRUFBRSxDQUFDO0lBQ3ZCLFVBQU0sR0FBSyxjQUFjLEVBQUUsT0FBckIsQ0FBc0I7SUFFcEMsdUVBQWUsQ0FBQztRQUNkLFFBQVEsQ0FBQyxvQkFBb0IsQ0FBQyxtS0FBVyxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUM7SUFDMUQsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO0lBRVAsT0FBTyxzRkFBTSxFQUFFLEVBQUMsYUFBYTtRQUMzQixzRkFBTSxTQUFTLEVBQUMsc0JBQXNCLGlCQUFhLE1BQU0sR0FBUTtRQUNqRSw0RUFBQyxnQkFBZ0IsSUFBQyxFQUFFLEVBQUMsK0JBQStCLEdBQUc7UUFDdkQsNEVBQUMsTUFBTSxJQUFDLE1BQU0sRUFBRSxNQUFNLEdBQUk7UUFDMUIsNEVBQUMsa0tBQVUsQ0FBQyxTQUFTO1lBQ25CLDRFQUFDLGtLQUFVLENBQUMsS0FBSyxJQUFDLFNBQVMsRUFBQyxtQ0FBbUM7Z0JBQzdELDRFQUFDLFVBQUksT0FBRyxDQUNTLENBQ0UsQ0FDbEIsQ0FBQztBQUNWLENBQUMsQ0FBQzs7OztBQzFDd0Q7QUFDM0I7QUFDd0I7QUFDakI7QUFHcEMsbUJBQWUsR0FDYixrS0FBVSxnQkFERyxDQUNGO0FBRVIsSUFBTSxHQUFHLEdBQUcsVUFBQyxLQUFVO0lBQzVCLElBQU0sT0FBTyxHQUFHLE9BQU8sRUFBRSxDQUFDO0lBQzFCLE9BQU8sQ0FDTCw0RUFBQyxlQUFlO1FBQ2QsNEVBQUMsV0FBVyxlQUFLLE9BQU87O1lBQ3RCLDRFQUFDLFdBQVcsT0FBRyxDQUNILENBQ0UsQ0FDbkIsQ0FBQztBQUNKLENBQUMsQ0FBQzs7OztBQ2xCNkI7QUFDVztBQUM2QztBQUM3QjtBQUMxQjtBQUVGO0FBQ0k7QUFDTjtBQUkxQixrQkFBYyxHQUVaLCtKQUFPLGVBRkssRUFDZCxzQkFBZSxHQUNiLCtKQUFPLGdCQURNLENBQ0w7QUFDWixJQUFNLGFBQWEsR0FBRyw2RkFBMEIsQ0FBQztBQUdqRDtJQUE2QyxtQ0FBVTtJQUNyRCx5QkFBb0IsS0FBWSxFQUFVLE1BQXlDLEVBQVUsTUFBYyxFQUFVLElBQVU7UUFDN0gsa0JBQUssV0FBRSxTQUFDO1FBRFUsV0FBSyxHQUFMLEtBQUssQ0FBTztRQUFVLFlBQU0sR0FBTixNQUFNLENBQW1DO1FBQVUsWUFBTSxHQUFOLE1BQU0sQ0FBUTtRQUFVLFVBQUksR0FBSixJQUFJLENBQU07O0lBRS9ILENBQUM7SUFRRCw4QkFBSSxHQUFKO1FBQ0UsSUFBSSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQztRQUNwRCxJQUFJLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUM7UUFDakQsSUFBSSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQztRQUN2RCxJQUFJLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxzQkFBZSxDQUFDLHFLQUFhLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztJQUMzRCxDQUFDO0lBT0QsaUNBQU8sR0FBUDtRQUNFLElBQUksQ0FBQyxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUM7UUFDeEIsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsQ0FBQztJQUN2QixDQUFDO0lBVUQsZ0NBQU0sR0FBTixVQUFPLElBQVU7UUFDUCxTQUFLLEdBQUssSUFBSSxNQUFULENBQVU7UUFDdkIsSUFBSSxDQUFDLE1BQU0sQ0FDVCw0RUFBQyx1S0FBZSxJQUFDLEtBQUssRUFBRSxFQUFFLE1BQU0sRUFBRSxJQUFJLENBQUMsTUFBTSxFQUFFO1lBQzdDLDRFQUFDLGFBQWEsSUFBTyxLQUFLO2dCQUFJLDRFQUFDLEdBQUcsT0FBRyxDQUFnQixDQUNyQyxDQUNuQixDQUFDO0lBQ0osQ0FBQztJQTNDa0IsZUFBZTtRQURuQyxvRUFBTSxDQUFDLEVBQUUsU0FBUyxFQUFFLFVBQVUsRUFBRSxDQUFDO3lDQUVMLEtBQUssRUFBa0Isd0VBQWMsRUFBcUMsTUFBTSxFQUFnQixJQUFJO09BRDVHLGVBQWUsQ0E0Q25DO0lBQUQsc0JBQUM7Q0FBQSxDQTVDNEMsb0VBQVUsR0E0Q3REOzJDQTVDb0IsZUFBZSIsInNvdXJjZXMiOlsib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi90c2xpYi90c2xpYi5lczYubWpzPyIsIm9tZi1jaGFuZ2VwYWNrYWdlLWFwcG9pbnRtZW50Oi8vLy4uL3NyYy9zdG9yZS9BY3Rpb25zLnRzPyIsIm9tZi1jaGFuZ2VwYWNrYWdlLWFwcG9pbnRtZW50Oi8vLy4uL3NyYy9Db25maWcudHM/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL0NsaWVudC50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvbW9kZWxzL1N0b3JlLnRzPyIsIm9tZi1jaGFuZ2VwYWNrYWdlLWFwcG9pbnRtZW50Oi8vLy4uL3NyYy9tb2RlbHMvQXBwLnRzPyIsIm9tZi1jaGFuZ2VwYWNrYWdlLWFwcG9pbnRtZW50Oi8vLy4uL3NyYy9tb2RlbHMvV2lkZ2V0LnRzPyIsIm9tZi1jaGFuZ2VwYWNrYWdlLWFwcG9pbnRtZW50Oi8vLy4uL3NyYy9tb2RlbHMvQXBwb2ludG1lbnQudHM/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL21vZGVscy9pbmRleC50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvc3RvcmUvRXBpY3MvQXBwb2ludG1lbnQudHM/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL3N0b3JlL0VwaWNzL09tbml0dXJlLnRzPyIsIm9tZi1jaGFuZ2VwYWNrYWdlLWFwcG9pbnRtZW50Oi8vLy4uL3NyYy9zdG9yZS9FcGljcy50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvTG9jYWxpemF0aW9uLnRzPyIsIm9tZi1jaGFuZ2VwYWNrYWdlLWFwcG9pbnRtZW50Oi8vLy4uL3NyYy9zdG9yZS9TdG9yZS50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvc3RvcmUvaW5kZXgudHM/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi9yZWFjdC1ob29rLWZvcm0vZGlzdC9yZWFjdC1ob29rLWZvcm0uZXMuanM/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL21vZGVscy9FbnVtcy50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvdXRpbHMvQXBwb2ludG1lbnRVdGlscy50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvdmlld3MvQ29tcG9uZW5ldHMvRm9ybUVsZW1lbnRzL0NoZWNrYm94LnRzeD8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvdmlld3MvQ29tcG9uZW5ldHMvRm9ybUVsZW1lbnRzL1JhZGlvQnRuLnRzeD8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvdmlld3MvQ29tcG9uZW5ldHMvRm9ybUVsZW1lbnRzL1RleHRBcmVhLnRzeD8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvdmlld3MvQ29tcG9uZW5ldHMvRm9ybUVsZW1lbnRzL1RleHRJbnB1dC50c3g/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL3ZpZXdzL0NvbXBvbmVuZXRzL0Zvcm1FbGVtZW50cy9GaWVsZHNldC50c3g/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL3ZpZXdzL0NvbXBvbmVuZXRzL0Zvcm1FbGVtZW50cy9pbmRleC50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvdmlld3MvQ29tcG9uZW5ldHMvSGVhZGVyL0Jhbm5lci50c3g/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL3ZpZXdzL0NvbXBvbmVuZXRzL0hlYWRlci9IZWFkaW5nLnRzeD8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvdmlld3MvQ29tcG9uZW5ldHMvSGVhZGVyL0hlYWRlci50c3g/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL3ZpZXdzL0NvbXBvbmVuZXRzL0hlYWRlci9pbmRleC50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvdmlld3MvQ29tcG9uZW5ldHMvQ29udGFjdEluZm9ybWF0aW9uL0NvbnRhY3RJbmZvcm1hdGlvbi50c3g/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL3ZpZXdzL0NvbXBvbmVuZXRzL0NvbnRhY3RJbmZvcm1hdGlvbi9pbmRleC50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvdmlld3MvQ29tcG9uZW5ldHMvSW5zdGFsbGF0aW9uL0RhdGVBbmRUaW1lL0RhdGVBbmRUaW1lLnRzeD8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvdmlld3MvQ29tcG9uZW5ldHMvSW5zdGFsbGF0aW9uL0RhdGVBbmRUaW1lL1RpbWVTbG90cy50c3g/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL3ZpZXdzL0NvbXBvbmVuZXRzL0luc3RhbGxhdGlvbi9EYXRlQW5kVGltZS9pbmRleC50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvdmlld3MvQ29tcG9uZW5ldHMvSW5zdGFsbGF0aW9uL0luc3RhbGxhdGlvbi50c3g/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL3ZpZXdzL0NvbXBvbmVuZXRzL0luc3RhbGxhdGlvbi9pbmRleC50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudDovLy8uLi9zcmMvdmlld3MvQ29tcG9uZW5ldHMvaW5kZXgudHM/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL3ZpZXdzL0Zvcm0udHN4PyIsIm9tZi1jaGFuZ2VwYWNrYWdlLWFwcG9pbnRtZW50Oi8vLy4uL3NyYy9QaXBlLnRzPyIsIm9tZi1jaGFuZ2VwYWNrYWdlLWFwcG9pbnRtZW50Oi8vLy4uL3NyYy92aWV3cy9pbmRleC50c3g/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL0FwcC50c3g/Iiwib21mLWNoYW5nZXBhY2thZ2UtYXBwb2ludG1lbnQ6Ly8vLi4vc3JjL1dpZGdldC50c3g/Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKipcbkNvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLlxuXG5QZXJtaXNzaW9uIHRvIHVzZSwgY29weSwgbW9kaWZ5LCBhbmQvb3IgZGlzdHJpYnV0ZSB0aGlzIHNvZnR3YXJlIGZvciBhbnlcbnB1cnBvc2Ugd2l0aCBvciB3aXRob3V0IGZlZSBpcyBoZXJlYnkgZ3JhbnRlZC5cblxuVEhFIFNPRlRXQVJFIElTIFBST1ZJREVEIFwiQVMgSVNcIiBBTkQgVEhFIEFVVEhPUiBESVNDTEFJTVMgQUxMIFdBUlJBTlRJRVMgV0lUSFxuUkVHQVJEIFRPIFRISVMgU09GVFdBUkUgSU5DTFVESU5HIEFMTCBJTVBMSUVEIFdBUlJBTlRJRVMgT0YgTUVSQ0hBTlRBQklMSVRZXG5BTkQgRklUTkVTUy4gSU4gTk8gRVZFTlQgU0hBTEwgVEhFIEFVVEhPUiBCRSBMSUFCTEUgRk9SIEFOWSBTUEVDSUFMLCBESVJFQ1QsXG5JTkRJUkVDVCwgT1IgQ09OU0VRVUVOVElBTCBEQU1BR0VTIE9SIEFOWSBEQU1BR0VTIFdIQVRTT0VWRVIgUkVTVUxUSU5HIEZST01cbkxPU1MgT0YgVVNFLCBEQVRBIE9SIFBST0ZJVFMsIFdIRVRIRVIgSU4gQU4gQUNUSU9OIE9GIENPTlRSQUNULCBORUdMSUdFTkNFIE9SXG5PVEhFUiBUT1JUSU9VUyBBQ1RJT04sIEFSSVNJTkcgT1VUIE9GIE9SIElOIENPTk5FQ1RJT04gV0lUSCBUSEUgVVNFIE9SXG5QRVJGT1JNQU5DRSBPRiBUSElTIFNPRlRXQVJFLlxuKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKiogKi9cbi8qIGdsb2JhbCBSZWZsZWN0LCBQcm9taXNlLCBTdXBwcmVzc2VkRXJyb3IsIFN5bWJvbCwgSXRlcmF0b3IgKi9cblxudmFyIGV4dGVuZFN0YXRpY3MgPSBmdW5jdGlvbihkLCBiKSB7XG4gIGV4dGVuZFN0YXRpY3MgPSBPYmplY3Quc2V0UHJvdG90eXBlT2YgfHxcbiAgICAgICh7IF9fcHJvdG9fXzogW10gfSBpbnN0YW5jZW9mIEFycmF5ICYmIGZ1bmN0aW9uIChkLCBiKSB7IGQuX19wcm90b19fID0gYjsgfSkgfHxcbiAgICAgIGZ1bmN0aW9uIChkLCBiKSB7IGZvciAodmFyIHAgaW4gYikgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChiLCBwKSkgZFtwXSA9IGJbcF07IH07XG4gIHJldHVybiBleHRlbmRTdGF0aWNzKGQsIGIpO1xufTtcblxuZXhwb3J0IGZ1bmN0aW9uIF9fZXh0ZW5kcyhkLCBiKSB7XG4gIGlmICh0eXBlb2YgYiAhPT0gXCJmdW5jdGlvblwiICYmIGIgIT09IG51bGwpXG4gICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiQ2xhc3MgZXh0ZW5kcyB2YWx1ZSBcIiArIFN0cmluZyhiKSArIFwiIGlzIG5vdCBhIGNvbnN0cnVjdG9yIG9yIG51bGxcIik7XG4gIGV4dGVuZFN0YXRpY3MoZCwgYik7XG4gIGZ1bmN0aW9uIF9fKCkgeyB0aGlzLmNvbnN0cnVjdG9yID0gZDsgfVxuICBkLnByb3RvdHlwZSA9IGIgPT09IG51bGwgPyBPYmplY3QuY3JlYXRlKGIpIDogKF9fLnByb3RvdHlwZSA9IGIucHJvdG90eXBlLCBuZXcgX18oKSk7XG59XG5cbmV4cG9ydCB2YXIgX19hc3NpZ24gPSBmdW5jdGlvbigpIHtcbiAgX19hc3NpZ24gPSBPYmplY3QuYXNzaWduIHx8IGZ1bmN0aW9uIF9fYXNzaWduKHQpIHtcbiAgICAgIGZvciAodmFyIHMsIGkgPSAxLCBuID0gYXJndW1lbnRzLmxlbmd0aDsgaSA8IG47IGkrKykge1xuICAgICAgICAgIHMgPSBhcmd1bWVudHNbaV07XG4gICAgICAgICAgZm9yICh2YXIgcCBpbiBzKSBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHMsIHApKSB0W3BdID0gc1twXTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB0O1xuICB9XG4gIHJldHVybiBfX2Fzc2lnbi5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19yZXN0KHMsIGUpIHtcbiAgdmFyIHQgPSB7fTtcbiAgZm9yICh2YXIgcCBpbiBzKSBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHMsIHApICYmIGUuaW5kZXhPZihwKSA8IDApXG4gICAgICB0W3BdID0gc1twXTtcbiAgaWYgKHMgIT0gbnVsbCAmJiB0eXBlb2YgT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyA9PT0gXCJmdW5jdGlvblwiKVxuICAgICAgZm9yICh2YXIgaSA9IDAsIHAgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKHMpOyBpIDwgcC5sZW5ndGg7IGkrKykge1xuICAgICAgICAgIGlmIChlLmluZGV4T2YocFtpXSkgPCAwICYmIE9iamVjdC5wcm90b3R5cGUucHJvcGVydHlJc0VudW1lcmFibGUuY2FsbChzLCBwW2ldKSlcbiAgICAgICAgICAgICAgdFtwW2ldXSA9IHNbcFtpXV07XG4gICAgICB9XG4gIHJldHVybiB0O1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19kZWNvcmF0ZShkZWNvcmF0b3JzLCB0YXJnZXQsIGtleSwgZGVzYykge1xuICB2YXIgYyA9IGFyZ3VtZW50cy5sZW5ndGgsIHIgPSBjIDwgMyA/IHRhcmdldCA6IGRlc2MgPT09IG51bGwgPyBkZXNjID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcih0YXJnZXQsIGtleSkgOiBkZXNjLCBkO1xuICBpZiAodHlwZW9mIFJlZmxlY3QgPT09IFwib2JqZWN0XCIgJiYgdHlwZW9mIFJlZmxlY3QuZGVjb3JhdGUgPT09IFwiZnVuY3Rpb25cIikgciA9IFJlZmxlY3QuZGVjb3JhdGUoZGVjb3JhdG9ycywgdGFyZ2V0LCBrZXksIGRlc2MpO1xuICBlbHNlIGZvciAodmFyIGkgPSBkZWNvcmF0b3JzLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSBpZiAoZCA9IGRlY29yYXRvcnNbaV0pIHIgPSAoYyA8IDMgPyBkKHIpIDogYyA+IDMgPyBkKHRhcmdldCwga2V5LCByKSA6IGQodGFyZ2V0LCBrZXkpKSB8fCByO1xuICByZXR1cm4gYyA+IDMgJiYgciAmJiBPYmplY3QuZGVmaW5lUHJvcGVydHkodGFyZ2V0LCBrZXksIHIpLCByO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19wYXJhbShwYXJhbUluZGV4LCBkZWNvcmF0b3IpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uICh0YXJnZXQsIGtleSkgeyBkZWNvcmF0b3IodGFyZ2V0LCBrZXksIHBhcmFtSW5kZXgpOyB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2VzRGVjb3JhdGUoY3RvciwgZGVzY3JpcHRvckluLCBkZWNvcmF0b3JzLCBjb250ZXh0SW4sIGluaXRpYWxpemVycywgZXh0cmFJbml0aWFsaXplcnMpIHtcbiAgZnVuY3Rpb24gYWNjZXB0KGYpIHsgaWYgKGYgIT09IHZvaWQgMCAmJiB0eXBlb2YgZiAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiRnVuY3Rpb24gZXhwZWN0ZWRcIik7IHJldHVybiBmOyB9XG4gIHZhciBraW5kID0gY29udGV4dEluLmtpbmQsIGtleSA9IGtpbmQgPT09IFwiZ2V0dGVyXCIgPyBcImdldFwiIDoga2luZCA9PT0gXCJzZXR0ZXJcIiA/IFwic2V0XCIgOiBcInZhbHVlXCI7XG4gIHZhciB0YXJnZXQgPSAhZGVzY3JpcHRvckluICYmIGN0b3IgPyBjb250ZXh0SW5bXCJzdGF0aWNcIl0gPyBjdG9yIDogY3Rvci5wcm90b3R5cGUgOiBudWxsO1xuICB2YXIgZGVzY3JpcHRvciA9IGRlc2NyaXB0b3JJbiB8fCAodGFyZ2V0ID8gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcih0YXJnZXQsIGNvbnRleHRJbi5uYW1lKSA6IHt9KTtcbiAgdmFyIF8sIGRvbmUgPSBmYWxzZTtcbiAgZm9yICh2YXIgaSA9IGRlY29yYXRvcnMubGVuZ3RoIC0gMTsgaSA+PSAwOyBpLS0pIHtcbiAgICAgIHZhciBjb250ZXh0ID0ge307XG4gICAgICBmb3IgKHZhciBwIGluIGNvbnRleHRJbikgY29udGV4dFtwXSA9IHAgPT09IFwiYWNjZXNzXCIgPyB7fSA6IGNvbnRleHRJbltwXTtcbiAgICAgIGZvciAodmFyIHAgaW4gY29udGV4dEluLmFjY2VzcykgY29udGV4dC5hY2Nlc3NbcF0gPSBjb250ZXh0SW4uYWNjZXNzW3BdO1xuICAgICAgY29udGV4dC5hZGRJbml0aWFsaXplciA9IGZ1bmN0aW9uIChmKSB7IGlmIChkb25lKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiQ2Fubm90IGFkZCBpbml0aWFsaXplcnMgYWZ0ZXIgZGVjb3JhdGlvbiBoYXMgY29tcGxldGVkXCIpOyBleHRyYUluaXRpYWxpemVycy5wdXNoKGFjY2VwdChmIHx8IG51bGwpKTsgfTtcbiAgICAgIHZhciByZXN1bHQgPSAoMCwgZGVjb3JhdG9yc1tpXSkoa2luZCA9PT0gXCJhY2Nlc3NvclwiID8geyBnZXQ6IGRlc2NyaXB0b3IuZ2V0LCBzZXQ6IGRlc2NyaXB0b3Iuc2V0IH0gOiBkZXNjcmlwdG9yW2tleV0sIGNvbnRleHQpO1xuICAgICAgaWYgKGtpbmQgPT09IFwiYWNjZXNzb3JcIikge1xuICAgICAgICAgIGlmIChyZXN1bHQgPT09IHZvaWQgMCkgY29udGludWU7XG4gICAgICAgICAgaWYgKHJlc3VsdCA9PT0gbnVsbCB8fCB0eXBlb2YgcmVzdWx0ICE9PSBcIm9iamVjdFwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiT2JqZWN0IGV4cGVjdGVkXCIpO1xuICAgICAgICAgIGlmIChfID0gYWNjZXB0KHJlc3VsdC5nZXQpKSBkZXNjcmlwdG9yLmdldCA9IF87XG4gICAgICAgICAgaWYgKF8gPSBhY2NlcHQocmVzdWx0LnNldCkpIGRlc2NyaXB0b3Iuc2V0ID0gXztcbiAgICAgICAgICBpZiAoXyA9IGFjY2VwdChyZXN1bHQuaW5pdCkpIGluaXRpYWxpemVycy51bnNoaWZ0KF8pO1xuICAgICAgfVxuICAgICAgZWxzZSBpZiAoXyA9IGFjY2VwdChyZXN1bHQpKSB7XG4gICAgICAgICAgaWYgKGtpbmQgPT09IFwiZmllbGRcIikgaW5pdGlhbGl6ZXJzLnVuc2hpZnQoXyk7XG4gICAgICAgICAgZWxzZSBkZXNjcmlwdG9yW2tleV0gPSBfO1xuICAgICAgfVxuICB9XG4gIGlmICh0YXJnZXQpIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIGNvbnRleHRJbi5uYW1lLCBkZXNjcmlwdG9yKTtcbiAgZG9uZSA9IHRydWU7XG59O1xuXG5leHBvcnQgZnVuY3Rpb24gX19ydW5Jbml0aWFsaXplcnModGhpc0FyZywgaW5pdGlhbGl6ZXJzLCB2YWx1ZSkge1xuICB2YXIgdXNlVmFsdWUgPSBhcmd1bWVudHMubGVuZ3RoID4gMjtcbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBpbml0aWFsaXplcnMubGVuZ3RoOyBpKyspIHtcbiAgICAgIHZhbHVlID0gdXNlVmFsdWUgPyBpbml0aWFsaXplcnNbaV0uY2FsbCh0aGlzQXJnLCB2YWx1ZSkgOiBpbml0aWFsaXplcnNbaV0uY2FsbCh0aGlzQXJnKTtcbiAgfVxuICByZXR1cm4gdXNlVmFsdWUgPyB2YWx1ZSA6IHZvaWQgMDtcbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBfX3Byb3BLZXkoeCkge1xuICByZXR1cm4gdHlwZW9mIHggPT09IFwic3ltYm9sXCIgPyB4IDogXCJcIi5jb25jYXQoeCk7XG59O1xuXG5leHBvcnQgZnVuY3Rpb24gX19zZXRGdW5jdGlvbk5hbWUoZiwgbmFtZSwgcHJlZml4KSB7XG4gIGlmICh0eXBlb2YgbmFtZSA9PT0gXCJzeW1ib2xcIikgbmFtZSA9IG5hbWUuZGVzY3JpcHRpb24gPyBcIltcIi5jb25jYXQobmFtZS5kZXNjcmlwdGlvbiwgXCJdXCIpIDogXCJcIjtcbiAgcmV0dXJuIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShmLCBcIm5hbWVcIiwgeyBjb25maWd1cmFibGU6IHRydWUsIHZhbHVlOiBwcmVmaXggPyBcIlwiLmNvbmNhdChwcmVmaXgsIFwiIFwiLCBuYW1lKSA6IG5hbWUgfSk7XG59O1xuXG5leHBvcnQgZnVuY3Rpb24gX19tZXRhZGF0YShtZXRhZGF0YUtleSwgbWV0YWRhdGFWYWx1ZSkge1xuICBpZiAodHlwZW9mIFJlZmxlY3QgPT09IFwib2JqZWN0XCIgJiYgdHlwZW9mIFJlZmxlY3QubWV0YWRhdGEgPT09IFwiZnVuY3Rpb25cIikgcmV0dXJuIFJlZmxlY3QubWV0YWRhdGEobWV0YWRhdGFLZXksIG1ldGFkYXRhVmFsdWUpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19hd2FpdGVyKHRoaXNBcmcsIF9hcmd1bWVudHMsIFAsIGdlbmVyYXRvcikge1xuICBmdW5jdGlvbiBhZG9wdCh2YWx1ZSkgeyByZXR1cm4gdmFsdWUgaW5zdGFuY2VvZiBQID8gdmFsdWUgOiBuZXcgUChmdW5jdGlvbiAocmVzb2x2ZSkgeyByZXNvbHZlKHZhbHVlKTsgfSk7IH1cbiAgcmV0dXJuIG5ldyAoUCB8fCAoUCA9IFByb21pc2UpKShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7XG4gICAgICBmdW5jdGlvbiBmdWxmaWxsZWQodmFsdWUpIHsgdHJ5IHsgc3RlcChnZW5lcmF0b3IubmV4dCh2YWx1ZSkpOyB9IGNhdGNoIChlKSB7IHJlamVjdChlKTsgfSB9XG4gICAgICBmdW5jdGlvbiByZWplY3RlZCh2YWx1ZSkgeyB0cnkgeyBzdGVwKGdlbmVyYXRvcltcInRocm93XCJdKHZhbHVlKSk7IH0gY2F0Y2ggKGUpIHsgcmVqZWN0KGUpOyB9IH1cbiAgICAgIGZ1bmN0aW9uIHN0ZXAocmVzdWx0KSB7IHJlc3VsdC5kb25lID8gcmVzb2x2ZShyZXN1bHQudmFsdWUpIDogYWRvcHQocmVzdWx0LnZhbHVlKS50aGVuKGZ1bGZpbGxlZCwgcmVqZWN0ZWQpOyB9XG4gICAgICBzdGVwKChnZW5lcmF0b3IgPSBnZW5lcmF0b3IuYXBwbHkodGhpc0FyZywgX2FyZ3VtZW50cyB8fCBbXSkpLm5leHQoKSk7XG4gIH0pO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19nZW5lcmF0b3IodGhpc0FyZywgYm9keSkge1xuICB2YXIgXyA9IHsgbGFiZWw6IDAsIHNlbnQ6IGZ1bmN0aW9uKCkgeyBpZiAodFswXSAmIDEpIHRocm93IHRbMV07IHJldHVybiB0WzFdOyB9LCB0cnlzOiBbXSwgb3BzOiBbXSB9LCBmLCB5LCB0LCBnID0gT2JqZWN0LmNyZWF0ZSgodHlwZW9mIEl0ZXJhdG9yID09PSBcImZ1bmN0aW9uXCIgPyBJdGVyYXRvciA6IE9iamVjdCkucHJvdG90eXBlKTtcbiAgcmV0dXJuIGcubmV4dCA9IHZlcmIoMCksIGdbXCJ0aHJvd1wiXSA9IHZlcmIoMSksIGdbXCJyZXR1cm5cIl0gPSB2ZXJiKDIpLCB0eXBlb2YgU3ltYm9sID09PSBcImZ1bmN0aW9uXCIgJiYgKGdbU3ltYm9sLml0ZXJhdG9yXSA9IGZ1bmN0aW9uKCkgeyByZXR1cm4gdGhpczsgfSksIGc7XG4gIGZ1bmN0aW9uIHZlcmIobikgeyByZXR1cm4gZnVuY3Rpb24gKHYpIHsgcmV0dXJuIHN0ZXAoW24sIHZdKTsgfTsgfVxuICBmdW5jdGlvbiBzdGVwKG9wKSB7XG4gICAgICBpZiAoZikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkdlbmVyYXRvciBpcyBhbHJlYWR5IGV4ZWN1dGluZy5cIik7XG4gICAgICB3aGlsZSAoZyAmJiAoZyA9IDAsIG9wWzBdICYmIChfID0gMCkpLCBfKSB0cnkge1xuICAgICAgICAgIGlmIChmID0gMSwgeSAmJiAodCA9IG9wWzBdICYgMiA/IHlbXCJyZXR1cm5cIl0gOiBvcFswXSA/IHlbXCJ0aHJvd1wiXSB8fCAoKHQgPSB5W1wicmV0dXJuXCJdKSAmJiB0LmNhbGwoeSksIDApIDogeS5uZXh0KSAmJiAhKHQgPSB0LmNhbGwoeSwgb3BbMV0pKS5kb25lKSByZXR1cm4gdDtcbiAgICAgICAgICBpZiAoeSA9IDAsIHQpIG9wID0gW29wWzBdICYgMiwgdC52YWx1ZV07XG4gICAgICAgICAgc3dpdGNoIChvcFswXSkge1xuICAgICAgICAgICAgICBjYXNlIDA6IGNhc2UgMTogdCA9IG9wOyBicmVhaztcbiAgICAgICAgICAgICAgY2FzZSA0OiBfLmxhYmVsKys7IHJldHVybiB7IHZhbHVlOiBvcFsxXSwgZG9uZTogZmFsc2UgfTtcbiAgICAgICAgICAgICAgY2FzZSA1OiBfLmxhYmVsKys7IHkgPSBvcFsxXTsgb3AgPSBbMF07IGNvbnRpbnVlO1xuICAgICAgICAgICAgICBjYXNlIDc6IG9wID0gXy5vcHMucG9wKCk7IF8udHJ5cy5wb3AoKTsgY29udGludWU7XG4gICAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgICBpZiAoISh0ID0gXy50cnlzLCB0ID0gdC5sZW5ndGggPiAwICYmIHRbdC5sZW5ndGggLSAxXSkgJiYgKG9wWzBdID09PSA2IHx8IG9wWzBdID09PSAyKSkgeyBfID0gMDsgY29udGludWU7IH1cbiAgICAgICAgICAgICAgICAgIGlmIChvcFswXSA9PT0gMyAmJiAoIXQgfHwgKG9wWzFdID4gdFswXSAmJiBvcFsxXSA8IHRbM10pKSkgeyBfLmxhYmVsID0gb3BbMV07IGJyZWFrOyB9XG4gICAgICAgICAgICAgICAgICBpZiAob3BbMF0gPT09IDYgJiYgXy5sYWJlbCA8IHRbMV0pIHsgXy5sYWJlbCA9IHRbMV07IHQgPSBvcDsgYnJlYWs7IH1cbiAgICAgICAgICAgICAgICAgIGlmICh0ICYmIF8ubGFiZWwgPCB0WzJdKSB7IF8ubGFiZWwgPSB0WzJdOyBfLm9wcy5wdXNoKG9wKTsgYnJlYWs7IH1cbiAgICAgICAgICAgICAgICAgIGlmICh0WzJdKSBfLm9wcy5wb3AoKTtcbiAgICAgICAgICAgICAgICAgIF8udHJ5cy5wb3AoKTsgY29udGludWU7XG4gICAgICAgICAgfVxuICAgICAgICAgIG9wID0gYm9keS5jYWxsKHRoaXNBcmcsIF8pO1xuICAgICAgfSBjYXRjaCAoZSkgeyBvcCA9IFs2LCBlXTsgeSA9IDA7IH0gZmluYWxseSB7IGYgPSB0ID0gMDsgfVxuICAgICAgaWYgKG9wWzBdICYgNSkgdGhyb3cgb3BbMV07IHJldHVybiB7IHZhbHVlOiBvcFswXSA/IG9wWzFdIDogdm9pZCAwLCBkb25lOiB0cnVlIH07XG4gIH1cbn1cblxuZXhwb3J0IHZhciBfX2NyZWF0ZUJpbmRpbmcgPSBPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gIHZhciBkZXNjID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihtLCBrKTtcbiAgaWYgKCFkZXNjIHx8IChcImdldFwiIGluIGRlc2MgPyAhbS5fX2VzTW9kdWxlIDogZGVzYy53cml0YWJsZSB8fCBkZXNjLmNvbmZpZ3VyYWJsZSkpIHtcbiAgICAgIGRlc2MgPSB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24oKSB7IHJldHVybiBtW2tdOyB9IH07XG4gIH1cbiAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIGsyLCBkZXNjKTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gIG9bazJdID0gbVtrXTtcbn0pO1xuXG5leHBvcnQgZnVuY3Rpb24gX19leHBvcnRTdGFyKG0sIG8pIHtcbiAgZm9yICh2YXIgcCBpbiBtKSBpZiAocCAhPT0gXCJkZWZhdWx0XCIgJiYgIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChvLCBwKSkgX19jcmVhdGVCaW5kaW5nKG8sIG0sIHApO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX192YWx1ZXMobykge1xuICB2YXIgcyA9IHR5cGVvZiBTeW1ib2wgPT09IFwiZnVuY3Rpb25cIiAmJiBTeW1ib2wuaXRlcmF0b3IsIG0gPSBzICYmIG9bc10sIGkgPSAwO1xuICBpZiAobSkgcmV0dXJuIG0uY2FsbChvKTtcbiAgaWYgKG8gJiYgdHlwZW9mIG8ubGVuZ3RoID09PSBcIm51bWJlclwiKSByZXR1cm4ge1xuICAgICAgbmV4dDogZnVuY3Rpb24gKCkge1xuICAgICAgICAgIGlmIChvICYmIGkgPj0gby5sZW5ndGgpIG8gPSB2b2lkIDA7XG4gICAgICAgICAgcmV0dXJuIHsgdmFsdWU6IG8gJiYgb1tpKytdLCBkb25lOiAhbyB9O1xuICAgICAgfVxuICB9O1xuICB0aHJvdyBuZXcgVHlwZUVycm9yKHMgPyBcIk9iamVjdCBpcyBub3QgaXRlcmFibGUuXCIgOiBcIlN5bWJvbC5pdGVyYXRvciBpcyBub3QgZGVmaW5lZC5cIik7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX3JlYWQobywgbikge1xuICB2YXIgbSA9IHR5cGVvZiBTeW1ib2wgPT09IFwiZnVuY3Rpb25cIiAmJiBvW1N5bWJvbC5pdGVyYXRvcl07XG4gIGlmICghbSkgcmV0dXJuIG87XG4gIHZhciBpID0gbS5jYWxsKG8pLCByLCBhciA9IFtdLCBlO1xuICB0cnkge1xuICAgICAgd2hpbGUgKChuID09PSB2b2lkIDAgfHwgbi0tID4gMCkgJiYgIShyID0gaS5uZXh0KCkpLmRvbmUpIGFyLnB1c2goci52YWx1ZSk7XG4gIH1cbiAgY2F0Y2ggKGVycm9yKSB7IGUgPSB7IGVycm9yOiBlcnJvciB9OyB9XG4gIGZpbmFsbHkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgICBpZiAociAmJiAhci5kb25lICYmIChtID0gaVtcInJldHVyblwiXSkpIG0uY2FsbChpKTtcbiAgICAgIH1cbiAgICAgIGZpbmFsbHkgeyBpZiAoZSkgdGhyb3cgZS5lcnJvcjsgfVxuICB9XG4gIHJldHVybiBhcjtcbn1cblxuLyoqIEBkZXByZWNhdGVkICovXG5leHBvcnQgZnVuY3Rpb24gX19zcHJlYWQoKSB7XG4gIGZvciAodmFyIGFyID0gW10sIGkgPSAwOyBpIDwgYXJndW1lbnRzLmxlbmd0aDsgaSsrKVxuICAgICAgYXIgPSBhci5jb25jYXQoX19yZWFkKGFyZ3VtZW50c1tpXSkpO1xuICByZXR1cm4gYXI7XG59XG5cbi8qKiBAZGVwcmVjYXRlZCAqL1xuZXhwb3J0IGZ1bmN0aW9uIF9fc3ByZWFkQXJyYXlzKCkge1xuICBmb3IgKHZhciBzID0gMCwgaSA9IDAsIGlsID0gYXJndW1lbnRzLmxlbmd0aDsgaSA8IGlsOyBpKyspIHMgKz0gYXJndW1lbnRzW2ldLmxlbmd0aDtcbiAgZm9yICh2YXIgciA9IEFycmF5KHMpLCBrID0gMCwgaSA9IDA7IGkgPCBpbDsgaSsrKVxuICAgICAgZm9yICh2YXIgYSA9IGFyZ3VtZW50c1tpXSwgaiA9IDAsIGpsID0gYS5sZW5ndGg7IGogPCBqbDsgaisrLCBrKyspXG4gICAgICAgICAgcltrXSA9IGFbal07XG4gIHJldHVybiByO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19zcHJlYWRBcnJheSh0bywgZnJvbSwgcGFjaykge1xuICBpZiAocGFjayB8fCBhcmd1bWVudHMubGVuZ3RoID09PSAyKSBmb3IgKHZhciBpID0gMCwgbCA9IGZyb20ubGVuZ3RoLCBhcjsgaSA8IGw7IGkrKykge1xuICAgICAgaWYgKGFyIHx8ICEoaSBpbiBmcm9tKSkge1xuICAgICAgICAgIGlmICghYXIpIGFyID0gQXJyYXkucHJvdG90eXBlLnNsaWNlLmNhbGwoZnJvbSwgMCwgaSk7XG4gICAgICAgICAgYXJbaV0gPSBmcm9tW2ldO1xuICAgICAgfVxuICB9XG4gIHJldHVybiB0by5jb25jYXQoYXIgfHwgQXJyYXkucHJvdG90eXBlLnNsaWNlLmNhbGwoZnJvbSkpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19hd2FpdCh2KSB7XG4gIHJldHVybiB0aGlzIGluc3RhbmNlb2YgX19hd2FpdCA/ICh0aGlzLnYgPSB2LCB0aGlzKSA6IG5ldyBfX2F3YWl0KHYpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19hc3luY0dlbmVyYXRvcih0aGlzQXJnLCBfYXJndW1lbnRzLCBnZW5lcmF0b3IpIHtcbiAgaWYgKCFTeW1ib2wuYXN5bmNJdGVyYXRvcikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIlN5bWJvbC5hc3luY0l0ZXJhdG9yIGlzIG5vdCBkZWZpbmVkLlwiKTtcbiAgdmFyIGcgPSBnZW5lcmF0b3IuYXBwbHkodGhpc0FyZywgX2FyZ3VtZW50cyB8fCBbXSksIGksIHEgPSBbXTtcbiAgcmV0dXJuIGkgPSBPYmplY3QuY3JlYXRlKCh0eXBlb2YgQXN5bmNJdGVyYXRvciA9PT0gXCJmdW5jdGlvblwiID8gQXN5bmNJdGVyYXRvciA6IE9iamVjdCkucHJvdG90eXBlKSwgdmVyYihcIm5leHRcIiksIHZlcmIoXCJ0aHJvd1wiKSwgdmVyYihcInJldHVyblwiLCBhd2FpdFJldHVybiksIGlbU3ltYm9sLmFzeW5jSXRlcmF0b3JdID0gZnVuY3Rpb24gKCkgeyByZXR1cm4gdGhpczsgfSwgaTtcbiAgZnVuY3Rpb24gYXdhaXRSZXR1cm4oZikgeyByZXR1cm4gZnVuY3Rpb24gKHYpIHsgcmV0dXJuIFByb21pc2UucmVzb2x2ZSh2KS50aGVuKGYsIHJlamVjdCk7IH07IH1cbiAgZnVuY3Rpb24gdmVyYihuLCBmKSB7IGlmIChnW25dKSB7IGlbbl0gPSBmdW5jdGlvbiAodikgeyByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKGEsIGIpIHsgcS5wdXNoKFtuLCB2LCBhLCBiXSkgPiAxIHx8IHJlc3VtZShuLCB2KTsgfSk7IH07IGlmIChmKSBpW25dID0gZihpW25dKTsgfSB9XG4gIGZ1bmN0aW9uIHJlc3VtZShuLCB2KSB7IHRyeSB7IHN0ZXAoZ1tuXSh2KSk7IH0gY2F0Y2ggKGUpIHsgc2V0dGxlKHFbMF1bM10sIGUpOyB9IH1cbiAgZnVuY3Rpb24gc3RlcChyKSB7IHIudmFsdWUgaW5zdGFuY2VvZiBfX2F3YWl0ID8gUHJvbWlzZS5yZXNvbHZlKHIudmFsdWUudikudGhlbihmdWxmaWxsLCByZWplY3QpIDogc2V0dGxlKHFbMF1bMl0sIHIpOyB9XG4gIGZ1bmN0aW9uIGZ1bGZpbGwodmFsdWUpIHsgcmVzdW1lKFwibmV4dFwiLCB2YWx1ZSk7IH1cbiAgZnVuY3Rpb24gcmVqZWN0KHZhbHVlKSB7IHJlc3VtZShcInRocm93XCIsIHZhbHVlKTsgfVxuICBmdW5jdGlvbiBzZXR0bGUoZiwgdikgeyBpZiAoZih2KSwgcS5zaGlmdCgpLCBxLmxlbmd0aCkgcmVzdW1lKHFbMF1bMF0sIHFbMF1bMV0pOyB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2FzeW5jRGVsZWdhdG9yKG8pIHtcbiAgdmFyIGksIHA7XG4gIHJldHVybiBpID0ge30sIHZlcmIoXCJuZXh0XCIpLCB2ZXJiKFwidGhyb3dcIiwgZnVuY3Rpb24gKGUpIHsgdGhyb3cgZTsgfSksIHZlcmIoXCJyZXR1cm5cIiksIGlbU3ltYm9sLml0ZXJhdG9yXSA9IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHRoaXM7IH0sIGk7XG4gIGZ1bmN0aW9uIHZlcmIobiwgZikgeyBpW25dID0gb1tuXSA/IGZ1bmN0aW9uICh2KSB7IHJldHVybiAocCA9ICFwKSA/IHsgdmFsdWU6IF9fYXdhaXQob1tuXSh2KSksIGRvbmU6IGZhbHNlIH0gOiBmID8gZih2KSA6IHY7IH0gOiBmOyB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2FzeW5jVmFsdWVzKG8pIHtcbiAgaWYgKCFTeW1ib2wuYXN5bmNJdGVyYXRvcikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIlN5bWJvbC5hc3luY0l0ZXJhdG9yIGlzIG5vdCBkZWZpbmVkLlwiKTtcbiAgdmFyIG0gPSBvW1N5bWJvbC5hc3luY0l0ZXJhdG9yXSwgaTtcbiAgcmV0dXJuIG0gPyBtLmNhbGwobykgOiAobyA9IHR5cGVvZiBfX3ZhbHVlcyA9PT0gXCJmdW5jdGlvblwiID8gX192YWx1ZXMobykgOiBvW1N5bWJvbC5pdGVyYXRvcl0oKSwgaSA9IHt9LCB2ZXJiKFwibmV4dFwiKSwgdmVyYihcInRocm93XCIpLCB2ZXJiKFwicmV0dXJuXCIpLCBpW1N5bWJvbC5hc3luY0l0ZXJhdG9yXSA9IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHRoaXM7IH0sIGkpO1xuICBmdW5jdGlvbiB2ZXJiKG4pIHsgaVtuXSA9IG9bbl0gJiYgZnVuY3Rpb24gKHYpIHsgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHsgdiA9IG9bbl0odiksIHNldHRsZShyZXNvbHZlLCByZWplY3QsIHYuZG9uZSwgdi52YWx1ZSk7IH0pOyB9OyB9XG4gIGZ1bmN0aW9uIHNldHRsZShyZXNvbHZlLCByZWplY3QsIGQsIHYpIHsgUHJvbWlzZS5yZXNvbHZlKHYpLnRoZW4oZnVuY3Rpb24odikgeyByZXNvbHZlKHsgdmFsdWU6IHYsIGRvbmU6IGQgfSk7IH0sIHJlamVjdCk7IH1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fbWFrZVRlbXBsYXRlT2JqZWN0KGNvb2tlZCwgcmF3KSB7XG4gIGlmIChPYmplY3QuZGVmaW5lUHJvcGVydHkpIHsgT2JqZWN0LmRlZmluZVByb3BlcnR5KGNvb2tlZCwgXCJyYXdcIiwgeyB2YWx1ZTogcmF3IH0pOyB9IGVsc2UgeyBjb29rZWQucmF3ID0gcmF3OyB9XG4gIHJldHVybiBjb29rZWQ7XG59O1xuXG52YXIgX19zZXRNb2R1bGVEZWZhdWx0ID0gT2JqZWN0LmNyZWF0ZSA/IChmdW5jdGlvbihvLCB2KSB7XG4gIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBcImRlZmF1bHRcIiwgeyBlbnVtZXJhYmxlOiB0cnVlLCB2YWx1ZTogdiB9KTtcbn0pIDogZnVuY3Rpb24obywgdikge1xuICBvW1wiZGVmYXVsdFwiXSA9IHY7XG59O1xuXG52YXIgb3duS2V5cyA9IGZ1bmN0aW9uKG8pIHtcbiAgb3duS2V5cyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eU5hbWVzIHx8IGZ1bmN0aW9uIChvKSB7XG4gICAgdmFyIGFyID0gW107XG4gICAgZm9yICh2YXIgayBpbiBvKSBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKG8sIGspKSBhclthci5sZW5ndGhdID0gaztcbiAgICByZXR1cm4gYXI7XG4gIH07XG4gIHJldHVybiBvd25LZXlzKG8pO1xufTtcblxuZXhwb3J0IGZ1bmN0aW9uIF9faW1wb3J0U3Rhcihtb2QpIHtcbiAgaWYgKG1vZCAmJiBtb2QuX19lc01vZHVsZSkgcmV0dXJuIG1vZDtcbiAgdmFyIHJlc3VsdCA9IHt9O1xuICBpZiAobW9kICE9IG51bGwpIGZvciAodmFyIGsgPSBvd25LZXlzKG1vZCksIGkgPSAwOyBpIDwgay5sZW5ndGg7IGkrKykgaWYgKGtbaV0gIT09IFwiZGVmYXVsdFwiKSBfX2NyZWF0ZUJpbmRpbmcocmVzdWx0LCBtb2QsIGtbaV0pO1xuICBfX3NldE1vZHVsZURlZmF1bHQocmVzdWx0LCBtb2QpO1xuICByZXR1cm4gcmVzdWx0O1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19pbXBvcnREZWZhdWx0KG1vZCkge1xuICByZXR1cm4gKG1vZCAmJiBtb2QuX19lc01vZHVsZSkgPyBtb2QgOiB7IGRlZmF1bHQ6IG1vZCB9O1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19jbGFzc1ByaXZhdGVGaWVsZEdldChyZWNlaXZlciwgc3RhdGUsIGtpbmQsIGYpIHtcbiAgaWYgKGtpbmQgPT09IFwiYVwiICYmICFmKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiUHJpdmF0ZSBhY2Nlc3NvciB3YXMgZGVmaW5lZCB3aXRob3V0IGEgZ2V0dGVyXCIpO1xuICBpZiAodHlwZW9mIHN0YXRlID09PSBcImZ1bmN0aW9uXCIgPyByZWNlaXZlciAhPT0gc3RhdGUgfHwgIWYgOiAhc3RhdGUuaGFzKHJlY2VpdmVyKSkgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkNhbm5vdCByZWFkIHByaXZhdGUgbWVtYmVyIGZyb20gYW4gb2JqZWN0IHdob3NlIGNsYXNzIGRpZCBub3QgZGVjbGFyZSBpdFwiKTtcbiAgcmV0dXJuIGtpbmQgPT09IFwibVwiID8gZiA6IGtpbmQgPT09IFwiYVwiID8gZi5jYWxsKHJlY2VpdmVyKSA6IGYgPyBmLnZhbHVlIDogc3RhdGUuZ2V0KHJlY2VpdmVyKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fY2xhc3NQcml2YXRlRmllbGRTZXQocmVjZWl2ZXIsIHN0YXRlLCB2YWx1ZSwga2luZCwgZikge1xuICBpZiAoa2luZCA9PT0gXCJtXCIpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJQcml2YXRlIG1ldGhvZCBpcyBub3Qgd3JpdGFibGVcIik7XG4gIGlmIChraW5kID09PSBcImFcIiAmJiAhZikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIlByaXZhdGUgYWNjZXNzb3Igd2FzIGRlZmluZWQgd2l0aG91dCBhIHNldHRlclwiKTtcbiAgaWYgKHR5cGVvZiBzdGF0ZSA9PT0gXCJmdW5jdGlvblwiID8gcmVjZWl2ZXIgIT09IHN0YXRlIHx8ICFmIDogIXN0YXRlLmhhcyhyZWNlaXZlcikpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJDYW5ub3Qgd3JpdGUgcHJpdmF0ZSBtZW1iZXIgdG8gYW4gb2JqZWN0IHdob3NlIGNsYXNzIGRpZCBub3QgZGVjbGFyZSBpdFwiKTtcbiAgcmV0dXJuIChraW5kID09PSBcImFcIiA/IGYuY2FsbChyZWNlaXZlciwgdmFsdWUpIDogZiA/IGYudmFsdWUgPSB2YWx1ZSA6IHN0YXRlLnNldChyZWNlaXZlciwgdmFsdWUpKSwgdmFsdWU7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2NsYXNzUHJpdmF0ZUZpZWxkSW4oc3RhdGUsIHJlY2VpdmVyKSB7XG4gIGlmIChyZWNlaXZlciA9PT0gbnVsbCB8fCAodHlwZW9mIHJlY2VpdmVyICE9PSBcIm9iamVjdFwiICYmIHR5cGVvZiByZWNlaXZlciAhPT0gXCJmdW5jdGlvblwiKSkgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkNhbm5vdCB1c2UgJ2luJyBvcGVyYXRvciBvbiBub24tb2JqZWN0XCIpO1xuICByZXR1cm4gdHlwZW9mIHN0YXRlID09PSBcImZ1bmN0aW9uXCIgPyByZWNlaXZlciA9PT0gc3RhdGUgOiBzdGF0ZS5oYXMocmVjZWl2ZXIpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19hZGREaXNwb3NhYmxlUmVzb3VyY2UoZW52LCB2YWx1ZSwgYXN5bmMpIHtcbiAgaWYgKHZhbHVlICE9PSBudWxsICYmIHZhbHVlICE9PSB2b2lkIDApIHtcbiAgICBpZiAodHlwZW9mIHZhbHVlICE9PSBcIm9iamVjdFwiICYmIHR5cGVvZiB2YWx1ZSAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiT2JqZWN0IGV4cGVjdGVkLlwiKTtcbiAgICB2YXIgZGlzcG9zZSwgaW5uZXI7XG4gICAgaWYgKGFzeW5jKSB7XG4gICAgICBpZiAoIVN5bWJvbC5hc3luY0Rpc3Bvc2UpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJTeW1ib2wuYXN5bmNEaXNwb3NlIGlzIG5vdCBkZWZpbmVkLlwiKTtcbiAgICAgIGRpc3Bvc2UgPSB2YWx1ZVtTeW1ib2wuYXN5bmNEaXNwb3NlXTtcbiAgICB9XG4gICAgaWYgKGRpc3Bvc2UgPT09IHZvaWQgMCkge1xuICAgICAgaWYgKCFTeW1ib2wuZGlzcG9zZSkgdGhyb3cgbmV3IFR5cGVFcnJvcihcIlN5bWJvbC5kaXNwb3NlIGlzIG5vdCBkZWZpbmVkLlwiKTtcbiAgICAgIGRpc3Bvc2UgPSB2YWx1ZVtTeW1ib2wuZGlzcG9zZV07XG4gICAgICBpZiAoYXN5bmMpIGlubmVyID0gZGlzcG9zZTtcbiAgICB9XG4gICAgaWYgKHR5cGVvZiBkaXNwb3NlICE9PSBcImZ1bmN0aW9uXCIpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJPYmplY3Qgbm90IGRpc3Bvc2FibGUuXCIpO1xuICAgIGlmIChpbm5lcikgZGlzcG9zZSA9IGZ1bmN0aW9uKCkgeyB0cnkgeyBpbm5lci5jYWxsKHRoaXMpOyB9IGNhdGNoIChlKSB7IHJldHVybiBQcm9taXNlLnJlamVjdChlKTsgfSB9O1xuICAgIGVudi5zdGFjay5wdXNoKHsgdmFsdWU6IHZhbHVlLCBkaXNwb3NlOiBkaXNwb3NlLCBhc3luYzogYXN5bmMgfSk7XG4gIH1cbiAgZWxzZSBpZiAoYXN5bmMpIHtcbiAgICBlbnYuc3RhY2sucHVzaCh7IGFzeW5jOiB0cnVlIH0pO1xuICB9XG4gIHJldHVybiB2YWx1ZTtcbn1cblxudmFyIF9TdXBwcmVzc2VkRXJyb3IgPSB0eXBlb2YgU3VwcHJlc3NlZEVycm9yID09PSBcImZ1bmN0aW9uXCIgPyBTdXBwcmVzc2VkRXJyb3IgOiBmdW5jdGlvbiAoZXJyb3IsIHN1cHByZXNzZWQsIG1lc3NhZ2UpIHtcbiAgdmFyIGUgPSBuZXcgRXJyb3IobWVzc2FnZSk7XG4gIHJldHVybiBlLm5hbWUgPSBcIlN1cHByZXNzZWRFcnJvclwiLCBlLmVycm9yID0gZXJyb3IsIGUuc3VwcHJlc3NlZCA9IHN1cHByZXNzZWQsIGU7XG59O1xuXG5leHBvcnQgZnVuY3Rpb24gX19kaXNwb3NlUmVzb3VyY2VzKGVudikge1xuICBmdW5jdGlvbiBmYWlsKGUpIHtcbiAgICBlbnYuZXJyb3IgPSBlbnYuaGFzRXJyb3IgPyBuZXcgX1N1cHByZXNzZWRFcnJvcihlLCBlbnYuZXJyb3IsIFwiQW4gZXJyb3Igd2FzIHN1cHByZXNzZWQgZHVyaW5nIGRpc3Bvc2FsLlwiKSA6IGU7XG4gICAgZW52Lmhhc0Vycm9yID0gdHJ1ZTtcbiAgfVxuICB2YXIgciwgcyA9IDA7XG4gIGZ1bmN0aW9uIG5leHQoKSB7XG4gICAgd2hpbGUgKHIgPSBlbnYuc3RhY2sucG9wKCkpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGlmICghci5hc3luYyAmJiBzID09PSAxKSByZXR1cm4gcyA9IDAsIGVudi5zdGFjay5wdXNoKHIpLCBQcm9taXNlLnJlc29sdmUoKS50aGVuKG5leHQpO1xuICAgICAgICBpZiAoci5kaXNwb3NlKSB7XG4gICAgICAgICAgdmFyIHJlc3VsdCA9IHIuZGlzcG9zZS5jYWxsKHIudmFsdWUpO1xuICAgICAgICAgIGlmIChyLmFzeW5jKSByZXR1cm4gcyB8PSAyLCBQcm9taXNlLnJlc29sdmUocmVzdWx0KS50aGVuKG5leHQsIGZ1bmN0aW9uKGUpIHsgZmFpbChlKTsgcmV0dXJuIG5leHQoKTsgfSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBzIHw9IDE7XG4gICAgICB9XG4gICAgICBjYXRjaCAoZSkge1xuICAgICAgICBmYWlsKGUpO1xuICAgICAgfVxuICAgIH1cbiAgICBpZiAocyA9PT0gMSkgcmV0dXJuIGVudi5oYXNFcnJvciA/IFByb21pc2UucmVqZWN0KGVudi5lcnJvcikgOiBQcm9taXNlLnJlc29sdmUoKTtcbiAgICBpZiAoZW52Lmhhc0Vycm9yKSB0aHJvdyBlbnYuZXJyb3I7XG4gIH1cbiAgcmV0dXJuIG5leHQoKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fcmV3cml0ZVJlbGF0aXZlSW1wb3J0RXh0ZW5zaW9uKHBhdGgsIHByZXNlcnZlSnN4KSB7XG4gIGlmICh0eXBlb2YgcGF0aCA9PT0gXCJzdHJpbmdcIiAmJiAvXlxcLlxcLj9cXC8vLnRlc3QocGF0aCkpIHtcbiAgICAgIHJldHVybiBwYXRoLnJlcGxhY2UoL1xcLih0c3gpJHwoKD86XFwuZCk/KSgoPzpcXC5bXi4vXSs/KT8pXFwuKFtjbV0/KXRzJC9pLCBmdW5jdGlvbiAobSwgdHN4LCBkLCBleHQsIGNtKSB7XG4gICAgICAgICAgcmV0dXJuIHRzeCA/IHByZXNlcnZlSnN4ID8gXCIuanN4XCIgOiBcIi5qc1wiIDogZCAmJiAoIWV4dCB8fCAhY20pID8gbSA6IChkICsgZXh0ICsgXCIuXCIgKyBjbS50b0xvd2VyQ2FzZSgpICsgXCJqc1wiKTtcbiAgICAgIH0pO1xuICB9XG4gIHJldHVybiBwYXRoO1xufVxuXG5leHBvcnQgZGVmYXVsdCB7XG4gIF9fZXh0ZW5kcyxcbiAgX19hc3NpZ24sXG4gIF9fcmVzdCxcbiAgX19kZWNvcmF0ZSxcbiAgX19wYXJhbSxcbiAgX19lc0RlY29yYXRlLFxuICBfX3J1bkluaXRpYWxpemVycyxcbiAgX19wcm9wS2V5LFxuICBfX3NldEZ1bmN0aW9uTmFtZSxcbiAgX19tZXRhZGF0YSxcbiAgX19hd2FpdGVyLFxuICBfX2dlbmVyYXRvcixcbiAgX19jcmVhdGVCaW5kaW5nLFxuICBfX2V4cG9ydFN0YXIsXG4gIF9fdmFsdWVzLFxuICBfX3JlYWQsXG4gIF9fc3ByZWFkLFxuICBfX3NwcmVhZEFycmF5cyxcbiAgX19zcHJlYWRBcnJheSxcbiAgX19hd2FpdCxcbiAgX19hc3luY0dlbmVyYXRvcixcbiAgX19hc3luY0RlbGVnYXRvcixcbiAgX19hc3luY1ZhbHVlcyxcbiAgX19tYWtlVGVtcGxhdGVPYmplY3QsXG4gIF9faW1wb3J0U3RhcixcbiAgX19pbXBvcnREZWZhdWx0LFxuICBfX2NsYXNzUHJpdmF0ZUZpZWxkR2V0LFxuICBfX2NsYXNzUHJpdmF0ZUZpZWxkU2V0LFxuICBfX2NsYXNzUHJpdmF0ZUZpZWxkSW4sXG4gIF9fYWRkRGlzcG9zYWJsZVJlc291cmNlLFxuICBfX2Rpc3Bvc2VSZXNvdXJjZXMsXG4gIF9fcmV3cml0ZVJlbGF0aXZlSW1wb3J0RXh0ZW5zaW9uLFxufTtcbiIsImltcG9ydCB7IGNyZWF0ZUFjdGlvbiwgQWN0aW9uIH0gZnJvbSBcInJlZHV4LWFjdGlvbnNcIjtcclxuLy8gaW1wb3J0IHsgSU9yZGVyQVBJUmVzcG9uc2UgfSBmcm9tIFwiLi4vbW9kZWxzXCI7XHJcbi8vIGltcG9ydCB7IG9yZWRlckRldGFpbHNNdXRhdG9yRm4gfSBmcm9tIFwiLi4vbXV0YXRvcnNcIjtcclxuXHJcbi8vIFdpZGdldCBhY3Rpb25zXHJcbmV4cG9ydCBjb25zdCBnZXRPZGVyRGV0YWlscyA9IGNyZWF0ZUFjdGlvbihcIkdFVF9PUkRFUl9ERVRBSUxTXCIpO1xyXG5leHBvcnQgY29uc3QgZ2V0QXBwb2ludG1lbnQgPSBjcmVhdGVBY3Rpb24oXCJHRVRfQVBQT0lOVE1FTlRcIik7XHJcbmV4cG9ydCBjb25zdCBzZXRBcHBvaW50bWVudCA9IGNyZWF0ZUFjdGlvbjxhbnk+KFwiU0VUX0FQUE9JTlRNRU5UXCIpIGFzIChwYXlsb2FkOiBhbnkpID0+IEFjdGlvbjxhbnk+O1xyXG5leHBvcnQgY29uc3Qgc2V0QXZhaWxhYmxlRGF0ZXMgPSBjcmVhdGVBY3Rpb248YW55PihcIlNFVF9BVkFJQUxCTEVfREFURVNcIikgYXMgKHBheWxvYWQ6IGFueSkgPT4gQWN0aW9uPGFueT47XHJcbi8vIGV4cG9ydCBjb25zdCBzZXRQcmVmZXJyZWREYXRlID0gY3JlYXRlQWN0aW9uPGFueT4oXCJTRVRfUFJFRkVSUkVEX0RBVEVcIikgYXMgKHBheWxvYWQ6IGFueSkgPT4gQWN0aW9uPGFueT47XHJcbmV4cG9ydCBjb25zdCBjb250YWN0SW5mb3JtYXRpb24gPSBjcmVhdGVBY3Rpb248YW55PihcIlNFVF9DT05UQUNUX0lORk9cIikgYXMgKHBheWxvYWQ6IGFueSkgPT4gQWN0aW9uPGFueT47XHJcbmV4cG9ydCBjb25zdCBzZXREdXJhdGlvbiA9IGNyZWF0ZUFjdGlvbjxhbnk+KFwiU0VUX0RVUkFUSU9OXCIpIGFzIChwYXlsb2FkOiBhbnkpID0+IEFjdGlvbjxhbnk+O1xyXG5leHBvcnQgY29uc3Qgc2V0SW5zdGFsbGF0aW9uQWRkcmVzcyA9IGNyZWF0ZUFjdGlvbjxhbnk+KFwiU0VUX0lOU1RBTExBVElPTl9BRERSRVNTXCIpIGFzIChwYXlsb2FkOiBhbnkpID0+IEFjdGlvbjxhbnk+O1xyXG5leHBvcnQgY29uc3Qgc2V0QWRkaXRpb25hbERldGFpbHMgPSBjcmVhdGVBY3Rpb248YW55PihcIlNFVF9BRERJVElPTkFMX0RFVEFJTFNcIikgYXMgKHBheWxvYWQ6IGFueSkgPT4gQWN0aW9uPGFueT47XHJcbmV4cG9ydCBjb25zdCBzZXRJc0luc3RhbGxhdGlvblJlcXVpcmVkID0gY3JlYXRlQWN0aW9uPGFueT4oXCJTRVRfSU5TVEFMTEFUSU9OX1JFUVVJUkVEXCIpIGFzIChwYXlsb2FkOiBhbnkpID0+IEFjdGlvbjxhbnk+O1xyXG5leHBvcnQgY29uc3Qgc2V0Rm9yRXJyb3JzID0gY3JlYXRlQWN0aW9uPGFueT4oXCJTRVRfRk9STV9FUlJPUlNcIikgYXMgKHBheWxvYWQ6IGFueSkgPT4gQWN0aW9uPGFueT47XHJcbi8vIGV4cG9ydCBjb25zdCBzZXRPZGVyRGV0YWlscyA9IGNyZWF0ZUFjdGlvbjxJT3JkZXJkZXRhaWxzPihcIlNFVF9GTE9XX1NVTU1BUllfVE9UQUxTXCIsIG9yZWRlckRldGFpbHNNdXRhdG9yRm4gYXMgYW55KSBhcyAocmVzcG9uZXM6IElPcmRlckFQSVJlc3BvbnNlKSA9PiBBY3Rpb248SU9yZGVyZGV0YWlscz47XHJcblxyXG4vLyBQaXBlZCBhY3Rpb25zXHJcblxyXG5cclxuLy8gQWN0aW9uIGZvciBHbG9iYWwgQWN0aW9ucyBMaXN0ZW5lclxyXG5leHBvcnQgY29uc3QgaW5pdFNsaWNrU2xpZGVyID0gY3JlYXRlQWN0aW9uKFwiSU5JVF9TTElDS19TTElERVJcIik7XHJcbiIsImltcG9ydCB7IEluamVjdGFibGUsIENvbW1vbkZlYXR1cmVzIH0gZnJvbSBcImJ3dGtcIjtcclxuaW1wb3J0IHsgTW9kZWxzIH0gZnJvbSBcIm9tZi1jaGFuZ2VwYWNrYWdlLWNvbXBvbmVudHNcIjtcclxuXHJcbmNvbnN0IHsgQmFzZUNvbmZpZywgY29uZmlnUHJvcGVydHkgfSA9IENvbW1vbkZlYXR1cmVzO1xyXG5cclxuaW50ZXJmYWNlIElBcHBDb25maWcgZXh0ZW5kcyBNb2RlbHMuSUJhc2VDb25maWcge1xyXG59XHJcblxyXG5pbnRlcmZhY2UgSUFwcEFQSSBleHRlbmRzIE1vZGVscy5JQmFzZVdpZGdldEFQSSB7XHJcbiAgb3JkZXJEZXRhaWxzQVBJOiBzdHJpbmc7XHJcbiAgYXBwb2ludG1lbnRBUEk6IHN0cmluZztcclxuICBvcmRlclN1Ym1pdEFQSTogc3RyaW5nO1xyXG59XHJcblxyXG4vKipcclxuICogV2lkZ2V0IGNvbmZpZ3VyYXRpb24gcHJvdmlkZXJcclxuICogQWxsb3dzIHRoZSBleHRlcm5hbCBpbW11dGFibGVcclxuICogY29uZmlnIHNldHRpbmdcclxuICogQGV4cG9ydFxyXG4gKiBAY2xhc3MgQ29uZmlnXHJcbiAqIEBleHRlbmRzIHtCYXNlQ29uZmlnPElBcHBDb25maWc+fVxyXG4gKi9cclxuQEluamVjdGFibGVcclxuZXhwb3J0IGNsYXNzIENvbmZpZyBleHRlbmRzIEJhc2VDb25maWc8SUFwcENvbmZpZz4ge1xyXG4gIEBjb25maWdQcm9wZXJ0eSh7fSkgaGVhZGVyczogYW55O1xyXG4gIEBjb25maWdQcm9wZXJ0eSh7fSkgZW52aXJvbm1lbnRWYXJpYWJsZXM6IGFueTtcclxuICBAY29uZmlnUHJvcGVydHkoe30pIG1vY2tkYXRhOiBhbnk7XHJcbiAgQGNvbmZpZ1Byb3BlcnR5KHtiYXNlOiBcImh0dHA6Ly8xMjcuMC4wLjE6ODg4MVwiLCBvcmRlckRldGFpbHNBUEk6IFwiL1wiLCBhcHBvaW50bWVudEFQSTogXCIvXCIsIG9yZGVyU3VibWl0QVBJOiBcIi9cIn0pIGFwaTogSUFwcEFQSTtcclxufVxyXG4iLCJpbXBvcnQgeyBJbmplY3RhYmxlLCBBamF4U2VydmljZXMgfSBmcm9tIFwiYnd0a1wiO1xyXG5pbXBvcnQgeyBCYXNlQ2xpZW50IH0gZnJvbSBcIm9tZi1jaGFuZ2VwYWNrYWdlLWNvbXBvbmVudHNcIjtcclxuXHJcbmltcG9ydCB7IENvbmZpZyB9IGZyb20gXCIuL0NvbmZpZ1wiO1xyXG5cclxuLyoqXHJcbiAqIEJhc2UgY2xpZW50IGltcGxlbWVudGF0aW9uXHJcbiAqIGZvciBBSkFYIGNhbGxzXHJcbiAqIEBleHBvcnRcclxuICogQGNsYXNzIENsaWVudFxyXG4gKiBAZXh0ZW5kcyB7QmFzZUNsaWVudH1cclxuICovXHJcbkBJbmplY3RhYmxlXHJcbmV4cG9ydCBjbGFzcyBDbGllbnQgZXh0ZW5kcyBCYXNlQ2xpZW50IHtcclxuICBjb25zdHJ1Y3RvcihhamF4Q2xpZW50OiBBamF4U2VydmljZXMsIGNvbmZpZzogQ29uZmlnKSB7XHJcbiAgICBzdXBlcihhamF4Q2xpZW50LCBjb25maWcpO1xyXG4gIH1cclxufVxyXG4iLCJpbXBvcnQgeyBNb2RlbHMgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgeyBJQXZhaWxhYmxlRGF0ZXMsIElJbnN0YWxsYXRpb25BZGRyZXNzLCBJQ29udGFjdEluZm9ybWF0aW9uLCBJQWRkaXRpb25hbERldGFpbHMgfSBmcm9tIFwiLi9BcHBcIjtcclxuaW1wb3J0IHsgRUR1cmF0aW9uIH0gZnJvbSBcIi4vRW51bXNcIjtcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgSVN0b3JlU3RhdGUgZXh0ZW5kcyBNb2RlbHMuSUJhc2VTdG9yZVN0YXRlIHtcclxuICBhdmFpbGFibGVEYXRlcz86IEFycmF5PElBdmFpbGFibGVEYXRlcz47XHJcbiAgLy8gcHJlZmVycmVkRGF0ZT86IElBdmFpbGFibGVEYXRlcztcclxuICBkdXJhdGlvbj86IEVEdXJhdGlvbjtcclxuICBpbnN0YWxsYXRpb25BZGRyZXNzPzogSUluc3RhbGxhdGlvbkFkZHJlc3M7XHJcbiAgY29udGFjdEluZm9ybWF0aW9uPzogSUNvbnRhY3RJbmZvcm1hdGlvbjtcclxuICBhZGRpdGlvbmFsRGV0YWlscz86IElBZGRpdGlvbmFsRGV0YWlscztcclxuICBpc0luc3RhbGxhdGlvblJlcXVpcmVkPzogYm9vbGVhbjtcclxufVxyXG4iLCJpbXBvcnQgeyBNb2RlbHMgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgeyBFUHJlZmVycmVkQ29udGFjdE1ldGhvZCwgRUR1cmF0aW9uIH0gZnJvbSBcIi4vRW51bXNcIjtcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgSUFwcFByb3BzIGV4dGVuZHMgTW9kZWxzLklCYXNlQXBwUHJvcHMgeyB9XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIElBcHBvaW50bWVudEFQSVJlc3BvbnNlIHtcclxuICBhcHBvaW50bWVudDogSUFwcG9pbnRtZW50O1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIElBcHBvaW50bWVudCB7XHJcbiAgYXZhaWxhYmxlRGF0ZXM/OiBBcnJheTxJQXZhaWxhYmxlRGF0ZXM+O1xyXG4gIHByZWZlcnJlZERhdGU/OiBJQXZhaWxhYmxlRGF0ZXM7XHJcbiAgZHVyYXRpb24/OiBFRHVyYXRpb247XHJcbiAgaW5zdGFsbGF0aW9uQWRkcmVzcz86IElJbnN0YWxsYXRpb25BZGRyZXNzO1xyXG4gIGNvbnRhY3RJbmZvcm1hdGlvbj86IElDb250YWN0SW5mb3JtYXRpb247XHJcbiAgYWRkaXRpb25hbERldGFpbHM/OiBJQWRkaXRpb25hbERldGFpbHM7XHJcbiAgaXNJbnN0YWxsYXRpb25SZXF1aXJlZD86IGJvb2xlYW47XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgSUF2YWlsYWJsZURhdGVzIHtcclxuICBkYXRlOiBzdHJpbmc7XHJcbiAgdGltZVNsb3RzOiBBcnJheTxJVGltZVNsb3RzPjtcclxuICBpc1ByZWZlcnJlZERhdGU/OiBib29sZWFuO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIElJbnN0YWxsYXRpb25BZGRyZXNzIHtcclxuICBhZGRyZXNzMT86IHN0cmluZztcclxuICBhZGRyZXNzMj86IHN0cmluZztcclxuICBjaXR5OiBzdHJpbmc7XHJcbiAgcHJvdmluY2U6IHN0cmluZztcclxuICBwb3N0YWxDb2RlOiBzdHJpbmc7XHJcbiAgYXBhcnRtZW50TnVtYmVyOiBzdHJpbmc7XHJcbiAgYXBhcnRtZW50VHlwZTogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIElDb250YWN0SW5mb3JtYXRpb24ge1xyXG4gIHByZWZlcnJlZENvbnRhY3RNZXRob2Q/OiBFUHJlZmVycmVkQ29udGFjdE1ldGhvZDtcclxuICBwcmltYXJ5UGhvbmU/OiBJUHJpbWFyeVBob25lO1xyXG4gIGFkZGl0aW9uYWxQaG9uZT86IElBZGRpdGlvbmFsUGhvbmU7XHJcbiAgdGV4dE1lc3NhZ2U/OiBzdHJpbmc7XHJcbiAgZW1haWw/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgSUFkZGl0aW9uYWxQaG9uZSB7XHJcbiAgcGhvbmVOdW1iZXI6IHN0cmluZztcclxuICBwaG9uZUV4dGVuc2lvbjogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIElUaW1lU2xvdHMge1xyXG4gIGludGVydmFsVHlwZTogRUR1cmF0aW9uO1xyXG4gIHRpbWVJbnRlcnZhbDogSVRpbWVJbnRlcnZhbDtcclxuICBpc0F2YWlsYWJsZTogYm9vbGVhbjtcclxuICBpc1NlbGVjdGVkOiBib29sZWFuO1xyXG4gIGR1cmF0aW9uOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgSVRpbWVJbnRlcnZhbCB7XHJcbiAgc3RhcnRUaW1lOiBudW1iZXI7XHJcbiAgZW5kVGltZTogbnVtYmVyO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIElGb3JtRGF0YSB7XHJcbiAgZGF0ZUFuZFRpbWU6IHN0cmluZztcclxuICBQUkVGRVJFRF9NRVRIT0RfT0ZfQ09OVEFDVDogc3RyaW5nO1xyXG4gIEVNQUlMOiBzdHJpbmc7XHJcbiAgVEVYVF9NRVNTQUdFOiBzdHJpbmc7XHJcbiAgUEhPTkU6IHN0cmluZztcclxuICBBRERJVElPTkFMX1BIT05FX05VTUJFUjogc3RyaW5nO1xyXG4gIEFERElUSU9OQUxfUEhPTkVfRVhUOiBzdHJpbmc7XHJcbiAgQVBQQVJUTUVOVDogc3RyaW5nO1xyXG4gIEVOVFJZX0NPREU6IHN0cmluZztcclxuICBTVVBFUklOVEVOREFOVF9OQU1FOiBzdHJpbmc7XHJcbiAgU1VQRVJJTlRFTkRBTlRfUEhPTkU6IHN0cmluZztcclxuICBJTkZPUk1FRF9TVVBFUklOVEVOREFOVDogc3RyaW5nO1xyXG4gIFNQRUNJQUxfSU5TVFJVQ1RJT05TOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgSUFkZGl0aW9uYWxEZXRhaWxzIHtcclxuICBhcGFydG1lbnQ6IHN0cmluZztcclxuICBlbnRyeUNvZGU6IHN0cmluZztcclxuICBzcGVjaWFsSW5zdHJ1Y3Rpb25zOiBzdHJpbmc7XHJcbiAgc3VwZXJpbnRlbmRhbnROYW1lOiBzdHJpbmc7XHJcbiAgc3VwZXJpbnRlbmRhbnRQaG9uZTogc3RyaW5nO1xyXG4gIGluZm9ybWVkU3VwZXJpbnRlbmRhbnQ6IGJvb2xlYW47XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgSVByaW1hcnlQaG9uZSB7XHJcbiAgcGhvbmVOdW1iZXI6IHN0cmluZztcclxuICBwaG9uZUV4dGVuc2lvbj86IHN0cmluZztcclxufVxyXG4iLCJpbXBvcnQgeyBDb25maWcgfSBmcm9tIFwiLi4vQ29uZmlnXCI7XHJcbmltcG9ydCB7IE1vZGVscyB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcblxyXG4vKipcclxuICogcHJvcHMgcGFzc2VkIHRvIGh0ZSB3aWRnZXQgZnJvbSBpdCdzIHBhcmVudFxyXG4gKiBvciBvbiBEb21SZW5kZXJcclxuICogQGV4cG9ydFxyXG4gKiBAaW50ZXJmYWNlIElXaWRnZXRQcm9wc1xyXG4gKi9cclxuZXhwb3J0IGludGVyZmFjZSBJV2lkZ2V0UHJvcHMgZXh0ZW5kcyBNb2RlbHMuSUJhc2VXaWRnZXRQcm9wcyB7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBpbW11dGFibGUgcHJvcHMgc2V0IG9udG8gd2lkZ2V0XHJcbiAqIGNvbnRleHQgYW5kIGFjY2Vzc2libGUgZnJvbVxyXG4gKiBhbnkgY29tcG9uZW50XHJcbiAqIEBleHBvcnRcclxuICogQGludGVyZmFjZSBJV2lkZ2V0Q29udGV4dFxyXG4gKi9cclxuZXhwb3J0IGludGVyZmFjZSBJV2lkZ2V0Q29udGV4dCBleHRlbmRzIE1vZGVscy5JV2lkZ2V0Q29udGV4dDxDb25maWc+IHtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBJRXJyb3JzTGlzdCB7XHJcbiAgaWQ6IHN0cmluZztcclxuICBlcnJvcjogc3RyaW5nO1xyXG59XHJcbiIsImltcG9ydCB7IElTdG9yZVN0YXRlIH0gZnJvbSBcIi4vU3RvcmVcIjtcclxuaW1wb3J0IHsgSUF2YWlsYWJsZURhdGVzIH0gZnJvbSBcIi4vQXBwXCI7XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIEFwcG9pbnRtZW50RGF0YSB7XHJcbiAgZGF0ZUFuZFRpbWU6IHN0cmluZztcclxuICBQUkVGRVJFRF9NRVRIT0RfT0ZfQ09OVEFDVDogc3RyaW5nO1xyXG4gIFBob25lX0xBQkVMPzogc3RyaW5nO1xyXG4gIFBob25lX0VYVD86IHN0cmluZztcclxuICBFbWFpbF9MQUJFTD86IHN0cmluZztcclxuICBUZXh0TWVzc2FnZV9MQUJFTD86IHN0cmluZztcclxuICBBRERJVElPTkFMX1BIT05FX05VTUJFUjogc3RyaW5nO1xyXG4gIEFERElUSU9OQUxfUEhPTkVfRVhUOiBzdHJpbmc7XHJcbiAgQVBQQVJUTUVOVDogc3RyaW5nO1xyXG4gIEVOVFJZX0NPREU6IHN0cmluZztcclxuICBTVVBFUklOVEVOREFOVF9OQU1FOiBzdHJpbmc7XHJcbiAgU1VQRVJJTlRFTkRBTlRfUEhPTkU6IHN0cmluZztcclxuICBJTkZPUk1FRF9TVVBFUklOVEVOREFOVDogYm9vbGVhbjtcclxuICBTUEVDSUFMX0lOU1RSVUNUSU9OUzogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgUmVxdWVzdCA9IHtcclxuICBhdmFpbGFibGVEYXRlczogbnVsbCxcclxuICBkdXJhdGlvbjogXCJcIixcclxuICBpbnN0YWxsYXRpb25BZGRyZXNzOiB7XHJcbiAgICBhZGRyZXNzMTogXCJcIixcclxuICAgIGFkZHJlc3MyOiBcIlwiLFxyXG4gICAgY2l0eTogXCJcIixcclxuICAgIHByb3ZpbmNlOiBcIlwiLFxyXG4gICAgcG9zdGFsQ29kZTogXCJcIixcclxuICAgIGFwYXJ0bWVudFR5cGU6IFwiXCIsXHJcbiAgICBhcGFydG1lbnROdW1iZXI6IFwiXCJcclxuICB9LFxyXG4gIGNvbnRhY3RJbmZvcm1hdGlvbjoge1xyXG4gICAgcHJlZmVycmVkQ29udGFjdE1ldGhvZDogXCJcIixcclxuICAgIHByaW1hcnlQaG9uZToge1xyXG4gICAgICBwaG9uZU51bWJlcjogXCJcIixcclxuICAgICAgcGhvbmVFeHRlbnNpb246IFwiXCJcclxuICAgIH0sXHJcbiAgICBtb2JpbGVOdW1iZXI6IG51bGwsXHJcbiAgICBhZGRpdGlvbmFsUGhvbmU6IHtcclxuICAgICAgcGhvbmVOdW1iZXI6IFwiXCIsXHJcbiAgICAgIHBob25lRXh0ZW5zaW9uOiBcIlwiXHJcbiAgICB9LFxyXG4gICAgdGV4dE1lc3NhZ2U6IFwiXCIsXHJcbiAgICBlbWFpbDogXCJcIlxyXG4gIH0sXHJcbiAgYWRkaXRpb25hbERldGFpbHM6IHtcclxuICAgIGFwYXJ0bWVudDogXCJcIixcclxuICAgIGVudHJ5Q29kZTogXCJcIixcclxuICAgIHNwZWNpYWxJbnN0cnVjdGlvbnM6IFwiXCIsXHJcbiAgICBzdXBlcmludGVuZGFudE5hbWU6IFwiXCIsXHJcbiAgICBzdXBlcmludGVuZGFudFBob25lOiBcIlwiLFxyXG4gICAgaW5mb3JtZWRTdXBlcmludGVuZGFudDogbnVsbFxyXG4gIH0sXHJcbiAgaXNJbnN0YWxsYXRpb25SZXF1aXJlZDogbnVsbFxyXG59O1xyXG5cclxuZXhwb3J0IGNsYXNzIE1hcFJlcXVlc3REYXRhIHtcclxuICBwdWJsaWMgc3RhdGljIGNyZWF0ZShwYXlsb2FkOiBBcHBvaW50bWVudERhdGEsIHJlcXVlc3Q6IHR5cGVvZiBSZXF1ZXN0LCBzdG9yZTogSVN0b3JlU3RhdGUpIHtcclxuICAgIC8vIFByZWZlcmVkIERhdGVcclxuICAgIC8vIHJlcXVlc3QucHJlZmVycmVkRGF0ZS5kYXRlID0gcGF5bG9hZC5kYXRlQW5kVGltZTtcclxuICAgIC8vIHJlcXVlc3QucHJlZmVycmVkRGF0ZS50aW1lU2xvdHNbMF0uaW50ZXJ2YWxUeXBlID0gXCJcIjtcclxuXHJcbiAgICAvLyBJbnN0YWxsYXRpb24gQWRkcmVzc1xyXG4gICAgcmVxdWVzdC5pbnN0YWxsYXRpb25BZGRyZXNzLmFkZHJlc3MxID0gc3RvcmUuaW5zdGFsbGF0aW9uQWRkcmVzcyAmJiBzdG9yZS5pbnN0YWxsYXRpb25BZGRyZXNzLmFkZHJlc3MxID8gc3RvcmUuaW5zdGFsbGF0aW9uQWRkcmVzcy5hZGRyZXNzMSA6IFwiXCI7XHJcbiAgICByZXF1ZXN0Lmluc3RhbGxhdGlvbkFkZHJlc3MuYWRkcmVzczIgPSBzdG9yZS5pbnN0YWxsYXRpb25BZGRyZXNzICYmIHN0b3JlLmluc3RhbGxhdGlvbkFkZHJlc3MuYWRkcmVzczIgPyBzdG9yZS5pbnN0YWxsYXRpb25BZGRyZXNzLmFkZHJlc3MyIDogXCJcIjtcclxuICAgIHJlcXVlc3QuaW5zdGFsbGF0aW9uQWRkcmVzcy5jaXR5ID0gc3RvcmUuaW5zdGFsbGF0aW9uQWRkcmVzcyAmJiBzdG9yZS5pbnN0YWxsYXRpb25BZGRyZXNzLmNpdHkgPyBzdG9yZS5pbnN0YWxsYXRpb25BZGRyZXNzLmNpdHkgOiBcIlwiO1xyXG4gICAgcmVxdWVzdC5pbnN0YWxsYXRpb25BZGRyZXNzLnBvc3RhbENvZGUgPSBzdG9yZS5pbnN0YWxsYXRpb25BZGRyZXNzICYmIHN0b3JlLmluc3RhbGxhdGlvbkFkZHJlc3MucG9zdGFsQ29kZSA/IHN0b3JlLmluc3RhbGxhdGlvbkFkZHJlc3MucG9zdGFsQ29kZSA6IFwiXCI7XHJcbiAgICByZXF1ZXN0Lmluc3RhbGxhdGlvbkFkZHJlc3MucHJvdmluY2UgPSBzdG9yZS5pbnN0YWxsYXRpb25BZGRyZXNzICYmIHN0b3JlLmluc3RhbGxhdGlvbkFkZHJlc3MucHJvdmluY2UgPyBzdG9yZS5pbnN0YWxsYXRpb25BZGRyZXNzLnByb3ZpbmNlIDogXCJcIjtcclxuICAgIHJlcXVlc3QuaW5zdGFsbGF0aW9uQWRkcmVzcy5hcGFydG1lbnRUeXBlID0gc3RvcmUuaW5zdGFsbGF0aW9uQWRkcmVzcyAmJiBzdG9yZS5pbnN0YWxsYXRpb25BZGRyZXNzLnByb3ZpbmNlID8gc3RvcmUuaW5zdGFsbGF0aW9uQWRkcmVzcy5hcGFydG1lbnRUeXBlIDogXCJcIjtcclxuICAgIHJlcXVlc3QuaW5zdGFsbGF0aW9uQWRkcmVzcy5hcGFydG1lbnROdW1iZXIgPSBzdG9yZS5pbnN0YWxsYXRpb25BZGRyZXNzICYmIHN0b3JlLmluc3RhbGxhdGlvbkFkZHJlc3MucHJvdmluY2UgPyBzdG9yZS5pbnN0YWxsYXRpb25BZGRyZXNzLmFwYXJ0bWVudE51bWJlciA6IFwiXCI7XHJcbiAgICAvLyBJbnN0YWxsYXRpb24gUmVxdWlyZWRcclxuICAgIHJlcXVlc3QuaXNJbnN0YWxsYXRpb25SZXF1aXJlZCA9IHN0b3JlLmlzSW5zdGFsbGF0aW9uUmVxdWlyZWQgYXMgYW55O1xyXG5cclxuICAgIC8vIER1cmF0aW9uXHJcbiAgICByZXF1ZXN0LmR1cmF0aW9uID0gc3RvcmUuZHVyYXRpb24gYXMgc3RyaW5nO1xyXG5cclxuICAgIC8vIENvbnRhY3QgSW5mb3JtYXRpb25cclxuICAgIHJlcXVlc3QuY29udGFjdEluZm9ybWF0aW9uLnByaW1hcnlQaG9uZS5waG9uZU51bWJlciA9IHBheWxvYWQuUGhvbmVfTEFCRUwgPyBwYXlsb2FkLlBob25lX0xBQkVMIDogXCJcIjtcclxuICAgIHJlcXVlc3QuY29udGFjdEluZm9ybWF0aW9uLnByaW1hcnlQaG9uZS5waG9uZUV4dGVuc2lvbiA9IHBheWxvYWQuUGhvbmVfRVhUID8gcGF5bG9hZC5QaG9uZV9FWFQgOiBcIlwiO1xyXG4gICAgcmVxdWVzdC5jb250YWN0SW5mb3JtYXRpb24uYWRkaXRpb25hbFBob25lLnBob25lTnVtYmVyID0gcGF5bG9hZC5BRERJVElPTkFMX1BIT05FX05VTUJFUjtcclxuICAgIHJlcXVlc3QuY29udGFjdEluZm9ybWF0aW9uLmFkZGl0aW9uYWxQaG9uZS5waG9uZUV4dGVuc2lvbiA9IHBheWxvYWQuQURESVRJT05BTF9QSE9ORV9FWFQ7XHJcbiAgICByZXF1ZXN0LmNvbnRhY3RJbmZvcm1hdGlvbi5wcmVmZXJyZWRDb250YWN0TWV0aG9kID0gcGF5bG9hZC5QUkVGRVJFRF9NRVRIT0RfT0ZfQ09OVEFDVDtcclxuICAgIHJlcXVlc3QuY29udGFjdEluZm9ybWF0aW9uLmVtYWlsID0gcGF5bG9hZC5FbWFpbF9MQUJFTCA/IHBheWxvYWQuRW1haWxfTEFCRUwgOiBcIlwiO1xyXG4gICAgcmVxdWVzdC5jb250YWN0SW5mb3JtYXRpb24udGV4dE1lc3NhZ2UgPSBwYXlsb2FkLlRleHRNZXNzYWdlX0xBQkVMID8gcGF5bG9hZC5UZXh0TWVzc2FnZV9MQUJFTCBhcyBhbnkgOiBcIlwiO1xyXG5cclxuICAgIC8vIEF2YWlsYWJsZSBEYXRlc1xyXG4gICAgcmVxdWVzdC5hdmFpbGFibGVEYXRlcyA9IHVwZGF0ZUF2YWlsYWJsZURhdGVzKHN0b3JlLmF2YWlsYWJsZURhdGVzLCBKU09OLnBhcnNlKHBheWxvYWQuZGF0ZUFuZFRpbWUpKSBhcyBhbnk7XHJcblxyXG4gICAgLy8gQWRkaXRpb25hbCBEZXRhaWxzXHJcbiAgICByZXF1ZXN0LmFkZGl0aW9uYWxEZXRhaWxzLmFwYXJ0bWVudCA9IHBheWxvYWQuQVBQQVJUTUVOVDtcclxuICAgIHJlcXVlc3QuYWRkaXRpb25hbERldGFpbHMuZW50cnlDb2RlID0gcGF5bG9hZC5FTlRSWV9DT0RFO1xyXG4gICAgcmVxdWVzdC5hZGRpdGlvbmFsRGV0YWlscy5pbmZvcm1lZFN1cGVyaW50ZW5kYW50ID0gcGF5bG9hZC5JTkZPUk1FRF9TVVBFUklOVEVOREFOVCBhcyBhbnk7XHJcbiAgICByZXF1ZXN0LmFkZGl0aW9uYWxEZXRhaWxzLnNwZWNpYWxJbnN0cnVjdGlvbnMgPSBwYXlsb2FkLlNQRUNJQUxfSU5TVFJVQ1RJT05TO1xyXG4gICAgcmVxdWVzdC5hZGRpdGlvbmFsRGV0YWlscy5zdXBlcmludGVuZGFudE5hbWUgPSBwYXlsb2FkLlNVUEVSSU5URU5EQU5UX05BTUU7XHJcbiAgICByZXF1ZXN0LmFkZGl0aW9uYWxEZXRhaWxzLnN1cGVyaW50ZW5kYW50UGhvbmUgPSBwYXlsb2FkLlNVUEVSSU5URU5EQU5UX1BIT05FO1xyXG4gICAgLy8gY29uc29sZS5sb2cocmVxdWVzdCk7XHJcbiAgICAvLyBjb25zb2xlLmxvZyhcInBheWxvYWQ6IFwiLCBwYXlsb2FkKTtcclxuICAgIHJldHVybiByZXF1ZXN0O1xyXG4gIH1cclxufVxyXG5cclxuZnVuY3Rpb24gdXBkYXRlQXZhaWxhYmxlRGF0ZXMoZGF0ZXM6IEFycmF5PElBdmFpbGFibGVEYXRlcz4gfCB1bmRlZmluZWQsIHNlbGVjdGVkRGF0ZTogSUF2YWlsYWJsZURhdGVzKSB7XHJcbiAgLy8gU2V0IGFsbCBpc1ByZWZlcnJlZERhdGUgYW5kIGlzU2VsZWN0ZWQgdG8gZmFsc2VcclxuICBkYXRlcz8uZm9yRWFjaChkYXRlID0+IHtcclxuICAgIC8vIGRhdGUuaXNQcmVmZXJyZWREYXRlID0gZmFsc2U7XHJcbiAgICBkYXRlLnRpbWVTbG90cy5mb3JFYWNoKHRpbWUgPT4gdGltZS5pc1NlbGVjdGVkID0gZmFsc2UpO1xyXG4gIH0pO1xyXG4gIC8vIFNldCBQcmVmZXJlZCBUcnVlXHJcbiAgZGF0ZXM/LmZvckVhY2goZGF0ZSA9PiAoXHJcbiAgICAvLyBTZXQgaXNTZWxlY3RlZCB0byB0cnVlXHJcbiAgICBkYXRlLnRpbWVTbG90cy5mb3JFYWNoKHRpbWUgPT4gdGltZS5pc1NlbGVjdGVkID0gKGRhdGUuZGF0ZSA9PT0gc2VsZWN0ZWREYXRlLmRhdGUgJiYgc2VsZWN0ZWREYXRlLnRpbWVTbG90cy5tYXAoc2VsZWN0ZWRUaW1lID0+IHNlbGVjdGVkVGltZS5pbnRlcnZhbFR5cGUgPT09IHRpbWUuaW50ZXJ2YWxUeXBlKSkgPyB0cnVlIDogZmFsc2UpXHJcbiAgKSk7XHJcblxyXG4gIHJldHVybiBkYXRlcztcclxufVxyXG4iLCJleHBvcnQgKiBmcm9tIFwiLi9TdG9yZVwiO1xyXG5leHBvcnQgKiBmcm9tIFwiLi9BcHBcIjtcclxuZXhwb3J0ICogZnJvbSBcIi4vV2lkZ2V0XCI7XHJcbmV4cG9ydCAqIGZyb20gXCIuL0FwcG9pbnRtZW50XCI7XHJcbiIsImltcG9ydCB7IEluamVjdGFibGUgfSBmcm9tIFwiYnd0a1wiO1xyXG5pbXBvcnQgeyBFcGljLCBjb21iaW5lRXBpY3MgLCBvZlR5cGUgfSBmcm9tIFwicmVkdXgtb2JzZXJ2YWJsZVwiO1xyXG5pbXBvcnQgeyBFV2lkZ2V0U3RhdHVzLCBBY3Rpb25zLCBNb2RlbHMsIEFqYXhSZXNwb25zZSwgVmFsdWVPZiwgRVdpZGdldE5hbWUsIEVXaWRnZXRSb3V0ZSB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcbmltcG9ydCB7IENsaWVudCB9IGZyb20gXCIuLi8uLi9DbGllbnRcIjtcclxuaW1wb3J0IHtcclxuICBJQXBwb2ludG1lbnRBUElSZXNwb25zZSwgTWFwUmVxdWVzdERhdGEsIFJlcXVlc3RcclxufSBmcm9tIFwiLi4vLi4vbW9kZWxzXCI7XHJcbmltcG9ydCB7XHJcbiAgZ2V0QXBwb2ludG1lbnQsXHJcbiAgc2V0QXZhaWxhYmxlRGF0ZXMsXHJcbiAgLy8gc2V0UHJlZmVycmVkRGF0ZSxcclxuICBjb250YWN0SW5mb3JtYXRpb24sXHJcbiAgc2V0RHVyYXRpb24sXHJcbiAgc2V0SW5zdGFsbGF0aW9uQWRkcmVzcyxcclxuICBzZXRBZGRpdGlvbmFsRGV0YWlscyxcclxuICBzZXRJc0luc3RhbGxhdGlvblJlcXVpcmVkLFxyXG4gIHNldEFwcG9pbnRtZW50XHJcbn0gZnJvbSBcIi4uL0FjdGlvbnNcIjtcclxuaW1wb3J0IHsgQ29uZmlnIH0gZnJvbSBcIi4uLy4uL0NvbmZpZ1wiO1xyXG5pbXBvcnQgeyBmaWx0ZXIsIG1lcmdlTWFwLCBjYXRjaEVycm9yICwgb2YsIGNvbmNhdCB9IGZyb20gXCJyeGpzXCI7XHJcblxyXG5cclxuXHJcbi8vIEFjdGlvbnMgZGVzdHJ1Y3R1cmluZ1xyXG5jb25zdCB7XHJcbiAgZXJyb3JPY2N1cmVkLFxyXG4gIHNldFdpZGdldFN0YXR1cyxcclxuICBzZXRQcm9kdWN0Q29uZmlndXJhdGlvblRvdGFsLFxyXG4gIGJyb2FkY2FzdFVwZGF0ZSxcclxuICBoaXN0b3J5R28sXHJcbiAgY2xlYXJDYWNoZWRTdGF0ZSxcclxuICBvbW5pUGFnZUxvYWRlZFxyXG59ID0gQWN0aW9ucztcclxuXHJcbkBJbmplY3RhYmxlXHJcbmV4cG9ydCBjbGFzcyBBcHBvaW50bWVudEVwaWNzIHtcclxuICB3aWRnZXRTdGF0ZTogRVdpZGdldFN0YXR1cyA9IEVXaWRnZXRTdGF0dXMuSU5JVDtcclxuXHJcbiAgY29uc3RydWN0b3IocHJpdmF0ZSBjbGllbnQ6IENsaWVudCwgcHJpdmF0ZSBjb25maWc6IENvbmZpZykgeyB9XHJcblxyXG4gIGNvbWJpbmVFcGljcygpIHtcclxuICAgIHJldHVybiBjb21iaW5lRXBpY3MoXHJcbiAgICAgIHRoaXMuYXBwb2ludG1lbnRFcGljLFxyXG4gICAgICB0aGlzLnN1Ym1pdEFwcG9pbnRtZW50RXBpY1xyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgZ2V0IGFwcG9pbnRtZW50RXBpYygpOiBBcHBvaW50bWVudEVwaWMge1xyXG4gICAgcmV0dXJuIChhY3Rpb24kOiBhbnkpID0+XHJcbiAgICAgIGFjdGlvbiQucGlwZShcclxuICAgICAgICBvZlR5cGUoZ2V0QXBwb2ludG1lbnQudG9TdHJpbmcoKSksXHJcbiAgICAgICAgZmlsdGVyKCgpID0+IHRoaXMud2lkZ2V0U3RhdGUgIT09IEVXaWRnZXRTdGF0dXMuVVBEQVRJTkcpLFxyXG4gICAgICAgIG1lcmdlTWFwKCgpID0+IFxyXG4gICAgICAgICAgY29uY2F0KFxyXG4gICAgICAgICAgICBvZihzZXRXaWRnZXRTdGF0dXModGhpcy53aWRnZXRTdGF0ZSA9IEVXaWRnZXRTdGF0dXMuVVBEQVRJTkcpKSxcclxuICAgICAgICAgICAgdGhpcy5jbGllbnQuZ2V0PEFqYXhSZXNwb25zZTxJQXBwb2ludG1lbnRBUElSZXNwb25zZT4+KHRoaXMuY29uZmlnLmFwaS5hcHBvaW50bWVudEFQSSkucGlwZShcclxuICAgICAgICAgICAgICBtZXJnZU1hcCgoeyBkYXRhIH0pID0+IFtcclxuICAgICAgICAgICAgICAgIHNldEF2YWlsYWJsZURhdGVzKGRhdGEuYXBwb2ludG1lbnQuYXZhaWxhYmxlRGF0ZXMpLFxyXG4gICAgICAgICAgICAgICAgLy8gc2V0UHJlZmVycmVkRGF0ZShkYXRhLmFwcG9pbnRtZW50LnByZWZlcnJlZERhdGUpLFxyXG4gICAgICAgICAgICAgICAgc2V0RHVyYXRpb24oZGF0YS5hcHBvaW50bWVudC5kdXJhdGlvbiksXHJcbiAgICAgICAgICAgICAgICBzZXRJbnN0YWxsYXRpb25BZGRyZXNzKGRhdGEuYXBwb2ludG1lbnQuaW5zdGFsbGF0aW9uQWRkcmVzcyksXHJcbiAgICAgICAgICAgICAgICBjb250YWN0SW5mb3JtYXRpb24oZGF0YS5hcHBvaW50bWVudC5jb250YWN0SW5mb3JtYXRpb24pLFxyXG4gICAgICAgICAgICAgICAgc2V0QWRkaXRpb25hbERldGFpbHMoZGF0YS5hcHBvaW50bWVudC5hZGRpdGlvbmFsRGV0YWlscyksXHJcbiAgICAgICAgICAgICAgICBzZXRJc0luc3RhbGxhdGlvblJlcXVpcmVkKGRhdGEuYXBwb2ludG1lbnQuaXNJbnN0YWxsYXRpb25SZXF1aXJlZCksXHJcbiAgICAgICAgICAgICAgICBicm9hZGNhc3RVcGRhdGUoc2V0UHJvZHVjdENvbmZpZ3VyYXRpb25Ub3RhbChWYWx1ZU9mKGRhdGEsIFwicHJvZHVjdENvbmZpZ3VyYXRpb25Ub3RhbFwiKSkpLFxyXG4gICAgICAgICAgICAgICAgc2V0V2lkZ2V0U3RhdHVzKHRoaXMud2lkZ2V0U3RhdGUgPSBFV2lkZ2V0U3RhdHVzLlJFTkRFUkVEKSxcclxuICAgICAgICAgICAgICAgIG9tbmlQYWdlTG9hZGVkKClcclxuICAgICAgICAgICAgICBdKVxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICApXHJcbiAgICAgICAgKSxcclxuICAgICAgICBjYXRjaEVycm9yKChlcnJvcjogUmVzcG9uc2UpID0+IG9mKFxyXG4gICAgICAgICAgZXJyb3JPY2N1cmVkKG5ldyBNb2RlbHMuRXJyb3JIYW5kbGVyKFwiZ2V0QXBwb2ludG1lbnRcIiwgZXJyb3IpKVxyXG4gICAgICAgICkpXHJcbiAgICAgICk7XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGdldCBzdWJtaXRBcHBvaW50bWVudEVwaWMoKTogQXBwb2ludG1lbnRFcGljIHtcclxuICAgIHJldHVybiAoYWN0aW9uJDogYW55LCBzdG9yZTogYW55KSA9PlxyXG4gICAgICBhY3Rpb24kLnBpcGUoXHJcbiAgICAgICAgb2ZUeXBlKHNldEFwcG9pbnRtZW50LnRvU3RyaW5nKCkpLFxyXG4gICAgICAgIGZpbHRlcigoKSA9PiB0aGlzLndpZGdldFN0YXRlICE9PSBFV2lkZ2V0U3RhdHVzLlVQREFUSU5HKSxcclxuICAgICAgICBtZXJnZU1hcCgoeyBwYXlsb2FkIH06IGFueSkgPT4gXHJcbiAgICAgICAgICBjb25jYXQoXHJcbiAgICAgICAgICAgIG9mKHNldFdpZGdldFN0YXR1cyh0aGlzLndpZGdldFN0YXRlID0gRVdpZGdldFN0YXR1cy5VUERBVElORykpLFxyXG4gICAgICAgICAgICB0aGlzLmNsaWVudC5wdXQ8YW55Pih0aGlzLmNvbmZpZy5hcGkuYXBwb2ludG1lbnRBUEksIE1hcFJlcXVlc3REYXRhLmNyZWF0ZShwYXlsb2FkLCBSZXF1ZXN0LCBzdG9yZS5nZXRTdGF0ZSgpKSkucGlwZShcclxuICAgICAgICAgICAgICBtZXJnZU1hcCgoKSA9PiBbXHJcbiAgICAgICAgICAgICAgICBicm9hZGNhc3RVcGRhdGUoaGlzdG9yeUdvKEVXaWRnZXRSb3V0ZS5SRVZJRVcpKSxcclxuICAgICAgICAgICAgICAgIGNsZWFyQ2FjaGVkU3RhdGUoW0VXaWRnZXROYW1lLlJFVklFV10pXHJcbiAgICAgICAgICAgICAgXSlcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICAgKVxyXG4gICAgICAgICksXHJcbiAgICAgICAgY2F0Y2hFcnJvcigoZXJyb3I6IFJlc3BvbnNlKSA9PiBvZihcclxuICAgICAgICAgIGVycm9yT2NjdXJlZChuZXcgTW9kZWxzLkVycm9ySGFuZGxlcihcImdldEFwcG9pbnRtZW50XCIsIGVycm9yKSlcclxuICAgICAgICApKVxyXG4gICAgICApO1xyXG4gIH1cclxufVxyXG5cclxudHlwZSBBcHBvaW50bWVudEVwaWMgPSBFcGljPGFueSwgYW55LCB2b2lkLCBhbnk+O1xyXG4iLCJpbXBvcnQgeyBJbmplY3RhYmxlIH0gZnJvbSBcImJ3dGtcIjtcclxuaW1wb3J0IHsgQWN0aW9ucywgRUZsb3dUeXBlLCBFV2lkZ2V0U3RhdHVzLCBPbW5pdHVyZSwgVXRpbHMgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgeyBjb21iaW5lRXBpY3MsIEVwaWMsIG9mVHlwZSB9IGZyb20gXCJyZWR1eC1vYnNlcnZhYmxlXCI7XHJcbmltcG9ydCB7IG1lcmdlTWFwLCBjYXRjaEVycm9yICwgb2YgfSBmcm9tIFwicnhqc1wiO1xyXG5cclxuXHJcbmNvbnN0IHtcclxuICBvbW5pUGFnZUxvYWRlZFxyXG59ID0gQWN0aW9ucztcclxuXHJcbkBJbmplY3RhYmxlXHJcbmV4cG9ydCBjbGFzcyBPbW5pdHVyZUVwaWNzIHtcclxuICB3aWRnZXRTdGF0ZTogRVdpZGdldFN0YXR1cyA9IEVXaWRnZXRTdGF0dXMuSU5JVDtcclxuXHJcbiAgY29tYmluZUVwaWNzKCkge1xyXG4gICAgcmV0dXJuIGNvbWJpbmVFcGljcyhcclxuICAgICAgdGhpcy5wYWdlTG9hZGVkRXBpY1xyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgZ2V0IHBhZ2VMb2FkZWRFcGljKCk6IFVzZXJBY2NvdW50RXBpYyB7XHJcbiAgICByZXR1cm4gKGFjdGlvbiQ6IGFueSwgc3RvcmUpID0+XHJcbiAgICAgIGFjdGlvbiQucGlwZShcclxuICAgICAgICBvZlR5cGUob21uaVBhZ2VMb2FkZWQudG9TdHJpbmcoKSksXHJcbiAgICAgICAgbWVyZ2VNYXAoKCkgPT4ge1xyXG4gICAgICAgICAgY29uc3Qgb21uaXR1cmUgPSBPbW5pdHVyZS51c2VPbW5pdHVyZSgpO1xyXG4gICAgICAgICAgY29uc3QgY3VycmVudEZsb3dUeXBlID0gVXRpbHMuZ2V0Rmxvd1R5cGUoKTtcclxuICAgICAgICAgIGxldCBzX29TUzMsIHNfb1NTMjtcclxuICAgICAgICAgIHN3aXRjaCAoY3VycmVudEZsb3dUeXBlKSB7XHJcbiAgICAgICAgICAgIGNhc2UgRUZsb3dUeXBlLklOVEVSTkVUOlxyXG4gICAgICAgICAgICAgIHNfb1NTMiA9IFwiSW50ZXJuZXRcIjtcclxuICAgICAgICAgICAgICBzX29TUzMgPSBcIkNoYW5nZSBwYWNrYWdlXCI7XHJcbiAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgIGNhc2UgRUZsb3dUeXBlLlRWOlxyXG4gICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICBjYXNlIEVGbG93VHlwZS5BRERUVjpcclxuICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgY2FzZSBFRmxvd1R5cGUuQlVORExFOlxyXG4gICAgICAgICAgICAgIHNfb1NTMiA9IFwiQnVuZGxlXCI7XHJcbiAgICAgICAgICAgICAgc19vU1MzID0gXCJBZGQgVHZcIjtcclxuICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIG9tbml0dXJlLnRyYWNrUGFnZSh7XHJcbiAgICAgICAgICAgIGlkOiBcIkFwcG9pbnRtZW50UGFnZVwiLFxyXG4gICAgICAgICAgICBzX29TUzE6IFwiflwiLFxyXG4gICAgICAgICAgICBzX29TUzI6IHNfb1NTMiA/IHNfb1NTMiA6IFwiflwiLFxyXG4gICAgICAgICAgICBzX29TUzM6IHNfb1NTMyA/IHNfb1NTMyA6IFwiQ2hhbmdlIHBhY2thZ2VcIixcclxuICAgICAgICAgICAgc19vUEdOOiBcIkluc3RhbGxhdGlvblwiLFxyXG4gICAgICAgICAgICBzX29QTEU6IHtcclxuICAgICAgICAgICAgICB0eXBlOiBPbW5pdHVyZS5FTWVzc2FnZVR5cGUuV2FybmluZyxcclxuICAgICAgICAgICAgICBjb250ZW50OiB7XHJcbiAgICAgICAgICAgICAgICByZWY6IFwiSXN0YWxsYXRpb25NZXNzYWdlQmFubmVyXCJcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgcmV0dXJuIG9mKFtdKTtcclxuICAgICAgICB9KSxcclxuICAgICAgICBjYXRjaEVycm9yKChlcnJvcjogUmVzcG9uc2UpID0+IG9mKFtdKSlcclxuICAgICAgKTtcclxuICB9XHJcbn1cclxuXHJcbnR5cGUgVXNlckFjY291bnRFcGljID0gRXBpYzxhbnksIGFueSwgdm9pZCwgYW55PjtcclxuIiwiaW1wb3J0IHsgSW5qZWN0YWJsZSB9IGZyb20gXCJid3RrXCI7XHJcbmltcG9ydCB7IEVwaWMsIGNvbWJpbmVFcGljcywgb2ZUeXBlIH0gZnJvbSBcInJlZHV4LW9ic2VydmFibGVcIjtcclxuaW1wb3J0IHsgRVdpZGdldFN0YXR1cywgQWN0aW9ucyB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcbmltcG9ydCB7IGZpbHRlciwgbWVyZ2VNYXAgLCBvZiB9IGZyb20gXCJyeGpzXCI7XHJcblxyXG5pbXBvcnQgeyBnZXRBcHBvaW50bWVudCB9IGZyb20gXCIuL0FjdGlvbnNcIjtcclxuaW1wb3J0IHsgQXBwb2ludG1lbnRFcGljcyB9IGZyb20gXCIuL0VwaWNzL0FwcG9pbnRtZW50XCI7XHJcbmltcG9ydCB7IE9tbml0dXJlRXBpY3MgfSBmcm9tIFwiLi9FcGljcy9PbW5pdHVyZVwiO1xyXG5cclxuY29uc3Qge1xyXG4gIHNldFdpZGdldFN0YXR1cyxcclxuICBicm9hZGNhc3RVcGRhdGUsXHJcbiAgc2V0QXBwb2ludG1lbnRWaXNpdGVkXHJcbn0gPSBBY3Rpb25zO1xyXG5cclxuQEluamVjdGFibGVcclxuZXhwb3J0IGNsYXNzIEVwaWNzIHtcclxuICBjb25zdHJ1Y3RvcihcclxuICAgIHB1YmxpYyBvbW5pdHVyZUVwaWNzOiBPbW5pdHVyZUVwaWNzLFxyXG4gICAgcHVibGljIGFwcG9pbnRtZW50RXBpY3M6IEFwcG9pbnRtZW50RXBpY3NcclxuICApIHt9XHJcblxyXG4gIGNvbWJpbmVFcGljcygpIHtcclxuICAgIHJldHVybiBjb21iaW5lRXBpY3MoXHJcbiAgICAgIHRoaXMub25XaWRnZXRTdGF0dXNFcGljXHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBnZXQgb25XaWRnZXRTdGF0dXNFcGljKCk6IEdlbmVyYWxFcGljIHtcclxuICAgIHJldHVybiAoYWN0aW9uJDogYW55KSA9PlxyXG4gICAgICBhY3Rpb24kLnBpcGUoXHJcbiAgICAgICAgb2ZUeXBlKHNldFdpZGdldFN0YXR1cy50b1N0cmluZygpKSxcclxuICAgICAgICBmaWx0ZXIoKHsgcGF5bG9hZCB9OiBSZWR1eEFjdGlvbnMuQWN0aW9uPEVXaWRnZXRTdGF0dXM+KSA9PiBwYXlsb2FkID09PSBFV2lkZ2V0U3RhdHVzLklOSVQpLFxyXG4gICAgICAgIG1lcmdlTWFwKCgpID0+IG9mKFxyXG4gICAgICAgICAgYnJvYWRjYXN0VXBkYXRlKHNldEFwcG9pbnRtZW50VmlzaXRlZCgpKSxcclxuICAgICAgICAgIGdldEFwcG9pbnRtZW50KClcclxuICAgICAgICApKVxyXG4gICAgICApO1xyXG4gIH1cclxuXHJcbn1cclxuXHJcbnR5cGUgR2VuZXJhbEVwaWMgPSBFcGljPGFueSwgYW55LCB2b2lkLCBhbnk+O1xyXG4iLCJpbXBvcnQgeyBJbmplY3RhYmxlLCBDb21tb25GZWF0dXJlcywgU2VydmljZUxvY2F0b3IsIENvbW1vblNlcnZpY2VzIH0gZnJvbSBcImJ3dGtcIjtcclxuXHJcbmNvbnN0IHsgQmFzZUxvY2FsaXphdGlvbiB9ID0gQ29tbW9uRmVhdHVyZXM7XHJcblxyXG5jb25zdCBTT1VSQ0VfV0lER0VUX0lEID0gXCJvbWYtY2hhbmdlcGFja2FnZS1hcHBvaW50bWVudFwiO1xyXG5ASW5qZWN0YWJsZVxyXG5leHBvcnQgY2xhc3MgTG9jYWxpemF0aW9uIGV4dGVuZHMgQmFzZUxvY2FsaXphdGlvbiB7XHJcbiAgc3RhdGljIEluc3RhbmNlID0gbnVsbDtcclxuICBzdGF0aWMgZ2V0TG9jYWxpemVkU3RyaW5nKGlkOiBzdHJpbmcpOiBzdHJpbmcge1xyXG4gICAgTG9jYWxpemF0aW9uLkluc3RhbmNlID0gTG9jYWxpemF0aW9uLkluc3RhbmNlIHx8XHJcbiAgICAgIFNlcnZpY2VMb2NhdG9yXHJcbiAgICAgICAgLmluc3RhbmNlXHJcbiAgICAgICAgLmdldFNlcnZpY2UoQ29tbW9uU2VydmljZXMuTG9jYWxpemF0aW9uKTtcclxuICAgIGNvbnN0IGluc3RhbmNlOiBhbnkgPSBMb2NhbGl6YXRpb24uSW5zdGFuY2U7XHJcbiAgICByZXR1cm4gaW5zdGFuY2UgPyBpbnN0YW5jZS5nZXRMb2NhbGl6ZWRTdHJpbmcoU09VUkNFX1dJREdFVF9JRCwgaWQsIGluc3RhbmNlLmxvY2FsZSkgOiBpZDtcclxuICB9XHJcbn1cclxuIiwiaW1wb3J0IHsgY29tYmluZVJlZHVjZXJzIH0gZnJvbSBcInJlZHV4XCI7XHJcbmltcG9ydCB7IEFjdGlvbiwgaGFuZGxlQWN0aW9ucyB9IGZyb20gXCJyZWR1eC1hY3Rpb25zXCI7XHJcbmltcG9ydCB7IGNvbWJpbmVFcGljcyB9IGZyb20gXCJyZWR1eC1vYnNlcnZhYmxlXCI7XHJcbmltcG9ydCB7IFJlZHVjZXJzLCBMaWZlY3ljbGVFcGljcywgUmVzdHJpY2l0b25zRXBpY3MsIE1vZGFsRXBpY3MgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5cclxuaW1wb3J0IHsgU3RvcmUgYXMgQnd0a1N0b3JlLCBJbmplY3RhYmxlLCBDb21tb25GZWF0dXJlcyB9IGZyb20gXCJid3RrXCI7XHJcblxyXG5cclxuaW1wb3J0ICogYXMgYWN0aW9ucyBmcm9tIFwiLi9BY3Rpb25zXCI7XHJcblxyXG5pbXBvcnQgeyBJU3RvcmVTdGF0ZSwgSUF2YWlsYWJsZURhdGVzLCBJSW5zdGFsbGF0aW9uQWRkcmVzcywgSUNvbnRhY3RJbmZvcm1hdGlvbiwgSUFkZGl0aW9uYWxEZXRhaWxzIH0gZnJvbSBcIi4uL21vZGVsc1wiO1xyXG5pbXBvcnQgeyBFcGljcyB9IGZyb20gXCIuL0VwaWNzXCI7XHJcbmltcG9ydCB7IExvY2FsaXphdGlvbiB9IGZyb20gXCIuLi9Mb2NhbGl6YXRpb25cIjtcclxuaW1wb3J0IHsgRUR1cmF0aW9uIH0gZnJvbSBcIi4uL21vZGVscy9FbnVtc1wiO1xyXG5pbXBvcnQgeyBDbGllbnQgfSBmcm9tIFwiLi4vQ2xpZW50XCI7XHJcblxyXG5jb25zdCB7IEJhc2VTdG9yZSwgYWN0aW9uc1RvQ29tcHV0ZWRQcm9wZXJ0eU5hbWUgfSA9IENvbW1vbkZlYXR1cmVzO1xyXG5jb25zdCB7XHJcbiAgc2V0QXZhaWxhYmxlRGF0ZXMsXHJcbiAgLy8gc2V0UHJlZmVycmVkRGF0ZSxcclxuICBzZXREdXJhdGlvbixcclxuICBzZXRJbnN0YWxsYXRpb25BZGRyZXNzLFxyXG4gIGNvbnRhY3RJbmZvcm1hdGlvbixcclxuICBzZXRBZGRpdGlvbmFsRGV0YWlscyxcclxuICBzZXRJc0luc3RhbGxhdGlvblJlcXVpcmVkXHJcbn0gPSBhY3Rpb25zVG9Db21wdXRlZFByb3BlcnR5TmFtZShhY3Rpb25zKTtcclxuXHJcbkBJbmplY3RhYmxlXHJcbmV4cG9ydCBjbGFzcyBTdG9yZSBleHRlbmRzIEJhc2VTdG9yZTxJU3RvcmVTdGF0ZT4ge1xyXG4gIGNvbnN0cnVjdG9yKHByaXZhdGUgY2xpZW50OiBDbGllbnQsIHN0b3JlOiBCd3RrU3RvcmUsIHByaXZhdGUgZXBpY3M6IEVwaWNzLCBwcml2YXRlIGxvY2FsaXphdGlvbjogTG9jYWxpemF0aW9uKSB7XHJcbiAgICBzdXBlcihzdG9yZSk7XHJcbiAgfVxyXG5cclxuICBnZXQgcmVkdWNlcigpIHtcclxuICAgIHJldHVybiBjb21iaW5lUmVkdWNlcnM8YW55Pih7XHJcbiAgICAgIC8vID09PT09PT09PT09IFdpZGdldCBsaWZlY3ljbGUgbWV0aG9kcyA9PT09PT09PT09PT09XHJcbiAgICAgIC4uLlJlZHVjZXJzLldpZGdldEJhc2VMaWZlY3ljbGUodGhpcy5sb2NhbGl6YXRpb24pLFxyXG4gICAgICAuLi5SZWR1Y2Vycy5XaWRnZXRMaWdodGJveGVzKCksXHJcbiAgICAgIC4uLlJlZHVjZXJzLldpZGdldFJlc3RyaWN0aW9ucygpLFxyXG4gICAgICAvLyA9PT09PT09PT09PSBXaWRnZXQgZGF0YSA9PT09PT09PT09PT09PT1cclxuICAgICAgYXZhaWxhYmxlRGF0ZXM6IGhhbmRsZUFjdGlvbnM8SUF2YWlsYWJsZURhdGVzIHwgbnVsbD4oe1xyXG4gICAgICAgIFtzZXRBdmFpbGFibGVEYXRlc106IChzdGF0ZSwgeyBwYXlsb2FkIH06IEFjdGlvbjxJQXZhaWxhYmxlRGF0ZXM+KSA9PiBwYXlsb2FkIHx8IHN0YXRlLFxyXG4gICAgICB9LCBudWxsKSxcclxuICAgICAgLy8gcHJlZmVycmVkRGF0ZTogaGFuZGxlQWN0aW9uczxJQXZhaWxhYmxlRGF0ZXMgfCBudWxsPih7XHJcbiAgICAgIC8vICAgW3NldFByZWZlcnJlZERhdGVdOiAoc3RhdGUsIHsgcGF5bG9hZCB9OiBBY3Rpb248SUF2YWlsYWJsZURhdGVzPikgPT4gcGF5bG9hZCB8fCBzdGF0ZSxcclxuICAgICAgLy8gfSwgbnVsbCksXHJcbiAgICAgIGR1cmF0aW9uOiBoYW5kbGVBY3Rpb25zPEVEdXJhdGlvbiB8IG51bGw+KHtcclxuICAgICAgICBbc2V0RHVyYXRpb25dOiAoc3RhdGUsIHsgcGF5bG9hZCB9OiBBY3Rpb248RUR1cmF0aW9uPikgPT4gcGF5bG9hZCB8fCBzdGF0ZSxcclxuICAgICAgfSwgbnVsbCksXHJcbiAgICAgIGluc3RhbGxhdGlvbkFkZHJlc3M6IGhhbmRsZUFjdGlvbnM8SUluc3RhbGxhdGlvbkFkZHJlc3MgfCBudWxsPih7XHJcbiAgICAgICAgW3NldEluc3RhbGxhdGlvbkFkZHJlc3NdOiAoc3RhdGUsIHsgcGF5bG9hZCB9OiBBY3Rpb248SUluc3RhbGxhdGlvbkFkZHJlc3M+KSA9PiBwYXlsb2FkIHx8IHN0YXRlLFxyXG4gICAgICB9LCBudWxsKSxcclxuICAgICAgY29udGFjdEluZm9ybWF0aW9uOiBoYW5kbGVBY3Rpb25zPElDb250YWN0SW5mb3JtYXRpb24gfCBudWxsPih7XHJcbiAgICAgICAgW2NvbnRhY3RJbmZvcm1hdGlvbl06IChzdGF0ZSwgeyBwYXlsb2FkIH06IEFjdGlvbjxJQ29udGFjdEluZm9ybWF0aW9uPikgPT4gcGF5bG9hZCB8fCBzdGF0ZSxcclxuICAgICAgfSwgbnVsbCksXHJcbiAgICAgIGFkZGl0aW9uYWxEZXRhaWxzOiBoYW5kbGVBY3Rpb25zPElBZGRpdGlvbmFsRGV0YWlscyB8IG51bGw+KHtcclxuICAgICAgICBbc2V0QWRkaXRpb25hbERldGFpbHNdOiAoc3RhdGUsIHsgcGF5bG9hZCB9OiBBY3Rpb248SUFkZGl0aW9uYWxEZXRhaWxzPikgPT4gcGF5bG9hZCB8fCBzdGF0ZSxcclxuICAgICAgfSwgbnVsbCksXHJcbiAgICAgIGlzSW5zdGFsbGF0aW9uUmVxdWlyZWQ6IGhhbmRsZUFjdGlvbnM8Ym9vbGVhbj4oe1xyXG4gICAgICAgIFtzZXRJc0luc3RhbGxhdGlvblJlcXVpcmVkXTogKHN0YXRlLCB7IHBheWxvYWQgfTogQWN0aW9uPGJvb2xlYW4+KSA9PiBwYXlsb2FkIHx8IHN0YXRlLFxyXG4gICAgICB9LCBmYWxzZSksXHJcbiAgICB9KTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIE1pZGRsZXdhcmVzIGFyZSBjb2xsZWN0ZWQgYm90dG9tLXRvLXRvcFxyXG4gICAqIHNvLCB0aGUgYm90dG9tLW1vc3QgZXBpYyB3aWxsIHJlY2VpdmUgdGhlXHJcbiAgICogYWN0aW9uIGZpcnN0LCB3aGlsZSB0aGUgdG9wLW1vc3QgLS0gbGFzdFxyXG4gICAqIEByZWFkb25seVxyXG4gICAqIEBtZW1iZXJvZiBTdG9yZVxyXG4gICAqL1xyXG4gIGdldCBtaWRkbGV3YXJlcygpOiBhbnkge1xyXG4gICAgcmV0dXJuIGNvbWJpbmVFcGljcyh0aGlzLmVwaWNzLm9tbml0dXJlRXBpY3MuY29tYmluZUVwaWNzKCksIHRoaXMuZXBpY3MuYXBwb2ludG1lbnRFcGljcy5jb21iaW5lRXBpY3MoKSwgXHJcbiAgICAgIHRoaXMuZXBpY3MuY29tYmluZUVwaWNzKCksIG5ldyBNb2RhbEVwaWNzKCkuY29tYmluZUVwaWNzKCksIG5ldyBSZXN0cmljaXRvbnNFcGljcyh0aGlzLmNsaWVudCwgXCJBUFBPSU5UTUVOVF9SRVNUUklDVElPTl9NT0RBTFwiKS5jb21iaW5lRXBpY3MoKSxcclxuICAgICAgbmV3IExpZmVjeWNsZUVwaWNzKCkuY29tYmluZUVwaWNzKCkpO1xyXG4gIH1cclxufVxyXG4iLCJleHBvcnQgKiBmcm9tIFwiLi9TdG9yZVwiO1xyXG5leHBvcnQgKiBmcm9tIFwiLi9BY3Rpb25zXCI7XHJcbiIsImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIGNyZWF0ZUVsZW1lbnQsIHVzZVJlZiBhcyB1c2VSZWYkMSwgdXNlU3RhdGUgYXMgdXNlU3RhdGUkMSwgdXNlRWZmZWN0IGFzIHVzZUVmZmVjdCQxLCBpc1ZhbGlkRWxlbWVudCwgY2xvbmVFbGVtZW50LCBGcmFnbWVudCB9IGZyb20gJ3JlYWN0JztcblxuY29uc3QgVkFMSURBVElPTl9NT0RFID0ge1xyXG4gICAgb25CbHVyOiAnb25CbHVyJyxcclxuICAgIG9uQ2hhbmdlOiAnb25DaGFuZ2UnLFxyXG4gICAgb25TdWJtaXQ6ICdvblN1Ym1pdCcsXHJcbn07XHJcbmNvbnN0IFJBRElPX0lOUFVUID0gJ3JhZGlvJztcclxuY29uc3QgRklMRV9JTlBVVCA9ICdmaWxlJztcclxuY29uc3QgVkFMVUUgPSAndmFsdWUnO1xyXG5jb25zdCBVTkRFRklORUQgPSAndW5kZWZpbmVkJztcclxuY29uc3QgRVZFTlRTID0ge1xyXG4gICAgQkxVUjogJ2JsdXInLFxyXG4gICAgQ0hBTkdFOiAnY2hhbmdlJyxcclxuICAgIElOUFVUOiAnaW5wdXQnLFxyXG59O1xyXG5jb25zdCBJTlBVVF9WQUxJREFUSU9OX1JVTEVTID0ge1xyXG4gICAgbWF4OiAnbWF4JyxcclxuICAgIG1pbjogJ21pbicsXHJcbiAgICBtYXhMZW5ndGg6ICdtYXhMZW5ndGgnLFxyXG4gICAgbWluTGVuZ3RoOiAnbWluTGVuZ3RoJyxcclxuICAgIHBhdHRlcm46ICdwYXR0ZXJuJyxcclxuICAgIHJlcXVpcmVkOiAncmVxdWlyZWQnLFxyXG4gICAgdmFsaWRhdGU6ICd2YWxpZGF0ZScsXHJcbn07XHJcbmNvbnN0IFJFR0VYX0lTX0RFRVBfUFJPUCA9IC9cXC58XFxbKD86W15bXFxdXSp8KFtcIiddKSg/Oig/IVxcMSlbXlxcXFxdfFxcXFwuKSo/XFwxKVxcXS87XHJcbmNvbnN0IFJFR0VYX0lTX1BMQUlOX1BST1AgPSAvXlxcdyokLztcclxuY29uc3QgUkVHRVhfUFJPUF9OQU1FID0gL1teLltcXF1dK3xcXFsoPzooLT9cXGQrKD86XFwuXFxkKyk/KXwoW1wiJ10pKCg/Oig/IVxcMilbXlxcXFxdfFxcXFwuKSo/KVxcMilcXF18KD89KD86XFwufFxcW1xcXSkoPzpcXC58XFxbXFxdfCQpKS9nO1xyXG5jb25zdCBSRUdFWF9FU0NBUEVfQ0hBUiA9IC9cXFxcKFxcXFwpPy9nO1xuXG5mdW5jdGlvbiBhdHRhY2hFdmVudExpc3RlbmVycyh7IGZpZWxkLCBoYW5kbGVDaGFuZ2UsIGlzUmFkaW9PckNoZWNrYm94LCB9KSB7XHJcbiAgICBjb25zdCB7IHJlZiB9ID0gZmllbGQ7XHJcbiAgICBpZiAocmVmLmFkZEV2ZW50TGlzdGVuZXIpIHtcclxuICAgICAgICByZWYuYWRkRXZlbnRMaXN0ZW5lcihpc1JhZGlvT3JDaGVja2JveCA/IEVWRU5UUy5DSEFOR0UgOiBFVkVOVFMuSU5QVVQsIGhhbmRsZUNoYW5nZSk7XHJcbiAgICAgICAgcmVmLmFkZEV2ZW50TGlzdGVuZXIoRVZFTlRTLkJMVVIsIGhhbmRsZUNoYW5nZSk7XHJcbiAgICB9XHJcbn1cblxudmFyIGlzVW5kZWZpbmVkID0gKHZhbCkgPT4gdmFsID09PSB1bmRlZmluZWQ7XG5cbnZhciBpc051bGxPclVuZGVmaW5lZCA9ICh2YWx1ZSkgPT4gdmFsdWUgPT09IG51bGwgfHwgaXNVbmRlZmluZWQodmFsdWUpO1xuXG52YXIgaXNBcnJheSA9ICh2YWx1ZSkgPT4gQXJyYXkuaXNBcnJheSh2YWx1ZSk7XG5cbmNvbnN0IGlzT2JqZWN0VHlwZSA9ICh2YWx1ZSkgPT4gdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JztcclxudmFyIGlzT2JqZWN0ID0gKHZhbHVlKSA9PiAhaXNOdWxsT3JVbmRlZmluZWQodmFsdWUpICYmICFpc0FycmF5KHZhbHVlKSAmJiBpc09iamVjdFR5cGUodmFsdWUpO1xuXG5jb25zdCBpc0tleSA9ICh2YWx1ZSkgPT4gIWlzQXJyYXkodmFsdWUpICYmXHJcbiAgICAoUkVHRVhfSVNfUExBSU5fUFJPUC50ZXN0KHZhbHVlKSB8fCAhUkVHRVhfSVNfREVFUF9QUk9QLnRlc3QodmFsdWUpKTtcclxuY29uc3Qgc3RyaW5nVG9QYXRoID0gKHN0cmluZykgPT4ge1xyXG4gICAgY29uc3QgcmVzdWx0ID0gW107XHJcbiAgICBzdHJpbmcucmVwbGFjZShSRUdFWF9QUk9QX05BTUUsIChtYXRjaCwgbnVtYmVyLCBxdW90ZSwgc3RyaW5nKSA9PiB7XHJcbiAgICAgICAgcmVzdWx0LnB1c2gocXVvdGUgPyBzdHJpbmcucmVwbGFjZShSRUdFWF9FU0NBUEVfQ0hBUiwgJyQxJykgOiBudW1iZXIgfHwgbWF0Y2gpO1xyXG4gICAgfSk7XHJcbiAgICByZXR1cm4gcmVzdWx0O1xyXG59O1xyXG5mdW5jdGlvbiBzZXQob2JqZWN0LCBwYXRoLCB2YWx1ZSkge1xyXG4gICAgbGV0IGluZGV4ID0gLTE7XHJcbiAgICBjb25zdCB0ZW1wUGF0aCA9IGlzS2V5KHBhdGgpID8gW3BhdGhdIDogc3RyaW5nVG9QYXRoKHBhdGgpO1xyXG4gICAgY29uc3QgbGVuZ3RoID0gdGVtcFBhdGgubGVuZ3RoO1xyXG4gICAgY29uc3QgbGFzdEluZGV4ID0gbGVuZ3RoIC0gMTtcclxuICAgIHdoaWxlICgrK2luZGV4IDwgbGVuZ3RoKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gdGVtcFBhdGhbaW5kZXhdO1xyXG4gICAgICAgIGxldCBuZXdWYWx1ZSA9IHZhbHVlO1xyXG4gICAgICAgIGlmIChpbmRleCAhPT0gbGFzdEluZGV4KSB7XHJcbiAgICAgICAgICAgIGNvbnN0IG9ialZhbHVlID0gb2JqZWN0W2tleV07XHJcbiAgICAgICAgICAgIG5ld1ZhbHVlID1cclxuICAgICAgICAgICAgICAgIGlzT2JqZWN0KG9ialZhbHVlKSB8fCBpc0FycmF5KG9ialZhbHVlKVxyXG4gICAgICAgICAgICAgICAgICAgID8gb2JqVmFsdWVcclxuICAgICAgICAgICAgICAgICAgICA6ICFpc05hTih0ZW1wUGF0aFtpbmRleCArIDFdKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFtdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDoge307XHJcbiAgICAgICAgfVxyXG4gICAgICAgIG9iamVjdFtrZXldID0gbmV3VmFsdWU7XHJcbiAgICAgICAgb2JqZWN0ID0gb2JqZWN0W2tleV07XHJcbiAgICB9XHJcbiAgICByZXR1cm4gb2JqZWN0O1xyXG59XG5cbnZhciB0cmFuc2Zvcm1Ub05lc3RPYmplY3QgPSAoZGF0YSkgPT4gT2JqZWN0LmVudHJpZXMoZGF0YSkucmVkdWNlKChwcmV2aW91cywgW2tleSwgdmFsdWVdKSA9PiB7XHJcbiAgICBpZiAoUkVHRVhfSVNfREVFUF9QUk9QLnRlc3Qoa2V5KSkge1xyXG4gICAgICAgIHNldChwcmV2aW91cywga2V5LCB2YWx1ZSk7XHJcbiAgICAgICAgcmV0dXJuIHByZXZpb3VzO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgcHJldmlvdXMpLCB7IFtrZXldOiB2YWx1ZSB9KTtcclxufSwge30pO1xuXG52YXIgcmVtb3ZlQWxsRXZlbnRMaXN0ZW5lcnMgPSAocmVmLCB2YWxpZGF0ZVdpdGhTdGF0ZVVwZGF0ZSkgPT4ge1xyXG4gICAgaWYgKHJlZi5yZW1vdmVFdmVudExpc3RlbmVyKSB7XHJcbiAgICAgICAgcmVmLnJlbW92ZUV2ZW50TGlzdGVuZXIoRVZFTlRTLklOUFVULCB2YWxpZGF0ZVdpdGhTdGF0ZVVwZGF0ZSk7XHJcbiAgICAgICAgcmVmLnJlbW92ZUV2ZW50TGlzdGVuZXIoRVZFTlRTLkNIQU5HRSwgdmFsaWRhdGVXaXRoU3RhdGVVcGRhdGUpO1xyXG4gICAgICAgIHJlZi5yZW1vdmVFdmVudExpc3RlbmVyKEVWRU5UUy5CTFVSLCB2YWxpZGF0ZVdpdGhTdGF0ZVVwZGF0ZSk7XHJcbiAgICB9XHJcbn07XG5cbnZhciBpc1JhZGlvSW5wdXQgPSAodHlwZSkgPT4gdHlwZSA9PT0gUkFESU9fSU5QVVQ7XG5cbnZhciBpc0NoZWNrQm94SW5wdXQgPSAodHlwZSkgPT4gdHlwZSA9PT0gJ2NoZWNrYm94JztcblxuZnVuY3Rpb24gaXNEZXRhY2hlZChlbGVtZW50KSB7XHJcbiAgICBpZiAoIWVsZW1lbnQpIHtcclxuICAgICAgICByZXR1cm4gdHJ1ZTtcclxuICAgIH1cclxuICAgIGlmICghKGVsZW1lbnQgaW5zdGFuY2VvZiBIVE1MRWxlbWVudCkgfHxcclxuICAgICAgICBlbGVtZW50Lm5vZGVUeXBlID09PSBOb2RlLkRPQ1VNRU5UX05PREUpIHtcclxuICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gaXNEZXRhY2hlZChlbGVtZW50LnBhcmVudE5vZGUpO1xyXG59XG5cbmZ1bmN0aW9uIGZpbmRSZW1vdmVkRmllbGRBbmRSZW1vdmVMaXN0ZW5lcihmaWVsZHMsIGhhbmRsZUNoYW5nZSwgZmllbGQsIGZvcmNlRGVsZXRlKSB7XHJcbiAgICBpZiAoIWZpZWxkKSB7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG4gICAgY29uc3QgeyByZWYsIHJlZjogeyBuYW1lLCB0eXBlIH0sIG11dGF0aW9uV2F0Y2hlciwgfSA9IGZpZWxkO1xyXG4gICAgaWYgKCF0eXBlKSB7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG4gICAgY29uc3QgZmllbGRWYWx1ZSA9IGZpZWxkc1tuYW1lXTtcclxuICAgIGlmICgoaXNSYWRpb0lucHV0KHR5cGUpIHx8IGlzQ2hlY2tCb3hJbnB1dCh0eXBlKSkgJiYgZmllbGRWYWx1ZSkge1xyXG4gICAgICAgIGNvbnN0IHsgb3B0aW9ucyB9ID0gZmllbGRWYWx1ZTtcclxuICAgICAgICBpZiAoaXNBcnJheShvcHRpb25zKSAmJiBvcHRpb25zLmxlbmd0aCkge1xyXG4gICAgICAgICAgICBvcHRpb25zLmZvckVhY2goKHsgcmVmIH0sIGluZGV4KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBpZiAoKHJlZiAmJiBpc0RldGFjaGVkKHJlZikpIHx8IGZvcmNlRGVsZXRlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbXV0YXRpb25XYXRjaGVyID0gcmVmLm11dGF0aW9uV2F0Y2hlcjtcclxuICAgICAgICAgICAgICAgICAgICByZW1vdmVBbGxFdmVudExpc3RlbmVycyhyZWYsIGhhbmRsZUNoYW5nZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKG11dGF0aW9uV2F0Y2hlcikge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBtdXRhdGlvbldhdGNoZXIuZGlzY29ubmVjdCgpO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBvcHRpb25zLnNwbGljZShpbmRleCwgMSk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICBpZiAob3B0aW9ucyAmJiAhb3B0aW9ucy5sZW5ndGgpIHtcclxuICAgICAgICAgICAgICAgIGRlbGV0ZSBmaWVsZHNbbmFtZV07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgIGRlbGV0ZSBmaWVsZHNbbmFtZV07XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgZWxzZSBpZiAoaXNEZXRhY2hlZChyZWYpIHx8IGZvcmNlRGVsZXRlKSB7XHJcbiAgICAgICAgcmVtb3ZlQWxsRXZlbnRMaXN0ZW5lcnMocmVmLCBoYW5kbGVDaGFuZ2UpO1xyXG4gICAgICAgIGlmIChtdXRhdGlvbldhdGNoZXIpIHtcclxuICAgICAgICAgICAgbXV0YXRpb25XYXRjaGVyLmRpc2Nvbm5lY3QoKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgZGVsZXRlIGZpZWxkc1tuYW1lXTtcclxuICAgIH1cclxufVxuXG5jb25zdCBkZWZhdWx0UmV0dXJuID0ge1xyXG4gICAgaXNWYWxpZDogZmFsc2UsXHJcbiAgICB2YWx1ZTogJycsXHJcbn07XHJcbnZhciBnZXRSYWRpb1ZhbHVlID0gKG9wdGlvbnMpID0+IGlzQXJyYXkob3B0aW9ucylcclxuICAgID8gb3B0aW9ucy5yZWR1Y2UoKHByZXZpb3VzLCB7IHJlZjogeyBjaGVja2VkLCB2YWx1ZSB9IH0pID0+IGNoZWNrZWRcclxuICAgICAgICA/IHtcclxuICAgICAgICAgICAgaXNWYWxpZDogdHJ1ZSxcclxuICAgICAgICAgICAgdmFsdWUsXHJcbiAgICAgICAgfVxyXG4gICAgICAgIDogcHJldmlvdXMsIGRlZmF1bHRSZXR1cm4pXHJcbiAgICA6IGRlZmF1bHRSZXR1cm47XG5cbnZhciBnZXRNdWx0aXBsZVNlbGVjdFZhbHVlID0gKG9wdGlvbnMpID0+IFsuLi5vcHRpb25zXVxyXG4gICAgLmZpbHRlcigoeyBzZWxlY3RlZCB9KSA9PiBzZWxlY3RlZClcclxuICAgIC5tYXAoKHsgdmFsdWUgfSkgPT4gdmFsdWUpO1xuXG52YXIgaXNGaWxlSW5wdXQgPSAodHlwZSkgPT4gdHlwZSA9PT0gRklMRV9JTlBVVDtcblxudmFyIGlzTXVsdGlwbGVTZWxlY3QgPSAodHlwZSkgPT4gdHlwZSA9PT0gJ3NlbGVjdC1tdWx0aXBsZSc7XG5cbnZhciBpc0VtcHR5U3RyaW5nID0gKHZhbHVlKSA9PiB2YWx1ZSA9PT0gJyc7XG5cbmNvbnN0IGRlZmF1bHRSZXN1bHQgPSB7XHJcbiAgICB2YWx1ZTogZmFsc2UsXHJcbiAgICBpc1ZhbGlkOiBmYWxzZSxcclxufTtcclxuY29uc3QgdmFsaWRSZXN1bHQgPSB7IHZhbHVlOiB0cnVlLCBpc1ZhbGlkOiB0cnVlIH07XHJcbnZhciBnZXRDaGVja2JveFZhbHVlID0gKG9wdGlvbnMpID0+IHtcclxuICAgIGlmIChpc0FycmF5KG9wdGlvbnMpKSB7XHJcbiAgICAgICAgaWYgKG9wdGlvbnMubGVuZ3RoID4gMSkge1xyXG4gICAgICAgICAgICBjb25zdCB2YWx1ZXMgPSBvcHRpb25zXHJcbiAgICAgICAgICAgICAgICAuZmlsdGVyKCh7IHJlZjogeyBjaGVja2VkIH0gfSkgPT4gY2hlY2tlZClcclxuICAgICAgICAgICAgICAgIC5tYXAoKHsgcmVmOiB7IHZhbHVlIH0gfSkgPT4gdmFsdWUpO1xyXG4gICAgICAgICAgICByZXR1cm4geyB2YWx1ZTogdmFsdWVzLCBpc1ZhbGlkOiAhIXZhbHVlcy5sZW5ndGggfTtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgeyBjaGVja2VkLCB2YWx1ZSwgYXR0cmlidXRlcyB9ID0gb3B0aW9uc1swXS5yZWY7XHJcbiAgICAgICAgcmV0dXJuIGNoZWNrZWRcclxuICAgICAgICAgICAgPyBhdHRyaWJ1dGVzICYmICFpc1VuZGVmaW5lZChhdHRyaWJ1dGVzLnZhbHVlKVxyXG4gICAgICAgICAgICAgICAgPyBpc1VuZGVmaW5lZCh2YWx1ZSkgfHwgaXNFbXB0eVN0cmluZyh2YWx1ZSlcclxuICAgICAgICAgICAgICAgICAgICA/IHZhbGlkUmVzdWx0XHJcbiAgICAgICAgICAgICAgICAgICAgOiB7IHZhbHVlOiB2YWx1ZSwgaXNWYWxpZDogdHJ1ZSB9XHJcbiAgICAgICAgICAgICAgICA6IHZhbGlkUmVzdWx0XHJcbiAgICAgICAgICAgIDogZGVmYXVsdFJlc3VsdDtcclxuICAgIH1cclxuICAgIHJldHVybiBkZWZhdWx0UmVzdWx0O1xyXG59O1xuXG5mdW5jdGlvbiBnZXRGaWVsZFZhbHVlKGZpZWxkcywgcmVmKSB7XHJcbiAgICBjb25zdCB7IHR5cGUsIG5hbWUsIG9wdGlvbnMsIHZhbHVlLCBmaWxlcyB9ID0gcmVmO1xyXG4gICAgY29uc3QgZmllbGQgPSBmaWVsZHNbbmFtZV07XHJcbiAgICBpZiAoaXNGaWxlSW5wdXQodHlwZSkpIHtcclxuICAgICAgICByZXR1cm4gZmlsZXM7XHJcbiAgICB9XHJcbiAgICBpZiAoaXNSYWRpb0lucHV0KHR5cGUpKSB7XHJcbiAgICAgICAgcmV0dXJuIGZpZWxkID8gZ2V0UmFkaW9WYWx1ZShmaWVsZC5vcHRpb25zKS52YWx1ZSA6ICcnO1xyXG4gICAgfVxyXG4gICAgaWYgKGlzTXVsdGlwbGVTZWxlY3QodHlwZSkpIHtcclxuICAgICAgICByZXR1cm4gZ2V0TXVsdGlwbGVTZWxlY3RWYWx1ZShvcHRpb25zKTtcclxuICAgIH1cclxuICAgIGlmIChpc0NoZWNrQm94SW5wdXQodHlwZSkpIHtcclxuICAgICAgICByZXR1cm4gZmllbGQgPyBnZXRDaGVja2JveFZhbHVlKGZpZWxkLm9wdGlvbnMpLnZhbHVlIDogZmFsc2U7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gdmFsdWU7XHJcbn1cblxudmFyIGdldEZpZWxkc1ZhbHVlcyA9IChmaWVsZHMpID0+IE9iamVjdC52YWx1ZXMoZmllbGRzKS5yZWR1Y2UoKHByZXZpb3VzLCB7IHJlZiwgcmVmOiB7IG5hbWUgfSB9KSA9PiAoT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBwcmV2aW91cyksIHsgW25hbWVdOiBnZXRGaWVsZFZhbHVlKGZpZWxkcywgcmVmKSB9KSksIHt9KTtcblxudmFyIGlzRW1wdHlPYmplY3QgPSAodmFsdWUpID0+IGlzT2JqZWN0KHZhbHVlKSAmJiAhT2JqZWN0LmtleXModmFsdWUpLmxlbmd0aDtcblxudmFyIGlzU2FtZUVycm9yID0gKGVycm9yLCB0eXBlLCBtZXNzYWdlKSA9PiBpc09iamVjdChlcnJvcikgJiYgZXJyb3IudHlwZSA9PT0gdHlwZSAmJiBlcnJvci5tZXNzYWdlID09PSBtZXNzYWdlO1xuXG52YXIgZ2V0ID0gKG9iaiwgcGF0aCwgZGVmYXVsdFZhbHVlKSA9PiB7XHJcbiAgICBjb25zdCByZXN1bHQgPSBwYXRoXHJcbiAgICAgICAgLnNwbGl0KC9bLFtcXF0uXSs/LylcclxuICAgICAgICAuZmlsdGVyKEJvb2xlYW4pXHJcbiAgICAgICAgLnJlZHVjZSgocmVzdWx0LCBrZXkpID0+IChpc051bGxPclVuZGVmaW5lZChyZXN1bHQpID8gcmVzdWx0IDogcmVzdWx0W2tleV0pLCBvYmopO1xyXG4gICAgcmV0dXJuIGlzVW5kZWZpbmVkKHJlc3VsdCkgfHwgcmVzdWx0ID09PSBvYmpcclxuICAgICAgICA/IG9ialtwYXRoXSB8fCBkZWZhdWx0VmFsdWVcclxuICAgICAgICA6IHJlc3VsdDtcclxufTtcblxuZnVuY3Rpb24gc2hvdWxkVXBkYXRlV2l0aEVycm9yKHsgZXJyb3JzLCBuYW1lLCBlcnJvciwgdmFsaWRGaWVsZHMsIGZpZWxkc1dpdGhWYWxpZGF0aW9uLCB9KSB7XHJcbiAgICBjb25zdCBpc0ZpZWxkVmFsaWQgPSBpc0VtcHR5T2JqZWN0KGVycm9yKTtcclxuICAgIGNvbnN0IGlzRm9ybVZhbGlkID0gaXNFbXB0eU9iamVjdChlcnJvcnMpO1xyXG4gICAgY29uc3QgY3VycmVudEZpZWxkRXJyb3IgPSBnZXQoZXJyb3IsIG5hbWUpO1xyXG4gICAgY29uc3QgZXhpc3RGaWVsZEVycm9yID0gZ2V0KGVycm9ycywgbmFtZSk7XHJcbiAgICBpZiAoKGlzRmllbGRWYWxpZCAmJiB2YWxpZEZpZWxkcy5oYXMobmFtZSkpIHx8XHJcbiAgICAgICAgKGV4aXN0RmllbGRFcnJvciAmJiBleGlzdEZpZWxkRXJyb3IuaXNNYW51YWwpKSB7XHJcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG4gICAgaWYgKGlzRm9ybVZhbGlkICE9PSBpc0ZpZWxkVmFsaWQgfHxcclxuICAgICAgICAoIWlzRm9ybVZhbGlkICYmICFleGlzdEZpZWxkRXJyb3IpIHx8XHJcbiAgICAgICAgKGlzRmllbGRWYWxpZCAmJiBmaWVsZHNXaXRoVmFsaWRhdGlvbi5oYXMobmFtZSkgJiYgIXZhbGlkRmllbGRzLmhhcyhuYW1lKSkpIHtcclxuICAgICAgICByZXR1cm4gdHJ1ZTtcclxuICAgIH1cclxuICAgIHJldHVybiAoY3VycmVudEZpZWxkRXJyb3IgJiZcclxuICAgICAgICAhaXNTYW1lRXJyb3IoZXhpc3RGaWVsZEVycm9yLCBjdXJyZW50RmllbGRFcnJvci50eXBlLCBjdXJyZW50RmllbGRFcnJvci5tZXNzYWdlKSk7XHJcbn1cblxudmFyIGlzUmVnZXggPSAodmFsdWUpID0+IHZhbHVlIGluc3RhbmNlb2YgUmVnRXhwO1xuXG52YXIgZ2V0VmFsdWVBbmRNZXNzYWdlID0gKHZhbGlkYXRpb25EYXRhKSA9PiB7XHJcbiAgICBjb25zdCBpc1B1cmVPYmplY3QgPSBpc09iamVjdCh2YWxpZGF0aW9uRGF0YSkgJiYgIWlzUmVnZXgodmFsaWRhdGlvbkRhdGEpO1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgICB2YWx1ZTogaXNQdXJlT2JqZWN0XHJcbiAgICAgICAgICAgID8gdmFsaWRhdGlvbkRhdGEudmFsdWVcclxuICAgICAgICAgICAgOiB2YWxpZGF0aW9uRGF0YSxcclxuICAgICAgICBtZXNzYWdlOiBpc1B1cmVPYmplY3RcclxuICAgICAgICAgICAgPyB2YWxpZGF0aW9uRGF0YS5tZXNzYWdlXHJcbiAgICAgICAgICAgIDogJycsXHJcbiAgICB9O1xyXG59O1xuXG52YXIgaXNTdHJpbmcgPSAodmFsdWUpID0+IHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZyc7XG5cbnZhciBpc0Z1bmN0aW9uID0gKHZhbHVlKSA9PiB0eXBlb2YgdmFsdWUgPT09ICdmdW5jdGlvbic7XG5cbnZhciBpc0Jvb2xlYW4gPSAodmFsdWUpID0+IHR5cGVvZiB2YWx1ZSA9PT0gJ2Jvb2xlYW4nO1xuXG5mdW5jdGlvbiBnZXRWYWxpZGF0ZUVycm9yKHJlc3VsdCwgcmVmLCB0eXBlID0gJ3ZhbGlkYXRlJykge1xyXG4gICAgY29uc3QgaXNTdHJpbmdWYWx1ZSA9IGlzU3RyaW5nKHJlc3VsdCk7XHJcbiAgICBpZiAoaXNTdHJpbmdWYWx1ZSB8fCAoaXNCb29sZWFuKHJlc3VsdCkgJiYgIXJlc3VsdCkpIHtcclxuICAgICAgICBjb25zdCBtZXNzYWdlID0gaXNTdHJpbmdWYWx1ZSA/IHJlc3VsdCA6ICcnO1xyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIHR5cGUsXHJcbiAgICAgICAgICAgIG1lc3NhZ2UsXHJcbiAgICAgICAgICAgIHJlZixcclxuICAgICAgICB9O1xyXG4gICAgfVxyXG59XG5cbnZhciBhcHBlbmRFcnJvcnMgPSAobmFtZSwgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhLCBlcnJvcnMsIHR5cGUsIG1lc3NhZ2UpID0+IHtcclxuICAgIGlmICghdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XHJcbiAgICAgICAgcmV0dXJuIHt9O1xyXG4gICAgfVxyXG4gICAgY29uc3QgZXJyb3IgPSBlcnJvcnNbbmFtZV07XHJcbiAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBlcnJvciksIHsgdHlwZXM6IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgKGVycm9yICYmIGVycm9yLnR5cGVzID8gZXJyb3IudHlwZXMgOiB7fSkpLCB7IFt0eXBlXTogbWVzc2FnZSB8fCB0cnVlIH0pIH0pO1xyXG59O1xuXG52YXIgdmFsaWRhdGVGaWVsZCA9IGFzeW5jIChmaWVsZHNSZWYsIHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSwgeyByZWYsIHJlZjogeyB0eXBlLCB2YWx1ZSwgbmFtZSwgdmFsdWVBc051bWJlciwgdmFsdWVBc0RhdGUgfSwgb3B0aW9ucywgcmVxdWlyZWQsIG1heExlbmd0aCwgbWluTGVuZ3RoLCBtaW4sIG1heCwgcGF0dGVybiwgdmFsaWRhdGUsIH0pID0+IHtcclxuICAgIGNvbnN0IGZpZWxkcyA9IGZpZWxkc1JlZi5jdXJyZW50O1xyXG4gICAgY29uc3QgZXJyb3IgPSB7fTtcclxuICAgIGNvbnN0IGlzUmFkaW8gPSBpc1JhZGlvSW5wdXQodHlwZSk7XHJcbiAgICBjb25zdCBpc0NoZWNrQm94ID0gaXNDaGVja0JveElucHV0KHR5cGUpO1xyXG4gICAgY29uc3QgaXNSYWRpb09yQ2hlY2tib3ggPSBpc1JhZGlvIHx8IGlzQ2hlY2tCb3g7XHJcbiAgICBjb25zdCBpc0VtcHR5ID0gaXNFbXB0eVN0cmluZyh2YWx1ZSk7XHJcbiAgICBjb25zdCBhcHBlbmRFcnJvcnNDdXJyeSA9IGFwcGVuZEVycm9ycy5iaW5kKG51bGwsIG5hbWUsIHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSwgZXJyb3IpO1xyXG4gICAgY29uc3QgZ2V0TWluTWF4TWVzc2FnZSA9IChleGNlZWRNYXgsIG1heExlbmd0aE1lc3NhZ2UsIG1pbkxlbmd0aE1lc3NhZ2UsIG1heFR5cGUgPSBJTlBVVF9WQUxJREFUSU9OX1JVTEVTLm1heExlbmd0aCwgbWluVHlwZSA9IElOUFVUX1ZBTElEQVRJT05fUlVMRVMubWluTGVuZ3RoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgbWVzc2FnZSA9IGV4Y2VlZE1heCA/IG1heExlbmd0aE1lc3NhZ2UgOiBtaW5MZW5ndGhNZXNzYWdlO1xyXG4gICAgICAgIGVycm9yW25hbWVdID0gT2JqZWN0LmFzc2lnbih7IHR5cGU6IGV4Y2VlZE1heCA/IG1heFR5cGUgOiBtaW5UeXBlLCBtZXNzYWdlLFxyXG4gICAgICAgICAgICByZWYgfSwgKGV4Y2VlZE1heFxyXG4gICAgICAgICAgICA/IGFwcGVuZEVycm9yc0N1cnJ5KG1heFR5cGUsIG1lc3NhZ2UpXHJcbiAgICAgICAgICAgIDogYXBwZW5kRXJyb3JzQ3VycnkobWluVHlwZSwgbWVzc2FnZSkpKTtcclxuICAgICAgICBpZiAoIXZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xyXG4gICAgICAgICAgICByZXR1cm4gZXJyb3I7XHJcbiAgICAgICAgfVxyXG4gICAgfTtcclxuICAgIGlmIChyZXF1aXJlZCAmJlxyXG4gICAgICAgICgoIWlzUmFkaW8gJiYgIWlzQ2hlY2tCb3ggJiYgKGlzRW1wdHkgfHwgaXNOdWxsT3JVbmRlZmluZWQodmFsdWUpKSkgfHxcclxuICAgICAgICAgICAgKGlzQm9vbGVhbih2YWx1ZSkgJiYgIXZhbHVlKSB8fFxyXG4gICAgICAgICAgICAoaXNDaGVja0JveCAmJiAhZ2V0Q2hlY2tib3hWYWx1ZShvcHRpb25zKS5pc1ZhbGlkKSB8fFxyXG4gICAgICAgICAgICAoaXNSYWRpbyAmJiAhZ2V0UmFkaW9WYWx1ZShvcHRpb25zKS5pc1ZhbGlkKSkpIHtcclxuICAgICAgICBjb25zdCBtZXNzYWdlID0gaXNTdHJpbmcocmVxdWlyZWQpXHJcbiAgICAgICAgICAgID8gcmVxdWlyZWRcclxuICAgICAgICAgICAgOiBnZXRWYWx1ZUFuZE1lc3NhZ2UocmVxdWlyZWQpLm1lc3NhZ2U7XHJcbiAgICAgICAgZXJyb3JbbmFtZV0gPSBPYmplY3QuYXNzaWduKHsgdHlwZTogSU5QVVRfVkFMSURBVElPTl9SVUxFUy5yZXF1aXJlZCwgbWVzc2FnZSwgcmVmOiBpc1JhZGlvT3JDaGVja2JveCA/IGZpZWxkc1tuYW1lXS5vcHRpb25zWzBdLnJlZiA6IHJlZiB9LCBhcHBlbmRFcnJvcnNDdXJyeShJTlBVVF9WQUxJREFUSU9OX1JVTEVTLnJlcXVpcmVkLCBtZXNzYWdlKSk7XHJcbiAgICAgICAgaWYgKCF2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEpIHtcclxuICAgICAgICAgICAgcmV0dXJuIGVycm9yO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGlmICghaXNOdWxsT3JVbmRlZmluZWQobWluKSB8fCAhaXNOdWxsT3JVbmRlZmluZWQobWF4KSkge1xyXG4gICAgICAgIGxldCBleGNlZWRNYXg7XHJcbiAgICAgICAgbGV0IGV4Y2VlZE1pbjtcclxuICAgICAgICBjb25zdCB7IHZhbHVlOiBtYXhWYWx1ZSwgbWVzc2FnZTogbWF4TWVzc2FnZSB9ID0gZ2V0VmFsdWVBbmRNZXNzYWdlKG1heCk7XHJcbiAgICAgICAgY29uc3QgeyB2YWx1ZTogbWluVmFsdWUsIG1lc3NhZ2U6IG1pbk1lc3NhZ2UgfSA9IGdldFZhbHVlQW5kTWVzc2FnZShtaW4pO1xyXG4gICAgICAgIGlmICh0eXBlID09PSAnbnVtYmVyJyB8fCAoIXR5cGUgJiYgIWlzTmFOKHZhbHVlKSkpIHtcclxuICAgICAgICAgICAgY29uc3QgdmFsdWVOdW1iZXIgPSB2YWx1ZUFzTnVtYmVyIHx8IHBhcnNlRmxvYXQodmFsdWUpO1xyXG4gICAgICAgICAgICBpZiAoIWlzTnVsbE9yVW5kZWZpbmVkKG1heFZhbHVlKSkge1xyXG4gICAgICAgICAgICAgICAgZXhjZWVkTWF4ID0gdmFsdWVOdW1iZXIgPiBtYXhWYWx1ZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBpZiAoIWlzTnVsbE9yVW5kZWZpbmVkKG1pblZhbHVlKSkge1xyXG4gICAgICAgICAgICAgICAgZXhjZWVkTWluID0gdmFsdWVOdW1iZXIgPCBtaW5WYWx1ZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgY29uc3QgdmFsdWVEYXRlID0gdmFsdWVBc0RhdGUgfHwgbmV3IERhdGUodmFsdWUpO1xyXG4gICAgICAgICAgICBpZiAoaXNTdHJpbmcobWF4VmFsdWUpKSB7XHJcbiAgICAgICAgICAgICAgICBleGNlZWRNYXggPSB2YWx1ZURhdGUgPiBuZXcgRGF0ZShtYXhWYWx1ZSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgaWYgKGlzU3RyaW5nKG1pblZhbHVlKSkge1xyXG4gICAgICAgICAgICAgICAgZXhjZWVkTWluID0gdmFsdWVEYXRlIDwgbmV3IERhdGUobWluVmFsdWUpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChleGNlZWRNYXggfHwgZXhjZWVkTWluKSB7XHJcbiAgICAgICAgICAgIGdldE1pbk1heE1lc3NhZ2UoISFleGNlZWRNYXgsIG1heE1lc3NhZ2UsIG1pbk1lc3NhZ2UsIElOUFVUX1ZBTElEQVRJT05fUlVMRVMubWF4LCBJTlBVVF9WQUxJREFUSU9OX1JVTEVTLm1pbik7XHJcbiAgICAgICAgICAgIGlmICghdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gZXJyb3I7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBpZiAoaXNTdHJpbmcodmFsdWUpICYmICFpc0VtcHR5ICYmIChtYXhMZW5ndGggfHwgbWluTGVuZ3RoKSkge1xyXG4gICAgICAgIGNvbnN0IHsgdmFsdWU6IG1heExlbmd0aFZhbHVlLCBtZXNzYWdlOiBtYXhMZW5ndGhNZXNzYWdlLCB9ID0gZ2V0VmFsdWVBbmRNZXNzYWdlKG1heExlbmd0aCk7XHJcbiAgICAgICAgY29uc3QgeyB2YWx1ZTogbWluTGVuZ3RoVmFsdWUsIG1lc3NhZ2U6IG1pbkxlbmd0aE1lc3NhZ2UsIH0gPSBnZXRWYWx1ZUFuZE1lc3NhZ2UobWluTGVuZ3RoKTtcclxuICAgICAgICBjb25zdCBpbnB1dExlbmd0aCA9IHZhbHVlLnRvU3RyaW5nKCkubGVuZ3RoO1xyXG4gICAgICAgIGNvbnN0IGV4Y2VlZE1heCA9IG1heExlbmd0aCAmJiBpbnB1dExlbmd0aCA+IG1heExlbmd0aFZhbHVlO1xyXG4gICAgICAgIGNvbnN0IGV4Y2VlZE1pbiA9IG1pbkxlbmd0aCAmJiBpbnB1dExlbmd0aCA8IG1pbkxlbmd0aFZhbHVlO1xyXG4gICAgICAgIGlmIChleGNlZWRNYXggfHwgZXhjZWVkTWluKSB7XHJcbiAgICAgICAgICAgIGdldE1pbk1heE1lc3NhZ2UoISFleGNlZWRNYXgsIG1heExlbmd0aE1lc3NhZ2UsIG1pbkxlbmd0aE1lc3NhZ2UpO1xyXG4gICAgICAgICAgICBpZiAoIXZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGVycm9yO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgaWYgKHBhdHRlcm4gJiYgIWlzRW1wdHkpIHtcclxuICAgICAgICBjb25zdCB7IHZhbHVlOiBwYXR0ZXJuVmFsdWUsIG1lc3NhZ2U6IHBhdHRlcm5NZXNzYWdlIH0gPSBnZXRWYWx1ZUFuZE1lc3NhZ2UocGF0dGVybik7XHJcbiAgICAgICAgaWYgKGlzUmVnZXgocGF0dGVyblZhbHVlKSAmJiAhcGF0dGVyblZhbHVlLnRlc3QodmFsdWUpKSB7XHJcbiAgICAgICAgICAgIGVycm9yW25hbWVdID0gT2JqZWN0LmFzc2lnbih7IHR5cGU6IElOUFVUX1ZBTElEQVRJT05fUlVMRVMucGF0dGVybiwgbWVzc2FnZTogcGF0dGVybk1lc3NhZ2UsIHJlZiB9LCBhcHBlbmRFcnJvcnNDdXJyeShJTlBVVF9WQUxJREFUSU9OX1JVTEVTLnBhdHRlcm4sIHBhdHRlcm5NZXNzYWdlKSk7XHJcbiAgICAgICAgICAgIGlmICghdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gZXJyb3I7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBpZiAodmFsaWRhdGUpIHtcclxuICAgICAgICBjb25zdCBmaWVsZFZhbHVlID0gZ2V0RmllbGRWYWx1ZShmaWVsZHMsIHJlZik7XHJcbiAgICAgICAgY29uc3QgdmFsaWRhdGVSZWYgPSBpc1JhZGlvT3JDaGVja2JveCAmJiBvcHRpb25zID8gb3B0aW9uc1swXS5yZWYgOiByZWY7XHJcbiAgICAgICAgaWYgKGlzRnVuY3Rpb24odmFsaWRhdGUpKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHZhbGlkYXRlKGZpZWxkVmFsdWUpO1xyXG4gICAgICAgICAgICBjb25zdCB2YWxpZGF0ZUVycm9yID0gZ2V0VmFsaWRhdGVFcnJvcihyZXN1bHQsIHZhbGlkYXRlUmVmKTtcclxuICAgICAgICAgICAgaWYgKHZhbGlkYXRlRXJyb3IpIHtcclxuICAgICAgICAgICAgICAgIGVycm9yW25hbWVdID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCB2YWxpZGF0ZUVycm9yKSwgYXBwZW5kRXJyb3JzQ3VycnkoSU5QVVRfVkFMSURBVElPTl9SVUxFUy52YWxpZGF0ZSwgdmFsaWRhdGVFcnJvci5tZXNzYWdlKSk7XHJcbiAgICAgICAgICAgICAgICBpZiAoIXZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBlcnJvcjtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBlbHNlIGlmIChpc09iamVjdCh2YWxpZGF0ZSkpIHtcclxuICAgICAgICAgICAgY29uc3QgdmFsaWRhdGVGdW5jdGlvbnMgPSBPYmplY3QuZW50cmllcyh2YWxpZGF0ZSk7XHJcbiAgICAgICAgICAgIGNvbnN0IHZhbGlkYXRpb25SZXN1bHQgPSBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgdmFsaWRhdGVGdW5jdGlvbnMucmVkdWNlKGFzeW5jIChwcmV2aW91cywgW2tleSwgdmFsaWRhdGVdLCBpbmRleCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGlmICgoIWlzRW1wdHlPYmplY3QoYXdhaXQgcHJldmlvdXMpICYmICF2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEpIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICFpc0Z1bmN0aW9uKHZhbGlkYXRlKSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVzb2x2ZShwcmV2aW91cyk7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIGxldCByZXN1bHQ7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsaWRhdGVSZXN1bHQgPSBhd2FpdCB2YWxpZGF0ZShmaWVsZFZhbHVlKTtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWxpZGF0ZUVycm9yID0gZ2V0VmFsaWRhdGVFcnJvcih2YWxpZGF0ZVJlc3VsdCwgdmFsaWRhdGVSZWYsIGtleSk7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKHZhbGlkYXRlRXJyb3IpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzdWx0ID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCB2YWxpZGF0ZUVycm9yKSwgYXBwZW5kRXJyb3JzQ3Vycnkoa2V5LCB2YWxpZGF0ZUVycm9yLm1lc3NhZ2UpKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JbbmFtZV0gPSByZXN1bHQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc3VsdCA9IHByZXZpb3VzO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmFsaWRhdGVGdW5jdGlvbnMubGVuZ3RoIC0gMSA9PT0gaW5kZXhcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyByZXNvbHZlKHJlc3VsdClcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiByZXN1bHQ7XHJcbiAgICAgICAgICAgICAgICB9LCB7fSk7XHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICBpZiAoIWlzRW1wdHlPYmplY3QodmFsaWRhdGlvblJlc3VsdCkpIHtcclxuICAgICAgICAgICAgICAgIGVycm9yW25hbWVdID0gT2JqZWN0LmFzc2lnbih7IHJlZjogdmFsaWRhdGVSZWYgfSwgdmFsaWRhdGlvblJlc3VsdCk7XHJcbiAgICAgICAgICAgICAgICBpZiAoIXZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBlcnJvcjtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHJldHVybiBlcnJvcjtcclxufTtcblxuY29uc3QgcGFyc2VFcnJvclNjaGVtYSA9IChlcnJvciwgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSA9PiBpc0FycmF5KGVycm9yLmlubmVyKVxyXG4gICAgPyBlcnJvci5pbm5lci5yZWR1Y2UoKHByZXZpb3VzLCB7IHBhdGgsIG1lc3NhZ2UsIHR5cGUgfSkgPT4gKE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgcHJldmlvdXMpLCAocHJldmlvdXNbcGF0aF0gJiYgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhXHJcbiAgICAgICAgPyB7XHJcbiAgICAgICAgICAgIFtwYXRoXTogYXBwZW5kRXJyb3JzKHBhdGgsIHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSwgcHJldmlvdXMsIHR5cGUsIG1lc3NhZ2UpLFxyXG4gICAgICAgIH1cclxuICAgICAgICA6IHtcclxuICAgICAgICAgICAgW3BhdGhdOiBwcmV2aW91c1twYXRoXSB8fCBPYmplY3QuYXNzaWduKHsgbWVzc2FnZSxcclxuICAgICAgICAgICAgICAgIHR5cGUgfSwgKHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYVxyXG4gICAgICAgICAgICAgICAgPyB7XHJcbiAgICAgICAgICAgICAgICAgICAgdHlwZXM6IHsgW3R5cGVdOiBtZXNzYWdlIHx8IHRydWUgfSxcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIDoge30pKSxcclxuICAgICAgICB9KSkpLCB7fSlcclxuICAgIDoge1xyXG4gICAgICAgIFtlcnJvci5wYXRoXTogeyBtZXNzYWdlOiBlcnJvci5tZXNzYWdlLCB0eXBlOiBlcnJvci50eXBlIH0sXHJcbiAgICB9O1xyXG5hc3luYyBmdW5jdGlvbiB2YWxpZGF0ZVdpdGhTY2hlbWEodmFsaWRhdGlvblNjaGVtYSwgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhLCBkYXRhKSB7XHJcbiAgICB0cnkge1xyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIHZhbHVlczogYXdhaXQgdmFsaWRhdGlvblNjaGVtYS52YWxpZGF0ZShkYXRhLCB7IGFib3J0RWFybHk6IGZhbHNlIH0pLFxyXG4gICAgICAgICAgICBlcnJvcnM6IHt9LFxyXG4gICAgICAgIH07XHJcbiAgICB9XHJcbiAgICBjYXRjaCAoZSkge1xyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIHZhbHVlczoge30sXHJcbiAgICAgICAgICAgIGVycm9yczogdHJhbnNmb3JtVG9OZXN0T2JqZWN0KHBhcnNlRXJyb3JTY2hlbWEoZSwgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSksXHJcbiAgICAgICAgfTtcclxuICAgIH1cclxufVxuXG52YXIgZ2V0RGVmYXVsdFZhbHVlID0gKGRlZmF1bHRWYWx1ZXMsIG5hbWUsIGRlZmF1bHRWYWx1ZSkgPT4gaXNVbmRlZmluZWQoZGVmYXVsdFZhbHVlc1tuYW1lXSlcclxuICAgID8gZ2V0KGRlZmF1bHRWYWx1ZXMsIG5hbWUsIGRlZmF1bHRWYWx1ZSlcclxuICAgIDogZGVmYXVsdFZhbHVlc1tuYW1lXTtcblxuZnVuY3Rpb24gZmxhdEFycmF5KGxpc3QpIHtcclxuICAgIHJldHVybiBsaXN0LnJlZHVjZSgoYSwgYikgPT4gYS5jb25jYXQoaXNBcnJheShiKSA/IGZsYXRBcnJheShiKSA6IGIpLCBbXSk7XHJcbn1cblxudmFyIGlzUHJpbWl0aXZlID0gKHZhbHVlKSA9PiBpc051bGxPclVuZGVmaW5lZCh2YWx1ZSkgfHwgIWlzT2JqZWN0VHlwZSh2YWx1ZSk7XG5cbmNvbnN0IGdldFBhdGggPSAocGF0aCwgdmFsdWVzKSA9PiB7XHJcbiAgICBjb25zdCBnZXRJbm5lclBhdGggPSAodmFsdWUsIGtleSwgaXNPYmplY3QpID0+IHtcclxuICAgICAgICBjb25zdCBwYXRoV2l0aEluZGV4ID0gaXNPYmplY3QgPyBgJHtwYXRofS4ke2tleX1gIDogYCR7cGF0aH1bJHtrZXl9XWA7XHJcbiAgICAgICAgcmV0dXJuIGlzUHJpbWl0aXZlKHZhbHVlKSA/IHBhdGhXaXRoSW5kZXggOiBnZXRQYXRoKHBhdGhXaXRoSW5kZXgsIHZhbHVlKTtcclxuICAgIH07XHJcbiAgICByZXR1cm4gaXNBcnJheSh2YWx1ZXMpXHJcbiAgICAgICAgPyB2YWx1ZXMubWFwKCh2YWx1ZSwga2V5KSA9PiBnZXRJbm5lclBhdGgodmFsdWUsIGtleSkpXHJcbiAgICAgICAgOiBPYmplY3QuZW50cmllcyh2YWx1ZXMpLm1hcCgoW2tleSwgdmFsdWVdKSA9PiBnZXRJbm5lclBhdGgodmFsdWUsIGtleSwgdHJ1ZSkpO1xyXG59O1xyXG52YXIgZ2V0UGF0aCQxID0gKHBhcmVudFBhdGgsIHZhbHVlKSA9PiBmbGF0QXJyYXkoZ2V0UGF0aChwYXJlbnRQYXRoLCB2YWx1ZSkpO1xuXG52YXIgYXNzaWduV2F0Y2hGaWVsZHMgPSAoZmllbGRWYWx1ZXMsIGZpZWxkTmFtZSwgd2F0Y2hGaWVsZHMsIGNvbWJpbmVkRGVmYXVsdFZhbHVlcykgPT4ge1xyXG4gICAgbGV0IHZhbHVlO1xyXG4gICAgaWYgKGlzRW1wdHlPYmplY3QoZmllbGRWYWx1ZXMpKSB7XHJcbiAgICAgICAgdmFsdWUgPSB1bmRlZmluZWQ7XHJcbiAgICB9XHJcbiAgICBlbHNlIGlmICghaXNVbmRlZmluZWQoZmllbGRWYWx1ZXNbZmllbGROYW1lXSkpIHtcclxuICAgICAgICB3YXRjaEZpZWxkcy5hZGQoZmllbGROYW1lKTtcclxuICAgICAgICB2YWx1ZSA9IGZpZWxkVmFsdWVzW2ZpZWxkTmFtZV07XHJcbiAgICB9XHJcbiAgICBlbHNlIHtcclxuICAgICAgICB2YWx1ZSA9IGdldCh0cmFuc2Zvcm1Ub05lc3RPYmplY3QoZmllbGRWYWx1ZXMpLCBmaWVsZE5hbWUpO1xyXG4gICAgICAgIGlmICghaXNVbmRlZmluZWQodmFsdWUpKSB7XHJcbiAgICAgICAgICAgIGdldFBhdGgkMShmaWVsZE5hbWUsIHZhbHVlKS5mb3JFYWNoKG5hbWUgPT4gd2F0Y2hGaWVsZHMuYWRkKG5hbWUpKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICByZXR1cm4gaXNVbmRlZmluZWQodmFsdWUpXHJcbiAgICAgICAgPyBpc09iamVjdChjb21iaW5lZERlZmF1bHRWYWx1ZXMpXHJcbiAgICAgICAgICAgID8gZ2V0RGVmYXVsdFZhbHVlKGNvbWJpbmVkRGVmYXVsdFZhbHVlcywgZmllbGROYW1lKVxyXG4gICAgICAgICAgICA6IGNvbWJpbmVkRGVmYXVsdFZhbHVlc1xyXG4gICAgICAgIDogdmFsdWU7XHJcbn07XG5cbnZhciBza2lwVmFsaWRhdGlvbiA9ICh7IGhhc0Vycm9yLCBpc0JsdXJFdmVudCwgaXNPblN1Ym1pdCwgaXNSZVZhbGlkYXRlT25TdWJtaXQsIGlzT25CbHVyLCBpc1JlVmFsaWRhdGVPbkJsdXIsIGlzU3VibWl0dGVkLCB9KSA9PiAoaXNPblN1Ym1pdCAmJiBpc1JlVmFsaWRhdGVPblN1Ym1pdCkgfHxcclxuICAgIChpc09uU3VibWl0ICYmICFpc1N1Ym1pdHRlZCkgfHxcclxuICAgIChpc09uQmx1ciAmJiAhaXNCbHVyRXZlbnQgJiYgIWhhc0Vycm9yKSB8fFxyXG4gICAgKGlzUmVWYWxpZGF0ZU9uQmx1ciAmJiAhaXNCbHVyRXZlbnQgJiYgaGFzRXJyb3IpIHx8XHJcbiAgICAoaXNSZVZhbGlkYXRlT25TdWJtaXQgJiYgaXNTdWJtaXR0ZWQpO1xuXG5mdW5jdGlvbiBnZXRJc0ZpZWxkc0RpZmZlcmVudChyZWZlcmVuY2VBcnJheSwgZGlmZmVyZW5jZUFycmF5KSB7XHJcbiAgICBsZXQgaXNNYXRjaCA9IGZhbHNlO1xyXG4gICAgaWYgKCFpc0FycmF5KHJlZmVyZW5jZUFycmF5KSB8fFxyXG4gICAgICAgICFpc0FycmF5KGRpZmZlcmVuY2VBcnJheSkgfHxcclxuICAgICAgICByZWZlcmVuY2VBcnJheS5sZW5ndGggIT09IGRpZmZlcmVuY2VBcnJheS5sZW5ndGgpIHtcclxuICAgICAgICByZXR1cm4gdHJ1ZTtcclxuICAgIH1cclxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcmVmZXJlbmNlQXJyYXkubGVuZ3RoOyBpKyspIHtcclxuICAgICAgICBpZiAoaXNNYXRjaCkge1xyXG4gICAgICAgICAgICBicmVhaztcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgZGF0YUEgPSByZWZlcmVuY2VBcnJheVtpXTtcclxuICAgICAgICBjb25zdCBkYXRhQiA9IGRpZmZlcmVuY2VBcnJheVtpXTtcclxuICAgICAgICBpZiAoIWRhdGFCIHx8IE9iamVjdC5rZXlzKGRhdGFBKS5sZW5ndGggIT09IE9iamVjdC5rZXlzKGRhdGFCKS5sZW5ndGgpIHtcclxuICAgICAgICAgICAgaXNNYXRjaCA9IHRydWU7XHJcbiAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIH1cclxuICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiBkYXRhQSkge1xyXG4gICAgICAgICAgICBpZiAoIWRhdGFCW2tleV0gfHwgZGF0YUFba2V5XSAhPT0gZGF0YUJba2V5XSkge1xyXG4gICAgICAgICAgICAgICAgaXNNYXRjaCA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHJldHVybiBpc01hdGNoO1xyXG59XG5cbmNvbnN0IGlzTWF0Y2hGaWVsZEFycmF5TmFtZSA9IChuYW1lLCBzZWFyY2hOYW1lKSA9PiBuYW1lLnN0YXJ0c1dpdGgoYCR7c2VhcmNoTmFtZX1bYCk7XHJcbnZhciBpc05hbWVJbkZpZWxkQXJyYXkgPSAobmFtZXMsIG5hbWUpID0+IFsuLi5uYW1lc10ucmVkdWNlKChwcmV2LCBjdXJyZW50KSA9PiAoaXNNYXRjaEZpZWxkQXJyYXlOYW1lKG5hbWUsIGN1cnJlbnQpID8gdHJ1ZSA6IHByZXYpLCBmYWxzZSk7XG5cbmZ1bmN0aW9uIG9uRG9tUmVtb3ZlKGVsZW1lbnQsIG9uRGV0YWNoQ2FsbGJhY2spIHtcclxuICAgIGNvbnN0IG9ic2VydmVyID0gbmV3IE11dGF0aW9uT2JzZXJ2ZXIoKCkgPT4ge1xyXG4gICAgICAgIGlmIChpc0RldGFjaGVkKGVsZW1lbnQpKSB7XHJcbiAgICAgICAgICAgIG9ic2VydmVyLmRpc2Nvbm5lY3QoKTtcclxuICAgICAgICAgICAgb25EZXRhY2hDYWxsYmFjaygpO1xyXG4gICAgICAgIH1cclxuICAgIH0pO1xyXG4gICAgb2JzZXJ2ZXIub2JzZXJ2ZSh3aW5kb3cuZG9jdW1lbnQsIHtcclxuICAgICAgICBjaGlsZExpc3Q6IHRydWUsXHJcbiAgICAgICAgc3VidHJlZTogdHJ1ZSxcclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIG9ic2VydmVyO1xyXG59XG5cbmNvbnN0IHVuc2V0T2JqZWN0ID0gKHRhcmdldCkgPT4ge1xyXG4gICAgZm9yIChjb25zdCBrZXkgaW4gdGFyZ2V0KSB7XHJcbiAgICAgICAgY29uc3QgZGF0YSA9IHRhcmdldFtrZXldO1xyXG4gICAgICAgIGNvbnN0IGlzQXJyYXlPYmplY3QgPSBpc0FycmF5KGRhdGEpO1xyXG4gICAgICAgIGlmICgoaXNPYmplY3QoZGF0YSkgfHwgaXNBcnJheU9iamVjdCkgJiYgIWRhdGEucmVmKSB7XHJcbiAgICAgICAgICAgIHVuc2V0T2JqZWN0KGRhdGEpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoaXNVbmRlZmluZWQoZGF0YSkgfHxcclxuICAgICAgICAgICAgaXNFbXB0eU9iamVjdChkYXRhKSB8fFxyXG4gICAgICAgICAgICAoaXNBcnJheU9iamVjdCAmJiAhdGFyZ2V0W2tleV0uZmlsdGVyKEJvb2xlYW4pLmxlbmd0aCkpIHtcclxuICAgICAgICAgICAgZGVsZXRlIHRhcmdldFtrZXldO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHJldHVybiB0YXJnZXQ7XHJcbn07XHJcbmNvbnN0IHVuc2V0ID0gKHRhcmdldCwgcGF0aHMpID0+IHtcclxuICAgIHBhdGhzLmZvckVhY2gocGF0aCA9PiB7XHJcbiAgICAgICAgc2V0KHRhcmdldCwgcGF0aCwgdW5kZWZpbmVkKTtcclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIHVuc2V0T2JqZWN0KHRhcmdldCk7XHJcbn07XG5cbnZhciBtb2RlQ2hlY2tlciA9IChtb2RlKSA9PiAoe1xyXG4gICAgaXNPblN1Ym1pdDogIW1vZGUgfHwgbW9kZSA9PT0gVkFMSURBVElPTl9NT0RFLm9uU3VibWl0LFxyXG4gICAgaXNPbkJsdXI6IG1vZGUgPT09IFZBTElEQVRJT05fTU9ERS5vbkJsdXIsXHJcbiAgICBpc09uQ2hhbmdlOiBtb2RlID09PSBWQUxJREFUSU9OX01PREUub25DaGFuZ2UsXHJcbn0pO1xuXG5jb25zdCB7IHVzZVJlZiwgdXNlU3RhdGUsIHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QgfSA9IFJlYWN0O1xyXG5mdW5jdGlvbiB1c2VGb3JtKHsgbW9kZSA9IFZBTElEQVRJT05fTU9ERS5vblN1Ym1pdCwgcmVWYWxpZGF0ZU1vZGUgPSBWQUxJREFUSU9OX01PREUub25DaGFuZ2UsIHZhbGlkYXRpb25TY2hlbWEsIGRlZmF1bHRWYWx1ZXMgPSB7fSwgc3VibWl0Rm9jdXNFcnJvciA9IHRydWUsIHZhbGlkYXRlQ3JpdGVyaWFNb2RlLCB9ID0ge30pIHtcclxuICAgIGNvbnN0IGZpZWxkc1JlZiA9IHVzZVJlZih7fSk7XHJcbiAgICBjb25zdCB2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEgPSB2YWxpZGF0ZUNyaXRlcmlhTW9kZSA9PT0gJ2FsbCc7XHJcbiAgICBjb25zdCBlcnJvcnNSZWYgPSB1c2VSZWYoe30pO1xyXG4gICAgY29uc3QgdG91Y2hlZEZpZWxkc1JlZiA9IHVzZVJlZih7fSk7XHJcbiAgICBjb25zdCB3YXRjaEZpZWxkc1JlZiA9IHVzZVJlZihuZXcgU2V0KCkpO1xyXG4gICAgY29uc3QgZGlydHlGaWVsZHNSZWYgPSB1c2VSZWYobmV3IFNldCgpKTtcclxuICAgIGNvbnN0IGZpZWxkc1dpdGhWYWxpZGF0aW9uUmVmID0gdXNlUmVmKG5ldyBTZXQoKSk7XHJcbiAgICBjb25zdCB2YWxpZEZpZWxkc1JlZiA9IHVzZVJlZihuZXcgU2V0KCkpO1xyXG4gICAgY29uc3QgaXNWYWxpZFJlZiA9IHVzZVJlZih0cnVlKTtcclxuICAgIGNvbnN0IGRlZmF1bHRSZW5kZXJWYWx1ZXNSZWYgPSB1c2VSZWYoe30pO1xyXG4gICAgY29uc3QgZGVmYXVsdFZhbHVlc1JlZiA9IHVzZVJlZihkZWZhdWx0VmFsdWVzKTtcclxuICAgIGNvbnN0IGlzVW5Nb3VudCA9IHVzZVJlZihmYWxzZSk7XHJcbiAgICBjb25zdCBpc1dhdGNoQWxsUmVmID0gdXNlUmVmKGZhbHNlKTtcclxuICAgIGNvbnN0IGlzU3VibWl0dGVkUmVmID0gdXNlUmVmKGZhbHNlKTtcclxuICAgIGNvbnN0IGlzRGlydHlSZWYgPSB1c2VSZWYoZmFsc2UpO1xyXG4gICAgY29uc3Qgc3VibWl0Q291bnRSZWYgPSB1c2VSZWYoMCk7XHJcbiAgICBjb25zdCBpc1N1Ym1pdHRpbmdSZWYgPSB1c2VSZWYoZmFsc2UpO1xyXG4gICAgY29uc3QgaGFuZGxlQ2hhbmdlUmVmID0gdXNlUmVmKCk7XHJcbiAgICBjb25zdCByZXNldEZpZWxkQXJyYXlGdW5jdGlvblJlZiA9IHVzZVJlZih7fSk7XHJcbiAgICBjb25zdCBmaWVsZEFycmF5TmFtZXNSZWYgPSB1c2VSZWYobmV3IFNldCgpKTtcclxuICAgIGNvbnN0IFssIHJlbmRlcl0gPSB1c2VTdGF0ZSgpO1xyXG4gICAgY29uc3QgeyBpc09uQmx1ciwgaXNPblN1Ym1pdCB9ID0gdXNlUmVmKG1vZGVDaGVja2VyKG1vZGUpKS5jdXJyZW50O1xyXG4gICAgY29uc3QgaXNXaW5kb3dVbmRlZmluZWQgPSB0eXBlb2Ygd2luZG93ID09PSBVTkRFRklORUQ7XHJcbiAgICBjb25zdCBpc1dlYiA9IHR5cGVvZiBkb2N1bWVudCAhPT0gVU5ERUZJTkVEICYmXHJcbiAgICAgICAgIWlzV2luZG93VW5kZWZpbmVkICYmXHJcbiAgICAgICAgIWlzVW5kZWZpbmVkKHdpbmRvdy5IVE1MRWxlbWVudCk7XHJcbiAgICBjb25zdCBpc1Byb3h5RW5hYmxlZCA9IGlzV2ViICYmICdQcm94eScgaW4gd2luZG93O1xyXG4gICAgY29uc3QgcmVhZEZvcm1TdGF0ZVJlZiA9IHVzZVJlZih7XHJcbiAgICAgICAgZGlydHk6ICFpc1Byb3h5RW5hYmxlZCxcclxuICAgICAgICBpc1N1Ym1pdHRlZDogaXNPblN1Ym1pdCxcclxuICAgICAgICBzdWJtaXRDb3VudDogIWlzUHJveHlFbmFibGVkLFxyXG4gICAgICAgIHRvdWNoZWQ6ICFpc1Byb3h5RW5hYmxlZCxcclxuICAgICAgICBpc1N1Ym1pdHRpbmc6ICFpc1Byb3h5RW5hYmxlZCxcclxuICAgICAgICBpc1ZhbGlkOiAhaXNQcm94eUVuYWJsZWQsXHJcbiAgICB9KTtcclxuICAgIGNvbnN0IHsgaXNPbkJsdXI6IGlzUmVWYWxpZGF0ZU9uQmx1ciwgaXNPblN1Ym1pdDogaXNSZVZhbGlkYXRlT25TdWJtaXQsIH0gPSB1c2VSZWYobW9kZUNoZWNrZXIocmVWYWxpZGF0ZU1vZGUpKS5jdXJyZW50O1xyXG4gICAgZGVmYXVsdFZhbHVlc1JlZi5jdXJyZW50ID0gZGVmYXVsdFZhbHVlc1JlZi5jdXJyZW50XHJcbiAgICAgICAgPyBkZWZhdWx0VmFsdWVzUmVmLmN1cnJlbnRcclxuICAgICAgICA6IGRlZmF1bHRWYWx1ZXM7XHJcbiAgICBjb25zdCByZVJlbmRlciA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgICAgICBpZiAoIWlzVW5Nb3VudC5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgIHJlbmRlcih7fSk7XHJcbiAgICAgICAgfVxyXG4gICAgfSwgW10pO1xyXG4gICAgY29uc3QgdmFsaWRhdGVGaWVsZEN1cnJ5ID0gdXNlQ2FsbGJhY2sodmFsaWRhdGVGaWVsZC5iaW5kKG51bGwsIGZpZWxkc1JlZiwgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSwgW10pO1xyXG4gICAgY29uc3QgdmFsaWRhdGVGaWVsZHNTY2hlbWFDdXJyeSA9IHVzZUNhbGxiYWNrKHZhbGlkYXRlV2l0aFNjaGVtYS5iaW5kKG51bGwsIHZhbGlkYXRpb25TY2hlbWEsIHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSksIFt2YWxpZGF0aW9uU2NoZW1hXSk7XHJcbiAgICBjb25zdCByZW5kZXJCYXNlT25FcnJvciA9IHVzZUNhbGxiYWNrKChuYW1lLCBlcnJvciwgc2hvdWxkUmVuZGVyLCBza2lwUmVSZW5kZXIpID0+IHtcclxuICAgICAgICBsZXQgc2hvdWxkUmVSZW5kZXIgPSBzaG91bGRSZW5kZXIgfHxcclxuICAgICAgICAgICAgc2hvdWxkVXBkYXRlV2l0aEVycm9yKHtcclxuICAgICAgICAgICAgICAgIGVycm9yczogZXJyb3JzUmVmLmN1cnJlbnQsXHJcbiAgICAgICAgICAgICAgICBlcnJvcixcclxuICAgICAgICAgICAgICAgIG5hbWUsXHJcbiAgICAgICAgICAgICAgICB2YWxpZEZpZWxkczogdmFsaWRGaWVsZHNSZWYuY3VycmVudCxcclxuICAgICAgICAgICAgICAgIGZpZWxkc1dpdGhWYWxpZGF0aW9uOiBmaWVsZHNXaXRoVmFsaWRhdGlvblJlZi5jdXJyZW50LFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICBpZiAoaXNFbXB0eU9iamVjdChlcnJvcikpIHtcclxuICAgICAgICAgICAgaWYgKGZpZWxkc1dpdGhWYWxpZGF0aW9uUmVmLmN1cnJlbnQuaGFzKG5hbWUpIHx8IHZhbGlkYXRpb25TY2hlbWEpIHtcclxuICAgICAgICAgICAgICAgIHZhbGlkRmllbGRzUmVmLmN1cnJlbnQuYWRkKG5hbWUpO1xyXG4gICAgICAgICAgICAgICAgc2hvdWxkUmVSZW5kZXIgPSBzaG91bGRSZVJlbmRlciB8fCBnZXQoZXJyb3JzUmVmLmN1cnJlbnQsIG5hbWUpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGVycm9yc1JlZi5jdXJyZW50ID0gdW5zZXQoZXJyb3JzUmVmLmN1cnJlbnQsIFtuYW1lXSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgICB2YWxpZEZpZWxkc1JlZi5jdXJyZW50LmRlbGV0ZShuYW1lKTtcclxuICAgICAgICAgICAgc2hvdWxkUmVSZW5kZXIgPSBzaG91bGRSZVJlbmRlciB8fCAhZ2V0KGVycm9yc1JlZi5jdXJyZW50LCBuYW1lKTtcclxuICAgICAgICAgICAgc2V0KGVycm9yc1JlZi5jdXJyZW50LCBuYW1lLCBlcnJvcltuYW1lXSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChzaG91bGRSZVJlbmRlciAmJiAhc2tpcFJlUmVuZGVyKSB7XHJcbiAgICAgICAgICAgIHJlUmVuZGVyKCk7XHJcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgICAgIH1cclxuICAgIH0sIFtyZVJlbmRlciwgdmFsaWRhdGlvblNjaGVtYV0pO1xyXG4gICAgY29uc3Qgc2V0RmllbGRWYWx1ZSA9IHVzZUNhbGxiYWNrKChuYW1lLCByYXdWYWx1ZSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGZpZWxkID0gZmllbGRzUmVmLmN1cnJlbnRbbmFtZV07XHJcbiAgICAgICAgaWYgKCFmaWVsZCkge1xyXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IHJlZiA9IGZpZWxkLnJlZjtcclxuICAgICAgICBjb25zdCB7IHR5cGUgfSA9IHJlZjtcclxuICAgICAgICBjb25zdCBvcHRpb25zID0gZmllbGQub3B0aW9ucztcclxuICAgICAgICBjb25zdCB2YWx1ZSA9IGlzV2ViICYmXHJcbiAgICAgICAgICAgIHJlZiBpbnN0YW5jZW9mIHdpbmRvdy5IVE1MRWxlbWVudCAmJlxyXG4gICAgICAgICAgICBpc051bGxPclVuZGVmaW5lZChyYXdWYWx1ZSlcclxuICAgICAgICAgICAgPyAnJ1xyXG4gICAgICAgICAgICA6IHJhd1ZhbHVlO1xyXG4gICAgICAgIGlmIChpc1JhZGlvSW5wdXQodHlwZSkgJiYgb3B0aW9ucykge1xyXG4gICAgICAgICAgICBvcHRpb25zLmZvckVhY2goKHsgcmVmOiByYWRpb1JlZiB9KSA9PiAocmFkaW9SZWYuY2hlY2tlZCA9IHJhZGlvUmVmLnZhbHVlID09PSB2YWx1ZSkpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBlbHNlIGlmIChpc0ZpbGVJbnB1dCh0eXBlKSkge1xyXG4gICAgICAgICAgICBpZiAodmFsdWUgaW5zdGFuY2VvZiBGaWxlTGlzdCB8fCB2YWx1ZSA9PT0gJycpIHtcclxuICAgICAgICAgICAgICAgIHJlZi5maWxlcyA9IHZhbHVlO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgcmVmLnZhbHVlID0gdmFsdWU7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZSBpZiAoaXNNdWx0aXBsZVNlbGVjdCh0eXBlKSkge1xyXG4gICAgICAgICAgICBbLi4ucmVmLm9wdGlvbnNdLmZvckVhY2goc2VsZWN0UmVmID0+IChzZWxlY3RSZWYuc2VsZWN0ZWQgPSB2YWx1ZS5pbmNsdWRlcyhzZWxlY3RSZWYudmFsdWUpKSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGVsc2UgaWYgKGlzQ2hlY2tCb3hJbnB1dCh0eXBlKSAmJiBvcHRpb25zKSB7XHJcbiAgICAgICAgICAgIG9wdGlvbnMubGVuZ3RoID4gMVxyXG4gICAgICAgICAgICAgICAgPyBvcHRpb25zLmZvckVhY2goKHsgcmVmOiBjaGVja2JveFJlZiB9KSA9PiAoY2hlY2tib3hSZWYuY2hlY2tlZCA9IHZhbHVlLmluY2x1ZGVzKGNoZWNrYm94UmVmLnZhbHVlKSkpXHJcbiAgICAgICAgICAgICAgICA6IChvcHRpb25zWzBdLnJlZi5jaGVja2VkID0gISF2YWx1ZSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgICByZWYudmFsdWUgPSB2YWx1ZTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIHR5cGU7XHJcbiAgICB9LCBbaXNXZWJdKTtcclxuICAgIGNvbnN0IHNldERpcnR5ID0gKG5hbWUpID0+IHtcclxuICAgICAgICBpZiAoIWZpZWxkc1JlZi5jdXJyZW50W25hbWVdIHx8ICFyZWFkRm9ybVN0YXRlUmVmLmN1cnJlbnQuZGlydHkpIHtcclxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCBpc0ZpZWxkQXJyYXkgPSBpc05hbWVJbkZpZWxkQXJyYXkoZmllbGRBcnJheU5hbWVzUmVmLmN1cnJlbnQsIG5hbWUpO1xyXG4gICAgICAgIGxldCBpc0RpcnR5ID0gZGVmYXVsdFJlbmRlclZhbHVlc1JlZi5jdXJyZW50W25hbWVdICE9PVxyXG4gICAgICAgICAgICBnZXRGaWVsZFZhbHVlKGZpZWxkc1JlZi5jdXJyZW50LCBmaWVsZHNSZWYuY3VycmVudFtuYW1lXS5yZWYpO1xyXG4gICAgICAgIGlmIChpc0ZpZWxkQXJyYXkpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coZGVmYXVsdFZhbHVlc1JlZi5jdXJyZW50KTtcclxuICAgICAgICAgICAgY29uc3QgZmllbGRBcnJheU5hbWUgPSBuYW1lLnN1YnN0cmluZygwLCBuYW1lLmluZGV4T2YoJ1snKSk7XHJcbiAgICAgICAgICAgIGlzRGlydHkgPSBnZXRJc0ZpZWxkc0RpZmZlcmVudCh0cmFuc2Zvcm1Ub05lc3RPYmplY3QoZ2V0RmllbGRzVmFsdWVzKGZpZWxkc1JlZi5jdXJyZW50KSlbZmllbGRBcnJheU5hbWVdLCBnZXQoZGVmYXVsdFZhbHVlc1JlZi5jdXJyZW50LCBmaWVsZEFycmF5TmFtZSkpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCBpc0RpcnR5Q2hhbmdlZCA9IGlzRmllbGRBcnJheVxyXG4gICAgICAgICAgICA/IGlzRGlydHlSZWYuY3VycmVudCAhPT0gaXNEaXJ0eVxyXG4gICAgICAgICAgICA6IGRpcnR5RmllbGRzUmVmLmN1cnJlbnQuaGFzKG5hbWUpICE9PSBpc0RpcnR5O1xyXG4gICAgICAgIGlmIChpc0RpcnR5KSB7XHJcbiAgICAgICAgICAgIGRpcnR5RmllbGRzUmVmLmN1cnJlbnQuYWRkKG5hbWUpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgZGlydHlGaWVsZHNSZWYuY3VycmVudC5kZWxldGUobmFtZSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlzRGlydHlSZWYuY3VycmVudCA9IGlzRmllbGRBcnJheSA/IGlzRGlydHkgOiAhIWRpcnR5RmllbGRzUmVmLmN1cnJlbnQuc2l6ZTtcclxuICAgICAgICByZXR1cm4gaXNEaXJ0eUNoYW5nZWQ7XHJcbiAgICB9O1xyXG4gICAgY29uc3Qgc2V0SW50ZXJuYWxWYWx1ZSA9IHVzZUNhbGxiYWNrKChuYW1lLCB2YWx1ZSkgPT4ge1xyXG4gICAgICAgIHNldEZpZWxkVmFsdWUobmFtZSwgdmFsdWUpO1xyXG4gICAgICAgIGlmIChzZXREaXJ0eShuYW1lKSB8fFxyXG4gICAgICAgICAgICAoIWdldCh0b3VjaGVkRmllbGRzUmVmLmN1cnJlbnQsIG5hbWUpICYmXHJcbiAgICAgICAgICAgICAgICByZWFkRm9ybVN0YXRlUmVmLmN1cnJlbnQudG91Y2hlZCkpIHtcclxuICAgICAgICAgICAgcmV0dXJuICEhc2V0KHRvdWNoZWRGaWVsZHNSZWYuY3VycmVudCwgbmFtZSwgdHJ1ZSk7XHJcbiAgICAgICAgfVxyXG4gICAgfSwgW3NldEZpZWxkVmFsdWVdKTtcclxuICAgIGNvbnN0IGV4ZWN1dGVWYWxpZGF0aW9uID0gdXNlQ2FsbGJhY2soYXN5bmMgKG5hbWUsIHNob3VsZFJlbmRlciwgc2tpcFJlUmVuZGVyKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZmllbGQgPSBmaWVsZHNSZWYuY3VycmVudFtuYW1lXTtcclxuICAgICAgICBpZiAoIWZpZWxkKSB7XHJcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHNob3VsZFJlbmRlcikge1xyXG4gICAgICAgICAgICByZVJlbmRlcigpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCBlcnJvciA9IGF3YWl0IHZhbGlkYXRlRmllbGQoZmllbGRzUmVmLCB2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEsIGZpZWxkKTtcclxuICAgICAgICByZW5kZXJCYXNlT25FcnJvcihuYW1lLCBlcnJvciwgZmFsc2UsIHNraXBSZVJlbmRlcik7XHJcbiAgICAgICAgcmV0dXJuIGlzRW1wdHlPYmplY3QoZXJyb3IpO1xyXG4gICAgfSwgW3JlUmVuZGVyLCByZW5kZXJCYXNlT25FcnJvciwgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhXSk7XHJcbiAgICBjb25zdCBleGVjdXRlU2NoZW1hVmFsaWRhdGlvbiA9IHVzZUNhbGxiYWNrKGFzeW5jIChwYXlsb2FkLCBzaG91bGRSZW5kZXIpID0+IHtcclxuICAgICAgICBjb25zdCB7IGVycm9ycyB9ID0gYXdhaXQgdmFsaWRhdGVXaXRoU2NoZW1hKHZhbGlkYXRpb25TY2hlbWEsIHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSwgdHJhbnNmb3JtVG9OZXN0T2JqZWN0KGdldEZpZWxkc1ZhbHVlcyhmaWVsZHNSZWYuY3VycmVudCkpKTtcclxuICAgICAgICBjb25zdCBwcmV2aW91c0Zvcm1Jc1ZhbGlkID0gaXNWYWxpZFJlZi5jdXJyZW50O1xyXG4gICAgICAgIGlzVmFsaWRSZWYuY3VycmVudCA9IGlzRW1wdHlPYmplY3QoZXJyb3JzKTtcclxuICAgICAgICBpZiAoaXNBcnJheShwYXlsb2FkKSkge1xyXG4gICAgICAgICAgICBwYXlsb2FkLmZvckVhY2gobmFtZSA9PiB7XHJcbiAgICAgICAgICAgICAgICBpZiAoZXJyb3JzW25hbWVdKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0KGVycm9yc1JlZi5jdXJyZW50LCBuYW1lLCBlcnJvcnNbbmFtZV0pO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgdW5zZXQoZXJyb3JzUmVmLmN1cnJlbnQsIFtuYW1lXSk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICByZVJlbmRlcigpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgY29uc3QgZmllbGROYW1lID0gcGF5bG9hZDtcclxuICAgICAgICAgICAgY29uc3QgZXJyb3IgPSAoZ2V0KGVycm9ycywgZmllbGROYW1lKVxyXG4gICAgICAgICAgICAgICAgPyB7IFtmaWVsZE5hbWVdOiBnZXQoZXJyb3JzLCBmaWVsZE5hbWUpIH1cclxuICAgICAgICAgICAgICAgIDoge30pO1xyXG4gICAgICAgICAgICByZW5kZXJCYXNlT25FcnJvcihmaWVsZE5hbWUsIGVycm9yLCBzaG91bGRSZW5kZXIgfHwgcHJldmlvdXNGb3JtSXNWYWxpZCAhPT0gaXNWYWxpZFJlZi5jdXJyZW50KTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIGlzRW1wdHlPYmplY3QoZXJyb3JzUmVmLmN1cnJlbnQpO1xyXG4gICAgfSwgW3JlUmVuZGVyLCByZW5kZXJCYXNlT25FcnJvciwgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhLCB2YWxpZGF0aW9uU2NoZW1hXSk7XHJcbiAgICBjb25zdCB0cmlnZ2VyVmFsaWRhdGlvbiA9IHVzZUNhbGxiYWNrKGFzeW5jIChwYXlsb2FkLCBzaG91bGRSZW5kZXIpID0+IHtcclxuICAgICAgICBjb25zdCBmaWVsZHMgPSBwYXlsb2FkIHx8IE9iamVjdC5rZXlzKGZpZWxkc1JlZi5jdXJyZW50KTtcclxuICAgICAgICBpZiAodmFsaWRhdGlvblNjaGVtYSkge1xyXG4gICAgICAgICAgICByZXR1cm4gZXhlY3V0ZVNjaGVtYVZhbGlkYXRpb24oZmllbGRzLCBzaG91bGRSZW5kZXIpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoaXNBcnJheShmaWVsZHMpKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFByb21pc2UuYWxsKGZpZWxkcy5tYXAoYXN5bmMgKGRhdGEpID0+IGF3YWl0IGV4ZWN1dGVWYWxpZGF0aW9uKGRhdGEsIGZhbHNlLCB0cnVlKSkpO1xyXG4gICAgICAgICAgICByZVJlbmRlcigpO1xyXG4gICAgICAgICAgICByZXR1cm4gcmVzdWx0LmV2ZXJ5KEJvb2xlYW4pO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gYXdhaXQgZXhlY3V0ZVZhbGlkYXRpb24oZmllbGRzLCBzaG91bGRSZW5kZXIpO1xyXG4gICAgfSwgW2V4ZWN1dGVTY2hlbWFWYWxpZGF0aW9uLCBleGVjdXRlVmFsaWRhdGlvbiwgcmVSZW5kZXIsIHZhbGlkYXRpb25TY2hlbWFdKTtcclxuICAgIGNvbnN0IHNldFZhbHVlID0gdXNlQ2FsbGJhY2soKG5hbWUsIHZhbHVlLCBzaG91bGRWYWxpZGF0ZSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IHNob3VsZFJlbmRlciA9IHNldEludGVybmFsVmFsdWUobmFtZSwgdmFsdWUpIHx8XHJcbiAgICAgICAgICAgIGlzV2F0Y2hBbGxSZWYuY3VycmVudCB8fFxyXG4gICAgICAgICAgICB3YXRjaEZpZWxkc1JlZi5jdXJyZW50LmhhcyhuYW1lKTtcclxuICAgICAgICBpZiAoc2hvdWxkVmFsaWRhdGUpIHtcclxuICAgICAgICAgICAgcmV0dXJuIHRyaWdnZXJWYWxpZGF0aW9uKG5hbWUsIHNob3VsZFJlbmRlcik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChzaG91bGRSZW5kZXIpIHtcclxuICAgICAgICAgICAgcmVSZW5kZXIoKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgfSwgW3JlUmVuZGVyLCBzZXRJbnRlcm5hbFZhbHVlLCB0cmlnZ2VyVmFsaWRhdGlvbl0pO1xyXG4gICAgaGFuZGxlQ2hhbmdlUmVmLmN1cnJlbnQgPSBoYW5kbGVDaGFuZ2VSZWYuY3VycmVudFxyXG4gICAgICAgID8gaGFuZGxlQ2hhbmdlUmVmLmN1cnJlbnRcclxuICAgICAgICA6IGFzeW5jICh7IHR5cGUsIHRhcmdldCB9KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IG5hbWUgPSB0YXJnZXQgPyB0YXJnZXQubmFtZSA6ICcnO1xyXG4gICAgICAgICAgICBjb25zdCBmaWVsZHMgPSBmaWVsZHNSZWYuY3VycmVudDtcclxuICAgICAgICAgICAgY29uc3QgZXJyb3JzID0gZXJyb3JzUmVmLmN1cnJlbnQ7XHJcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZmllbGRzW25hbWVdO1xyXG4gICAgICAgICAgICBjb25zdCBjdXJyZW50RXJyb3IgPSBnZXQoZXJyb3JzLCBuYW1lKTtcclxuICAgICAgICAgICAgbGV0IGVycm9yO1xyXG4gICAgICAgICAgICBpZiAoIWZpZWxkKSB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgY29uc3QgaXNCbHVyRXZlbnQgPSB0eXBlID09PSBFVkVOVFMuQkxVUjtcclxuICAgICAgICAgICAgY29uc3Qgc2hvdWxkU2tpcFZhbGlkYXRpb24gPSBza2lwVmFsaWRhdGlvbih7XHJcbiAgICAgICAgICAgICAgICBoYXNFcnJvcjogISFjdXJyZW50RXJyb3IsXHJcbiAgICAgICAgICAgICAgICBpc0JsdXJFdmVudCxcclxuICAgICAgICAgICAgICAgIGlzT25TdWJtaXQsXHJcbiAgICAgICAgICAgICAgICBpc1JlVmFsaWRhdGVPblN1Ym1pdCxcclxuICAgICAgICAgICAgICAgIGlzT25CbHVyLFxyXG4gICAgICAgICAgICAgICAgaXNSZVZhbGlkYXRlT25CbHVyLFxyXG4gICAgICAgICAgICAgICAgaXNTdWJtaXR0ZWQ6IGlzU3VibWl0dGVkUmVmLmN1cnJlbnQsXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICBjb25zdCBzaG91bGRVcGRhdGVEaXJ0eSA9IHNldERpcnR5KG5hbWUpO1xyXG4gICAgICAgICAgICBsZXQgc2hvdWxkVXBkYXRlU3RhdGUgPSBpc1dhdGNoQWxsUmVmLmN1cnJlbnQgfHxcclxuICAgICAgICAgICAgICAgIHdhdGNoRmllbGRzUmVmLmN1cnJlbnQuaGFzKG5hbWUpIHx8XHJcbiAgICAgICAgICAgICAgICBzaG91bGRVcGRhdGVEaXJ0eTtcclxuICAgICAgICAgICAgaWYgKGlzQmx1ckV2ZW50ICYmXHJcbiAgICAgICAgICAgICAgICAhZ2V0KHRvdWNoZWRGaWVsZHNSZWYuY3VycmVudCwgbmFtZSkgJiZcclxuICAgICAgICAgICAgICAgIHJlYWRGb3JtU3RhdGVSZWYuY3VycmVudC50b3VjaGVkKSB7XHJcbiAgICAgICAgICAgICAgICBzZXQodG91Y2hlZEZpZWxkc1JlZi5jdXJyZW50LCBuYW1lLCB0cnVlKTtcclxuICAgICAgICAgICAgICAgIHNob3VsZFVwZGF0ZVN0YXRlID0gdHJ1ZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBpZiAoc2hvdWxkU2tpcFZhbGlkYXRpb24pIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBzaG91bGRVcGRhdGVTdGF0ZSAmJiByZVJlbmRlcigpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGlmICh2YWxpZGF0aW9uU2NoZW1hKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB7IGVycm9ycyB9ID0gYXdhaXQgdmFsaWRhdGVXaXRoU2NoZW1hKHZhbGlkYXRpb25TY2hlbWEsIHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSwgdHJhbnNmb3JtVG9OZXN0T2JqZWN0KGdldEZpZWxkc1ZhbHVlcyhmaWVsZHMpKSk7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWxpZEZvcm0gPSBpc0VtcHR5T2JqZWN0KGVycm9ycyk7XHJcbiAgICAgICAgICAgICAgICBlcnJvciA9IChnZXQoZXJyb3JzLCBuYW1lKVxyXG4gICAgICAgICAgICAgICAgICAgID8geyBbbmFtZV06IGdldChlcnJvcnMsIG5hbWUpIH1cclxuICAgICAgICAgICAgICAgICAgICA6IHt9KTtcclxuICAgICAgICAgICAgICAgIGlmIChpc1ZhbGlkUmVmLmN1cnJlbnQgIT09IHZhbGlkRm9ybSkge1xyXG4gICAgICAgICAgICAgICAgICAgIHNob3VsZFVwZGF0ZVN0YXRlID0gdHJ1ZTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGlzVmFsaWRSZWYuY3VycmVudCA9IHZhbGlkRm9ybTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGVycm9yID0gYXdhaXQgdmFsaWRhdGVGaWVsZChmaWVsZHNSZWYsIHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSwgZmllbGQpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGlmICghcmVuZGVyQmFzZU9uRXJyb3IobmFtZSwgZXJyb3IpICYmIHNob3VsZFVwZGF0ZVN0YXRlKSB7XHJcbiAgICAgICAgICAgICAgICByZVJlbmRlcigpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfTtcclxuICAgIGNvbnN0IHZhbGlkYXRlU2NoZW1hSXNWYWxpZCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgICAgICBjb25zdCBmaWVsZFZhbHVlcyA9IGlzRW1wdHlPYmplY3QoZGVmYXVsdFZhbHVlc1JlZi5jdXJyZW50KVxyXG4gICAgICAgICAgICA/IGdldEZpZWxkc1ZhbHVlcyhmaWVsZHNSZWYuY3VycmVudClcclxuICAgICAgICAgICAgOiBkZWZhdWx0VmFsdWVzUmVmLmN1cnJlbnQ7XHJcbiAgICAgICAgdmFsaWRhdGVGaWVsZHNTY2hlbWFDdXJyeSh0cmFuc2Zvcm1Ub05lc3RPYmplY3QoZmllbGRWYWx1ZXMpKS50aGVuKCh7IGVycm9ycyB9KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IHByZXZpb3VzRm9ybUlzVmFsaWQgPSBpc1ZhbGlkUmVmLmN1cnJlbnQ7XHJcbiAgICAgICAgICAgIGlzVmFsaWRSZWYuY3VycmVudCA9IGlzRW1wdHlPYmplY3QoZXJyb3JzKTtcclxuICAgICAgICAgICAgaWYgKHByZXZpb3VzRm9ybUlzVmFsaWQgJiYgcHJldmlvdXNGb3JtSXNWYWxpZCAhPT0gaXNWYWxpZFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgICAgICByZVJlbmRlcigpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSk7XHJcbiAgICB9LCBbcmVSZW5kZXIsIHZhbGlkYXRlRmllbGRzU2NoZW1hQ3VycnldKTtcclxuICAgIGNvbnN0IHJlc2V0RmllbGRSZWYgPSB1c2VDYWxsYmFjaygobmFtZSkgPT4ge1xyXG4gICAgICAgIGVycm9yc1JlZi5jdXJyZW50ID0gdW5zZXQoZXJyb3JzUmVmLmN1cnJlbnQsIFtuYW1lXSk7XHJcbiAgICAgICAgdG91Y2hlZEZpZWxkc1JlZi5jdXJyZW50ID0gdW5zZXQodG91Y2hlZEZpZWxkc1JlZi5jdXJyZW50LCBbbmFtZV0pO1xyXG4gICAgICAgIGRlZmF1bHRSZW5kZXJWYWx1ZXNSZWYuY3VycmVudCA9IHVuc2V0KGRlZmF1bHRSZW5kZXJWYWx1ZXNSZWYuY3VycmVudCwgW1xyXG4gICAgICAgICAgICBuYW1lLFxyXG4gICAgICAgIF0pO1xyXG4gICAgICAgIFtcclxuICAgICAgICAgICAgZGlydHlGaWVsZHNSZWYsXHJcbiAgICAgICAgICAgIGZpZWxkc1dpdGhWYWxpZGF0aW9uUmVmLFxyXG4gICAgICAgICAgICB2YWxpZEZpZWxkc1JlZixcclxuICAgICAgICAgICAgd2F0Y2hGaWVsZHNSZWYsXHJcbiAgICAgICAgXS5mb3JFYWNoKGRhdGEgPT4gZGF0YS5jdXJyZW50LmRlbGV0ZShuYW1lKSk7XHJcbiAgICAgICAgaWYgKHJlYWRGb3JtU3RhdGVSZWYuY3VycmVudC5pc1ZhbGlkIHx8XHJcbiAgICAgICAgICAgIHJlYWRGb3JtU3RhdGVSZWYuY3VycmVudC50b3VjaGVkKSB7XHJcbiAgICAgICAgICAgIHJlUmVuZGVyKCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmICh2YWxpZGF0aW9uU2NoZW1hKSB7XHJcbiAgICAgICAgICAgIHZhbGlkYXRlU2NoZW1hSXNWYWxpZCgpO1xyXG4gICAgICAgIH1cclxuICAgIH0sIFtyZVJlbmRlcl0pO1xyXG4gICAgY29uc3QgcmVtb3ZlRXZlbnRMaXN0ZW5lckFuZFJlZiA9IHVzZUNhbGxiYWNrKChmaWVsZCwgZm9yY2VEZWxldGUpID0+IHtcclxuICAgICAgICBpZiAoIWZpZWxkKSB7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKCFpc1VuZGVmaW5lZChoYW5kbGVDaGFuZ2VSZWYuY3VycmVudCkpIHtcclxuICAgICAgICAgICAgZmluZFJlbW92ZWRGaWVsZEFuZFJlbW92ZUxpc3RlbmVyKGZpZWxkc1JlZi5jdXJyZW50LCBoYW5kbGVDaGFuZ2VSZWYuY3VycmVudCwgZmllbGQsIGZvcmNlRGVsZXRlKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmVzZXRGaWVsZFJlZihmaWVsZC5yZWYubmFtZSk7XHJcbiAgICB9LCBbcmVzZXRGaWVsZFJlZl0pO1xyXG4gICAgZnVuY3Rpb24gY2xlYXJFcnJvcihuYW1lKSB7XHJcbiAgICAgICAgaWYgKGlzVW5kZWZpbmVkKG5hbWUpKSB7XHJcbiAgICAgICAgICAgIGVycm9yc1JlZi5jdXJyZW50ID0ge307XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgICB1bnNldChlcnJvcnNSZWYuY3VycmVudCwgaXNBcnJheShuYW1lKSA/IG5hbWUgOiBbbmFtZV0pO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZVJlbmRlcigpO1xyXG4gICAgfVxyXG4gICAgY29uc3Qgc2V0SW50ZXJuYWxFcnJvciA9ICh7IG5hbWUsIHR5cGUsIHR5cGVzLCBtZXNzYWdlLCBwcmV2ZW50UmVuZGVyLCB9KSA9PiB7XHJcbiAgICAgICAgY29uc3QgZmllbGQgPSBmaWVsZHNSZWYuY3VycmVudFtuYW1lXTtcclxuICAgICAgICBpZiAoIWlzU2FtZUVycm9yKGVycm9yc1JlZi5jdXJyZW50W25hbWVdLCB0eXBlLCBtZXNzYWdlKSkge1xyXG4gICAgICAgICAgICBzZXQoZXJyb3JzUmVmLmN1cnJlbnQsIG5hbWUsIHtcclxuICAgICAgICAgICAgICAgIHR5cGUsXHJcbiAgICAgICAgICAgICAgICB0eXBlcyxcclxuICAgICAgICAgICAgICAgIG1lc3NhZ2UsXHJcbiAgICAgICAgICAgICAgICByZWY6IGZpZWxkID8gZmllbGQucmVmIDoge30sXHJcbiAgICAgICAgICAgICAgICBpc01hbnVhbDogdHJ1ZSxcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIGlmICghcHJldmVudFJlbmRlcikge1xyXG4gICAgICAgICAgICAgICAgcmVSZW5kZXIoKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH07XHJcbiAgICBmdW5jdGlvbiBzZXRFcnJvcihuYW1lLCB0eXBlID0gJycsIG1lc3NhZ2UpIHtcclxuICAgICAgICBpZiAoaXNTdHJpbmcobmFtZSkpIHtcclxuICAgICAgICAgICAgc2V0SW50ZXJuYWxFcnJvcihPYmplY3QuYXNzaWduKHsgbmFtZSB9LCAoaXNPYmplY3QodHlwZSlcclxuICAgICAgICAgICAgICAgID8ge1xyXG4gICAgICAgICAgICAgICAgICAgIHR5cGVzOiB0eXBlLFxyXG4gICAgICAgICAgICAgICAgICAgIHR5cGU6ICcnLFxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgdHlwZSxcclxuICAgICAgICAgICAgICAgICAgICBtZXNzYWdlLFxyXG4gICAgICAgICAgICAgICAgfSkpKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZSBpZiAoaXNBcnJheShuYW1lKSkge1xyXG4gICAgICAgICAgICBuYW1lLmZvckVhY2goZXJyb3IgPT4gc2V0SW50ZXJuYWxFcnJvcihPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIGVycm9yKSwgeyBwcmV2ZW50UmVuZGVyOiB0cnVlIH0pKSk7XHJcbiAgICAgICAgICAgIHJlUmVuZGVyKCk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgZnVuY3Rpb24gd2F0Y2goZmllbGROYW1lcywgZGVmYXVsdFZhbHVlKSB7XHJcbiAgICAgICAgY29uc3QgY29tYmluZWREZWZhdWx0VmFsdWVzID0gaXNVbmRlZmluZWQoZGVmYXVsdFZhbHVlKVxyXG4gICAgICAgICAgICA/IGlzVW5kZWZpbmVkKGRlZmF1bHRWYWx1ZXNSZWYuY3VycmVudClcclxuICAgICAgICAgICAgICAgID8ge31cclxuICAgICAgICAgICAgICAgIDogZGVmYXVsdFZhbHVlc1JlZi5jdXJyZW50XHJcbiAgICAgICAgICAgIDogZGVmYXVsdFZhbHVlO1xyXG4gICAgICAgIGNvbnN0IGZpZWxkVmFsdWVzID0gZ2V0RmllbGRzVmFsdWVzKGZpZWxkc1JlZi5jdXJyZW50KTtcclxuICAgICAgICBjb25zdCB3YXRjaEZpZWxkcyA9IHdhdGNoRmllbGRzUmVmLmN1cnJlbnQ7XHJcbiAgICAgICAgaWYgKGlzUHJveHlFbmFibGVkKSB7XHJcbiAgICAgICAgICAgIHJlYWRGb3JtU3RhdGVSZWYuY3VycmVudC5kaXJ0eSA9IHRydWU7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChpc1N0cmluZyhmaWVsZE5hbWVzKSkge1xyXG4gICAgICAgICAgICByZXR1cm4gYXNzaWduV2F0Y2hGaWVsZHMoZmllbGRWYWx1ZXMsIGZpZWxkTmFtZXMsIHdhdGNoRmllbGRzLCBjb21iaW5lZERlZmF1bHRWYWx1ZXMpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoaXNBcnJheShmaWVsZE5hbWVzKSkge1xyXG4gICAgICAgICAgICByZXR1cm4gZmllbGROYW1lcy5yZWR1Y2UoKHByZXZpb3VzLCBuYW1lKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBsZXQgdmFsdWU7XHJcbiAgICAgICAgICAgICAgICBpZiAoaXNFbXB0eU9iamVjdChmaWVsZHNSZWYuY3VycmVudCkgJiZcclxuICAgICAgICAgICAgICAgICAgICBpc09iamVjdChjb21iaW5lZERlZmF1bHRWYWx1ZXMpKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSBnZXREZWZhdWx0VmFsdWUoY29tYmluZWREZWZhdWx0VmFsdWVzLCBuYW1lKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlID0gYXNzaWduV2F0Y2hGaWVsZHMoZmllbGRWYWx1ZXMsIG5hbWUsIHdhdGNoRmllbGRzLCBjb21iaW5lZERlZmF1bHRWYWx1ZXMpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgcHJldmlvdXMpLCB7IFtuYW1lXTogdmFsdWUgfSk7XHJcbiAgICAgICAgICAgIH0sIHt9KTtcclxuICAgICAgICB9XHJcbiAgICAgICAgaXNXYXRjaEFsbFJlZi5jdXJyZW50ID0gdHJ1ZTtcclxuICAgICAgICBjb25zdCByZXN1bHQgPSAoIWlzRW1wdHlPYmplY3QoZmllbGRWYWx1ZXMpICYmIGZpZWxkVmFsdWVzKSB8fFxyXG4gICAgICAgICAgICBkZWZhdWx0VmFsdWUgfHxcclxuICAgICAgICAgICAgZGVmYXVsdFZhbHVlc1JlZi5jdXJyZW50O1xyXG4gICAgICAgIHJldHVybiBmaWVsZE5hbWVzICYmIGZpZWxkTmFtZXMubmVzdFxyXG4gICAgICAgICAgICA/IHRyYW5zZm9ybVRvTmVzdE9iamVjdChyZXN1bHQpXHJcbiAgICAgICAgICAgIDogcmVzdWx0O1xyXG4gICAgfVxyXG4gICAgZnVuY3Rpb24gdW5yZWdpc3RlcihuYW1lcykge1xyXG4gICAgICAgIGlmICghaXNFbXB0eU9iamVjdChmaWVsZHNSZWYuY3VycmVudCkpIHtcclxuICAgICAgICAgICAgKGlzQXJyYXkobmFtZXMpID8gbmFtZXMgOiBbbmFtZXNdKS5mb3JFYWNoKGZpZWxkTmFtZSA9PiByZW1vdmVFdmVudExpc3RlbmVyQW5kUmVmKGZpZWxkc1JlZi5jdXJyZW50W2ZpZWxkTmFtZV0sIHRydWUpKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBmdW5jdGlvbiByZWdpc3RlckZpZWxkc1JlZihyZWYsIHZhbGlkYXRlT3B0aW9ucyA9IHt9KSB7XHJcbiAgICAgICAgaWYgKCFyZWYubmFtZSkge1xyXG4gICAgICAgICAgICByZXR1cm4gY29uc29sZS53YXJuKCdNaXNzaW5nIG5hbWUgQCcsIHJlZik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IHsgbmFtZSwgdHlwZSwgdmFsdWUgfSA9IHJlZjtcclxuICAgICAgICBjb25zdCBmaWVsZEF0dHJpYnV0ZXMgPSBPYmplY3QuYXNzaWduKHsgcmVmIH0sIHZhbGlkYXRlT3B0aW9ucyk7XHJcbiAgICAgICAgY29uc3QgZmllbGRzID0gZmllbGRzUmVmLmN1cnJlbnQ7XHJcbiAgICAgICAgY29uc3QgaXNSYWRpb09yQ2hlY2tib3ggPSBpc1JhZGlvSW5wdXQodHlwZSkgfHwgaXNDaGVja0JveElucHV0KHR5cGUpO1xyXG4gICAgICAgIGxldCBjdXJyZW50RmllbGQgPSBmaWVsZHNbbmFtZV07XHJcbiAgICAgICAgbGV0IGlzRW1wdHlEZWZhdWx0VmFsdWUgPSB0cnVlO1xyXG4gICAgICAgIGxldCBpc0ZpZWxkQXJyYXkgPSBmYWxzZTtcclxuICAgICAgICBsZXQgZGVmYXVsdFZhbHVlO1xyXG4gICAgICAgIGlmIChpc1JhZGlvT3JDaGVja2JveFxyXG4gICAgICAgICAgICA/IGN1cnJlbnRGaWVsZCAmJlxyXG4gICAgICAgICAgICAgICAgaXNBcnJheShjdXJyZW50RmllbGQub3B0aW9ucykgJiZcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRGaWVsZC5vcHRpb25zLmZpbmQoKHsgcmVmIH0pID0+IHZhbHVlID09PSByZWYudmFsdWUpXHJcbiAgICAgICAgICAgIDogY3VycmVudEZpZWxkKSB7XHJcbiAgICAgICAgICAgIGZpZWxkc1tuYW1lXSA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgY3VycmVudEZpZWxkKSwgdmFsaWRhdGVPcHRpb25zKTtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAodHlwZSkge1xyXG4gICAgICAgICAgICBjb25zdCBtdXRhdGlvbldhdGNoZXIgPSBvbkRvbVJlbW92ZShyZWYsICgpID0+IHJlbW92ZUV2ZW50TGlzdGVuZXJBbmRSZWYoZmllbGRBdHRyaWJ1dGVzKSk7XHJcbiAgICAgICAgICAgIGlmIChpc1JhZGlvT3JDaGVja2JveCkge1xyXG4gICAgICAgICAgICAgICAgY3VycmVudEZpZWxkID0gT2JqZWN0LmFzc2lnbih7IG9wdGlvbnM6IFtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4uKChjdXJyZW50RmllbGQgJiYgY3VycmVudEZpZWxkLm9wdGlvbnMpIHx8IFtdKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVmLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbXV0YXRpb25XYXRjaGVyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIF0sIHJlZjogeyB0eXBlLCBuYW1lIH0gfSwgdmFsaWRhdGVPcHRpb25zKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRGaWVsZCA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgZmllbGRBdHRyaWJ1dGVzKSwgeyBtdXRhdGlvbldhdGNoZXIgfSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgIGN1cnJlbnRGaWVsZCA9IGZpZWxkQXR0cmlidXRlcztcclxuICAgICAgICB9XHJcbiAgICAgICAgZmllbGRzW25hbWVdID0gY3VycmVudEZpZWxkO1xyXG4gICAgICAgIGlmICghaXNFbXB0eU9iamVjdChkZWZhdWx0VmFsdWVzUmVmLmN1cnJlbnQpKSB7XHJcbiAgICAgICAgICAgIGRlZmF1bHRWYWx1ZSA9IGdldERlZmF1bHRWYWx1ZShkZWZhdWx0VmFsdWVzUmVmLmN1cnJlbnQsIG5hbWUpO1xyXG4gICAgICAgICAgICBpc0VtcHR5RGVmYXVsdFZhbHVlID0gaXNVbmRlZmluZWQoZGVmYXVsdFZhbHVlKTtcclxuICAgICAgICAgICAgaXNGaWVsZEFycmF5ID0gaXNOYW1lSW5GaWVsZEFycmF5KGZpZWxkQXJyYXlOYW1lc1JlZi5jdXJyZW50LCBuYW1lKTtcclxuICAgICAgICAgICAgaWYgKCFpc0VtcHR5RGVmYXVsdFZhbHVlICYmICFpc0ZpZWxkQXJyYXkpIHtcclxuICAgICAgICAgICAgICAgIHNldEZpZWxkVmFsdWUobmFtZSwgZGVmYXVsdFZhbHVlKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAodmFsaWRhdGlvblNjaGVtYSAmJiByZWFkRm9ybVN0YXRlUmVmLmN1cnJlbnQuaXNWYWxpZCkge1xyXG4gICAgICAgICAgICB2YWxpZGF0ZVNjaGVtYUlzVmFsaWQoKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZSBpZiAoIWlzRW1wdHlPYmplY3QodmFsaWRhdGVPcHRpb25zKSkge1xyXG4gICAgICAgICAgICBmaWVsZHNXaXRoVmFsaWRhdGlvblJlZi5jdXJyZW50LmFkZChuYW1lKTtcclxuICAgICAgICAgICAgaWYgKCFpc09uU3VibWl0ICYmIHJlYWRGb3JtU3RhdGVSZWYuY3VycmVudC5pc1ZhbGlkKSB7XHJcbiAgICAgICAgICAgICAgICB2YWxpZGF0ZUZpZWxkQ3VycnkoY3VycmVudEZpZWxkKS50aGVuKGVycm9yID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBwcmV2aW91c0Zvcm1Jc1ZhbGlkID0gaXNWYWxpZFJlZi5jdXJyZW50O1xyXG4gICAgICAgICAgICAgICAgICAgIGlmIChpc0VtcHR5T2JqZWN0KGVycm9yKSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWxpZEZpZWxkc1JlZi5jdXJyZW50LmFkZChuYW1lKTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzVmFsaWRSZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBpZiAocHJldmlvdXNGb3JtSXNWYWxpZCAhPT0gaXNWYWxpZFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlUmVuZGVyKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKCFkZWZhdWx0UmVuZGVyVmFsdWVzUmVmLmN1cnJlbnRbbmFtZV0gJiZcclxuICAgICAgICAgICAgIShpc0ZpZWxkQXJyYXkgJiYgaXNFbXB0eURlZmF1bHRWYWx1ZSkpIHtcclxuICAgICAgICAgICAgZGVmYXVsdFJlbmRlclZhbHVlc1JlZi5jdXJyZW50W25hbWVdID0gaXNFbXB0eURlZmF1bHRWYWx1ZVxyXG4gICAgICAgICAgICAgICAgPyBnZXRGaWVsZFZhbHVlKGZpZWxkcywgY3VycmVudEZpZWxkLnJlZilcclxuICAgICAgICAgICAgICAgIDogZGVmYXVsdFZhbHVlO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoIXR5cGUpIHtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCBmaWVsZFRvQXR0YWNoTGlzdGVuZXIgPSBpc1JhZGlvT3JDaGVja2JveCAmJiBjdXJyZW50RmllbGQub3B0aW9uc1xyXG4gICAgICAgICAgICA/IGN1cnJlbnRGaWVsZC5vcHRpb25zW2N1cnJlbnRGaWVsZC5vcHRpb25zLmxlbmd0aCAtIDFdXHJcbiAgICAgICAgICAgIDogY3VycmVudEZpZWxkO1xyXG4gICAgICAgIGF0dGFjaEV2ZW50TGlzdGVuZXJzKHtcclxuICAgICAgICAgICAgZmllbGQ6IGZpZWxkVG9BdHRhY2hMaXN0ZW5lcixcclxuICAgICAgICAgICAgaXNSYWRpb09yQ2hlY2tib3gsXHJcbiAgICAgICAgICAgIGhhbmRsZUNoYW5nZTogaGFuZGxlQ2hhbmdlUmVmLmN1cnJlbnQsXHJcbiAgICAgICAgfSk7XHJcbiAgICB9XHJcbiAgICBmdW5jdGlvbiByZWdpc3RlcihyZWZPclZhbGlkYXRpb25PcHRpb25zLCB2YWxpZGF0aW9uT3B0aW9ucykge1xyXG4gICAgICAgIGlmIChpc1dpbmRvd1VuZGVmaW5lZCB8fCAhcmVmT3JWYWxpZGF0aW9uT3B0aW9ucykge1xyXG4gICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChpc1N0cmluZyhyZWZPclZhbGlkYXRpb25PcHRpb25zKSkge1xyXG4gICAgICAgICAgICByZWdpc3RlckZpZWxkc1JlZih7IG5hbWU6IHJlZk9yVmFsaWRhdGlvbk9wdGlvbnMgfSwgdmFsaWRhdGlvbk9wdGlvbnMpO1xyXG4gICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChpc09iamVjdChyZWZPclZhbGlkYXRpb25PcHRpb25zKSAmJiAnbmFtZScgaW4gcmVmT3JWYWxpZGF0aW9uT3B0aW9ucykge1xyXG4gICAgICAgICAgICByZWdpc3RlckZpZWxkc1JlZihyZWZPclZhbGlkYXRpb25PcHRpb25zLCB2YWxpZGF0aW9uT3B0aW9ucyk7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIChyZWYpID0+IHJlZiAmJiByZWdpc3RlckZpZWxkc1JlZihyZWYsIHJlZk9yVmFsaWRhdGlvbk9wdGlvbnMpO1xyXG4gICAgfVxyXG4gICAgY29uc3QgaGFuZGxlU3VibWl0ID0gdXNlQ2FsbGJhY2soKGNhbGxiYWNrKSA9PiBhc3luYyAoZSkgPT4ge1xyXG4gICAgICAgIGlmIChlKSB7XHJcbiAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcclxuICAgICAgICAgICAgZS5wZXJzaXN0KCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGxldCBmaWVsZEVycm9ycztcclxuICAgICAgICBsZXQgZmllbGRWYWx1ZXM7XHJcbiAgICAgICAgY29uc3QgZmllbGRzID0gZmllbGRzUmVmLmN1cnJlbnQ7XHJcbiAgICAgICAgaWYgKHJlYWRGb3JtU3RhdGVSZWYuY3VycmVudC5pc1N1Ym1pdHRpbmcpIHtcclxuICAgICAgICAgICAgaXNTdWJtaXR0aW5nUmVmLmN1cnJlbnQgPSB0cnVlO1xyXG4gICAgICAgICAgICByZVJlbmRlcigpO1xyXG4gICAgICAgIH1cclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBpZiAodmFsaWRhdGlvblNjaGVtYSkge1xyXG4gICAgICAgICAgICAgICAgZmllbGRWYWx1ZXMgPSBnZXRGaWVsZHNWYWx1ZXMoZmllbGRzKTtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHsgZXJyb3JzLCB2YWx1ZXMgfSA9IGF3YWl0IHZhbGlkYXRlRmllbGRzU2NoZW1hQ3VycnkodHJhbnNmb3JtVG9OZXN0T2JqZWN0KGZpZWxkVmFsdWVzKSk7XHJcbiAgICAgICAgICAgICAgICBlcnJvcnNSZWYuY3VycmVudCA9IGVycm9ycztcclxuICAgICAgICAgICAgICAgIGZpZWxkRXJyb3JzID0gZXJyb3JzO1xyXG4gICAgICAgICAgICAgICAgZmllbGRWYWx1ZXMgPSB2YWx1ZXM7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB7IGVycm9ycywgdmFsdWVzLCB9ID0gYXdhaXQgT2JqZWN0LnZhbHVlcyhmaWVsZHMpLnJlZHVjZShhc3luYyAocHJldmlvdXMsIGZpZWxkKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFmaWVsZCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gcHJldmlvdXM7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc29sdmVkUHJldmlvdXMgPSBhd2FpdCBwcmV2aW91cztcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB7IHJlZiwgcmVmOiB7IG5hbWUgfSwgfSA9IGZpZWxkO1xyXG4gICAgICAgICAgICAgICAgICAgIGlmICghZmllbGRzW25hbWVdKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUocmVzb2x2ZWRQcmV2aW91cyk7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkRXJyb3IgPSBhd2FpdCB2YWxpZGF0ZUZpZWxkQ3VycnkoZmllbGQpO1xyXG4gICAgICAgICAgICAgICAgICAgIGlmIChmaWVsZEVycm9yW25hbWVdKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldChyZXNvbHZlZFByZXZpb3VzLmVycm9ycywgbmFtZSwgZmllbGRFcnJvcltuYW1lXSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbGlkRmllbGRzUmVmLmN1cnJlbnQuZGVsZXRlKG5hbWUpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKHJlc29sdmVkUHJldmlvdXMpO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBpZiAoZmllbGRzV2l0aFZhbGlkYXRpb25SZWYuY3VycmVudC5oYXMobmFtZSkpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsaWRGaWVsZHNSZWYuY3VycmVudC5hZGQobmFtZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIHJlc29sdmVkUHJldmlvdXMudmFsdWVzW25hbWVdID0gZ2V0RmllbGRWYWx1ZShmaWVsZHMsIHJlZik7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZShyZXNvbHZlZFByZXZpb3VzKTtcclxuICAgICAgICAgICAgICAgIH0sIFByb21pc2UucmVzb2x2ZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgZXJyb3JzOiB7fSxcclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZXM6IHt9LFxyXG4gICAgICAgICAgICAgICAgfSkpO1xyXG4gICAgICAgICAgICAgICAgZmllbGRFcnJvcnMgPSBlcnJvcnM7XHJcbiAgICAgICAgICAgICAgICBmaWVsZFZhbHVlcyA9IHZhbHVlcztcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBpZiAoaXNFbXB0eU9iamVjdChmaWVsZEVycm9ycykpIHtcclxuICAgICAgICAgICAgICAgIGVycm9yc1JlZi5jdXJyZW50ID0ge307XHJcbiAgICAgICAgICAgICAgICBhd2FpdCBjYWxsYmFjayh0cmFuc2Zvcm1Ub05lc3RPYmplY3QoZmllbGRWYWx1ZXMpLCBlKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGlmIChzdWJtaXRGb2N1c0Vycm9yKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gZmllbGRzUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGdldChmaWVsZEVycm9ycywga2V5KSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZmllbGQgPSBmaWVsZHNSZWYuY3VycmVudFtrZXldO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZpZWxkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZpZWxkLnJlZi5mb2N1cykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZC5yZWYuZm9jdXMoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsc2UgaWYgKGZpZWxkLm9wdGlvbnMpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGQub3B0aW9uc1swXS5yZWYuZm9jdXMoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgZXJyb3JzUmVmLmN1cnJlbnQgPSBmaWVsZEVycm9ycztcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBmaW5hbGx5IHtcclxuICAgICAgICAgICAgaXNTdWJtaXR0ZWRSZWYuY3VycmVudCA9IHRydWU7XHJcbiAgICAgICAgICAgIGlzU3VibWl0dGluZ1JlZi5jdXJyZW50ID0gZmFsc2U7XHJcbiAgICAgICAgICAgIHN1Ym1pdENvdW50UmVmLmN1cnJlbnQgPSBzdWJtaXRDb3VudFJlZi5jdXJyZW50ICsgMTtcclxuICAgICAgICAgICAgcmVSZW5kZXIoKTtcclxuICAgICAgICB9XHJcbiAgICB9LCBbXHJcbiAgICAgICAgcmVSZW5kZXIsXHJcbiAgICAgICAgc3VibWl0Rm9jdXNFcnJvcixcclxuICAgICAgICB2YWxpZGF0ZUZpZWxkQ3VycnksXHJcbiAgICAgICAgdmFsaWRhdGVGaWVsZHNTY2hlbWFDdXJyeSxcclxuICAgICAgICB2YWxpZGF0aW9uU2NoZW1hLFxyXG4gICAgXSk7XHJcbiAgICBjb25zdCByZXNldFJlZnMgPSAoKSA9PiB7XHJcbiAgICAgICAgZXJyb3JzUmVmLmN1cnJlbnQgPSB7fTtcclxuICAgICAgICBmaWVsZHNSZWYuY3VycmVudCA9IHt9O1xyXG4gICAgICAgIHRvdWNoZWRGaWVsZHNSZWYuY3VycmVudCA9IHt9O1xyXG4gICAgICAgIHZhbGlkRmllbGRzUmVmLmN1cnJlbnQgPSBuZXcgU2V0KCk7XHJcbiAgICAgICAgZmllbGRzV2l0aFZhbGlkYXRpb25SZWYuY3VycmVudCA9IG5ldyBTZXQoKTtcclxuICAgICAgICBkZWZhdWx0UmVuZGVyVmFsdWVzUmVmLmN1cnJlbnQgPSB7fTtcclxuICAgICAgICB3YXRjaEZpZWxkc1JlZi5jdXJyZW50ID0gbmV3IFNldCgpO1xyXG4gICAgICAgIGRpcnR5RmllbGRzUmVmLmN1cnJlbnQgPSBuZXcgU2V0KCk7XHJcbiAgICAgICAgaXNXYXRjaEFsbFJlZi5jdXJyZW50ID0gZmFsc2U7XHJcbiAgICAgICAgaXNTdWJtaXR0ZWRSZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgICAgIGlzRGlydHlSZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgICAgIGlzVmFsaWRSZWYuY3VycmVudCA9IHRydWU7XHJcbiAgICAgICAgc3VibWl0Q291bnRSZWYuY3VycmVudCA9IDA7XHJcbiAgICB9O1xyXG4gICAgY29uc3QgcmVzZXQgPSAodmFsdWVzKSA9PiB7XHJcbiAgICAgICAgZm9yIChjb25zdCB2YWx1ZSBvZiBPYmplY3QudmFsdWVzKGZpZWxkc1JlZi5jdXJyZW50KSkge1xyXG4gICAgICAgICAgICBpZiAodmFsdWUgJiYgdmFsdWUucmVmICYmIHZhbHVlLnJlZi5jbG9zZXN0KSB7XHJcbiAgICAgICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlLnJlZi5jbG9zZXN0KCdmb3JtJykucmVzZXQoKTtcclxuICAgICAgICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGNhdGNoIChfYSkgeyB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHZhbHVlcykge1xyXG4gICAgICAgICAgICBkZWZhdWx0VmFsdWVzUmVmLmN1cnJlbnQgPSB2YWx1ZXM7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIE9iamVjdC52YWx1ZXMocmVzZXRGaWVsZEFycmF5RnVuY3Rpb25SZWYuY3VycmVudCkuZm9yRWFjaChyZXNldEZpZWxkQXJyYXkgPT4gaXNGdW5jdGlvbihyZXNldEZpZWxkQXJyYXkpICYmIHJlc2V0RmllbGRBcnJheSh2YWx1ZXMpKTtcclxuICAgICAgICByZXNldFJlZnMoKTtcclxuICAgICAgICByZVJlbmRlcigpO1xyXG4gICAgfTtcclxuICAgIGNvbnN0IGdldFZhbHVlcyA9IChwYXlsb2FkKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZmllbGRWYWx1ZXMgPSBnZXRGaWVsZHNWYWx1ZXMoZmllbGRzUmVmLmN1cnJlbnQpO1xyXG4gICAgICAgIGNvbnN0IG91dHB1dFZhbHVlcyA9IGlzRW1wdHlPYmplY3QoZmllbGRWYWx1ZXMpXHJcbiAgICAgICAgICAgID8gZGVmYXVsdFZhbHVlc1JlZi5jdXJyZW50XHJcbiAgICAgICAgICAgIDogZmllbGRWYWx1ZXM7XHJcbiAgICAgICAgcmV0dXJuIHBheWxvYWQgJiYgcGF5bG9hZC5uZXN0XHJcbiAgICAgICAgICAgID8gdHJhbnNmb3JtVG9OZXN0T2JqZWN0KG91dHB1dFZhbHVlcylcclxuICAgICAgICAgICAgOiBvdXRwdXRWYWx1ZXM7XHJcbiAgICB9O1xyXG4gICAgdXNlRWZmZWN0KCgpID0+ICgpID0+IHtcclxuICAgICAgICBpc1VuTW91bnQuY3VycmVudCA9IHRydWU7XHJcbiAgICAgICAgZmllbGRzUmVmLmN1cnJlbnQgJiZcclxuICAgICAgICAgICAgT2JqZWN0LnZhbHVlcyhmaWVsZHNSZWYuY3VycmVudCkuZm9yRWFjaCgoZmllbGQpID0+IHJlbW92ZUV2ZW50TGlzdGVuZXJBbmRSZWYoZmllbGQsIHRydWUpKTtcclxuICAgIH0sIFtyZW1vdmVFdmVudExpc3RlbmVyQW5kUmVmXSk7XHJcbiAgICBpZiAoIXZhbGlkYXRpb25TY2hlbWEpIHtcclxuICAgICAgICBpc1ZhbGlkUmVmLmN1cnJlbnQgPVxyXG4gICAgICAgICAgICB2YWxpZEZpZWxkc1JlZi5jdXJyZW50LnNpemUgPj0gZmllbGRzV2l0aFZhbGlkYXRpb25SZWYuY3VycmVudC5zaXplICYmXHJcbiAgICAgICAgICAgICAgICBpc0VtcHR5T2JqZWN0KGVycm9yc1JlZi5jdXJyZW50KTtcclxuICAgIH1cclxuICAgIGNvbnN0IGZvcm1TdGF0ZSA9IHtcclxuICAgICAgICBkaXJ0eTogaXNEaXJ0eVJlZi5jdXJyZW50LFxyXG4gICAgICAgIGlzU3VibWl0dGVkOiBpc1N1Ym1pdHRlZFJlZi5jdXJyZW50LFxyXG4gICAgICAgIHN1Ym1pdENvdW50OiBzdWJtaXRDb3VudFJlZi5jdXJyZW50LFxyXG4gICAgICAgIHRvdWNoZWQ6IHRvdWNoZWRGaWVsZHNSZWYuY3VycmVudCxcclxuICAgICAgICBpc1N1Ym1pdHRpbmc6IGlzU3VibWl0dGluZ1JlZi5jdXJyZW50LFxyXG4gICAgICAgIGlzVmFsaWQ6IGlzT25TdWJtaXRcclxuICAgICAgICAgICAgPyBpc1N1Ym1pdHRlZFJlZi5jdXJyZW50ICYmIGlzRW1wdHlPYmplY3QoZXJyb3JzUmVmLmN1cnJlbnQpXHJcbiAgICAgICAgICAgIDogaXNFbXB0eU9iamVjdChmaWVsZHNSZWYuY3VycmVudCkgfHwgaXNWYWxpZFJlZi5jdXJyZW50LFxyXG4gICAgfTtcclxuICAgIGNvbnN0IGNvbnRyb2wgPSB7XHJcbiAgICAgICAgcmVnaXN0ZXIsXHJcbiAgICAgICAgdW5yZWdpc3RlcixcclxuICAgICAgICBzZXRWYWx1ZSxcclxuICAgICAgICB0cmlnZ2VyVmFsaWRhdGlvbixcclxuICAgICAgICBmb3JtU3RhdGUsXHJcbiAgICAgICAgbW9kZToge1xyXG4gICAgICAgICAgICBpc09uQmx1cixcclxuICAgICAgICAgICAgaXNPblN1Ym1pdCxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHJlVmFsaWRhdGVNb2RlOiB7XHJcbiAgICAgICAgICAgIGlzUmVWYWxpZGF0ZU9uQmx1cixcclxuICAgICAgICAgICAgaXNSZVZhbGlkYXRlT25TdWJtaXQsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBlcnJvcnM6IGVycm9yc1JlZi5jdXJyZW50LFxyXG4gICAgICAgIGZpZWxkc1JlZixcclxuICAgICAgICByZXNldEZpZWxkQXJyYXlGdW5jdGlvblJlZixcclxuICAgICAgICBmaWVsZEFycmF5TmFtZXNSZWYsXHJcbiAgICAgICAgaXNEaXJ0eVJlZixcclxuICAgICAgICByZWFkRm9ybVN0YXRlUmVmLFxyXG4gICAgICAgIGRlZmF1bHRWYWx1ZXNSZWYsXHJcbiAgICB9O1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgICB3YXRjaCxcclxuICAgICAgICBjb250cm9sLFxyXG4gICAgICAgIGhhbmRsZVN1Ym1pdCxcclxuICAgICAgICBzZXRWYWx1ZSxcclxuICAgICAgICB0cmlnZ2VyVmFsaWRhdGlvbixcclxuICAgICAgICBnZXRWYWx1ZXM6IHVzZUNhbGxiYWNrKGdldFZhbHVlcywgW10pLFxyXG4gICAgICAgIHJlc2V0OiB1c2VDYWxsYmFjayhyZXNldCwgW3JlUmVuZGVyXSksXHJcbiAgICAgICAgcmVnaXN0ZXI6IHVzZUNhbGxiYWNrKHJlZ2lzdGVyLCBbZGVmYXVsdFZhbHVlc1JlZi5jdXJyZW50XSksXHJcbiAgICAgICAgdW5yZWdpc3RlcjogdXNlQ2FsbGJhY2sodW5yZWdpc3RlciwgW3JlbW92ZUV2ZW50TGlzdGVuZXJBbmRSZWZdKSxcclxuICAgICAgICBjbGVhckVycm9yOiB1c2VDYWxsYmFjayhjbGVhckVycm9yLCBbXSksXHJcbiAgICAgICAgc2V0RXJyb3I6IHVzZUNhbGxiYWNrKHNldEVycm9yLCBbXSksXHJcbiAgICAgICAgZXJyb3JzOiBlcnJvcnNSZWYuY3VycmVudCxcclxuICAgICAgICBmb3JtU3RhdGU6IGlzUHJveHlFbmFibGVkXHJcbiAgICAgICAgICAgID8gbmV3IFByb3h5KGZvcm1TdGF0ZSwge1xyXG4gICAgICAgICAgICAgICAgZ2V0OiAob2JqLCBwcm9wKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKHByb3AgaW4gb2JqKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlYWRGb3JtU3RhdGVSZWYuY3VycmVudFtwcm9wXSA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBvYmpbcHJvcF07XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7fTtcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIDogZm9ybVN0YXRlLFxyXG4gICAgfTtcclxufVxuXG4vKiEgKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKipcclxuQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXHJcbkxpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7IHlvdSBtYXkgbm90IHVzZVxyXG50aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS4gWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZVxyXG5MaWNlbnNlIGF0IGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxyXG5cclxuVEhJUyBDT0RFIElTIFBST1ZJREVEIE9OIEFOICpBUyBJUyogQkFTSVMsIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWVxyXG5LSU5ELCBFSVRIRVIgRVhQUkVTUyBPUiBJTVBMSUVELCBJTkNMVURJTkcgV0lUSE9VVCBMSU1JVEFUSU9OIEFOWSBJTVBMSUVEXHJcbldBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBUSVRMRSwgRklUTkVTUyBGT1IgQSBQQVJUSUNVTEFSIFBVUlBPU0UsXHJcbk1FUkNIQU5UQUJMSVRZIE9SIE5PTi1JTkZSSU5HRU1FTlQuXHJcblxyXG5TZWUgdGhlIEFwYWNoZSBWZXJzaW9uIDIuMCBMaWNlbnNlIGZvciBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnNcclxuYW5kIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxyXG4qKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKiAqL1xyXG5cclxuZnVuY3Rpb24gX19yZXN0KHMsIGUpIHtcclxuICAgIHZhciB0ID0ge307XHJcbiAgICBmb3IgKHZhciBwIGluIHMpIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwocywgcCkgJiYgZS5pbmRleE9mKHApIDwgMClcclxuICAgICAgICB0W3BdID0gc1twXTtcclxuICAgIGlmIChzICE9IG51bGwgJiYgdHlwZW9mIE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMgPT09IFwiZnVuY3Rpb25cIilcclxuICAgICAgICBmb3IgKHZhciBpID0gMCwgcCA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMocyk7IGkgPCBwLmxlbmd0aDsgaSsrKSB7XHJcbiAgICAgICAgICAgIGlmIChlLmluZGV4T2YocFtpXSkgPCAwICYmIE9iamVjdC5wcm90b3R5cGUucHJvcGVydHlJc0VudW1lcmFibGUuY2FsbChzLCBwW2ldKSlcclxuICAgICAgICAgICAgICAgIHRbcFtpXV0gPSBzW3BbaV1dO1xyXG4gICAgICAgIH1cclxuICAgIHJldHVybiB0O1xyXG59XG5cbmNvbnN0IEZvcm1HbG9iYWxDb250ZXh0ID0gY3JlYXRlQ29udGV4dChudWxsKTtcclxuZnVuY3Rpb24gdXNlRm9ybUNvbnRleHQoKSB7XHJcbiAgICByZXR1cm4gdXNlQ29udGV4dChGb3JtR2xvYmFsQ29udGV4dCk7XHJcbn1cclxuZnVuY3Rpb24gRm9ybUNvbnRleHQoX2EpIHtcclxuICAgIHZhciB7IGNoaWxkcmVuLCBmb3JtU3RhdGUsIGVycm9ycyB9ID0gX2EsIHJlc3RNZXRob2RzID0gX19yZXN0KF9hLCBbXCJjaGlsZHJlblwiLCBcImZvcm1TdGF0ZVwiLCBcImVycm9yc1wiXSk7XHJcbiAgICByZXR1cm4gKGNyZWF0ZUVsZW1lbnQoRm9ybUdsb2JhbENvbnRleHQuUHJvdmlkZXIsIHsgdmFsdWU6IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgcmVzdE1ldGhvZHMpLCB7IGZvcm1TdGF0ZSwgZXJyb3JzIH0pIH0sIGNoaWxkcmVuKSk7XHJcbn1cblxudmFyIGdlbmVyYXRlSWQgPSAoKSA9PiB7XHJcbiAgICBjb25zdCBkID0gdHlwZW9mIHBlcmZvcm1hbmNlID09PSBVTkRFRklORUQgPyBEYXRlLm5vdygpIDogcGVyZm9ybWFuY2Uubm93KCkgKiAxMDAwO1xyXG4gICAgcmV0dXJuICd4eHh4eHh4eC14eHh4LTR4eHgteXh4eC14eHh4eHh4eHh4eHgnLnJlcGxhY2UoL1t4eV0vZywgZnVuY3Rpb24gKGMpIHtcclxuICAgICAgICBjb25zdCByID0gKE1hdGgucmFuZG9tKCkgKiAxNiArIGQpICUgMTYgfCAwO1xyXG4gICAgICAgIHJldHVybiAoYyA9PSAneCcgPyByIDogKHIgJiAweDMpIHwgMHg4KS50b1N0cmluZygxNik7XHJcbiAgICB9KTtcclxufTtcblxuY29uc3QgYXBwZW5kSWQgPSAodmFsdWUpID0+IChPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHZhbHVlKSwgeyBpZDogZ2VuZXJhdGVJZCgpIH0pKTtcclxuY29uc3QgbWFwSWRzID0gKGRhdGEpID0+IChpc0FycmF5KGRhdGEpID8gZGF0YSA6IFtdKS5tYXAodmFsdWUgPT4gYXBwZW5kSWQodmFsdWUpKTtcblxudmFyIHJlbW92ZUFycmF5QXQgPSAoZGF0YSwgaW5kZXgpID0+ICFpc1VuZGVmaW5lZChpbmRleCkgJiYgaXNBcnJheShkYXRhKVxyXG4gICAgPyBbLi4uZGF0YS5zbGljZSgwLCBpbmRleCksIC4uLmRhdGEuc2xpY2UoaW5kZXggKyAxKV1cclxuICAgIDogW107XG5cbnZhciBtb3ZlQXJyYXlBdCA9IChkYXRhLCBmcm9tLCB0bykgPT4gaXNBcnJheShkYXRhKSAmJiBkYXRhLnNwbGljZSh0bywgMCwgZGF0YS5zcGxpY2UoZnJvbSwgMSlbMF0pO1xuXG52YXIgc3dhcEFycmF5QXQgPSAoZmllbGRzLCBpbmRleEEsIGluZGV4QikgPT4gaXNBcnJheShmaWVsZHMpICYmXHJcbiAgICAoW2ZpZWxkc1tpbmRleEFdLCBmaWVsZHNbaW5kZXhCXV0gPSBbZmllbGRzW2luZGV4Ql0sIGZpZWxkc1tpbmRleEFdXSk7XG5cbmZ1bmN0aW9uIHVzZUZpZWxkQXJyYXkoeyBjb250cm9sLCBuYW1lIH0pIHtcclxuICAgIGNvbnN0IG1ldGhvZHMgPSB1c2VGb3JtQ29udGV4dCgpO1xyXG4gICAgY29uc3QgeyByZXNldEZpZWxkQXJyYXlGdW5jdGlvblJlZiwgZmllbGRBcnJheU5hbWVzUmVmLCBmaWVsZHNSZWYsIGRlZmF1bHRWYWx1ZXNSZWYsIHVucmVnaXN0ZXIsIGlzRGlydHlSZWYsIHJlYWRGb3JtU3RhdGVSZWYsIH0gPSBjb250cm9sIHx8IG1ldGhvZHMuY29udHJvbDtcclxuICAgIGNvbnN0IG1lbW9pemVkRGVmYXVsdFZhbHVlcyA9IHVzZVJlZiQxKGdldChkZWZhdWx0VmFsdWVzUmVmLmN1cnJlbnQsIG5hbWUsIFtdKSk7XHJcbiAgICBjb25zdCBbZmllbGRzLCBzZXRGaWVsZF0gPSB1c2VTdGF0ZSQxKG1hcElkcyhtZW1vaXplZERlZmF1bHRWYWx1ZXMuY3VycmVudCkpO1xyXG4gICAgY29uc3QgZ2V0RmllbGRWYWx1ZXNCeU5hbWUgPSAoZmllbGRzLCBuYW1lKSA9PiB0cmFuc2Zvcm1Ub05lc3RPYmplY3QoZ2V0RmllbGRzVmFsdWVzKGZpZWxkcykpW25hbWVdO1xyXG4gICAgY29uc3QgcmVzZXRGaWVsZHMgPSAoZmxhZ09yRmllbGRzKSA9PiB7XHJcbiAgICAgICAgaWYgKHJlYWRGb3JtU3RhdGVSZWYuY3VycmVudC5kaXJ0eSkge1xyXG4gICAgICAgICAgICBpc0RpcnR5UmVmLmN1cnJlbnQgPSBpc1VuZGVmaW5lZChmbGFnT3JGaWVsZHMpXHJcbiAgICAgICAgICAgICAgICA/IHRydWVcclxuICAgICAgICAgICAgICAgIDogZ2V0SXNGaWVsZHNEaWZmZXJlbnQoZmxhZ09yRmllbGRzLCBtZW1vaXplZERlZmF1bHRWYWx1ZXMuY3VycmVudCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGZvciAoY29uc3Qga2V5IGluIGZpZWxkc1JlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgIGlmIChpc01hdGNoRmllbGRBcnJheU5hbWUoa2V5LCBuYW1lKSkge1xyXG4gICAgICAgICAgICAgICAgdW5yZWdpc3RlcihrZXkpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfTtcclxuICAgIGNvbnN0IGFwcGVuZCA9ICh2YWx1ZSkgPT4ge1xyXG4gICAgICAgIGlmIChyZWFkRm9ybVN0YXRlUmVmLmN1cnJlbnQuZGlydHkpIHtcclxuICAgICAgICAgICAgaXNEaXJ0eVJlZi5jdXJyZW50ID0gdHJ1ZTtcclxuICAgICAgICB9XHJcbiAgICAgICAgc2V0RmllbGQoWy4uLmZpZWxkcywgYXBwZW5kSWQodmFsdWUpXSk7XHJcbiAgICB9O1xyXG4gICAgY29uc3QgcHJlcGVuZCA9ICh2YWx1ZSkgPT4ge1xyXG4gICAgICAgIHJlc2V0RmllbGRzKCk7XHJcbiAgICAgICAgc2V0RmllbGQobWFwSWRzKFthcHBlbmRJZCh2YWx1ZSksIC4uLmZpZWxkc10pKTtcclxuICAgIH07XHJcbiAgICBjb25zdCByZW1vdmUgPSAoaW5kZXgpID0+IHtcclxuICAgICAgICBjb25zdCB1cGRhdGVkRmllbGRzID0gcmVtb3ZlQXJyYXlBdChnZXRGaWVsZFZhbHVlc0J5TmFtZShmaWVsZHNSZWYuY3VycmVudCwgbmFtZSksIGluZGV4KTtcclxuICAgICAgICByZXNldEZpZWxkcyh1cGRhdGVkRmllbGRzKTtcclxuICAgICAgICBzZXRGaWVsZChtYXBJZHModXBkYXRlZEZpZWxkcykpO1xyXG4gICAgfTtcclxuICAgIGNvbnN0IGluc2VydCA9IChpbmRleCwgdmFsdWUpID0+IHtcclxuICAgICAgICBjb25zdCBmaWVsZFZhbHVlcyA9IGdldEZpZWxkVmFsdWVzQnlOYW1lKGZpZWxkc1JlZi5jdXJyZW50LCBuYW1lKTtcclxuICAgICAgICByZXNldEZpZWxkcygpO1xyXG4gICAgICAgIHNldEZpZWxkKG1hcElkcyhbXHJcbiAgICAgICAgICAgIC4uLmZpZWxkVmFsdWVzLnNsaWNlKDAsIGluZGV4KSxcclxuICAgICAgICAgICAgYXBwZW5kSWQodmFsdWUpLFxyXG4gICAgICAgICAgICAuLi5maWVsZFZhbHVlcy5zbGljZShpbmRleCksXHJcbiAgICAgICAgXSkpO1xyXG4gICAgfTtcclxuICAgIGNvbnN0IHN3YXAgPSAoaW5kZXhBLCBpbmRleEIpID0+IHtcclxuICAgICAgICBjb25zdCBmaWVsZFZhbHVlcyA9IGdldEZpZWxkVmFsdWVzQnlOYW1lKGZpZWxkc1JlZi5jdXJyZW50LCBuYW1lKTtcclxuICAgICAgICBzd2FwQXJyYXlBdChmaWVsZFZhbHVlcywgaW5kZXhBLCBpbmRleEIpO1xyXG4gICAgICAgIHJlc2V0RmllbGRzKGZpZWxkVmFsdWVzKTtcclxuICAgICAgICBzZXRGaWVsZChtYXBJZHMoZmllbGRWYWx1ZXMpKTtcclxuICAgIH07XHJcbiAgICBjb25zdCBtb3ZlID0gKGZyb20sIHRvKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZmllbGRWYWx1ZXMgPSBnZXRGaWVsZFZhbHVlc0J5TmFtZShmaWVsZHNSZWYuY3VycmVudCwgbmFtZSk7XHJcbiAgICAgICAgbW92ZUFycmF5QXQoZmllbGRWYWx1ZXMsIGZyb20sIHRvKTtcclxuICAgICAgICByZXNldEZpZWxkcyhmaWVsZFZhbHVlcyk7XHJcbiAgICAgICAgc2V0RmllbGQobWFwSWRzKGZpZWxkVmFsdWVzKSk7XHJcbiAgICB9O1xyXG4gICAgY29uc3QgcmVzZXQgPSAodmFsdWVzKSA9PiB7XHJcbiAgICAgICAgcmVzZXRGaWVsZHMoKTtcclxuICAgICAgICBzZXRGaWVsZChtYXBJZHMoZ2V0KHZhbHVlcywgbmFtZSkpKTtcclxuICAgICAgICBtZW1vaXplZERlZmF1bHRWYWx1ZXMuY3VycmVudCA9IGdldChkZWZhdWx0VmFsdWVzUmVmLmN1cnJlbnQsIG5hbWUsIFtdKTtcclxuICAgIH07XHJcbiAgICB1c2VFZmZlY3QkMSgoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgcmVzZXRGdW5jdGlvbnMgPSByZXNldEZpZWxkQXJyYXlGdW5jdGlvblJlZi5jdXJyZW50O1xyXG4gICAgICAgIGNvbnN0IGZpZWxkQXJyYXlOYW1lcyA9IGZpZWxkQXJyYXlOYW1lc1JlZi5jdXJyZW50O1xyXG4gICAgICAgIGZpZWxkQXJyYXlOYW1lcy5hZGQobmFtZSk7XHJcbiAgICAgICAgcmVzZXRGdW5jdGlvbnNbbmFtZV0gPSByZXNldDtcclxuICAgICAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICAgICAgICByZXNldEZpZWxkcygpO1xyXG4gICAgICAgICAgICBkZWxldGUgcmVzZXRGdW5jdGlvbnNbbmFtZV07XHJcbiAgICAgICAgICAgIGZpZWxkQXJyYXlOYW1lcy5kZWxldGUobmFtZSk7XHJcbiAgICAgICAgfTtcclxuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXHJcbiAgICB9LCBbbmFtZV0pO1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgICBzd2FwLFxyXG4gICAgICAgIG1vdmUsXHJcbiAgICAgICAgcHJlcGVuZCxcclxuICAgICAgICBhcHBlbmQsXHJcbiAgICAgICAgcmVtb3ZlLFxyXG4gICAgICAgIGluc2VydCxcclxuICAgICAgICBmaWVsZHMsXHJcbiAgICB9O1xyXG59XG5cbnZhciBnZXRJbnB1dFZhbHVlID0gKHRhcmdldCwgaXNDaGVja2JveCkgPT4ge1xyXG4gICAgaWYgKGlzTnVsbE9yVW5kZWZpbmVkKHRhcmdldCkpIHtcclxuICAgICAgICByZXR1cm4gdGFyZ2V0O1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIGlzQ2hlY2tib3hcclxuICAgICAgICA/IGlzVW5kZWZpbmVkKHRhcmdldC5jaGVja2VkKVxyXG4gICAgICAgICAgICA/IHRhcmdldFxyXG4gICAgICAgICAgICA6IHRhcmdldC5jaGVja2VkXHJcbiAgICAgICAgOiBpc1VuZGVmaW5lZCh0YXJnZXQudmFsdWUpXHJcbiAgICAgICAgICAgID8gdGFyZ2V0XHJcbiAgICAgICAgICAgIDogdGFyZ2V0LnZhbHVlO1xyXG59O1xuXG5jb25zdCBDb250cm9sbGVyID0gKF9hKSA9PiB7XHJcbiAgICB2YXIgeyBuYW1lLCBydWxlcywgYXM6IElubmVyQ29tcG9uZW50LCBvbkNoYW5nZSwgb25DaGFuZ2VOYW1lID0gVkFMSURBVElPTl9NT0RFLm9uQ2hhbmdlLCBvbkJsdXJOYW1lID0gVkFMSURBVElPTl9NT0RFLm9uQmx1ciwgdmFsdWVOYW1lLCBkZWZhdWx0VmFsdWUsIGNvbnRyb2wgfSA9IF9hLCByZXN0ID0gX19yZXN0KF9hLCBbXCJuYW1lXCIsIFwicnVsZXNcIiwgXCJhc1wiLCBcIm9uQ2hhbmdlXCIsIFwib25DaGFuZ2VOYW1lXCIsIFwib25CbHVyTmFtZVwiLCBcInZhbHVlTmFtZVwiLCBcImRlZmF1bHRWYWx1ZVwiLCBcImNvbnRyb2xcIl0pO1xyXG4gICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm1Db250ZXh0KCk7XHJcbiAgICBjb25zdCB7IGRlZmF1bHRWYWx1ZXNSZWYsIHNldFZhbHVlLCByZWdpc3RlciwgdW5yZWdpc3RlciwgZXJyb3JzLCB0cmlnZ2VyVmFsaWRhdGlvbiwgbW9kZTogeyBpc09uU3VibWl0LCBpc09uQmx1ciB9LCByZVZhbGlkYXRlTW9kZTogeyBpc1JlVmFsaWRhdGVPbkJsdXIsIGlzUmVWYWxpZGF0ZU9uU3VibWl0IH0sIGZvcm1TdGF0ZTogeyBpc1N1Ym1pdHRlZCB9LCBmaWVsZHNSZWYsIGZpZWxkQXJyYXlOYW1lc1JlZiwgfSA9IGNvbnRyb2wgfHwgbWV0aG9kcy5jb250cm9sO1xyXG4gICAgY29uc3QgW3ZhbHVlLCBzZXRJbnB1dFN0YXRlVmFsdWVdID0gdXNlU3RhdGUkMShpc1VuZGVmaW5lZChkZWZhdWx0VmFsdWUpXHJcbiAgICAgICAgPyBnZXQoZGVmYXVsdFZhbHVlc1JlZi5jdXJyZW50LCBuYW1lKVxyXG4gICAgICAgIDogZGVmYXVsdFZhbHVlKTtcclxuICAgIGNvbnN0IHZhbHVlUmVmID0gdXNlUmVmJDEodmFsdWUpO1xyXG4gICAgY29uc3QgaXNDaGVja2JveElucHV0ID0gaXNCb29sZWFuKHZhbHVlKTtcclxuICAgIGNvbnN0IHNob3VsZFZhbGlkYXRlID0gKCkgPT4gIXNraXBWYWxpZGF0aW9uKHtcclxuICAgICAgICBoYXNFcnJvcjogISFnZXQoZXJyb3JzLCBuYW1lKSxcclxuICAgICAgICBpc09uQmx1cixcclxuICAgICAgICBpc09uU3VibWl0LFxyXG4gICAgICAgIGlzUmVWYWxpZGF0ZU9uQmx1cixcclxuICAgICAgICBpc1JlVmFsaWRhdGVPblN1Ym1pdCxcclxuICAgICAgICBpc1N1Ym1pdHRlZCxcclxuICAgIH0pO1xyXG4gICAgY29uc3QgY29tbW9uVGFzayA9ICh0YXJnZXQpID0+IHtcclxuICAgICAgICBjb25zdCBkYXRhID0gZ2V0SW5wdXRWYWx1ZSh0YXJnZXQsIGlzQ2hlY2tib3hJbnB1dCk7XHJcbiAgICAgICAgc2V0SW5wdXRTdGF0ZVZhbHVlKGRhdGEpO1xyXG4gICAgICAgIHZhbHVlUmVmLmN1cnJlbnQgPSBkYXRhO1xyXG4gICAgICAgIHJldHVybiBkYXRhO1xyXG4gICAgfTtcclxuICAgIGNvbnN0IGV2ZW50V3JhcHBlciA9IChldmVudCkgPT4gKC4uLmFyZykgPT4gc2V0VmFsdWUobmFtZSwgY29tbW9uVGFzayhldmVudChhcmcpKSwgc2hvdWxkVmFsaWRhdGUoKSk7XHJcbiAgICBjb25zdCBoYW5kbGVDaGFuZ2UgPSAoZSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGRhdGEgPSBjb21tb25UYXNrKGUgJiYgZS50YXJnZXQgPyBlLnRhcmdldCA6IGUpO1xyXG4gICAgICAgIHNldFZhbHVlKG5hbWUsIGRhdGEsIHNob3VsZFZhbGlkYXRlKCkpO1xyXG4gICAgfTtcclxuICAgIGNvbnN0IHJlZ2lzdGVyRmllbGQgPSAoKSA9PiByZWdpc3RlcihPYmplY3QuZGVmaW5lUHJvcGVydHkoe1xyXG4gICAgICAgIG5hbWUsXHJcbiAgICB9LCBWQUxVRSwge1xyXG4gICAgICAgIHNldChkYXRhKSB7XHJcbiAgICAgICAgICAgIHNldElucHV0U3RhdGVWYWx1ZShkYXRhKTtcclxuICAgICAgICAgICAgdmFsdWVSZWYuY3VycmVudCA9IGRhdGE7XHJcbiAgICAgICAgfSxcclxuICAgICAgICBnZXQoKSB7XHJcbiAgICAgICAgICAgIHJldHVybiB2YWx1ZVJlZi5jdXJyZW50O1xyXG4gICAgICAgIH0sXHJcbiAgICB9KSwgT2JqZWN0LmFzc2lnbih7fSwgcnVsZXMpKTtcclxuICAgIGlmICghZmllbGRzUmVmLmN1cnJlbnRbbmFtZV0pIHtcclxuICAgICAgICByZWdpc3RlckZpZWxkKCk7XHJcbiAgICB9XHJcbiAgICB1c2VFZmZlY3QkMSgoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZmllbGRBcnJheU5hbWVzID0gZmllbGRBcnJheU5hbWVzUmVmLmN1cnJlbnQ7XHJcbiAgICAgICAgcmVnaXN0ZXJGaWVsZCgpO1xyXG4gICAgICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgICAgICAgIGlmICghaXNOYW1lSW5GaWVsZEFycmF5KGZpZWxkQXJyYXlOYW1lcywgbmFtZSkpIHtcclxuICAgICAgICAgICAgICAgIHVucmVnaXN0ZXIobmFtZSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9O1xyXG4gICAgfSwgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gICAgW25hbWVdKTtcclxuICAgIGNvbnN0IHByb3BzID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7IG5hbWUgfSwgcmVzdCksIChvbkNoYW5nZVxyXG4gICAgICAgID8geyBbb25DaGFuZ2VOYW1lXTogZXZlbnRXcmFwcGVyKG9uQ2hhbmdlKSB9XHJcbiAgICAgICAgOiB7IFtvbkNoYW5nZU5hbWVdOiBoYW5kbGVDaGFuZ2UgfSkpLCAoaXNPbkJsdXIgfHwgaXNSZVZhbGlkYXRlT25CbHVyXHJcbiAgICAgICAgPyB7IFtvbkJsdXJOYW1lXTogKCkgPT4gdHJpZ2dlclZhbGlkYXRpb24obmFtZSkgfVxyXG4gICAgICAgIDoge30pKSwgeyBbdmFsdWVOYW1lIHx8IChpc0NoZWNrYm94SW5wdXQgPyAnY2hlY2tlZCcgOiBWQUxVRSldOiB2YWx1ZSB9KTtcclxuICAgIHJldHVybiBpc1ZhbGlkRWxlbWVudChJbm5lckNvbXBvbmVudCkgPyAoY2xvbmVFbGVtZW50KElubmVyQ29tcG9uZW50LCBwcm9wcykpIDogKGNyZWF0ZUVsZW1lbnQoSW5uZXJDb21wb25lbnQsIE9iamVjdC5hc3NpZ24oe30sIHByb3BzKSkpO1xyXG59O1xuXG5jb25zdCBFcnJvck1lc3NhZ2UgPSAoeyBhczogSW5uZXJDb21wb25lbnQsIGVycm9ycywgbmFtZSwgY2hpbGRyZW4sIH0pID0+IHtcclxuICAgIGNvbnN0IG1ldGhvZHMgPSB1c2VGb3JtQ29udGV4dCgpO1xyXG4gICAgY29uc3QgeyBtZXNzYWdlLCB0eXBlcyB9ID0gZ2V0KGVycm9ycyB8fCBtZXRob2RzLmVycm9ycywgbmFtZSwge30pO1xyXG4gICAgaWYgKCFtZXNzYWdlKSB7XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcbiAgICBjb25zdCBwcm9wcyA9IHtcclxuICAgICAgICBjaGlsZHJlbjogY2hpbGRyZW4gPyBjaGlsZHJlbih7IG1lc3NhZ2UsIG1lc3NhZ2VzOiB0eXBlcyB9KSA6IG1lc3NhZ2UsXHJcbiAgICB9O1xyXG4gICAgcmV0dXJuIElubmVyQ29tcG9uZW50ID8gKGlzVmFsaWRFbGVtZW50KElubmVyQ29tcG9uZW50KSA/IChjbG9uZUVsZW1lbnQoSW5uZXJDb21wb25lbnQsIHByb3BzKSkgOiAoY3JlYXRlRWxlbWVudChJbm5lckNvbXBvbmVudCwgT2JqZWN0LmFzc2lnbih7fSwgcHJvcHMpKSkpIDogKGNyZWF0ZUVsZW1lbnQoRnJhZ21lbnQsIE9iamVjdC5hc3NpZ24oe30sIHByb3BzKSkpO1xyXG59O1xuXG5leHBvcnQgeyBDb250cm9sbGVyLCBFcnJvck1lc3NhZ2UsIEZvcm1Db250ZXh0LCB1c2VGaWVsZEFycmF5LCB1c2VGb3JtLCB1c2VGb3JtQ29udGV4dCB9O1xuIiwiZXhwb3J0IGVudW0gRUNvbnRhY3RNZXRob2Qge1xyXG4gIFBIT05FID0gXCJQaG9uZVwiLFxyXG4gIFRFWFRfTUVTU0FHRSA9IFwiVGV4dE1lc3NhZ2VcIixcclxuICBFTUFJTCA9IFwiRW1haWxcIlxyXG59XHJcblxyXG5leHBvcnQgZW51bSBFRHVyYXRpb24ge1xyXG4gIEFNID0gXCJBTVwiLFxyXG4gIFBNID0gXCJQTVwiLFxyXG4gIEV2ZW5pbmcgPSBcIkV2ZW5pbmdcIixcclxuICBBbGxEYXkgPSBcIkFsbERheVwiLFxyXG4gIEl0ZW0wODEwID0gXCJJdGVtMDgxMFwiLFxyXG4gIEl0ZW0xMDEyID0gXCJJdGVtMTAxMlwiLFxyXG4gIEl0ZW0xMzE1ID0gXCJJdGVtMTMxNVwiLFxyXG4gIEl0ZW0xNTE3ID0gXCJJdGVtMTUxN1wiLFxyXG4gIEl0ZW0xNzE5ID0gXCJJdGVtMTcxOVwiLFxyXG4gIEl0ZW0xOTIxID0gXCJJdGVtMTkyMVwiXHJcbn1cclxuXHJcbmV4cG9ydCBlbnVtIEVQcmVmZXJyZWRDb250YWN0TWV0aG9kIHtcclxuICBFTUFJTCA9IFwiRW1haWxcIixcclxuICBURVhUX01FU1NBR0UgPSBcIlRleHRNZXNzYWdlXCIsXHJcbiAgUEhPTkUgPSBcIlBob25lXCJcclxufVxyXG4iLCJpbXBvcnQgeyBPbW5pdHVyZSwgVXRpbHMsIEVGbG93VHlwZSB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcbmltcG9ydCB7IElFcnJvcnNMaXN0LCBJQXZhaWxhYmxlRGF0ZXMsIElDb250YWN0SW5mb3JtYXRpb24gfSBmcm9tIFwiLi4vbW9kZWxzXCI7XHJcbmltcG9ydCB7IEVDb250YWN0TWV0aG9kIH0gZnJvbSBcIi4uL21vZGVscy9FbnVtc1wiO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIHN0cmlwVGltZUJpdChkYXRlOiBzdHJpbmcpOiBEYXRlIHwgc3RyaW5nIHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgZnJhZ21lbnRzID0gZGF0ZS5zcGxpdChcIlRcIik7XHJcbiAgICBjb25zdCBuZXdEYXRlID0gbmV3IERhdGUoZnJhZ21lbnRzWzBdKTtcclxuICAgIG5ld0RhdGUuc2V0TWludXRlcyhuZXcgRGF0ZShkYXRlKS5nZXRNaW51dGVzKCkgKyBuZXcgRGF0ZShkYXRlKS5nZXRUaW1lem9uZU9mZnNldCgpKTtcclxuICAgIG5ld0RhdGUuc2V0SG91cnMoMCk7XHJcbiAgICBuZXdEYXRlLnNldE1pbnV0ZXMoMCk7XHJcbiAgICByZXR1cm4gbmV3RGF0ZTtcclxuICB9IGNhdGNoIChlKSB7XHJcbiAgICByZXR1cm4gZGF0ZTtcclxuICB9XHJcbn1cclxuXHJcbmludGVyZmFjZSBFbnVtVHlwZSB7IFtzOiBudW1iZXJdOiBzdHJpbmcgfVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIG1hcEVudW0oZW51bWVyYWJsZTogRW51bVR5cGUsIGZuOiBGdW5jdGlvbik6IGFueVtdIHtcclxuICAvLyBnZXQgYWxsIHRoZSBtZW1iZXJzIG9mIHRoZSBlbnVtXHJcbiAgY29uc3QgZW51bU1lbWJlcnM6IGFueVtdID0gT2JqZWN0LmtleXMoZW51bWVyYWJsZSkubWFwKChrZXk6IGFueSkgPT4gZW51bWVyYWJsZVtrZXldKTtcclxuXHJcbiAgLy8gbm93IG1hcCB0aHJvdWdoIHRoZSBlbnVtIHZhbHVlc1xyXG4gIHJldHVybiBlbnVtTWVtYmVycy5tYXAobSA9PiBmbihtKSk7XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBub1NwZWNpYWxDaGFyUmVnZXggPSAvXlthLXpBLVowLTldKyQvaTtcclxuXHJcbmV4cG9ydCBjb25zdCBlbWFpbFJlZ2V4ID0gL15bYS16MC05YCEjXFwkJSZcXCpcXCtcXC89XFw/XFxeXFwnXFwtX10rKChcXC4pK1thLXowLTlgISNcXCQlJlxcKlxcK1xcLz1cXD9cXF5cXCdcXC1fXSspKkAoW2EtejAtOV0rKFtcXC1dW2EtejAtOV0pKikrKFtcXC5dKFthLXowLTldKyhbXFwtXVthLXowLTldKSopKykrJC9pO1xyXG5cclxuZXhwb3J0IGNvbnN0IGZvcm1hdHRlZFBob25lUmVnZXggPSAvXlswLTldXFxkezJ9LVxcZHszfS1cXGR7NH0kL2k7IC8vIDAwMC0wMDAtMDAwMFxyXG4vLyBleHBvcnQgY29uc3QgZm9ybWF0dGVkUGhvbmVSZWdleCA9IC9eXFxkezEwfSQvaTsgLy8gMTAgZGlnaXRzIG9ubHlcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRNZXNzYWdlc0xpc3QgPSAoZXJyb3JzOiBQYXJ0aWFsPFJlY29yZDxzdHJpbmcsIGFueT4+KTogQXJyYXk8SUVycm9yc0xpc3Q+ID0+IHtcclxuICBjb25zdCBlcnJvcnNsaXN0OiBBcnJheTxJRXJyb3JzTGlzdD4gPSBbXTtcclxuICBsZXQgYWN0aW9uO1xyXG4gIGlmIChVdGlscy5nZXRGbG93VHlwZSgpID09PSBFRmxvd1R5cGUuSU5URVJORVQpXHJcbiAgICBhY3Rpb24gPSA1MjM7XHJcblxyXG4gIGlmIChVdGlscy5nZXRGbG93VHlwZSgpID09PSBFRmxvd1R5cGUuQlVORExFKVxyXG4gICAgYWN0aW9uID0gNTA4O1xyXG5cclxuICBPYmplY3Qua2V5cyhlcnJvcnMpLm1hcChrID0+IHtcclxuICAgIGNvbnN0IGVycjogYW55ID0gZXJyb3JzW2tdO1xyXG4gICAgZXJyb3JzbGlzdC5wdXNoKHtcclxuICAgICAgaWQ6IGVyci5yZWYubmFtZSxcclxuICAgICAgZXJyb3I6IGVyci50eXBlXHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgT21uaXR1cmUudXNlT21uaXR1cmUoKS50cmFja0Vycm9yKFxyXG4gICAgZXJyb3JzbGlzdC5tYXAoKGVycikgPT4gKHtcclxuICAgICAgY29kZTogZXJyLmlkLFxyXG4gICAgICB0eXBlOiBPbW5pdHVyZS5FRXJyb3JUeXBlLlZhbGlkYXRpb24sXHJcbiAgICAgIGxheWVyOiBPbW5pdHVyZS5FQXBwbGljYXRpb25MYXllci5Gcm9udGVuZCxcclxuICAgICAgZGVzY3JpcHRpb246IGVyci5lcnJvclxyXG4gICAgfSkpLCBhY3Rpb24pO1xyXG5cclxuICByZXR1cm4gZXJyb3JzbGlzdDtcclxufTtcclxuXHJcbi8qKlxyXG4gKiBGdW5jdGlvbiBjYWxsZWQgb24gQ29udGFjdCBOdW1iZXIgZmllbGQgb25DaGFuZ2VcclxuICogSXQgZm9ybWF0cyB0aGUgbnVtYmVyIHRvIDAwMC0wMDAtMDAwMCBmb3JtYXRcclxuICogQGV4cG9ydFxyXG4gKiBAcGFyYW0ge3N0cmluZ30gdmFsdWVcclxuICogQHJldHVybnNcclxuICovXHJcbmV4cG9ydCBmdW5jdGlvbiBhdXRvRm9ybWF0KHZhbHVlOiBzdHJpbmcpIHtcclxuICBsZXQgbmV3VmFsID0gZmlsdGVyTnVtYmVycyh2YWx1ZSk7XHJcbiAgbmV3VmFsID0gbmV3VmFsLnN1YnN0cigwLCAxMCk7IC8vIExpbWl0IGVudHJ5IHRvIDEwIG51bWJlcnMgb25seS5cclxuICByZXR1cm4gbmV3VmFsLmxlbmd0aCA9PT0gMTAgPyBuZXdWYWwuc2xpY2UoMCwgMykgKyBcIi1cIiArIG5ld1ZhbC5zbGljZSgzLCA2KSArIFwiLVwiICsgbmV3VmFsLnNsaWNlKDYpIDogbmV3VmFsO1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gZmlsdGVyTnVtYmVycyh2YWx1ZTogc3RyaW5nKSB7XHJcbiAgY29uc3QgbnVtID0gdmFsdWUucmVwbGFjZSgvXFxEL2csIFwiXCIpOyAvLyBvbmx5IG51bWJlcnNcclxuICByZXR1cm4gbnVtO1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gVmFsdWVPZih2YWw6IGFueSkge1xyXG4gIHJldHVybiB2YWw7XHJcbn1cclxuXHJcbi8vIGV4cG9ydCBmdW5jdGlvbiBnZXRQcmVmZXJlZERhdGVzKGRhdGVzOiBBcnJheTxJQXZhaWxhYmxlRGF0ZXM+KSB7XHJcbi8vICAgICByZXR1cm4gZGF0ZXMuZmlsdGVyKGRhdGUgPT4gZGF0ZS5pc1ByZWZlcnJlZERhdGUgPT09IHRydWUpO1xyXG4vLyB9XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gZ2V0U2VsZWN0ZWREYXRlKGRhdGVzOiBBcnJheTxJQXZhaWxhYmxlRGF0ZXM+KSB7XHJcbiAgY29uc3Qgc2VsZWN0ZWREYXRlcyA9IGRhdGVzLmZpbHRlcihkYXRlID0+IGRhdGUudGltZVNsb3RzLmZpbmQodGltZSA9PiB0aW1lLmlzU2VsZWN0ZWQgPT09IHRydWUpKTtcclxuICByZXR1cm4gc2VsZWN0ZWREYXRlcy5sZW5ndGggPiAwID8gc2VsZWN0ZWREYXRlcyA6IFtkYXRlc1swXV07XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBnZXRQcmltYXJ5VmFsdWUgPSAobWV0aG9kOiBFQ29udGFjdE1ldGhvZCwgdmFsdWU6IElDb250YWN0SW5mb3JtYXRpb24gfCB1bmRlZmluZWQpID0+IHtcclxuICBzd2l0Y2ggKG1ldGhvZCkge1xyXG4gICAgY2FzZSBFQ29udGFjdE1ldGhvZC5FTUFJTDpcclxuICAgICAgcmV0dXJuIHZhbHVlPy5lbWFpbDtcclxuICAgIGNhc2UgRUNvbnRhY3RNZXRob2QuUEhPTkU6XHJcbiAgICAgIHJldHVybiB2YWx1ZT8ucHJpbWFyeVBob25lPy5waG9uZU51bWJlciAmJiBhdXRvRm9ybWF0KHZhbHVlPy5wcmltYXJ5UGhvbmU/LnBob25lTnVtYmVyKTtcclxuICAgIGNhc2UgRUNvbnRhY3RNZXRob2QuVEVYVF9NRVNTQUdFOlxyXG4gICAgICByZXR1cm4gdmFsdWU/LnRleHRNZXNzYWdlICYmIGF1dG9Gb3JtYXQodmFsdWU/LnRleHRNZXNzYWdlKTtcclxuICAgIGRlZmF1bHQ6XHJcbiAgICAgIHJldHVybiBcIlwiO1xyXG4gIH1cclxufTtcclxuXHJcbi8vIGV4cG9ydCBmdW5jdGlvbiBtZXJnZUFycmF5cyguLi5hcnJheXM6IGFueSkge1xyXG4vLyAgICAgbGV0IGpvaW50QXJyYXk6IGFueSA9IFtdO1xyXG5cclxuLy8gICAgIGFycmF5cy5mb3JFYWNoKChhcnJheTogYW55KSA9PiB7XHJcbi8vICAgICAgICAgam9pbnRBcnJheSA9IFsuLi5qb2ludEFycmF5LCAuLi5hcnJheV07XHJcbi8vICAgICB9KTtcclxuLy8gICAgIHJldHVybiBBcnJheS5mcm9tKG5ldyBTZXQoWy4uLmpvaW50QXJyYXldKSk7XHJcbi8vIH1cclxuIiwiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IEZvcm1hdHRlZE1lc3NhZ2UgfSBmcm9tIFwicmVhY3QtaW50bFwiO1xyXG5pbXBvcnQgeyB1c2VGb3JtQ29udGV4dCB9IGZyb20gXCJyZWFjdC1ob29rLWZvcm1cIjtcclxuaW1wb3J0IHsgRm9ybWF0dGVkSFRNTE1lc3NhZ2UgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5cclxuaW50ZXJmYWNlIENvbXBvbmVudFByb3BzIHtcclxuICBsYWJlbDogc3RyaW5nO1xyXG4gIHZhbHVlOiBzdHJpbmc7XHJcbiAgaGFuZGxlQ2hhbmdlOiBGdW5jdGlvbjtcclxuICBzdWJMYWJlbD86IHN0cmluZztcclxuICBjaGVja2VkPzogYm9vbGVhbjtcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IENoZWNrYm94ID0gKHByb3BzOiBDb21wb25lbnRQcm9wcykgPT4ge1xyXG4gIGNvbnN0IHByaXZhdGVQcm9wcyA9IHsgLi4uZGVmYXVsdFByb3BzLCAuLi5wcm9wc307XHJcbiAgY29uc3QgeyBsYWJlbCwgdmFsdWUsIGhhbmRsZUNoYW5nZSwgc3ViTGFiZWwsIGNoZWNrZWQgfSA9IHByaXZhdGVQcm9wcztcclxuICBjb25zdCB7IHJlZ2lzdGVyIH06IGFueSA9IHVzZUZvcm1Db250ZXh0KCk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXhCbG9jayBmbGV4Q29sLXhzIG1hcmdpbi0xNS1ib3R0b21cIj5cclxuICAgICAgPGxhYmVsIGh0bWxGb3I9XCJhZGRpdGlvbmFsUGhvbmVOdW1iZXJcIiBjbGFzc05hbWU9XCJpbnN0YWxsYXRpb24tZm9ybS1sYWJlbFwiPlxyXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInR4dEJvbGQgYmxvY2tcIj48Rm9ybWF0dGVkTWVzc2FnZSBpZD17bGFiZWx9IC8+PC9zcGFuPlxyXG4gICAgICAgIHtzdWJMYWJlbCA/IDxzcGFuIGNsYXNzTmFtZT1cInR4dEl0YWxpYyBibG9jayB0eHROb3JtYWxcIj48Rm9ybWF0dGVkSFRNTE1lc3NhZ2UgaWQ9e3N1YkxhYmVsfSAvPjwvc3Bhbj4gOiBudWxsfVxyXG4gICAgICA8L2xhYmVsPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXhDb2wgbWFyZ2luLTUtdG9wXCI+XHJcbiAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImdyYXBoaWNhbF9jdHJsIGdyYXBoaWNhbF9jdHJsX2NoZWNrYm94IHR4dE5vcm1hbFwiPlxyXG4gICAgICAgICAgPEZvcm1hdHRlZEhUTUxNZXNzYWdlIGlkPXt2YWx1ZSArIFwiX0xBQkVMXCJ9IC8+XHJcbiAgICAgICAgICA8aW5wdXQgdHlwZT1cImNoZWNrYm94XCIgcmVmPXtyZWdpc3Rlcn0gaWQ9e2xhYmVsfSBuYW1lPXtsYWJlbH0gZGVmYXVsdENoZWNrZWQ9e2NoZWNrZWR9IG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlQ2hhbmdlKGUpfSAvPlxyXG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiY3RybF9lbGVtZW50IGNoa19yYWRpdXNcIj48L3NwYW4+XHJcbiAgICAgICAgPC9sYWJlbD5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuY29uc3QgZGVmYXVsdFByb3BzID0ge1xyXG4gIGNoZWNrZWQ6IGZhbHNlXHJcbn07XHJcbiIsImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBGb3JtYXR0ZWRNZXNzYWdlIH0gZnJvbSBcInJlYWN0LWludGxcIjtcclxuaW1wb3J0IHsgdXNlRm9ybUNvbnRleHQgfSBmcm9tIFwicmVhY3QtaG9vay1mb3JtXCI7XHJcblxyXG5pbnRlcmZhY2UgQ29tcG9uZW50UHJvcHMge1xyXG4gIGxhYmVsOiBzdHJpbmc7XHJcbiAgdmFsdWU6IHN0cmluZztcclxuICBoYW5kbGVDaGFuZ2U6IEZ1bmN0aW9uO1xyXG4gIGNoZWNrZWQ/OiBib29sZWFuO1xyXG4gIHJlcXVpcmVkSW5wdXQ/OiBib29sZWFuO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgUmFkaW9CdG4gPSAocHJvcHM6IENvbXBvbmVudFByb3BzKSA9PiB7XHJcbiAgY29uc3QgcHJpdmF0ZVByb3BzOiBDb21wb25lbnRQcm9wcyA9IHsgLi4uZGVmYXVsdFByb3BzLCAuLi5wcm9wc307XHJcbiAgY29uc3QgeyBsYWJlbCwgdmFsdWUsIGhhbmRsZUNoYW5nZSwgY2hlY2tlZCwgcmVxdWlyZWRJbnB1dCB9ID0gcHJpdmF0ZVByb3BzO1xyXG4gIGNvbnN0IHsgcmVnaXN0ZXIgfTogYW55ID0gdXNlRm9ybUNvbnRleHQoKTtcclxuXHJcbiAgcmV0dXJuICBsYWJlbCA/IDxsYWJlbCBjbGFzc05hbWU9XCJncmFwaGljYWxfY3RybCBwb2ludGVyIGN0cmxfcmFkaW9CdG4gbWFyZ2luLTEwLWJvdHRvbVwiPlxyXG4gICAgPGxhYmVsIGNsYXNzTmFtZT1cInR4dEJvbGQgYmxvY2tcIiBodG1sRm9yPXtgb3B0aW9uXyR7dmFsdWV9YH0+PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9e3ZhbHVlfSAvPjwvbGFiZWw+XHJcbiAgICA8aW5wdXRcclxuICAgICAgdHlwZT1cInJhZGlvXCJcclxuICAgICAgaWQ9e2BvcHRpb25fJHt2YWx1ZX1gfVxyXG4gICAgICByZWY9e3JlZ2lzdGVyKHsgcmVxdWlyZWQ6IHJlcXVpcmVkSW5wdXR9KX1cclxuICAgICAgbmFtZT17bGFiZWx9XHJcbiAgICAgIHZhbHVlPXt2YWx1ZX1cclxuICAgICAgY2hlY2tlZD17Y2hlY2tlZH1cclxuICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVDaGFuZ2UoZSl9XHJcbiAgICAvPlxyXG4gICAgPHNwYW4gY2xhc3NOYW1lPVwiY3RybF9lbGVtZW50XCI+PC9zcGFuPlxyXG5cclxuICAgIHsvKiBTaG93IHRvcCBhcnJvdyBhYm92ZSB0aGUgY2FsZW5kYXIsIHdoZW4gb3RoZXIgaXMgc2VsZWN0ZWQqL31cclxuICAgIHsgdmFsdWUgPT09IFwiT1RIRVJcIiAmJiBjaGVja2VkID09PSB0cnVlID8gPHNwYW4gY2xhc3NOYW1lPVwidG9wQXJyb3cgdGV4dC1sZWZ0IG90aGVyT3B0aW9uXCIgYXJpYS1oaWRkZW49XCJ0cnVlXCI+PC9zcGFuPiA6IG51bGwgfVxyXG4gIDwvbGFiZWw+IDogbnVsbDtcclxufTtcclxuXHJcbmNvbnN0IGRlZmF1bHRQcm9wcyA9IHtcclxuICBjaGVja2VkOiBmYWxzZSxcclxuICByZXF1aXJlZElucHV0OiBmYWxzZVxyXG59O1xyXG4iLCJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgRm9ybWF0dGVkTWVzc2FnZSB9IGZyb20gXCJyZWFjdC1pbnRsXCI7XHJcbmltcG9ydCB7IHVzZUZvcm1Db250ZXh0IH0gZnJvbSBcInJlYWN0LWhvb2stZm9ybVwiO1xyXG5pbXBvcnQgeyBGb3JtYXR0ZWRIVE1MTWVzc2FnZSB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcblxyXG5pbnRlcmZhY2UgQ29tcG9uZW50UHJvcHMge1xyXG4gIGxhYmVsOiBzdHJpbmc7XHJcbiAgcmVxdWlyZWQ/OiBib29sZWFuO1xyXG4gIHZhbHVlPzogc3RyaW5nO1xyXG4gIHN1YkxhYmVsPzogc3RyaW5nO1xyXG4gIGhhbmRsZUNoYW5nZTogRnVuY3Rpb247XHJcbiAgcmVxdWlyZWRJbnB1dD86IGJvb2xlYW47XHJcbiAgbWF4TGVuZ3RoPzogbnVtYmVyO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgVGV4dEFyZWEgPSAocHJvcHM6IENvbXBvbmVudFByb3BzKSA9PiB7XHJcbiAgY29uc3QgcHJpdmF0ZVByb3BzID0geyAuLi5kZWZhdWx0UHJvcHMsIC4uLnByb3BzIH07XHJcbiAgY29uc3QgeyBsYWJlbCwgcmVxdWlyZWQsIHZhbHVlLCBzdWJMYWJlbCwgaGFuZGxlQ2hhbmdlLCByZXF1aXJlZElucHV0LCBtYXhMZW5ndGggfSA9IHByaXZhdGVQcm9wcztcclxuICBjb25zdCB7IHJlZ2lzdGVyIH06IGFueSA9IHVzZUZvcm1Db250ZXh0KCk7XHJcbiAgY29uc3QgW2NyQ291bnQsIHNldENvdW50XSA9IFJlYWN0LnVzZVN0YXRlKFxyXG4gICAgKG1heExlbmd0aCB8fCAwKSAtICh2YWx1ZSB8fCBcIlwiKS5sZW5ndGhcclxuICApO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4QmxvY2sgZmxleENvbC14cyBtYXJnaW4tMTUtYm90dG9tXCI+XHJcbiAgICAgIDxsYWJlbCBodG1sRm9yPXtsYWJlbH0gY2xhc3NOYW1lPVwiaW5zdGFsbGF0aW9uLWZvcm0tbGFiZWxcIj5cclxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0eHRCb2xkXCI+PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9e2xhYmVsfSAvPjwvc3Bhbj5cclxuICAgICAgICB7cmVxdWlyZWQgPyA8c3BhbiBjbGFzc05hbWU9XCJ0eHROb3JtYWxcIj4ob3B0aW9uYWwpPC9zcGFuPiA6IFwiXCJ9XHJcbiAgICAgICAge3N1YkxhYmVsID8gPHNwYW4gY2xhc3NOYW1lPVwidHh0SXRhbGljIGJsb2NrIHR4dE5vcm1hbFwiPjxGb3JtYXR0ZWRIVE1MTWVzc2FnZSBpZD17c3ViTGFiZWx9IC8+PC9zcGFuPiA6IG51bGx9XHJcbiAgICAgIDwvbGFiZWw+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleENvbFwiPlxyXG4gICAgICAgIDx0ZXh0YXJlYVxyXG4gICAgICAgICAgcmVmPXtyZWdpc3Rlcih7IHJlcXVpcmVkOiByZXF1aXJlZElucHV0IH0pfVxyXG4gICAgICAgICAgaWQ9e2xhYmVsfVxyXG4gICAgICAgICAgbmFtZT17bGFiZWx9XHJcbiAgICAgICAgICBkZWZhdWx0VmFsdWU9e3ZhbHVlfVxyXG4gICAgICAgICAgbWF4TGVuZ3RoPXttYXhMZW5ndGh9XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJicmYzLXRleHRhcmVhIGZvcm0tY29udHJvbFwiXHJcbiAgICAgICAgICBvbkNoYW5nZT17KGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxUZXh0QXJlYUVsZW1lbnQ+KSA9PiB7XHJcbiAgICAgICAgICAgIHNldENvdW50KFxyXG4gICAgICAgICAgICAgIChtYXhMZW5ndGggfHwgMCkgLSAoZS5jdXJyZW50VGFyZ2V0LnZhbHVlIHx8IFwiXCIpLmxlbmd0aFxyXG4gICAgICAgICAgICApO1xyXG4gICAgICAgICAgICBoYW5kbGVDaGFuZ2UoZSk7XHJcbiAgICAgICAgICB9fT5cclxuICAgICAgICA8L3RleHRhcmVhPlxyXG4gICAgICAgIDxwPlxyXG4gICAgICAgICAgPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9e2xhYmVsICsgXCJfREVTQ1JJUFRJT05cIn0gdmFsdWVzPXt7IG1heDogbWF4TGVuZ3RoLCBjb3VudDogY3JDb3VudCB9fSAvPlxyXG4gICAgICAgIDwvcD5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuY29uc3QgZGVmYXVsdFByb3BzID0ge1xyXG4gIHJlcXVpcmVkOiBmYWxzZSxcclxuICByZXF1aXJlZElucHV0OiBmYWxzZSxcclxuICB2YWx1ZTogXCJcIixcclxuICBzdWJMYWJlbDogXCJcIlxyXG59O1xyXG4iLCJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgdXNlRm9ybUNvbnRleHQgfSBmcm9tIFwicmVhY3QtaG9vay1mb3JtXCI7XHJcbmltcG9ydCB7IEZvcm1hdHRlZE1lc3NhZ2UgfSBmcm9tIFwicmVhY3QtaW50bFwiO1xyXG5pbXBvcnQgeyBMb2NhbGl6YXRpb24gfSBmcm9tIFwiLi4vLi4vLi4vTG9jYWxpemF0aW9uXCI7XHJcbmltcG9ydCB7IEZvcm1hdHRlZEhUTUxNZXNzYWdlIH0gZnJvbSBcIm9tZi1jaGFuZ2VwYWNrYWdlLWNvbXBvbmVudHNcIjtcclxuXHJcbmludGVyZmFjZSBDb21wb25lbnRQcm9wcyB7XHJcbiAgbGFiZWw6IHN0cmluZztcclxuICBzdWJMYWJlbD86IHN0cmluZztcclxuICBoYW5kbGVDaGFuZ2U6IEZ1bmN0aW9uO1xyXG4gIGV4dGVudGlvbj86IHN0cmluZztcclxuICBvcHRpb25hbEV4dGVuc3Rpb24/OiBib29sZWFuO1xyXG4gIHJlcXVpcmVkSW5wdXQ/OiBib29sZWFuO1xyXG4gIHJlcXVpcmVkUGF0dGVybj86IHN0cmluZztcclxuICBjb250YWluZXJDbGFzcz86IHN0cmluZztcclxuICB2YWx1ZT86IHN0cmluZztcclxuICBzdWJWYWx1ZT86IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IFRleHRJbnB1dDogYW55ID0gKHByb3BzOiBDb21wb25lbnRQcm9wcykgPT4ge1xyXG4gIGNvbnN0IHByaXZhdGVQcm9wcyA9IHsgLi4uZGVmYXVsdFByb3BzLCAuLi5wcm9wcyB9O1xyXG4gIGNvbnN0IHsgbGFiZWwsIHN1YkxhYmVsLCBoYW5kbGVDaGFuZ2UsIGNvbnRhaW5lckNsYXNzLCBleHRlbnRpb24sIG9wdGlvbmFsRXh0ZW5zdGlvbiwgcmVxdWlyZWRJbnB1dCwgcmVxdWlyZWRQYXR0ZXJuLCB2YWx1ZSwgc3ViVmFsdWUgfSA9IHByaXZhdGVQcm9wcztcclxuICBjb25zdCB7IHJlZ2lzdGVyLCBlcnJvcnMgfTogYW55ID0gdXNlRm9ybUNvbnRleHQoKTtcclxuXHJcbiAgY29uc3QgZ2V0QXJpYUxhYmVsID0gKGxhYmVsOiBzdHJpbmcpID0+IHtcclxuICAgIHN3aXRjaCAobGFiZWwpIHtcclxuICAgICAgY2FzZSBcIlRFTEVQSE9ORV9GT1JNQVRcIjpcclxuICAgICAgY2FzZSBcIlBSRUZFUkVEX1BIT05FX0ZPUk1BVFwiOlxyXG4gICAgICBjYXNlIFwiUFJFRkVSRURfVEVYVF9NRVNTQUdFX0ZPUk1BVFwiOlxyXG4gICAgICBjYXNlIFwiUGhvbmVfRk9STUFUXCI6XHJcbiAgICAgIGNhc2UgXCJUZXh0TWVzc2FnZV9GT1JNQVRcIjpcclxuICAgICAgICByZXR1cm4gTG9jYWxpemF0aW9uLmdldExvY2FsaXplZFN0cmluZyhcIlRFTEVQSE9ORV9GT1JNQVRfQVJJQVwiKTtcclxuICAgICAgZGVmYXVsdDpcclxuICAgICAgICByZXR1cm4gTG9jYWxpemF0aW9uLmdldExvY2FsaXplZFN0cmluZyhsYWJlbCk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleEJsb2NrIGZsZXhDb2wteHMgbWFyZ2luLTE1LWJvdHRvbSBmbGV4V3JhcCAke2NvbnRhaW5lckNsYXNzfWB9PlxyXG4gICAgICA8bGFiZWwgaHRtbEZvcj1cImFkZGl0aW9uYWxQaG9uZU51bWJlclwiIGNsYXNzTmFtZT17YGluc3RhbGxhdGlvbi1mb3JtLWxhYmVsICR7cmVxdWlyZWRJbnB1dCA/IFwiZm9ybS1yZXF1aXJlZFwiIDogXCJcIn0gJHtlcnJvcnMgJiYgZXJyb3JzW2xhYmVsXSA/IFwiZXJyb3JcIiA6IFwiXCJ9YH0+XHJcbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidHh0Qm9sZCBibG9ja1wiPjxGb3JtYXR0ZWRNZXNzYWdlIGlkPXtsYWJlbH0gLz48L3NwYW4+XHJcbiAgICAgICAge3N1YkxhYmVsID8gPHNwYW4gY2xhc3NOYW1lPVwidHh0SXRhbGljIGJsb2NrIHR4dE5vcm1hbFwiIGFyaWEtbGFiZWw9e2dldEFyaWFMYWJlbChzdWJMYWJlbCl9PjxGb3JtYXR0ZWRIVE1MTWVzc2FnZSBpZD17c3ViTGFiZWx9IC8+PC9zcGFuPiA6IG51bGx9XHJcbiAgICAgIDwvbGFiZWw+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleENvbCByZWxhdGl2ZSAke2Vycm9ycyAmJiBlcnJvcnNbbGFiZWxdID8gXCJoYXMtZXJyb3JcIiA6IFwiXCJ9YH0+XHJcbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidG9wQXJyb3cgdGV4dC1sZWZ0IGhpZGVcIiBhcmlhLWhpZGRlbj1cInRydWVcIj48L3NwYW4+XHJcbiAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICByZWY9e3JlZ2lzdGVyKHsgcmVxdWlyZWQ6IHJlcXVpcmVkSW5wdXQsIHBhdHRlcm46IHJlcXVpcmVkUGF0dGVybiB9KX1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cImJyZjMtdmlyZ2luLWZvcm0taW5wdXQgZm9ybS1jb250cm9sXCJcclxuICAgICAgICAgIGlkPXtsYWJlbH1cclxuICAgICAgICAgIG5hbWU9e2xhYmVsfVxyXG4gICAgICAgICAgdGl0bGU9e2xhYmVsfVxyXG4gICAgICAgICAgZGVmYXVsdFZhbHVlPXt2YWx1ZX1cclxuICAgICAgICAgIG9uQmx1cj17aGFuZGxlQ2hhbmdlIGFzIGFueX1cclxuICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlQ2hhbmdlKGUpfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICAge2Vycm9ycyAmJiBlcnJvcnNbbGFiZWxdID8gPHNwYW4gY2xhc3NOYW1lPVwiZXJyb3IgbWFyZ2luLTUtdG9wXCI+XHJcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ2aXJnaW4taWNvbiBpY29uLXdhcm5pbmcgbWFyZ2luLTE1LXJpZ2h0XCIgYXJpYS1oaWRkZW49e3RydWV9PlxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ2b2x0LWljb24gcGF0aDFcIj48L3NwYW4+PHNwYW4gY2xhc3NOYW1lPVwidm9sdC1pY29uIHBhdGgyXCI+PC9zcGFuPlxyXG4gICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidHh0U2l6ZTEyXCI+PEZvcm1hdHRlZEhUTUxNZXNzYWdlIGlkPXtlcnJvcnNbbGFiZWxdLnR5cGUgIT09IFwicGF0dGVyblwiID8gYElOTElORV9FUlJPUl9yZXF1aXJlZGAgOiBgSU5MSU5FX0VSUk9SXyR7bGFiZWx9X3BhdHRlcm5gfSAvPjwvc3Bhbj5cclxuICAgICAgICA8L3NwYW4+IDogbnVsbH1cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIHtcclxuICAgICAgICBleHRlbnRpb24gPyA8ZGl2IGNsYXNzTmFtZT1cImZsZXhDb2wgYnJmMy12aXJnaW4tZm9ybS1zdWJJbnB1dCBmaWxsLXNtXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXhCbG9jayBmbGV4Q29sLXhzXCI+XHJcbiAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwiZXh0ZW5zaW9uXCIgY2xhc3NOYW1lPVwiaW5zdGFsbGF0aW9uLWZvcm0tbGFiZWxcIj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0eHRCb2xkIGJsb2NrXCI+PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9e2V4dGVudGlvbn0gLz48L3NwYW4+XHJcbiAgICAgICAgICAgICAge29wdGlvbmFsRXh0ZW5zdGlvbiA/IDxzcGFuIGNsYXNzTmFtZT1cInR4dEl0YWxpYyBibG9jayB0eHROb3JtYWxcIj48Rm9ybWF0dGVkTWVzc2FnZSBpZD1cIk9QVElPTkFMX0xBQkVMXCIgLz48L3NwYW4+IDogbnVsbH1cclxuICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4Q29sXCI+XHJcbiAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICByZWY9e3JlZ2lzdGVyfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnJmMy12aXJnaW4tZm9ybS1pbnB1dCBmb3JtLWNvbnRyb2xcIlxyXG4gICAgICAgICAgICAgICAgaWQ9e2V4dGVudGlvbn1cclxuICAgICAgICAgICAgICAgIG5hbWU9e2V4dGVudGlvbn1cclxuICAgICAgICAgICAgICAgIHRpdGxlPXtleHRlbnRpb259XHJcbiAgICAgICAgICAgICAgICBtYXhMZW5ndGg9ezEwfVxyXG4gICAgICAgICAgICAgICAgZGVmYXVsdFZhbHVlPXtzdWJWYWx1ZX1cclxuICAgICAgICAgICAgICAgIG9uQmx1cj17aGFuZGxlQ2hhbmdlIGFzIGFueX1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlQ2hhbmdlKGUpfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+IDogbnVsbFxyXG4gICAgICB9XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuY29uc3QgZGVmYXVsdFByb3BzID0ge1xyXG4gIHJlcXVpcmVkSW5wdXQ6IGZhbHNlLFxyXG4gIHJlcXVpcmVkUGF0dGVybjogLy4qL2ksXHJcbiAgY29udGFpbmVyQ2xhc3M6IFwiXCIsXHJcbiAgdmFsdWU6IFwiXCIsXHJcbiAgc3ViVmFsdWU6IFwiXCJcclxufTtcclxuIiwiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IE1vZGVscyB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcbmltcG9ydCB7IEZvcm1hdHRlZE1lc3NhZ2UgfSBmcm9tIFwicmVhY3QtaW50bFwiO1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBJRmllbGRzZXRQcm9wcyBleHRlbmRzIE1vZGVscy5JQmFzZUNvbXBvbmVudFByb3BzIHtcclxuICBsZWdlbmRBZGRpdGlvbmFsQ2xhc3M/OiBzdHJpbmc7XHJcbiAgbGVnZW5kOiBzdHJpbmcgfCBmYWxzZTtcclxuICBhY2Nlc3NpYmxlTGVnZW5kPzogYm9vbGVhbjtcclxuICByZXF1aXJlZD86IGJvb2xlYW47XHJcbiAgYWRkaXRpb25hbENsYXNzPzogc3RyaW5nO1xyXG4gIGNoaWxkcmVuPzogYW55O1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIElGaWVsZHNldENvbXBvbmVudCBleHRlbmRzIFJlYWN0LkZDPElGaWVsZHNldFByb3BzPiB7XHJcbn1cclxuXHJcbmNvbnN0IExlZ2VuZDogSUZpZWxkc2V0Q29tcG9uZW50ID0gKHsgbGVnZW5kLCByZXF1aXJlZCwgYWNjZXNzaWJsZUxlZ2VuZCwgbGVnZW5kQWRkaXRpb25hbENsYXNzIH0pID0+IGxlZ2VuZCA/IDxsZWdlbmQgY2xhc3NOYW1lPXtgaW5zdGFsbGF0aW9uLWZvcm0tbGFiZWwgJHtyZXF1aXJlZCA/IFwiZm9ybS1yZXF1aXJlZFwiIDogXCJcIn0gJHthY2Nlc3NpYmxlTGVnZW5kID8gXCJzci1vbmx5XCIgOiBcIlwifSAke2xlZ2VuZEFkZGl0aW9uYWxDbGFzc31gfT5cclxuICA8Rm9ybWF0dGVkTWVzc2FnZSBpZD17bGVnZW5kfSAvPlxyXG48L2xlZ2VuZD4gOiBudWxsO1xyXG5cclxuZXhwb3J0IGNvbnN0IEZpZWxkc2V0OiBJRmllbGRzZXRDb21wb25lbnQgPSAoe1xyXG4gIGNsYXNzTmFtZSwgY2hpbGRyZW4sIGxlZ2VuZCwgYWNjZXNzaWJsZUxlZ2VuZCwgbGVnZW5kQWRkaXRpb25hbENsYXNzLCByZXF1aXJlZCwgYWRkaXRpb25hbENsYXNzXHJcbn0pID0+IDxmaWVsZHNldCBjbGFzc05hbWU9e2BtYXJnaW4tMTUtYm90dG9tICR7Y2xhc3NOYW1lfWB9PlxyXG4gIHthY2Nlc3NpYmxlTGVnZW5kID9cclxuICAgIDw+XHJcbiAgICAgIDxMZWdlbmQgbGVnZW5kPXtsZWdlbmR9IHJlcXVpcmVkPXtyZXF1aXJlZH0gYWNjZXNzaWJsZUxlZ2VuZD17YWNjZXNzaWJsZUxlZ2VuZH0gbGVnZW5kQWRkaXRpb25hbENsYXNzPXtsZWdlbmRBZGRpdGlvbmFsQ2xhc3N9IC8+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvPiA6XHJcbiAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXhCbG9jayBmbGV4Q29sLXhzICR7YWRkaXRpb25hbENsYXNzfWB9PlxyXG4gICAgICA8TGVnZW5kIGxlZ2VuZD17bGVnZW5kfSByZXF1aXJlZD17cmVxdWlyZWR9IGFjY2Vzc2libGVMZWdlbmQ9e2FjY2Vzc2libGVMZWdlbmR9IGxlZ2VuZEFkZGl0aW9uYWxDbGFzcz17bGVnZW5kQWRkaXRpb25hbENsYXNzfSAvPlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L2Rpdj59XHJcbjwvZmllbGRzZXQ+O1xyXG5cclxuIiwiZXhwb3J0ICogZnJvbSBcIi4vQ2hlY2tib3hcIjtcclxuZXhwb3J0ICogZnJvbSBcIi4vUmFkaW9CdG5cIjtcclxuZXhwb3J0ICogZnJvbSBcIi4vVGV4dEFyZWFcIjtcclxuZXhwb3J0ICogZnJvbSBcIi4vVGV4dElucHV0XCI7XHJcbmV4cG9ydCAqIGZyb20gXCIuL0ZpZWxkc2V0XCI7XHJcbiIsImltcG9ydCB7IENvbXBvbmVudHMgLCBGb3JtYXR0ZWRIVE1MTWVzc2FnZSB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBGb3JtYXR0ZWRNZXNzYWdlIH0gZnJvbSBcInJlYWN0LWludGxcIjtcclxuaW1wb3J0IHsgSUVycm9yc0xpc3QgfSBmcm9tIFwiLi4vLi4vLi4vbW9kZWxzXCI7XHJcblxyXG5cclxuZXhwb3J0IGVudW0gRUJhbm5lckljb25zIHsgLy8gTmVlZCB0byBiZSB1cGRhdGVkIGFmdGVyIEJSRjMgaXMgZG9uZVxyXG4gIEVSUk9SID0gXCJpY29uLXdhcm5pbmdcIixcclxuICBOT1RFID0gXCJpY29uLWluZm9cIixcclxuICBWQUxJREFUSU9OID0gXCJpY29uLUJpZ19jaGVja19jb25maXJtXCIsXHJcbiAgSU5GTyA9IFwiaWNvbi1CSUdfV0FSTklOR1wiXHJcbn1cclxuXHJcbmludGVyZmFjZSBDb21wb25lbnRQcm9wcyB7XHJcbiAgaWNvblR5cGU/OiBFQmFubmVySWNvbnMgfCBzdHJpbmc7XHJcbiAgaGVhZGluZzogc3RyaW5nO1xyXG4gIG1lc3NhZ2U/OiBzdHJpbmc7XHJcbiAgbWVzc2FnZXM/OiBBcnJheTxJRXJyb3JzTGlzdD47XHJcbiAgaWNvblNpemVDU1M/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBCYW5uZXIgPSAocHJvcHM6IENvbXBvbmVudFByb3BzKSA9PiB7XHJcbiAgY29uc3QgcHJpdmF0ZVByb3BzID0geyAuLi5kZWZhdWx0UHJvcHMsIC4uLnByb3BzIH07XHJcbiAgY29uc3QgeyBpY29uVHlwZSwgaGVhZGluZywgbWVzc2FnZSwgbWVzc2FnZXMsIGljb25TaXplQ1NTIH0gPSBwcml2YXRlUHJvcHM7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8Q29tcG9uZW50cy5Db250YWluZXI+XHJcbiAgICAgIDxDb21wb25lbnRzLlBhbmVsIGNsYXNzTmFtZT17YGZsZXhCbG9jayBwYWQtMjAtbGVmdCBwYWQtMjAtcmlnaHQgcGFkLTI1LXRvcCBwYWQtMjUtYm90dG9tIG1hcmdpbi0xNS1ib3R0b20gdHh0QmxhY2tgfT5cclxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B2aXJnaW4taWNvbiAke2ljb25UeXBlfSAke2ljb25TaXplQ1NTfSB0eHRTaXplMzZgfSBhcmlhLWhpZGRlbj17dHJ1ZX0+PHNwYW4gY2xhc3NOYW1lPXtgdmlyZ2luLWljb24gcGF0aDFgfT48L3NwYW4+PHNwYW4gY2xhc3NOYW1lPVwidmlyZ2luLWljb24gcGF0aDJcIj48L3NwYW4+PC9zcGFuPlxyXG4gICAgICAgIDxkaXYgaWQ9XCJJc3RhbGxhdGlvbk1lc3NhZ2VCYW5uZXJcIiBjbGFzc05hbWU9XCJmbGV4Q29sIHBhZC0xNS1sZWZ0IGNvbnRlbnQtd2lkdGggdmFsaWduLXRvcCBwYWQtMC14c1wiPlxyXG4gICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInZpcmdpblVsdHJhUmVnIHR4dFNpemUxOCB0eHREYXJrR3JleTEgbm8tbWFyZ2luLXRvcCB0eHRVcHBlcmNhc2VcIj48Rm9ybWF0dGVkSFRNTE1lc3NhZ2UgaWQ9e2hlYWRpbmd9IC8+PC9oND5cclxuICAgICAgICAgIHtcclxuICAgICAgICAgICAgbWVzc2FnZSA/IDxwIGNsYXNzTmFtZT1cInR4dFNpemUxNCB0eHRHcmF5NEEgc2Fucy1zZXJpZiBuby1tYXJnaW5cIj48Rm9ybWF0dGVkSFRNTE1lc3NhZ2UgaWQ9e21lc3NhZ2V9IC8+PC9wPiA6IG51bGxcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHtcclxuICAgICAgICAgICAgbWVzc2FnZXMgPyA8dWw+XHJcbiAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgbWVzc2FnZXMgJiYgbWVzc2FnZXMubWFwKG1lc3NhZ2UgPT4gPGxpIGNsYXNzTmFtZT1cImVycm9yXCI+PGEgaWQ9e2BtZXNzYWdlXyR7bWVzc2FnZS5pZH1gfSBocmVmPXtgIyR7bWVzc2FnZS5pZH1gfSBjbGFzc05hbWU9XCJ0eHRSZWQgdHh0Qm9sZCB0eHRVbmRlcmxpbmVcIiB0aXRsZT17bWVzc2FnZS5pZH0+PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9e21lc3NhZ2UuaWR9IC8+PC9hPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0eHREYXJrR3JleVwiPiZuYnNwOy0mbmJzcDt7bWVzc2FnZS5lcnJvciA9PT0gXCJyZXF1aXJlZFwiID8gPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9XCJJTkxJTkVfRVJST1JfcmVxdWlyZWRcIiAvPiA6IDxGb3JtYXR0ZWRNZXNzYWdlIGlkPXtcIklOTElORV9FUlJPUl9cIiArIG1lc3NhZ2UuaWQgKyBcIl9cIiArIG1lc3NhZ2UuZXJyb3J9IC8+fTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvbGk+KVxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgPC91bD4gOiBudWxsXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvQ29tcG9uZW50cy5QYW5lbD5cclxuICAgIDwvQ29tcG9uZW50cy5Db250YWluZXI+XHJcbiAgKTtcclxufTtcclxuXHJcbmNvbnN0IGRlZmF1bHRQcm9wcyA9IHtcclxuICBpY29uVHlwZTogRUJhbm5lckljb25zLklORk8sXHJcbiAgaWNvblNpemVDU1M6IFwidHh0U2l6ZTM2XCJcclxufTtcclxuIiwiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IEZvcm1hdHRlZE1lc3NhZ2UgfSBmcm9tIFwicmVhY3QtaW50bFwiO1xyXG5pbXBvcnQgeyBGb3JtYXR0ZWRIVE1MTWVzc2FnZSB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcblxyXG5leHBvcnQgZW51bSBIZWFkaW5nVGFncyB7XHJcbiAgSDEgPSBcImgxXCIsXHJcbiAgSDIgPSBcImgyXCIsXHJcbiAgSDMgPSBcImgzXCIsXHJcbiAgSDQgPSBcImg0XCIsXHJcbiAgSDUgPSBcImg1XCIsXHJcbiAgSDYgPSBcImg2XCJcclxufVxyXG5cclxuaW50ZXJmYWNlIENvbXBvbmVudFByb3BzIHtcclxuICB0YWc/OiBIZWFkaW5nVGFncztcclxuICBhZGRpdGlvbmFsQ2xhc3M/OiBzdHJpbmc7XHJcbiAgY29udGVudDogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgSGVhZGluZyA9IChwcm9wczogQ29tcG9uZW50UHJvcHMpID0+IHtcclxuICBjb25zdCBwcml2YXRlUHJvcHMgPSB7IC4uLmRlZmF1bHRQcm9wcywgLi4ucHJvcHN9O1xyXG4gIGNvbnN0IHsgdGFnLCBhZGRpdGlvbmFsQ2xhc3MsIGNvbnRlbnQsIGRlc2NyaXB0aW9uIH0gPSBwcml2YXRlUHJvcHM7XHJcbiAgY29uc3QgVGFnID0gdGFnIHx8IFwiaDJcIjtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxUYWcgY2xhc3NOYW1lPXtgdmlyZ2luVWx0cmEgdHh0QmxhY2sgdHh0Q2FwaXRhbCBub01hcmdpbiAke2FkZGl0aW9uYWxDbGFzc31gfT5cclxuICAgICAgICA8Rm9ybWF0dGVkTWVzc2FnZSBpZD17Y29udGVudH0gLz5cclxuICAgICAgPC9UYWc+XHJcbiAgICAgIHtcclxuICAgICAgICBkZXNjcmlwdGlvbiA/IDw+XHJcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzcGFjZXIxMCBjb2wteHMtMTIgY2xlYXJcIj48L3NwYW4+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJub01hcmdpblwiPjxGb3JtYXR0ZWRIVE1MTWVzc2FnZSBpZD17ZGVzY3JpcHRpb259IC8+PC9wPlxyXG4gICAgICAgIDwvPiA6IG51bGxcclxuICAgICAgfVxyXG4gICAgPC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmNvbnN0IGRlZmF1bHRQcm9wcyA9IHtcclxuICBhZGRpdGlvbmFsQ2xhc3M6IFwiXCIsXHJcbiAgZGVzY3JpcHRpb246IFwiXCJcclxufTtcclxuIiwiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IENvbXBvbmVudHMgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgeyBIZWFkaW5nLCBIZWFkaW5nVGFncyB9IGZyb20gXCIuL0hlYWRpbmdcIjtcclxuaW1wb3J0IHsgQmFubmVyLCBFQmFubmVySWNvbnMgfSBmcm9tIFwiLi9CYW5uZXJcIjtcclxuaW1wb3J0IHsgZ2V0TWVzc2FnZXNMaXN0IH0gZnJvbSBcIi4uLy4uLy4uL3V0aWxzL0FwcG9pbnRtZW50VXRpbHNcIjtcclxuXHJcbmludGVyZmFjZSBDb21wb25lbnRQcm9wcyB7XHJcbiAgZXJyb3JzOiBhbnk7XHJcbn1cclxuXHJcbmludGVyZmFjZSBDb21wb25lbnRTdGF0ZSB7fVxyXG5cclxuZXhwb3J0IGNsYXNzIEhlYWRlciBleHRlbmRzIFJlYWN0LlB1cmVDb21wb25lbnQ8Q29tcG9uZW50UHJvcHMsIENvbXBvbmVudFN0YXRlPiB7XHJcbiAgY29uc3RydWN0b3IocHJvcHM6IGFueSkge1xyXG4gICAgc3VwZXIocHJvcHMpO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBoZWFkaW5nUHJvcHMgPSB7XHJcbiAgICB0YWc6IEhlYWRpbmdUYWdzLkgyLFxyXG4gICAgY2xhc3NOYW1lczogXCJ0eHRTaXplMjggdHh0U2l6ZTI0LXhzXCIsXHJcbiAgICBjb250ZW50OiBcIklOU1RBTExBVElPTl9QQUdFX0hFQURJTkdcIixcclxuICB9O1xyXG5cclxuICByZW5kZXIoKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8PlxyXG4gICAgICAgIDxDb21wb25lbnRzLkNvbnRhaW5lcj5cclxuICAgICAgICAgIDxDb21wb25lbnRzLkJSRjNDb250YWluZXI+XHJcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNwYWNlcjUgZmxleCBjb2wtMTJcIj48L3NwYW4+XHJcbiAgICAgICAgICAgIDxIZWFkaW5nIHsuLi50aGlzLmhlYWRpbmdQcm9wc30gLz5cclxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3BhY2VyMjUgZmxleCBjb2wtMTIgY2xlYXJcIj48L3NwYW4+XHJcbiAgICAgICAgICA8L0NvbXBvbmVudHMuQlJGM0NvbnRhaW5lcj5cclxuICAgICAgICA8L0NvbXBvbmVudHMuQ29udGFpbmVyPlxyXG4gICAgICAgIDxCYW5uZXIgaWNvblR5cGU9e0VCYW5uZXJJY29ucy5JTkZPfSBoZWFkaW5nPXtcIklOU1RBTExBVElPTl9IRUFESU5HXCJ9IG1lc3NhZ2U9e1wiSU5TVEFMTEFUSU9OX01FU1NBR0VcIn0gLz5cclxuICAgICAgICB7T2JqZWN0LmtleXModGhpcy5wcm9wcy5lcnJvcnMpLmxlbmd0aCA/IChcclxuICAgICAgICAgIDxCYW5uZXIgaWNvblR5cGU9e0VCYW5uZXJJY29ucy5FUlJPUn0gaGVhZGluZz17XCJFUlJPUlNfSEVBRElOR1wifSBtZXNzYWdlcz17Z2V0TWVzc2FnZXNMaXN0KHRoaXMucHJvcHMuZXJyb3JzKX0gLz5cclxuICAgICAgICApIDogbnVsbH1cclxuICAgICAgPC8+XHJcbiAgICApO1xyXG4gIH1cclxufVxyXG4iLCJleHBvcnQgKiBmcm9tIFwiLi9CYW5uZXJcIjtcclxuZXhwb3J0ICogZnJvbSBcIi4vSGVhZGluZ1wiO1xyXG5leHBvcnQgKiBmcm9tIFwiLi9IZWFkZXJcIjtcclxuIiwiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IHVzZUZvcm1Db250ZXh0IH0gZnJvbSBcInJlYWN0LWhvb2stZm9ybVwiO1xyXG5pbXBvcnQgeyB1c2VTZWxlY3RvciB9IGZyb20gXCJyZWFjdC1yZWR1eFwiO1xyXG5pbXBvcnQgeyBJQWRkaXRpb25hbERldGFpbHMsIElDb250YWN0SW5mb3JtYXRpb24sIElTdG9yZVN0YXRlIH0gZnJvbSBcIi4uLy4uLy4uL21vZGVsc1wiO1xyXG5pbXBvcnQgeyBFQ29udGFjdE1ldGhvZCB9IGZyb20gXCIuLi8uLi8uLi9tb2RlbHMvRW51bXNcIjtcclxuaW1wb3J0IHsgYXV0b0Zvcm1hdCwgZW1haWxSZWdleCwgZmlsdGVyTnVtYmVycywgZm9ybWF0dGVkUGhvbmVSZWdleCwgZ2V0UHJpbWFyeVZhbHVlLCBtYXBFbnVtIH0gZnJvbSBcIi4uLy4uLy4uL3V0aWxzL0FwcG9pbnRtZW50VXRpbHNcIjtcclxuaW1wb3J0IHsgQ2hlY2tib3gsIEZpZWxkc2V0LCBSYWRpb0J0biwgVGV4dEFyZWEsIFRleHRJbnB1dCB9IGZyb20gXCIuLi9Gb3JtRWxlbWVudHNcIjtcclxuaW1wb3J0IHsgSGVhZGluZywgSGVhZGluZ1RhZ3MgfSBmcm9tIFwiLi4vSGVhZGVyXCI7XHJcblxyXG5leHBvcnQgY29uc3QgQ29udGFjdEluZm9ybWF0aW9uID0gKCkgPT4ge1xyXG4gIGNvbnN0IGNvbnRhY3RJbmZvcm1hdGlvbjogSUNvbnRhY3RJbmZvcm1hdGlvbiB8IHVuZGVmaW5lZCA9IHVzZVNlbGVjdG9yKChzdGF0ZTogSVN0b3JlU3RhdGUpID0+IHN0YXRlPy5jb250YWN0SW5mb3JtYXRpb24pO1xyXG4gIGNvbnN0IGFkZGl0aW9uYWxEZXRhaWxzOiBJQWRkaXRpb25hbERldGFpbHMgfCB1bmRlZmluZWQgPSB1c2VTZWxlY3Rvcigoc3RhdGU6IElTdG9yZVN0YXRlKSA9PiBzdGF0ZT8uYWRkaXRpb25hbERldGFpbHMpO1xyXG4gIGNvbnN0IFtjb250YWN0TWV0aG9kLCBzZXRDb250YWN0TWV0aG9kXSA9IFJlYWN0LnVzZVN0YXRlKEVDb250YWN0TWV0aG9kLlBIT05FKTtcclxuICBjb25zdCB7IHNldFZhbHVlIH0gPSB1c2VGb3JtQ29udGV4dCgpO1xyXG5cclxuICBjb25zdCBoYW5kbGVDaGFuZ2UgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcclxuICAgIGNvbnN0IHsgdmFsdWUsIG5hbWUgfSA9IGUudGFyZ2V0O1xyXG4gICAgLy8gQmFzZWQgb24gVmFsdWVcclxuICAgIHN3aXRjaCAodmFsdWUpIHtcclxuICAgICAgY2FzZSBFQ29udGFjdE1ldGhvZC5QSE9ORTpcclxuICAgICAgY2FzZSBFQ29udGFjdE1ldGhvZC5FTUFJTDpcclxuICAgICAgY2FzZSBFQ29udGFjdE1ldGhvZC5URVhUX01FU1NBR0U6XHJcbiAgICAgICAgc2V0Q29udGFjdE1ldGhvZCh2YWx1ZSk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQmFzZWQgb24gTmFtZVxyXG4gICAgc3dpdGNoIChuYW1lKSB7XHJcbiAgICAgIGNhc2UgRUNvbnRhY3RNZXRob2QuUEhPTkUgKyBcIl9MQUJFTFwiOlxyXG4gICAgICBjYXNlIEVDb250YWN0TWV0aG9kLlRFWFRfTUVTU0FHRSArIFwiX0xBQkVMXCI6XHJcbiAgICAgIGNhc2UgXCJBRERJVElPTkFMX1BIT05FX05VTUJFUlwiOlxyXG4gICAgICAgIHNldFZhbHVlKG5hbWUsIGF1dG9Gb3JtYXQodmFsdWUpLCB0cnVlKTtcclxuICAgICAgICAvLyB0aGlzLnByb3BzLnNldEVycm9yKEVDb250YWN0TWV0aG9kLlBIT05FICsgXCJfTEFCRUxcIiwgXCJtYXhMZW5ndGhcIik7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgRUNvbnRhY3RNZXRob2QuUEhPTkUgKyBcIl9FWFRcIjpcclxuICAgICAgY2FzZSBcIkFERElUSU9OQUxfUEhPTkVfRVhUXCI6XHJcbiAgICAgICAgc2V0VmFsdWUobmFtZSwgZmlsdGVyTnVtYmVycyh2YWx1ZSksIHRydWUpO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBjYXNlIFwiU1VQRVJJTlRFTkRBTlRfUEhPTkVcIjpcclxuICAgICAgICBzZXRWYWx1ZShuYW1lLCBhdXRvRm9ybWF0KHZhbHVlKSwgdHJ1ZSk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgRUNvbnRhY3RNZXRob2QuRU1BSUwgKyBcIl9MQUJFTFwiOlxyXG4gICAgICAgIHNldFZhbHVlKG5hbWUsIHZhbHVlLCB0cnVlKTtcclxuICAgICAgICBicmVhaztcclxuICAgICAgZGVmYXVsdDpcclxuICAgICAgICBicmVhaztcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgc2V0Q29udGFjdE1ldGhvZChjb250YWN0SW5mb3JtYXRpb24/LnByZWZlcnJlZENvbnRhY3RNZXRob2QgPyBjb250YWN0SW5mb3JtYXRpb24ucHJlZmVycmVkQ29udGFjdE1ldGhvZCA6IEVDb250YWN0TWV0aG9kLlBIT05FIGFzIGFueSk7XHJcbiAgfSwgW2NvbnRhY3RJbmZvcm1hdGlvbl0pO1xyXG5cclxuICBjb25zdCBoZWFkaW5nUHJvcHMgPSB7XHJcbiAgICB0YWc6IEhlYWRpbmdUYWdzLkgyLFxyXG4gICAgYWRkaXRpb25hbENsYXNzOiBcInR4dFNpemUyMiB0eHRTaXplMjQteHNcIixcclxuICAgIGNvbnRlbnQ6IFwiQ09OVEFDVF9JTkZPUk1BVElPTlwiXHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWFyZ2luLTMwLWJvdHRvbVwiIGlkPVwic2VjdGlvbjJcIj5cclxuICAgICAgPEhlYWRpbmcgey4uLmhlYWRpbmdQcm9wc30gLz5cclxuICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3BhY2VyMTAgdmlzaWJsZS14c1wiPjwvc3Bhbj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwYWQtMjUtdG9wIG5vLXBhZC14c1wiPlxyXG4gICAgICAgIDxGaWVsZHNldFxyXG4gICAgICAgICAgbGVnZW5kPXtcIlBSRUZFUkVEX01FVEhPRF9PRl9DT05UQUNUXCJ9XHJcbiAgICAgICAgICByZXF1aXJlZD17dHJ1ZX1cclxuICAgICAgICAgIGFkZGl0aW9uYWxDbGFzcz17XCJmbGV4V3JhcFwifVxyXG4gICAgICAgICAgYWNjZXNzaWJsZUxlZ2VuZD17ZmFsc2V9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4Q29sIGxpbmVIZWlnaHQxOFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlcjE1IHZpc2libGUteHNcIj48L2Rpdj5cclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgIG1hcEVudW0oRUNvbnRhY3RNZXRob2QsIChpdGVtOiBFQ29udGFjdE1ldGhvZCkgPT5cclxuICAgICAgICAgICAgICAgIDxSYWRpb0J0blxyXG4gICAgICAgICAgICAgICAgICBsYWJlbD17XCJQUkVGRVJFRF9NRVRIT0RfT0ZfQ09OVEFDVFwifVxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17aXRlbX1cclxuICAgICAgICAgICAgICAgICAgaGFuZGxlQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2l0ZW0gPT09IGNvbnRhY3RNZXRob2R9XHJcbiAgICAgICAgICAgICAgICAvPilcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICB7XHJcbiAgICAgICAgICAgIG1hcEVudW0oRUNvbnRhY3RNZXRob2QsIChpdGVtOiBFQ29udGFjdE1ldGhvZCkgPT4gPFRleHRJbnB1dFxyXG4gICAgICAgICAgICAgIHJlcXVpcmVkSW5wdXQ9e2NvbnRhY3RNZXRob2QgPT09IGl0ZW19XHJcbiAgICAgICAgICAgICAgbGFiZWw9e2l0ZW0gKyBcIl9MQUJFTFwifVxyXG4gICAgICAgICAgICAgIGNvbnRhaW5lckNsYXNzPXtgc3ViLW9wdGlvbiBmbGV4LXdyYXAgJHtpdGVtID09PSBjb250YWN0TWV0aG9kID8gXCJzaG93XCIgOiBcImhpZGVcIn1gfVxyXG4gICAgICAgICAgICAgIHN1YkxhYmVsPXtpdGVtICsgXCJfRk9STUFUXCJ9XHJcbiAgICAgICAgICAgICAgZXh0ZW50aW9uPXtpdGVtID09PSBFQ29udGFjdE1ldGhvZC5QSE9ORSA/IGl0ZW0gKyBcIl9FWFRcIiA6IGZhbHNlfVxyXG4gICAgICAgICAgICAgIG9wdGlvbmFsRXh0ZW5zdGlvbj17dHJ1ZX1cclxuICAgICAgICAgICAgICByZXF1aXJlZFBhdHRlcm49e2l0ZW0gPT09IEVDb250YWN0TWV0aG9kLkVNQUlMID8gZW1haWxSZWdleCA6IGZvcm1hdHRlZFBob25lUmVnZXh9XHJcbiAgICAgICAgICAgICAgdmFsdWU9e2dldFByaW1hcnlWYWx1ZShpdGVtLCBjb250YWN0SW5mb3JtYXRpb24pfVxyXG4gICAgICAgICAgICAgIHN1YlZhbHVlPXtjb250YWN0SW5mb3JtYXRpb24/LnByaW1hcnlQaG9uZT8ucGhvbmVFeHRlbnNpb259XHJcbiAgICAgICAgICAgICAgaGFuZGxlQ2hhbmdlPXsoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IGhhbmRsZUNoYW5nZShlKX1cclxuICAgICAgICAgICAgLz4pXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgPC9GaWVsZHNldD5cclxuICAgICAgICA8RmllbGRzZXRcclxuICAgICAgICAgIGxlZ2VuZD17XCJBRERJVElPTkFMX1BIT05FX05VTUJFUlwifVxyXG4gICAgICAgICAgcmVxdWlyZWQ9e2ZhbHNlfVxyXG4gICAgICAgICAgYWNjZXNzaWJsZUxlZ2VuZD17dHJ1ZX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgIHJlcXVpcmVkSW5wdXQ9e2ZhbHNlfVxyXG4gICAgICAgICAgICBsYWJlbD17XCJBRERJVElPTkFMX1BIT05FX05VTUJFUlwifVxyXG4gICAgICAgICAgICBzdWJMYWJlbD17XCJURUxFUEhPTkVfRk9STUFUXCJ9XHJcbiAgICAgICAgICAgIGV4dGVudGlvbj17XCJBRERJVElPTkFMX1BIT05FX0VYVFwifVxyXG4gICAgICAgICAgICBvcHRpb25hbEV4dGVuc3Rpb249e3RydWV9XHJcbiAgICAgICAgICAgIHJlcXVpcmVkUGF0dGVybj17Zm9ybWF0dGVkUGhvbmVSZWdleH1cclxuICAgICAgICAgICAgdmFsdWU9e2NvbnRhY3RJbmZvcm1hdGlvbj8uYWRkaXRpb25hbFBob25lPy5waG9uZU51bWJlcn1cclxuICAgICAgICAgICAgc3ViVmFsdWU9e2NvbnRhY3RJbmZvcm1hdGlvbj8uYWRkaXRpb25hbFBob25lPy5waG9uZUV4dGVuc2lvbn1cclxuICAgICAgICAgICAgaGFuZGxlQ2hhbmdlPXsoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IGhhbmRsZUNoYW5nZShlKX1cclxuICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgPFRleHRJbnB1dFxyXG4gICAgICAgICAgICBsYWJlbD17XCJBUFBBUlRNRU5UXCJ9XHJcbiAgICAgICAgICAgIHZhbHVlPXthZGRpdGlvbmFsRGV0YWlscz8uYXBhcnRtZW50fVxyXG4gICAgICAgICAgICBoYW5kbGVDaGFuZ2U9eyhlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4gaGFuZGxlQ2hhbmdlKGUpfVxyXG4gICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgIGxhYmVsPXtcIkVOVFJZX0NPREVcIn1cclxuICAgICAgICAgICAgdmFsdWU9e2FkZGl0aW9uYWxEZXRhaWxzPy5lbnRyeUNvZGV9XHJcbiAgICAgICAgICAgIGhhbmRsZUNoYW5nZT17KGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiBoYW5kbGVDaGFuZ2UoZSl9XHJcbiAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgIDxUZXh0SW5wdXRcclxuICAgICAgICAgICAgbGFiZWw9e1wiU1VQRVJJTlRFTkRBTlRfTkFNRVwifVxyXG4gICAgICAgICAgICB2YWx1ZT17YWRkaXRpb25hbERldGFpbHM/LnN1cGVyaW50ZW5kYW50TmFtZX1cclxuICAgICAgICAgICAgaGFuZGxlQ2hhbmdlPXsoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IGhhbmRsZUNoYW5nZShlKX1cclxuICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgPFRleHRJbnB1dFxyXG4gICAgICAgICAgICBsYWJlbD17XCJTVVBFUklOVEVOREFOVF9QSE9ORVwifVxyXG4gICAgICAgICAgICByZXF1aXJlZFBhdHRlcm49e2Zvcm1hdHRlZFBob25lUmVnZXh9XHJcbiAgICAgICAgICAgIHZhbHVlPXthZGRpdGlvbmFsRGV0YWlscz8uc3VwZXJpbnRlbmRhbnRQaG9uZX1cclxuICAgICAgICAgICAgaGFuZGxlQ2hhbmdlPXsoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IGhhbmRsZUNoYW5nZShlKX1cclxuICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgPENoZWNrYm94XHJcbiAgICAgICAgICAgIGxhYmVsPXtcIklORk9STUVEX1NVUEVSSU5URU5EQU5UXCJ9XHJcbiAgICAgICAgICAgIHZhbHVlPXtcIllFU1wifVxyXG4gICAgICAgICAgICBjaGVja2VkPXthZGRpdGlvbmFsRGV0YWlscz8uaW5mb3JtZWRTdXBlcmludGVuZGFudH1cclxuICAgICAgICAgICAgaGFuZGxlQ2hhbmdlPXsoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IGhhbmRsZUNoYW5nZShlKX1cclxuICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgPFRleHRBcmVhXHJcbiAgICAgICAgICAgIGxhYmVsPXtcIlNQRUNJQUxfSU5TVFJVQ1RJT05TXCJ9XHJcbiAgICAgICAgICAgIHN1YkxhYmVsPXtcIlNQRUNJQUxfSU5TVFJVQ1RJT05TX1NVQkxBQkVMXCJ9XHJcbiAgICAgICAgICAgIHZhbHVlPXthZGRpdGlvbmFsRGV0YWlscz8uc3BlY2lhbEluc3RydWN0aW9uc31cclxuICAgICAgICAgICAgbWF4TGVuZ3RoPXsyMDB9XHJcbiAgICAgICAgICAgIGhhbmRsZUNoYW5nZT17KGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiBoYW5kbGVDaGFuZ2UoZSl9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvRmllbGRzZXQ+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuIiwiZXhwb3J0ICogZnJvbSBcIi4vQ29udGFjdEluZm9ybWF0aW9uXCI7XHJcbiIsImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBGb3JtYXR0ZWRNZXNzYWdlLCBGb3JtYXR0ZWREYXRlIH0gZnJvbSBcInJlYWN0LWludGxcIjtcclxuLy8gaW1wb3J0IHsgdXNlU2VsZWN0b3IgfSBmcm9tIFwicmVhY3QtcmVkdXhcIjtcclxuaW1wb3J0IHsgSUF2YWlsYWJsZURhdGVzIH0gZnJvbSBcIi4uLy4uLy4uLy4uL21vZGVsc1wiO1xyXG5pbXBvcnQgeyB1c2VGb3JtQ29udGV4dCB9IGZyb20gXCJyZWFjdC1ob29rLWZvcm1cIjtcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgSURhdGVBbmRUaW1lUHJvcHMge1xyXG4gIGhhbmRsZUNoYW5nZTogRnVuY3Rpb247XHJcbiAgcHJlZmVycmVkRGF0ZTogSUF2YWlsYWJsZURhdGVzO1xyXG4gIGNoZWNrZWQ6IG51bGwgfCBJQXZhaWxhYmxlRGF0ZXMgfCB0cnVlO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgRGF0ZUFuZFRpbWU6IFJlYWN0LkZ1bmN0aW9uQ29tcG9uZW50PElEYXRlQW5kVGltZVByb3BzPiA9IFJlYWN0Lm1lbW8oKHByb3BzOiBhbnkpID0+IHtcclxuICBjb25zdCB7IGhhbmRsZUNoYW5nZSwgcHJlZmVycmVkRGF0ZSwgY2hlY2tlZCB9ID0gcHJvcHMsXHJcbiAgICB7IHJlZ2lzdGVyIH0gPSB1c2VGb3JtQ29udGV4dCgpO1xyXG5cclxuICByZXR1cm4gPD5cclxuICAgIDxsYWJlbCBpZD17XCJkYXRlQW5kVGltZVwiICsgcHJlZmVycmVkRGF0ZS5kYXRlfSBjbGFzc05hbWU9XCJncmFwaGljYWxfY3RybCBwb2ludGVyIGN0cmxfcmFkaW9CdG4gbWFyZ2luLTEwLWJvdHRvbVwiPlxyXG4gICAgICA8aW5wdXRcclxuICAgICAgICB0eXBlPVwicmFkaW9cIlxyXG4gICAgICAgIHJlZj17cmVnaXN0ZXIoeyByZXF1aXJlZDogdHJ1ZSB9KX1cclxuICAgICAgICBpZD17XCJ0aW1lT3B0aW9uXCIgKyBwcmVmZXJyZWREYXRlLmRhdGV9XHJcbiAgICAgICAgbmFtZT1cImRhdGVBbmRUaW1lXCJcclxuICAgICAgICB2YWx1ZT17SlNPTi5zdHJpbmdpZnkocHJlZmVycmVkRGF0ZSl9XHJcbiAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVDaGFuZ2UoZSl9XHJcbiAgICAgICAgY2hlY2tlZD17Y2hlY2tlZC5kYXRlID09PSBwcmVmZXJyZWREYXRlLmRhdGV9XHJcbiAgICAgIC8+XHJcbiAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayBuby1tYXJnaW5cIiBodG1sRm9yPXtcInRpbWVPcHRpb25cIiArIHByZWZlcnJlZERhdGUuZGF0ZX0+XHJcbiAgICAgICAge0Jvb2xlYW4ocHJlZmVycmVkRGF0ZS5kYXRlKSA/XHJcbiAgICAgICAgICA8Rm9ybWF0dGVkRGF0ZSB2YWx1ZT17cHJlZmVycmVkRGF0ZS5kYXRlIGFzIHN0cmluZ30geWVhcj1cIm51bWVyaWNcIiB3ZWVrZGF5PVwibG9uZ1wiIG1vbnRoPVwibG9uZ1wiIGRheT1cIjItZGlnaXRcIiB0aW1lWm9uZT1cIlVUQ1wiIC8+IDpcclxuICAgICAgICAgIFwiTm8gQXBwb2ludG1lbnQgRGV0YWlsc1wifVxyXG4gICAgICA8L2xhYmVsPlxyXG4gICAgICB7Qm9vbGVhbihwcmVmZXJyZWREYXRlLnRpbWVTbG90cy5sZW5ndGgpID8gPHNwYW4gY2xhc3NOYW1lPVwidHh0Tm9ybWFsIGJsb2NrXCI+PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9e3ByZWZlcnJlZERhdGUudGltZVNsb3RzLmZpbmQoKGl0ZW06IGFueSkgPT4gaXRlbS5pc0F2YWlsYWJsZSk/LmludGVydmFsVHlwZX0gLz48L3NwYW4+IDogbnVsbH1cclxuICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiY3RybF9lbGVtZW50XCI+PC9zcGFuPlxyXG4gICAgPC9sYWJlbD5cclxuICA8Lz47XHJcbn0pO1xyXG4iLCJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgRm9ybWF0dGVkRGF0ZSB9IGZyb20gXCJyZWFjdC1pbnRsXCI7XHJcbmltcG9ydCB7IHN0cmlwVGltZUJpdCB9IGZyb20gXCIuLi8uLi8uLi8uLi91dGlscy9BcHBvaW50bWVudFV0aWxzXCI7XHJcbmltcG9ydCB7IElBdmFpbGFibGVEYXRlcyB9IGZyb20gXCIuLi8uLi8uLi8uLi9tb2RlbHNcIjtcclxuaW1wb3J0IHsgRUR1cmF0aW9uIH0gZnJvbSBcIi4uLy4uLy4uLy4uL21vZGVscy9FbnVtc1wiO1xyXG5pbXBvcnQgeyBGb3JtYXR0ZWRIVE1MTWVzc2FnZSB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcblxyXG5pbnRlcmZhY2UgQ29tcG9uZW50UHJvcHMge1xyXG4gIGF2YWlsYWJsZURhdGVzPzogQXJyYXk8SUF2YWlsYWJsZURhdGVzPjtcclxuICBpbml0U2xpY2tTbGlkZXI6IEZ1bmN0aW9uO1xyXG4gIHNlbGVjdERhdGU6IEZ1bmN0aW9uO1xyXG4gIHNlbGVjdGVkRGF0ZVRpbWU6IElBdmFpbGFibGVEYXRlcztcclxufVxyXG5cclxuZXhwb3J0IGNsYXNzIFRpbWVTbG90cyBleHRlbmRzIFJlYWN0LkNvbXBvbmVudDxDb21wb25lbnRQcm9wcywgYW55PiB7XHJcbiAgc3RhdGljIGRpc3BsYXlOYW1lID0gXCJUaW1lU2xvdHNcIjtcclxuICBjb25zdHJ1Y3Rvcihwcm9wczogQ29tcG9uZW50UHJvcHMpIHtcclxuICAgIHN1cGVyKHByb3BzKTtcclxuICB9XHJcblxyXG4gIGNvbXBvbmVudERpZE1vdW50KCkge1xyXG4gICAgdGhpcy5wcm9wcy5pbml0U2xpY2tTbGlkZXIoKTtcclxuICB9XHJcblxyXG4gIHJlbmRlcigpIHtcclxuICAgIGNvbnN0IHsgYXZhaWxhYmxlRGF0ZXMsIHNlbGVjdERhdGUsIHNlbGVjdGVkRGF0ZVRpbWUgfSA9IHRoaXMucHJvcHM7XHJcblxyXG4gICAgcmV0dXJuIDxkaXYgY2xhc3NOYW1lPVwiZmxleEJsb2NrIG1hcmdpbi0xNS1ib3R0b20gc3ViLW9wdGlvbiByZWxhdGl2ZSB0aW1lc2xvdC1waWNrZXJcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWxlY3QtdGltZXNsb3QgZmlsbFwiPlxyXG5cclxuICAgICAgICB7YXZhaWxhYmxlRGF0ZXMgJiYgYXZhaWxhYmxlRGF0ZXMubWFwKChkYXksIGRheUluZGV4KSA9PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2RheS50aW1lU2xvdHNbMF0uaW50ZXJ2YWxUeXBlID09PSBFRHVyYXRpb24uQWxsRGF5ID8gXCJhbGxEYXlDb250YWluZXJcIiA6IFwiZGF5LWNvbnRhaW5lclwifSA+XHJcbiAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9e1wiZGF5SW5kZXhfXCIgKyBkYXlJbmRleH0gY2xhc3NOYW1lPVwidmlyZ2luVWx0cmEgc2Fucy1zZXJpZi14cyB0eHRCb2xkLXhzIHR4dFNpemUxNiB0eHRCbGFjayBsaW5lSGVpZ2h0MS0zIG1hcmdpbi0xNS1ib3R0b21cIj5cclxuICAgICAgICAgICAgICAgIDxGb3JtYXR0ZWREYXRlIHZhbHVlPXtzdHJpcFRpbWVCaXQoZGF5LmRhdGUgYXMgc3RyaW5nKX0gd2Vla2RheT1cImxvbmdcIiB0aW1lWm9uZT1cIlVUQ1wiIC8+XHJcbiAgICAgICAgICAgICAgICA8YnIgY2xhc3NOYW1lPXtgaGlkZGVuLW1gfSAvPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZC1zbS1ub25lIGQtbWQtbm9uZSBkLWxnLW5vbmUgZC14bC1ub25lXCI+LCA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8Rm9ybWF0dGVkRGF0ZSB2YWx1ZT17c3RyaXBUaW1lQml0KGRheS5kYXRlIGFzIHN0cmluZyl9IHllYXI9XCJudW1lcmljXCIgbW9udGg9XCJzaG9ydFwiIGRheT1cIjItZGlnaXRcIiB0aW1lWm9uZT1cIlVUQ1wiIC8+XHJcbiAgICAgICAgICAgICAgPC9sYWJlbD5cclxuXHJcbiAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cIm5vTWFyZ2luIGxpc3QtdW5zdHlsZWQgdGltZUl0ZW1cIiBhcmlhLWxhYmVsbGVkYnk9XCJtb25kYXlMaXN0XCI+XHJcbiAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgIGRheS50aW1lU2xvdHMubWFwKHRpbWVTbG90ID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBzZWxlY3RlZEludGVydmFsID0gc2VsZWN0ZWREYXRlVGltZS50aW1lU2xvdHNbMF0uaW50ZXJ2YWxUeXBlID09PSB0aW1lU2xvdC5pbnRlcnZhbFR5cGUgJiYgc2VsZWN0ZWREYXRlVGltZS5kYXRlID09PSBkYXkuZGF0ZTtcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gPGxpIGNsYXNzTmFtZT17YHR4dEJsdWUgJHtzZWxlY3RlZEludGVydmFsID8gXCJzZWxlY3RlZFwiIDogXCJcIn1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gaWQ9e2BzbG90XyR7dGltZVNsb3QuaW50ZXJ2YWxUeXBlfWB9IG9uQ2xpY2s9eyhlKSA9PiBzZWxlY3REYXRlKGUsIGRheS5kYXRlLCB0aW1lU2xvdCl9IGNsYXNzTmFtZT17YGJ0biBidG4tbGluayAke3RpbWVTbG90LmludGVydmFsVHlwZSA9PT0gRUR1cmF0aW9uLkFsbERheSA/IFwiZmxleENvbCBmbGV4SnVzdGlmeVwiIDogXCJcIn0gJHt0aW1lU2xvdC5pc0F2YWlsYWJsZSA/IFwiXCIgOiBcImRpc2FibGVkXCJ9ICR7dGltZVNsb3QuaXNTZWxlY3RlZCA/IFwic2VsZWN0ZWRcIiA6IFwiXCJ9YH0gdGFiSW5kZXg9ezB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Rm9ybWF0dGVkSFRNTE1lc3NhZ2UgaWQ9e3RpbWVTbG90LmludGVydmFsVHlwZX0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvbGk+O1xyXG4gICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+O1xyXG4gIH1cclxufVxyXG4iLCJleHBvcnQgKiBmcm9tIFwiLi9EYXRlQW5kVGltZVwiO1xyXG5leHBvcnQgKiBmcm9tIFwiLi9UaW1lU2xvdHNcIjtcclxuIiwiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IEhlYWRpbmcsIEhlYWRpbmdUYWdzIH0gZnJvbSBcIi4uL0hlYWRlclwiO1xyXG5pbXBvcnQgeyBGb3JtYXR0ZWRNZXNzYWdlIH0gZnJvbSBcInJlYWN0LWludGxcIjtcclxuaW1wb3J0IHsgQ29tcG9uZW50cywgVmFsdWVPZiwgRm9ybWF0dGVkSFRNTE1lc3NhZ2UgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgeyBGaWVsZHNldCwgUmFkaW9CdG4gfSBmcm9tIFwiLi4vRm9ybUVsZW1lbnRzXCI7XHJcbmltcG9ydCB7IERhdGVBbmRUaW1lLCBUaW1lU2xvdHMgfSBmcm9tIFwiLi9EYXRlQW5kVGltZVwiO1xyXG5pbXBvcnQgeyBJSW5zdGFsbGF0aW9uQWRkcmVzcywgSUF2YWlsYWJsZURhdGVzLCBJVGltZVNsb3RzIH0gZnJvbSBcIi4uLy4uLy4uL21vZGVsc1wiO1xyXG5pbXBvcnQgeyBnZXRTZWxlY3RlZERhdGUgfSBmcm9tIFwiLi4vLi4vLi4vdXRpbHMvQXBwb2ludG1lbnRVdGlsc1wiO1xyXG5cclxuY29uc3QgeyBWaXNpYmxlIH0gPSBDb21wb25lbnRzO1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBJSW5zdGFsbGF0aW9uUHJvcHMge1xyXG4gIC8vIHByZWZlcnJlZERhdGU/OiBJQXZhaWxhYmxlRGF0ZXM7XHJcbiAgaW5zdGFsbGF0aW9uQWRkcmVzcz86IElJbnN0YWxsYXRpb25BZGRyZXNzO1xyXG4gIGF2YWlsYWJsZURhdGVzPzogQXJyYXk8SUF2YWlsYWJsZURhdGVzPjtcclxuICBkdXJhdGlvbjogYW55O1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIElJbnN0YWxsYXRpb25EaXNwYXRjaGVzIHtcclxuICBpbml0U2xpY2tTbGlkZXI6IEZ1bmN0aW9uO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgSUluc3RhbGxhdGlvblN0YXRlIHtcclxuICBzaG93VGltZVNsb3RzOiBib29sZWFuO1xyXG4gIHNlbGVjdGVkRGF0ZVRpbWU6IElBdmFpbGFibGVEYXRlcyB8IG51bGwgfCBcIk9USEVSXCI7XHJcbiAgc2hvd090aGVyOiBib29sZWFuO1xyXG4gIHByZWZlcnJlZERhdGVzOiBBcnJheTxJQXZhaWxhYmxlRGF0ZXM+O1xyXG59XHJcblxyXG5leHBvcnQgY2xhc3MgQ29tcG9uZW50IGV4dGVuZHMgUmVhY3QuQ29tcG9uZW50PElJbnN0YWxsYXRpb25Qcm9wcyAmIElJbnN0YWxsYXRpb25EaXNwYXRjaGVzLCBJSW5zdGFsbGF0aW9uU3RhdGU+IHtcclxuICBjb25zdHJ1Y3Rvcihwcm9wczogYW55KSB7XHJcbiAgICBzdXBlcihwcm9wcyk7XHJcbiAgICB0aGlzLnN0YXRlID0ge1xyXG4gICAgICBzaG93VGltZVNsb3RzOiBmYWxzZSxcclxuICAgICAgc2VsZWN0ZWREYXRlVGltZTogbnVsbCxcclxuICAgICAgcHJlZmVycmVkRGF0ZXM6IFtdLFxyXG4gICAgICBzaG93T3RoZXI6IHRydWVcclxuICAgIH07XHJcbiAgICB0aGlzLmhhbmRsZUNoYW5nZS5iaW5kKHRoaXMpO1xyXG4gICAgdGhpcy5jaGFuZ2VCdG4uYmluZCh0aGlzKTtcclxuICB9XHJcblxyXG4gIGhhbmRsZUNoYW5nZSA9IChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xyXG4gICAgY29uc3QgeyB2YWx1ZSB9ID0gZS50YXJnZXQ7XHJcbiAgICBzd2l0Y2ggKHZhbHVlKSB7XHJcbiAgICAgIGNhc2UgXCJPVEhFUlwiOlxyXG4gICAgICAgIC8vIElmIHNlbGVjdGlvbiBpcyBvdGhlciBzaG93IHRpbWVzbG90c1xyXG4gICAgICAgIHRoaXMuc2V0U3RhdGUoe1xyXG4gICAgICAgICAgc2hvd1RpbWVTbG90czogdHJ1ZSxcclxuICAgICAgICAgIC8vIHNlbGVjdGVkRGF0ZVRpbWU6IHZhbHVlIGFzIGFueVxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIHRoaXMuc2V0U3RhdGUoe1xyXG4gICAgICAgICAgc2hvd1RpbWVTbG90czogZmFsc2UsXHJcbiAgICAgICAgICBzZWxlY3RlZERhdGVUaW1lOiBKU09OLnBhcnNlKHZhbHVlKSBhcyBhbnlcclxuICAgICAgICB9KTtcclxuICAgICAgICBicmVhaztcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBzZWxlY3REYXRlID0gKGU6IGFueSwgZGF5OiBzdHJpbmcsIGludGVydmFsOiBJVGltZVNsb3RzKSA9PiB7XHJcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XHJcbiAgICAvLyBDbG9uZSBwcmVmZXJlZERhdGVzIE9iamVjdFxyXG4gICAgY29uc3QgbmV3UHJlZmVyZWREYXRlcyA9IFsuLi50aGlzLnN0YXRlLnByZWZlcnJlZERhdGVzXTtcclxuICAgIC8vIENvbXBhcmUgaWYgc2VsZWN0ZWQgZGF0ZSBpcyBub3Qgc2FtZSBhcyBwcmVmZXJlZCBkYXRlIGZyb20gY2FsZW5kYXJcclxuICAgIGlmICh0aGlzLnN0YXRlLnByZWZlcnJlZERhdGVzWzBdLmRhdGUgPT09IGRheSAmJlxyXG4gICAgICB0aGlzLnN0YXRlLnByZWZlcnJlZERhdGVzWzBdLnRpbWVTbG90c1swXS5pbnRlcnZhbFR5cGUgPT09IGludGVydmFsLmludGVydmFsVHlwZSkge1xyXG4gICAgICAvLyBpZiBzYW1lLCBzZWxlY3QgdGhlIGRlZmF1bHQgcHJlZmVyZWQgZGF0ZSBhZ2FpblxyXG4gICAgICB0aGlzLnNldFN0YXRlKHtcclxuICAgICAgICBwcmVmZXJyZWREYXRlczogdGhpcy5zdGF0ZS5wcmVmZXJyZWREYXRlcyxcclxuICAgICAgICBzZWxlY3RlZERhdGVUaW1lOiB0aGlzLnN0YXRlLnByZWZlcnJlZERhdGVzWzBdLFxyXG4gICAgICAgIHNob3dUaW1lU2xvdHM6IGZhbHNlLFxyXG4gICAgICAgIHNob3dPdGhlcjogZmFsc2VcclxuICAgICAgfSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBuZXdQcmVmZXJlZERhdGVzWzFdID0geyBkYXRlOiBkYXksIHRpbWVTbG90czogW3sgLi4uaW50ZXJ2YWwsIGlzU2VsZWN0ZWQ6IHRydWUgfV0gfTtcclxuICAgICAgdGhpcy5zZXRTdGF0ZSh7XHJcbiAgICAgICAgcHJlZmVycmVkRGF0ZXM6IG5ld1ByZWZlcmVkRGF0ZXMsXHJcbiAgICAgICAgc2VsZWN0ZWREYXRlVGltZTogbmV3UHJlZmVyZWREYXRlc1sxXSxcclxuICAgICAgICBzaG93VGltZVNsb3RzOiBmYWxzZSxcclxuICAgICAgICBzaG93T3RoZXI6IGZhbHNlXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNoYW5nZUJ0biA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50PEhUTUxCdXR0b25FbGVtZW50PikgPT4ge1xyXG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgdGhpcy5zZXRTdGF0ZSh7XHJcbiAgICAgIHNob3dPdGhlcjogdHJ1ZSxcclxuICAgICAgc2hvd1RpbWVTbG90czogdHJ1ZSxcclxuICAgICAgLy8gc2VsZWN0ZWREYXRlVGltZTogXCJPVEhFUlwiLFxyXG4gICAgICAvLyBSZW1vdmUgc2Vjb25kIGRhdGUgc2VsZWN0ZWRcclxuICAgICAgcHJlZmVycmVkRGF0ZXM6IFt0aGlzLnN0YXRlLnByZWZlcnJlZERhdGVzWzBdXVxyXG4gICAgfSk7XHJcbiAgfTtcclxuXHJcbiAgY29tcG9uZW50RGlkVXBkYXRlKHByb3BzOiBJSW5zdGFsbGF0aW9uUHJvcHMpIHtcclxuICAgIGlmIChcclxuICAgICAgdGhpcy5wcm9wcy5hdmFpbGFibGVEYXRlcyAmJiB0aGlzLnByb3BzLmF2YWlsYWJsZURhdGVzLmxlbmd0aCAmJiBKU09OLnN0cmluZ2lmeSh0aGlzLnByb3BzLmF2YWlsYWJsZURhdGVzKSAhPT0gSlNPTi5zdHJpbmdpZnkocHJvcHMuYXZhaWxhYmxlRGF0ZXMpXHJcbiAgICApIHtcclxuICAgICAgLy8gbGV0IHByZWZlcnJlZERhdGU6IEFycmF5PElBdmFpbGFibGVEYXRlcz4gPSBnZXRQcmVmZXJlZERhdGVzKHRoaXMucHJvcHMuYXZhaWxhYmxlRGF0ZXMpO1xyXG4gICAgICBjb25zdCBzZWxlY3RlZERhdGU6IEFycmF5PElBdmFpbGFibGVEYXRlcz4gPSBnZXRTZWxlY3RlZERhdGUodGhpcy5wcm9wcy5hdmFpbGFibGVEYXRlcyk7XHJcbiAgICAgIC8vIHByZWZlcnJlZERhdGUgPSBtZXJnZUFycmF5cyguLi5wcmVmZXJyZWREYXRlLCAuLi5zZWxlY3RlZERhdGUpO1xyXG4gICAgICB0aGlzLnNldFN0YXRlKHtcclxuICAgICAgICBwcmVmZXJyZWREYXRlczogc2VsZWN0ZWREYXRlLFxyXG4gICAgICAgIHNlbGVjdGVkRGF0ZVRpbWU6IHNlbGVjdGVkRGF0ZVswXS5kYXRlID8gc2VsZWN0ZWREYXRlWzBdIDogbnVsbCxcclxuICAgICAgICBzaG93T3RoZXI6IHNlbGVjdGVkRGF0ZS5sZW5ndGggPiAxID8gZmFsc2UgOiB0cnVlXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgcmVuZGVyKCkge1xyXG4gICAgY29uc3QgeyBpbnN0YWxsYXRpb25BZGRyZXNzLCBhdmFpbGFibGVEYXRlcywgaW5pdFNsaWNrU2xpZGVyIH0gPSB0aGlzLnByb3BzO1xyXG4gICAgY29uc3QgeyBzaG93VGltZVNsb3RzLCBzZWxlY3RlZERhdGVUaW1lLCBzaG93T3RoZXIsIHByZWZlcnJlZERhdGVzIH0gPSB0aGlzLnN0YXRlO1xyXG4gICAgY29uc3QgaGVhZGluZ1Byb3BzID0ge1xyXG4gICAgICB0YWc6IEhlYWRpbmdUYWdzLkgyLFxyXG4gICAgICBhZGRpdGlvbmFsQ2xhc3M6IFwidHh0U2l6ZTIyIHR4dFNpemUyNC14c1wiLFxyXG4gICAgICBjb250ZW50OiBcIklOU1RBTExBVElPTl9ERVRBSUxTXCIsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiBcIklOU1RBTExBVElPTl9ERVRBSUxTX0RFU0NcIlxyXG4gICAgfTtcclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1hcmdpbi0zMC1ib3R0b21cIiBpZD1cInNlY3Rpb24xXCI+XHJcbiAgICAgICAgPEhlYWRpbmcgey4uLmhlYWRpbmdQcm9wc30gLz5cclxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzcGFjZXIxMCBmbGV4IGNvbC0xMiBjbGVhclwiPjwvc3Bhbj5cclxuICAgICAgICA8cCBjbGFzc05hbWU9XCJub01hcmdpbiB0eHRJdGFsaWNcIj48Rm9ybWF0dGVkTWVzc2FnZSBpZD1cIlJFUVVJUkVEX0lORk9fRkxBR1wiIC8+PC9wPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicGFkLTE1LXRvcFwiPlxyXG4gICAgICAgICAgPEZpZWxkc2V0IGxlZ2VuZD17XCJEQVRFX0FORF9USU1FX0xBQkVMXCJ9IHJlcXVpcmVkPXt0cnVlfSBhY2Nlc3NpYmxlTGVnZW5kPXtmYWxzZX0gYWRkaXRpb25hbENsYXNzPXtcImZsZXgtd3JhcFwifT5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZXIxMCB2aXNpYmxlLXhzXCI+PC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleENvbCBsaW5lSGVpZ2h0MThcIj5cclxuICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICBwcmVmZXJyZWREYXRlcyAmJiBwcmVmZXJyZWREYXRlcy5sZW5ndGggJiYgcHJlZmVycmVkRGF0ZXMubWFwKGRhdGUgPT4gPERhdGVBbmRUaW1lXHJcbiAgICAgICAgICAgICAgICAgIGhhbmRsZUNoYW5nZT17dGhpcy5oYW5kbGVDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgIHByZWZlcnJlZERhdGU9e2RhdGV9XHJcbiAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3Nob3dUaW1lU2xvdHMgfHwgKHNlbGVjdGVkRGF0ZVRpbWUgYXMgSUF2YWlsYWJsZURhdGVzKX1cclxuICAgICAgICAgICAgICAgIC8+KVxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICA8VmlzaWJsZSB3aGVuPXtzaG93T3RoZXJ9XHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17XHJcbiAgICAgICAgICAgICAgICAgIC8qKiBTaG93IENoYW5nZSBidXR0b24gaWYgT3RoZXIgaXMgbm90IHRoZXJlIGFuZCBkYXRlIGlzIHNlbGVjdGVkICovXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicGFkLTM1LWxlZnQgcmVsYXRpdmUgY2hhbmdlQnRuXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBpZD1cIkNIQU5HRV9CVE5cIiBjbGFzc05hbWU9XCJidG4gYnRuLWxpbmsgcGFkLTAgdHh0U2l6ZTE0IHR4dFVuZGVybGluZSB0eHRWaXJnaW5CbHVlXCIgb25DbGljaz17KGUpID0+IHRoaXMuY2hhbmdlQnRuKGUpfT5DaGFuZ2U8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICB9PlxyXG4gICAgICAgICAgICAgICAgey8qKiBTaG93IG90aGVyIGJ1dHRvbiBhbmQgaGlkZSB0aGUgc2Vjb25kIHByZWZlcmVkIGRhdGUgKi99XHJcbiAgICAgICAgICAgICAgICA8UmFkaW9CdG5cclxuICAgICAgICAgICAgICAgICAgaGFuZGxlQ2hhbmdlPXt0aGlzLmhhbmRsZUNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWRJbnB1dD17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17c2hvd1RpbWVTbG90c31cclxuICAgICAgICAgICAgICAgICAgbGFiZWw9e1wiZGF0ZUFuZFRpbWVcIn1cclxuICAgICAgICAgICAgICAgICAgdmFsdWU9e1wiT1RIRVJcIn0gLz5cclxuICAgICAgICAgICAgICA8L1Zpc2libGU+XHJcblxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgIHNob3dUaW1lU2xvdHMgPyA8VGltZVNsb3RzIHNlbGVjdERhdGU9e3RoaXMuc2VsZWN0RGF0ZX0gYXZhaWxhYmxlRGF0ZXM9e2F2YWlsYWJsZURhdGVzfSBpbml0U2xpY2tTbGlkZXI9e2luaXRTbGlja1NsaWRlcn0gc2VsZWN0ZWREYXRlVGltZT17c2VsZWN0ZWREYXRlVGltZSBhcyBJQXZhaWxhYmxlRGF0ZXN9IC8+IDogbnVsbFxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICA8L0ZpZWxkc2V0PlxyXG4gICAgICAgICAgPFZpc2libGUgd2hlbj17Qm9vbGVhbihzZWxlY3RlZERhdGVUaW1lKX0+XHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICBzZWxlY3RlZERhdGVUaW1lICYmIHNlbGVjdGVkRGF0ZVRpbWUgIT09IFwiT1RIRVJcIiA/XHJcbiAgICAgICAgICAgICAgICA8RmllbGRzZXQgbGVnZW5kPXtcIkVTVElNQVRFRF9EVVJBVElPTlwifSByZXF1aXJlZD17ZmFsc2V9IGFjY2Vzc2libGVMZWdlbmQ9e2ZhbHNlfT5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4Q29sXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmxvY2tcIj48Rm9ybWF0dGVkTWVzc2FnZSBpZD17c2VsZWN0ZWREYXRlVGltZS50aW1lU2xvdHNbMF0uZHVyYXRpb259IC8+PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJsb2NrXCI+PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9XCJBUlJJVkFMX09GX1RFQ0hOSUNJQU5cIiAvPjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L0ZpZWxkc2V0PiA6IG51bGxcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgPC9WaXNpYmxlPlxyXG4gICAgICAgICAgPFZpc2libGUgd2hlbj17Qm9vbGVhbihpbnN0YWxsYXRpb25BZGRyZXNzKX0+XHJcbiAgICAgICAgICAgIDxGaWVsZHNldCBsZWdlbmQ9e1wiU0hJUFBJTkdfSU5TVEFMTEFUSU9OX0FERFJFU1NcIn0gcmVxdWlyZWQ9e2ZhbHNlfSBhY2Nlc3NpYmxlTGVnZW5kPXtmYWxzZX0+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4Q29sXCI+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJibG9ja1wiPlxyXG4gICAgICAgICAgICAgICAgICA8VmlzaWJsZSB3aGVuPXtWYWx1ZU9mKGluc3RhbGxhdGlvbkFkZHJlc3MsIFwiYXBhcnRtZW50TnVtYmVyXCIsIGZhbHNlKX0+XHJcbiAgICAgICAgICAgICAgICAgICAge1ZhbHVlT2YoaW5zdGFsbGF0aW9uQWRkcmVzcywgXCJhcGFydG1lbnROdW1iZXJcIiwgXCJcIil9Jm5ic3A7LSZuYnNwO1xyXG4gICAgICAgICAgICAgICAgICA8L1Zpc2libGU+XHJcbiAgICAgICAgICAgICAgICAgIHtWYWx1ZU9mKGluc3RhbGxhdGlvbkFkZHJlc3MsIFwiYWRkcmVzczFcIiwgXCJcIil9Jm5ic3A7XHJcbiAgICAgICAgICAgICAgICAgIHtWYWx1ZU9mKGluc3RhbGxhdGlvbkFkZHJlc3MsIFwiYWRkcmVzczJcIiwgXCJcIil9Jm5ic3A7XHJcbiAgICAgICAgICAgICAgICAgIHtWYWx1ZU9mKGluc3RhbGxhdGlvbkFkZHJlc3MsIFwic3RyZWV0VHlwZVwiLCBcIlwiKX0sJm5ic3A7XHJcbiAgICAgICAgICAgICAgICAgIHtWYWx1ZU9mKGluc3RhbGxhdGlvbkFkZHJlc3MsIFwiY2l0eVwiLCBcIlwiKX0sJm5ic3A7XHJcbiAgICAgICAgICAgICAgICAgIHtWYWx1ZU9mKGluc3RhbGxhdGlvbkFkZHJlc3MsIFwicHJvdmluY2VcIiwgXCJcIil9LCZuYnNwO1xyXG4gICAgICAgICAgICAgICAgICB7VmFsdWVPZihpbnN0YWxsYXRpb25BZGRyZXNzLCBcInBvc3RhbENvZGVcIiwgXCJcIil9XHJcbiAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtYXJnaW4tMTAtdG9wXCI+PEZvcm1hdHRlZEhUTUxNZXNzYWdlIGlkPVwiQ09OVEFDVF9VU19OT1RFXCIgLz48L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvRmllbGRzZXQ+XHJcbiAgICAgICAgICA8L1Zpc2libGU+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcbn1cclxuIiwiaW1wb3J0IHsgQ29tcG9uZW50LCBJSW5zdGFsbGF0aW9uUHJvcHMsIElJbnN0YWxsYXRpb25EaXNwYXRjaGVzIH0gZnJvbSBcIi4vSW5zdGFsbGF0aW9uXCI7XHJcbmltcG9ydCB7IGNvbm5lY3QgfSBmcm9tIFwicmVhY3QtcmVkdXhcIjtcclxuaW1wb3J0IHsgSVN0b3JlU3RhdGUgfSBmcm9tIFwiLi4vLi4vLi4vbW9kZWxzXCI7XHJcbmltcG9ydCB7IGluaXRTbGlja1NsaWRlciB9IGZyb20gXCIuLi8uLi8uLi9zdG9yZVwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IEluc3RhbGxhdGlvbiA9IGNvbm5lY3Q8SUluc3RhbGxhdGlvblByb3BzLCBJSW5zdGFsbGF0aW9uRGlzcGF0Y2hlcz4oXHJcbiAgKHsgaW5zdGFsbGF0aW9uQWRkcmVzcywgYXZhaWxhYmxlRGF0ZXMsIGR1cmF0aW9uICB9OiBJU3RvcmVTdGF0ZSkgPT5cclxuICAgICh7IGluc3RhbGxhdGlvbkFkZHJlc3MsIGF2YWlsYWJsZURhdGVzLCBkdXJhdGlvbiB9KSxcclxuICAoZGlzcGF0Y2gpID0+ICh7XHJcbiAgICBpbml0U2xpY2tTbGlkZXI6ICgpID0+IGRpc3BhdGNoKGluaXRTbGlja1NsaWRlcigpKVxyXG4gIH0pXHJcbikoQ29tcG9uZW50KTtcclxuIiwiZXhwb3J0ICogZnJvbSBcIi4vQ29udGFjdEluZm9ybWF0aW9uXCI7XHJcbmV4cG9ydCAqIGZyb20gXCIuL0Zvcm1FbGVtZW50c1wiO1xyXG5leHBvcnQgKiBmcm9tIFwiLi9IZWFkZXJcIjtcclxuZXhwb3J0ICogZnJvbSBcIi4vSW5zdGFsbGF0aW9uXCI7XHJcbiIsImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyB1c2VGb3JtQ29udGV4dCB9IGZyb20gXCJyZWFjdC1ob29rLWZvcm1cIjtcclxuaW1wb3J0IHsgSW5zdGFsbGF0aW9uLCBDb250YWN0SW5mb3JtYXRpb24gfSBmcm9tIFwiLi9Db21wb25lbmV0c1wiO1xyXG5pbXBvcnQgeyB1c2VEaXNwYXRjaCB9IGZyb20gXCJyZWFjdC1yZWR1eFwiO1xyXG5pbXBvcnQgeyBzZXRBcHBvaW50bWVudCB9IGZyb20gXCIuLi9zdG9yZVwiO1xyXG5cclxubGV0IF9zdWJtaXRGb3JtOiBhbnkgPSBudWxsO1xyXG5cclxuaW50ZXJmYWNlIENvbXBvbmVudFByb3BzIHtcclxufVxyXG5cclxuY29uc3QgRm9ybTogYW55ID0gKHByb3BzOiBDb21wb25lbnRQcm9wcykgPT4ge1xyXG4gIGNvbnN0IHN1Ym1pdFJlZjogYW55ID0gUmVhY3QudXNlUmVmKG51bGwpO1xyXG5cclxuICAvLyBSZWFjdCBIb29rc1xyXG4gIGNvbnN0IHsgaGFuZGxlU3VibWl0IH0gPSB1c2VGb3JtQ29udGV4dCgpO1xyXG4gIGNvbnN0IGRpc3BhdGNoID0gdXNlRGlzcGF0Y2goKTtcclxuXHJcbiAgX3N1Ym1pdEZvcm0gPSAoKSA9PiB7XHJcbiAgICAoc3VibWl0UmVmIGFzIGFueSkuY3VycmVudC5jbGljaygpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGN1c3RvbVN1Ym1pdCA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQ8SFRNTEZvcm1FbGVtZW50PikgPT4ge1xyXG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgaGFuZGxlU3VibWl0KChkYXRhOiBhbnkpID0+IHtcclxuICAgICAgLy8gUmVkdXggRGlzcGF0Y2hcclxuICAgICAgZGlzcGF0Y2goc2V0QXBwb2ludG1lbnQoZGF0YSkpO1xyXG4gICAgfSkoZSk7XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxmb3JtIGlkPVwiQXBwb2ludG1lbnRGb3JtXCIgb25TdWJtaXQ9e2N1c3RvbVN1Ym1pdH0gPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlcjQ1IGhpZGRlbi1tXCI+PC9kaXY+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2VyMjAgZC1ibG9jayBkLXNtLW5vbmVcIj48L2Rpdj5cclxuICAgICAgPEluc3RhbGxhdGlvbiAvPiB7IC8qKiBJbnN0YWxsYXRpb24gZGV0YWlscyBhbmQgQ2FsZW5kcmluZyAqL31cclxuICAgICAgPENvbnRhY3RJbmZvcm1hdGlvbiAvPiB7IC8qKiBDb250YWN0IEZvcm0gQ29tcG9uZW5ldCAqL31cclxuICAgICAgPGJ1dHRvbiByZWY9e3N1Ym1pdFJlZn0gdHlwZT1cInN1Ym1pdFwiIGFyaWEtaGlkZGVuPVwidHJ1ZVwiIHN0eWxlPXt7IGRpc3BsYXk6IFwibm9uZVwiIH19IC8+XHJcbiAgICA8L2Zvcm0gPlxyXG4gICk7XHJcbn07XHJcblxyXG5Gb3JtLnVzZVN1Ym1pdFJlZiA9ICgpOiBhbnkgPT4gX3N1Ym1pdEZvcm07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBGb3JtO1xyXG4iLCJpbXBvcnQgeyBDb21tb25GZWF0dXJlcyB9IGZyb20gXCJid3RrXCI7XHJcbmltcG9ydCB7IEFjdGlvbiB9IGZyb20gXCJyZWR1eC1hY3Rpb25zXCI7XHJcbmltcG9ydCB7IEFjdGlvbnMsIEVXaWRnZXRTdGF0dXMgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgeyBTdG9yZSB9IGZyb20gXCIuL3N0b3JlXCI7XHJcbmltcG9ydCBGb3JtIGZyb20gXCIuL3ZpZXdzL0Zvcm1cIjtcclxuXHJcbmNvbnN0IHsgQmFzZVBpcGUgfSA9IENvbW1vbkZlYXR1cmVzO1xyXG5cclxuLyoqXHJcbiAqIHJ4anMgcGlwZSBwcm92aWRlclxyXG4gKiB0aGlzIGZhc2NpbGl0YXRlcyB0aGUgZGlyZWN0IGNvbm5lY3Rpb25cclxuICogYmV0d2VlbiB3aWRnZXRzIHRocm91Z2ggcnhqcyBPYnNlcnZhYmxlXHJcbiAqIEBleHBvcnRcclxuICogQGNsYXNzIFBpcGVcclxuICogQGV4dGVuZHMge0Jhc2VQaXBlfVxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIFBpcGUgZXh0ZW5kcyBCYXNlUGlwZSB7XHJcbiAgc3RhdGljIFN1YnNjcmlwdGlvbnMoc3RvcmU6IFN0b3JlKSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBbQWN0aW9ucy5vbkNvbnRpbnVlLnRvU3RyaW5nKCldOiAoeyB9OiBBY3Rpb248c3RyaW5nPikgPT4ge1xyXG4gICAgICAgIGNvbnN0IHJlZiA9IEZvcm0udXNlU3VibWl0UmVmKCk7XHJcbiAgICAgICAgcmVmICYmIHJlZigpO1xyXG4gICAgICAgIHN0b3JlLmRpc3BhdGNoKEFjdGlvbnMuc2V0V2lkZ2V0U3RhdHVzKEVXaWRnZXRTdGF0dXMuUkVOREVSRUQpKTtcclxuICAgICAgfSxcclxuICAgIH07XHJcbiAgfVxyXG4gIC8qKlxyXG4gICAgICpDcmVhdGVzIGEgc3RhdGljIGluc3RhbmNlIG9mIFBpcGUuXHJcbiAgICAgKiBAcGFyYW0geyp9IGFyZ1xyXG4gICAgICogQG1lbWJlcm9mIFBpcGVcclxuICAgICAqL1xyXG4gIHN0YXRpYyBpbnN0YW5jZTogUGlwZTtcclxuICBjb25zdHJ1Y3Rvcihhcmc6IGFueSkge1xyXG4gICAgc3VwZXIoYXJnKTtcclxuICAgIFBpcGUuaW5zdGFuY2UgPSB0aGlzO1xyXG4gIH1cclxufVxyXG4iLCJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgdXNlRGlzcGF0Y2ggfSBmcm9tIFwicmVhY3QtcmVkdXhcIjtcclxuaW1wb3J0IHsgQWN0aW9ucywgQ29tcG9uZW50cywgRVdpZGdldE5hbWUgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgeyB1c2VGb3JtQ29udGV4dCB9IGZyb20gXCJyZWFjdC1ob29rLWZvcm1cIjtcclxuaW1wb3J0IHsgSGVhZGVyIH0gZnJvbSBcIi4vQ29tcG9uZW5ldHNcIjtcclxuaW1wb3J0IEZvcm0gZnJvbSBcIi4vRm9ybVwiO1xyXG5cclxuY29uc3Qge1xyXG4gIFJlc3RyaWN0aW9uTW9kYWxcclxufSA9IENvbXBvbmVudHM7XHJcblxyXG5jb25zdCB7XHJcbiAgd2lkZ2V0UmVuZGVyQ29tcGxldGVcclxufSA9IEFjdGlvbnM7XHJcblxyXG5pbnRlcmZhY2UgSUNvbXBvbmVudFByb3BzIHtcclxufVxyXG5cclxuLyoqXHJcbiAqIEhlYWRlciBDb21wb25lbmV0XHJcbiAqIENvbnRhaW5lciBIVE1MXHJcbiAqIEBwYXJhbSBwcm9wc1xyXG4gKi9cclxuXHJcbmV4cG9ydCBjb25zdCBBcHBsaWNhdGlvbiA9IChwcm9wczogSUNvbXBvbmVudFByb3BzKSA9PiB7XHJcbiAgY29uc3QgZGlzcGF0Y2ggPSB1c2VEaXNwYXRjaCgpO1xyXG4gIGNvbnN0IHsgZXJyb3JzIH0gPSB1c2VGb3JtQ29udGV4dCgpO1xyXG5cclxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgZGlzcGF0Y2god2lkZ2V0UmVuZGVyQ29tcGxldGUoRVdpZGdldE5hbWUuQVBQT0lOVE1FTlQpKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIHJldHVybiA8bWFpbiBpZD1cIm1haW5Db250ZW50XCI+XHJcbiAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlcjMwIGNvbC0xMlwiIGFyaWEtaGlkZGVuPVwidHJ1ZVwiPjwvc3Bhbj5cclxuICAgIDxSZXN0cmljdGlvbk1vZGFsIGlkPVwiQVBQT0lOVE1FTlRfUkVTVFJJQ1RJT05fTU9EQUxcIiAvPlxyXG4gICAgPEhlYWRlciBlcnJvcnM9e2Vycm9yc30gLz5cclxuICAgIDxDb21wb25lbnRzLkNvbnRhaW5lcj5cclxuICAgICAgPENvbXBvbmVudHMuUGFuZWwgY2xhc3NOYW1lPVwicGFkLTI1LWxlZnQgcGFkLTI1LXJpZ2h0IGNsZWFyZml4XCI+XHJcbiAgICAgICAgPEZvcm0gLz5cclxuICAgICAgPC9Db21wb25lbnRzLlBhbmVsPlxyXG4gICAgPC9Db21wb25lbnRzLkNvbnRhaW5lcj5cclxuICA8L21haW4+O1xyXG59O1xyXG4iLCJpbXBvcnQgeyBDb21wb25lbnRzIH0gZnJvbSBcIm9tZi1jaGFuZ2VwYWNrYWdlLWNvbXBvbmVudHNcIjtcclxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IEZvcm1Db250ZXh0LCB1c2VGb3JtIH0gZnJvbSBcInJlYWN0LWhvb2stZm9ybVwiO1xyXG5pbXBvcnQgeyBBcHBsaWNhdGlvbiB9IGZyb20gXCIuL3ZpZXdzXCI7XHJcblxyXG5jb25zdCB7XHJcbiAgQXBwbGljYXRpb25Sb290XHJcbn0gPSBDb21wb25lbnRzO1xyXG5cclxuZXhwb3J0IGNvbnN0IEFwcCA9IChwcm9wczogYW55KSA9PiB7XHJcbiAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm0oKTtcclxuICByZXR1cm4gKFxyXG4gICAgPEFwcGxpY2F0aW9uUm9vdD5cclxuICAgICAgPEZvcm1Db250ZXh0IHsuLi5tZXRob2RzfT4geyAvKiogQ3JlYXRlIGNvbnRleHQgZm9yIHJlYWN0LWhvb2stZm9ybSAqL31cclxuICAgICAgICA8QXBwbGljYXRpb24gLz5cclxuICAgICAgPC9Gb3JtQ29udGV4dD5cclxuICAgIDwvQXBwbGljYXRpb25Sb290PlxyXG4gICk7XHJcbn07XHJcbiIsImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgKiBhcyBSZWFjdFJlZHV4IGZyb20gXCJyZWFjdC1yZWR1eFwiO1xyXG5pbXBvcnQgeyBFV2lkZ2V0U3RhdHVzLCBBY3Rpb25zLCBDb250ZXh0UHJvdmlkZXIgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgeyBWaWV3V2lkZ2V0LCBXaWRnZXQsIFBhcmFtc1Byb3ZpZGVyIH0gZnJvbSBcImJ3dGtcIjtcclxuaW1wb3J0IHsgU3RvcmUgfSBmcm9tIFwiLi9zdG9yZVwiO1xyXG5pbXBvcnQgeyBJV2lkZ2V0UHJvcHMgfSBmcm9tIFwiLi9tb2RlbHNcIjtcclxuaW1wb3J0IHsgUGlwZSB9IGZyb20gXCIuL1BpcGVcIjtcclxuaW1wb3J0IHsgQ29uZmlnIH0gZnJvbSBcIi4vQ29uZmlnXCI7XHJcbmltcG9ydCB7IEFwcCB9IGZyb20gXCIuL0FwcFwiO1xyXG5pbXBvcnQgeyBSb290IH0gZnJvbSBcInJlYWN0LWRvbS9jbGllbnRcIjtcclxuXHJcbmNvbnN0IHtcclxuICBzZXRXaWRnZXRQcm9wcyxcclxuICBzZXRXaWRnZXRTdGF0dXNcclxufSA9IEFjdGlvbnM7XHJcbmNvbnN0IFN0b3JlUHJvdmlkZXIgPSBSZWFjdFJlZHV4LlByb3ZpZGVyIGFzIGFueTtcclxuXHJcbkBXaWRnZXQoeyBuYW1lc3BhY2U6IFwiT3JkZXJpbmdcIiB9KVxyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBXaWRnZXRDb250YWluZXIgZXh0ZW5kcyBWaWV3V2lkZ2V0IHtcclxuICBjb25zdHJ1Y3Rvcihwcml2YXRlIHN0b3JlOiBTdG9yZSwgcHJpdmF0ZSBwYXJhbXM6IFBhcmFtc1Byb3ZpZGVyPElXaWRnZXRQcm9wcywgYW55PiwgcHJpdmF0ZSBjb25maWc6IENvbmZpZywgcHJpdmF0ZSBwaXBlOiBQaXBlKSB7XHJcbiAgICBzdXBlcigpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogSW5pdGlhbGl6ZSB3aWRnZXQgZmxvd1xyXG4gICAqIHBsZWFzZSBkbyBub3QgcGxhY2UgYW55IHN0YXJ0dXAgbG9naW4gaW4gaGVyZVxyXG4gICAqIGFsbCBsb2dpYyBzaG91bGQgcmVzaWRlIGluIEVwaWNzLm9uV2lkZ2V0U3RhdHVzRXBpY1xyXG4gICAqIEBtZW1iZXJvZiBXaWRnZXRDb250YWluZXJcclxuICAgKi9cclxuICBpbml0KCkge1xyXG4gICAgdGhpcy5waXBlLnN1YnNjcmliZShQaXBlLlN1YnNjcmlwdGlvbnModGhpcy5zdG9yZSkpO1xyXG4gICAgdGhpcy5zdG9yZS5kaXNwYXRjaChzZXRXaWRnZXRQcm9wcyh0aGlzLmNvbmZpZykpO1xyXG4gICAgdGhpcy5zdG9yZS5kaXNwYXRjaChzZXRXaWRnZXRQcm9wcyh0aGlzLnBhcmFtcy5wcm9wcykpO1xyXG4gICAgdGhpcy5zdG9yZS5kaXNwYXRjaChzZXRXaWRnZXRTdGF0dXMoRVdpZGdldFN0YXR1cy5JTklUKSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBEZWluaXRpYWxpemUgd2lkZ2V0IGZsb3dcclxuICAgKiBEZXN0cm95IGFsbCBsaXN0ZW5lcmVzIGFuZCBjb25uZWN0aW9uc1xyXG4gICAqIEBtZW1iZXJvZiBXaWRnZXRDb250YWluZXJcclxuICAgKi9cclxuICBkZXN0cm95KCkge1xyXG4gICAgdGhpcy5waXBlLnVuc3Vic2NyaWJlKCk7XHJcbiAgICB0aGlzLnN0b3JlLmRlc3Ryb3koKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFJlbmRlciB3aWRnZXRcclxuICAgKiBTZXQgYWxsIGNvbnRleHR1YWwgcHJvdmlkZXJzOlxyXG4gICAqICogQ29udGV4dFByb3ZpZGVyOiB0b3AtbW9zdCB3cmFwcGVyIHVzZWQgdG8gcHJvcGFnYXRlIGFsbCAqaW1tdXRhYmxlKiBzdGF0ZSBwYXJhbXNcclxuICAgKiAqIFN0b3JlUHJvdmlkZXI6IHJlZHV4IHN0b3JlIHdyYXBwZXIgdXNlZCB0byBwcm9wYWdhdGUgYWxsICptdXRhYmxlKiBzdGF0ZSBwYXJhbXNcclxuICAgKiBAcGFyYW0ge0VsZW1lbnR9IHJvb3RcclxuICAgKiBAbWVtYmVyb2YgV2lkZ2V0Q29udGFpbmVyXHJcbiAgICovXHJcbiAgcmVuZGVyKHJvb3Q6IFJvb3QpIHtcclxuICAgIGNvbnN0IHsgc3RvcmUgfSA9IHRoaXM7XHJcbiAgICByb290LnJlbmRlcihcclxuICAgICAgPENvbnRleHRQcm92aWRlciB2YWx1ZT17eyBjb25maWc6IHRoaXMuY29uZmlnIH19PlxyXG4gICAgICAgIDxTdG9yZVByb3ZpZGVyIHsuLi57IHN0b3JlIH19PjxBcHAgLz48L1N0b3JlUHJvdmlkZXI+XHJcbiAgICAgIDwvQ29udGV4dFByb3ZpZGVyPlxyXG4gICAgKTtcclxuICB9XHJcbn1cclxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../src/Widget.tsx\n\n}");

/***/ }),

/***/ "bwtk":
/*!**********************************************************************************!*\
  !*** external {"root":"bwtk","commonjs2":"bwtk","commonjs":"bwtk","amd":"bwtk"} ***!
  \**********************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_bwtk__;

/***/ }),

/***/ "omf-changepackage-components":
/*!********************************************************************************************************************************************************************************!*\
  !*** external {"root":"OMFChangepackageComponents","commonjs2":"omf-changepackage-components","commonjs":"omf-changepackage-components","amd":"omf-changepackage-components"} ***!
  \********************************************************************************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_omf_changepackage_components__;

/***/ }),

/***/ "react":
/*!**************************************************************************************!*\
  !*** external {"root":"React","commonjs2":"react","commonjs":"react","amd":"react"} ***!
  \**************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

/***/ }),

/***/ "react-intl":
/*!*********************************************************************************************************!*\
  !*** external {"root":"ReactIntl","commonjs2":"react-intl","commonjs":"react-intl","amd":"react-intl"} ***!
  \*********************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_intl__;

/***/ }),

/***/ "react-redux":
/*!*************************************************************************************************************!*\
  !*** external {"root":"ReactRedux","commonjs2":"react-redux","commonjs":"react-redux","amd":"react-redux"} ***!
  \*************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_redux__;

/***/ }),

/***/ "redux":
/*!**************************************************************************************!*\
  !*** external {"root":"Redux","commonjs2":"redux","commonjs":"redux","amd":"redux"} ***!
  \**************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux__;

/***/ }),

/***/ "redux-actions":
/*!*********************************************************************************************************************!*\
  !*** external {"root":"ReduxActions","commonjs2":"redux-actions","commonjs":"redux-actions","amd":"redux-actions"} ***!
  \*********************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux_actions__;

/***/ }),

/***/ "redux-observable":
/*!*********************************************************************************************************************************!*\
  !*** external {"root":"ReduxObservable","commonjs2":"redux-observable","commonjs":"redux-observable","amd":"redux-observable"} ***!
  \*********************************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux_observable__;

/***/ }),

/***/ "rxjs":
/*!**********************************************************************************!*\
  !*** external {"root":"rxjs","commonjs2":"rxjs","commonjs":"rxjs","amd":"rxjs"} ***!
  \**********************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_rxjs__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("../src/Widget.tsx");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});