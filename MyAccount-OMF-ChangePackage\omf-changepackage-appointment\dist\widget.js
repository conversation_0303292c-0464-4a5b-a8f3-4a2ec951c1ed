/*! omf-changepackage-appointment (widget) 0.1.0 | bwtk 6.1.0 | 2025-08-22T20:47:54.360Z */
!function(e,t){var n,r;if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("react"),require("react-redux"),require("omf-changepackage-components"),require("bwtk"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs"),require("rxjs/operators"),require("react-intl"));else if("function"==typeof define&&define.amd)define(["react","react-redux","omf-changepackage-components","bwtk","redux","redux-actions","redux-observable","rxjs","rxjs/operators","react-intl"],t);else for(r in n="object"==typeof exports?t(require("react"),require("react-redux"),require("omf-changepackage-components"),require("bwtk"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs"),require("rxjs/operators"),require("react-intl")):t(e.React,e.ReactRedux,e.OMFChangepackageComponents,e.bwtk,e.Redux,e.ReduxActions,e.ReduxObservable,e.rxjs,e.Rx,e.ReactIntl))("object"==typeof exports?exports:e)[r]=n[r]}(self,function(e,t,n,r,a,i,o,s,c,l){return function(){"use strict";function u(e){var t,n=tn[e];return void 0!==n?n.exports:(t=tn[e]={exports:{}},en[e](t,t.exports,u),t.exports)}function d(e,t){function n(){this.constructor=e}if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");C(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function m(e,t,n,r){var a,i,o=arguments.length,s=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(i=e.length-1;i>=0;i--)(a=e[i])&&(s=(o<3?a(s):o>3?a(t,n,s):a(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s}function p(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function f(e,t){var n,r,a,i,o="function"==typeof Symbol&&e[Symbol.iterator];if(!o)return e;n=o.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=n.next()).done;)a.push(r.value)}catch(s){i={error:s}}finally{try{r&&!r.done&&(o=n.return)&&o.call(n)}finally{if(i)throw i.error}}return a}function E(e,t,n){if(n||2===arguments.length)for(var r,a=0,i=t.length;a<i;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))}function g(e,t,n){let r=-1;const a=(e=>!Le(e)&&(dn.test(e)||!un.test(e)))(t)?[t]:(e=>{const t=[];return e.replace(mn,(e,n,r,a)=>{t.push(r?a.replace(pn,"$1"):n||e)}),t})(t),i=a.length,o=i-1;for(;++r<i;){const t=a[r];let i=n;if(r!==o){const n=e[t];i=Pe(n)||Le(n)?n:isNaN(a[r+1])?{}:[]}e[t]=i,e=e[t]}return e}function h(e){return!e||e instanceof HTMLElement&&e.nodeType!==Node.DOCUMENT_NODE&&h(e.parentNode)}function b(e,t){const{type:n,name:r,options:a,value:i,files:o}=t,s=e[r];return He(n)?o:je(n)?s?Fe(s.options).value:"":qe(n)?Be(a):ke(n)?!!s&&Ue(s.options).value:i}function v(e,t,n="validate"){const r=Ye(e);if(r||Qe(e)&&!e)return{type:n,message:r?e:"",ref:t}}async function N(e,t,n){try{return{values:await e.validate(n,{abortEarly:!1}),errors:{}}}catch(r){return{values:{},errors:Me(bn(r,t))}}}function y(e){return e.reduce((e,t)=>e.concat(Le(t)?y(t):t),[])}function O(){return(0,P.useContext)(In)}function A(e){var{children:t,formState:n,errors:r}=e,a=
/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */
function(e,t){var n,r,a={};for(n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]]);return a}(e,["children","formState","errors"]);return(0,P.createElement)(In.Provider,{value:Object.assign(Object.assign({},a),{formState:n,errors:r})},t)}function S(e){var t,n;try{return t=e.split("T"),(n=new Date(t[0])).setMinutes(new Date(e).getMinutes()+new Date(e).getTimezoneOffset()),n.setHours(0),n.setMinutes(0),n}catch(r){return e}}function T(e,t){return Object.keys(e).map(function(t){return e[t]}).map(function(e){return t(e)})}function I(e){var t=x(e);return 10===(t=t.substr(0,10)).length?t.slice(0,3)+"-"+t.slice(3,6)+"-"+t.slice(6):t}function x(e){return e.replace(/\D/g,"")}var D,_,C,R,L,P,M,w,j,k,F,B,H,q,V,U,W,z,G,X,$,J,Y,Z,Q,K,ee,te,ne,re,ae,ie,oe,se,ce,le,ue,de,me,pe,fe,Ee,ge,he,be,ve,Ne,ye,Oe,Ae,Se,Te,Ie,xe,De,_e,Ce,Re,Le,Pe,Me,we,je,ke,Fe,Be,He,qe,Ve,Ue,We,ze,Ge,Xe,$e,Je,Ye,Ze,Qe,Ke,et,tt,nt,rt,at,it,ot,st,ct,lt,ut,dt,mt,pt,ft,Et,gt,ht,bt,vt,Nt,yt,Ot,At,St,Tt,It,xt,Dt,_t,Ct,Rt,Lt,Pt,Mt,wt,jt,kt,Ft,Bt,Ht,qt,Vt,Ut,Wt,zt,Gt,Xt,$t,Jt,Yt,Zt,Qt,Kt,en={102:function(e){e.exports=r},418:function(e){e.exports=s},419:function(e){e.exports=l},442:function(t){t.exports=e},446:function(e){e.exports=n},541:function(e){e.exports=i},750:function(e){e.exports=a},769:function(e){e.exports=o},843:function(e){e.exports=c},999:function(e){e.exports=t}},tn={};u.d=function(e,t){for(var n in t)u.o(t,n)&&!u.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},u.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},u.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},D={},u.r(D),u.d(D,{default:function(){return Kt}}),_={},u.r(_),u.d(_,{contactInformation:function(){return W},getAppointment:function(){return q},getOderDetails:function(){return H},initSlickSlider:function(){return Y},setAdditionalDetails:function(){return X},setAppointment:function(){return V},setAvailableDates:function(){return U},setDuration:function(){return z},setForErrors:function(){return J},setInstallationAddress:function(){return G},setIsInstallationRequired:function(){return $}}),C=function(e,t){return C=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},C(e,t)},R=function(){return R=Object.assign||function(e){var t,n,r,a;for(n=1,r=arguments.length;n<r;n++)for(a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},R.apply(this,arguments)},Object.create,Object.create,L=function(e){return L=Object.getOwnPropertyNames||function(e){var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},L(e)},"function"==typeof SuppressedError&&SuppressedError,P=u(442),M=u(999),w=u(446),j=u(102),k=u(750),F=u(541),B=u(769),H=(0,F.createAction)("GET_ORDER_DETAILS"),q=(0,F.createAction)("GET_APPOINTMENT"),V=(0,F.createAction)("SET_APPOINTMENT"),U=(0,F.createAction)("SET_AVAIALBLE_DATES"),W=(0,F.createAction)("SET_CONTACT_INFO"),z=(0,F.createAction)("SET_DURATION"),G=(0,F.createAction)("SET_INSTALLATION_ADDRESS"),X=(0,F.createAction)("SET_ADDITIONAL_DETAILS"),$=(0,F.createAction)("SET_INSTALLATION_REQUIRED"),J=(0,F.createAction)("SET_FORM_ERRORS"),Y=(0,F.createAction)("INIT_SLICK_SLIDER"),Z=u(418),Q=j.CommonFeatures.BaseConfig,K=j.CommonFeatures.configProperty,ee=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return d(t,e),m([K({}),p("design:type",Object)],t.prototype,"headers",void 0),m([K({}),p("design:type",Object)],t.prototype,"environmentVariables",void 0),m([K({}),p("design:type",Object)],t.prototype,"mockdata",void 0),m([K({base:"http://127.0.0.1:8881",orderDetailsAPI:"/",appointmentAPI:"/",orderSubmitAPI:"/"}),p("design:type",Object)],t.prototype,"api",void 0),m([j.Injectable],t)}(Q),te=function(e){function t(t,n){return e.call(this,t,n)||this}return d(t,e),m([j.Injectable,p("design:paramtypes",[j.AjaxServices,ee])],t)}(w.BaseClient),ne={availableDates:null,duration:"",installationAddress:{address1:"",address2:"",city:"",province:"",postalCode:"",apartmentType:"",apartmentNumber:""},contactInformation:{preferredContactMethod:"",primaryPhone:{phoneNumber:"",phoneExtension:""},mobileNumber:null,additionalPhone:{phoneNumber:"",phoneExtension:""},textMessage:"",email:""},additionalDetails:{apartment:"",entryCode:"",specialInstructions:"",superintendantName:"",superintendantPhone:"",informedSuperintendant:null},isInstallationRequired:null},re=function(){function e(){}return e.create=function(e,t,n){var r,a;return t.installationAddress.address1=n.installationAddress&&n.installationAddress.address1?n.installationAddress.address1:"",t.installationAddress.address2=n.installationAddress&&n.installationAddress.address2?n.installationAddress.address2:"",t.installationAddress.city=n.installationAddress&&n.installationAddress.city?n.installationAddress.city:"",t.installationAddress.postalCode=n.installationAddress&&n.installationAddress.postalCode?n.installationAddress.postalCode:"",t.installationAddress.province=n.installationAddress&&n.installationAddress.province?n.installationAddress.province:"",t.installationAddress.apartmentType=n.installationAddress&&n.installationAddress.province?n.installationAddress.apartmentType:"",t.installationAddress.apartmentNumber=n.installationAddress&&n.installationAddress.province?n.installationAddress.apartmentNumber:"",t.isInstallationRequired=n.isInstallationRequired,t.duration=n.duration,t.contactInformation.primaryPhone.phoneNumber=e.Phone_LABEL?e.Phone_LABEL:"",t.contactInformation.primaryPhone.phoneExtension=e.Phone_EXT?e.Phone_EXT:"",t.contactInformation.additionalPhone.phoneNumber=e.ADDITIONAL_PHONE_NUMBER,t.contactInformation.additionalPhone.phoneExtension=e.ADDITIONAL_PHONE_EXT,t.contactInformation.preferredContactMethod=e.PREFERED_METHOD_OF_CONTACT,t.contactInformation.email=e.Email_LABEL?e.Email_LABEL:"",t.contactInformation.textMessage=e.TextMessage_LABEL?e.TextMessage_LABEL:"",t.availableDates=(r=n.availableDates,a=JSON.parse(e.dateAndTime),null==r||r.forEach(function(e){e.timeSlots.forEach(function(e){return e.isSelected=!1})}),null==r||r.forEach(function(e){return e.timeSlots.forEach(function(t){return t.isSelected=!(e.date!==a.date||!a.timeSlots.map(function(e){return e.intervalType===t.intervalType}))})}),r),t.additionalDetails.apartment=e.APPARTMENT,t.additionalDetails.entryCode=e.ENTRY_CODE,t.additionalDetails.informedSuperintendant=e.INFORMED_SUPERINTENDANT,t.additionalDetails.specialInstructions=e.SPECIAL_INSTRUCTIONS,t.additionalDetails.superintendantName=e.SUPERINTENDANT_NAME,t.additionalDetails.superintendantPhone=e.SUPERINTENDANT_PHONE,t},e}(),ae=u(843),ie=w.Actions.errorOccured,oe=w.Actions.setWidgetStatus,se=w.Actions.setProductConfigurationTotal,ce=w.Actions.broadcastUpdate,le=w.Actions.historyGo,ue=w.Actions.clearCachedState,de=w.Actions.omniPageLoaded,me=function(){function e(e,t){this.client=e,this.config=t,this.widgetState=w.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return(0,B.combineEpics)(this.appointmentEpic,this.submitAppointmentEpic)},Object.defineProperty(e.prototype,"appointmentEpic",{get:function(){var e=this;return function(t){return t.pipe((0,B.ofType)(q.toString()),(0,ae.filter)(function(){return e.widgetState!==w.EWidgetStatus.UPDATING}),(0,ae.mergeMap)(function(){var t=oe(e.widgetState=w.EWidgetStatus.UPDATING);return e.client.get(e.config.api.appointmentAPI).pipe((0,ae.mergeMap)(function(n){var r=n.data,a=[U(r.appointment.availableDates),z(r.appointment.duration),G(r.appointment.installationAddress),W(r.appointment.contactInformation),X(r.appointment.additionalDetails),$(r.appointment.isInstallationRequired),ce(se((0,w.ValueOf)(r,"productConfigurationTotal"))),oe(e.widgetState=w.EWidgetStatus.RENDERED),de()];return E([t],f(a),!1)}))}),(0,ae.catchError)(function(e){return(0,Z.of)(ie(new w.Models.ErrorHandler("getAppointment",e)))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"submitAppointmentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,B.ofType)(V.toString()),(0,ae.filter)(function(){return e.widgetState!==w.EWidgetStatus.UPDATING}),(0,ae.mergeMap)(function(t){var r=t.payload,a=oe(e.widgetState=w.EWidgetStatus.UPDATING);return e.client.put(e.config.api.appointmentAPI,re.create(r,ne,n.getState())).pipe((0,ae.mergeMap)(function(){var e=[ce(le(w.EWidgetRoute.REVIEW)),ue([w.EWidgetName.REVIEW])];return E([a],f(e),!1)}))}),(0,ae.catchError)(function(e){return(0,Z.of)(ie(new w.Models.ErrorHandler("getAppointment",e)))}))}},enumerable:!1,configurable:!0}),m([j.Injectable,p("design:paramtypes",[te,ee])],e)}(),pe=w.Actions.omniPageLoaded,fe=function(){function e(){this.widgetState=w.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return(0,B.combineEpics)(this.pageLoadedEpic)},Object.defineProperty(e.prototype,"pageLoadedEpic",{get:function(){return function(e,t){return e.pipe((0,B.ofType)(pe.toString()),(0,Z.mergeMap)(function(){var e,t,n=w.Omniture.useOmniture();switch(w.Utils.getFlowType()){case w.EFlowType.INTERNET:t="Internet",e="Change package";break;case w.EFlowType.TV:case w.EFlowType.ADDTV:break;case w.EFlowType.BUNDLE:t="Bundle",e="Add Tv"}return n.trackPage({id:"AppointmentPage",s_oSS1:"~",s_oSS2:t||"~",s_oSS3:e||"Change package",s_oPGN:"Installation",s_oPLE:{type:w.Omniture.EMessageType.Warning,content:{ref:"IstallationMessageBanner"}}}),(0,Z.of)([])}),(0,Z.catchError)(function(e){return(0,Z.of)([])}))}},enumerable:!1,configurable:!0}),m([j.Injectable],e)}(),Ee=w.Actions.setWidgetStatus,ge=w.Actions.broadcastUpdate,he=w.Actions.setAppointmentVisited,be=function(){function e(e,t){this.omnitureEpics=e,this.appointmentEpics=t}return e.prototype.combineEpics=function(){return(0,B.combineEpics)(this.onWidgetStatusEpic)},Object.defineProperty(e.prototype,"onWidgetStatusEpic",{get:function(){return function(e){return e.pipe((0,B.ofType)(Ee.toString()),(0,Z.filter)(function(e){return e.payload===w.EWidgetStatus.INIT}),(0,Z.mergeMap)(function(){return(0,Z.of)(ge(he()),q())}))}},enumerable:!1,configurable:!0}),m([j.Injectable,p("design:paramtypes",[fe,me])],e)}(),ve=j.CommonFeatures.BaseLocalization,Ne=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}var n;return d(t,e),n=t,t.getLocalizedString=function(e){n.Instance=n.Instance||j.ServiceLocator.instance.getService(j.CommonServices.Localization);var t=n.Instance;return t?t.getLocalizedString("omf-changepackage-appointment",e,t.locale):e},t.Instance=null,n=m([j.Injectable],t)}(ve),ye=j.CommonFeatures.BaseStore,Oe=(0,j.CommonFeatures.actionsToComputedPropertyName)(_),Ae=Oe.setAvailableDates,Se=Oe.setDuration,Te=Oe.setInstallationAddress,Ie=Oe.contactInformation,xe=Oe.setAdditionalDetails,De=Oe.setIsInstallationRequired,_e=function(e){function t(t,n,r,a){var i=e.call(this,n)||this;return i.client=t,i.epics=r,i.localization=a,i}return d(t,e),Object.defineProperty(t.prototype,"reducer",{get:function(){var e,t,n,r,a,i;return(0,k.combineReducers)(R(R(R(R({},w.Reducers.WidgetBaseLifecycle(this.localization)),w.Reducers.WidgetLightboxes()),w.Reducers.WidgetRestrictions()),{availableDates:(0,F.handleActions)((e={},e[Ae]=function(e,t){return t.payload||e},e),null),duration:(0,F.handleActions)((t={},t[Se]=function(e,t){return t.payload||e},t),null),installationAddress:(0,F.handleActions)((n={},n[Te]=function(e,t){return t.payload||e},n),null),contactInformation:(0,F.handleActions)((r={},r[Ie]=function(e,t){return t.payload||e},r),null),additionalDetails:(0,F.handleActions)((a={},a[xe]=function(e,t){return t.payload||e},a),null),isInstallationRequired:(0,F.handleActions)((i={},i[De]=function(e,t){return t.payload||e},i),!1)}))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"middlewares",{get:function(){return(0,B.combineEpics)(this.epics.omnitureEpics.combineEpics(),this.epics.appointmentEpics.combineEpics(),this.epics.combineEpics(),(new w.ModalEpics).combineEpics(),new w.RestricitonsEpics(this.client,"APPOINTMENT_RESTRICTION_MODAL").combineEpics(),(new w.LifecycleEpics).combineEpics())},enumerable:!1,configurable:!0}),m([j.Injectable,p("design:paramtypes",[te,j.Store,be,Ne])],t)}(ye);const nn={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit"},rn="undefined",an="blur",on="change",sn="input",cn="pattern",ln="required",un=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,dn=/^\w*$/,mn=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,pn=/\\(\\)?/g;Ce=e=>void 0===e,Re=e=>null===e||Ce(e),Le=e=>Array.isArray(e);const fn=e=>"object"==typeof e;Pe=e=>!Re(e)&&!Le(e)&&fn(e),Me=e=>Object.entries(e).reduce((e,[t,n])=>un.test(t)?(g(e,t,n),e):Object.assign(Object.assign({},e),{[t]:n}),{}),we=(e,t)=>{e.removeEventListener&&(e.removeEventListener(sn,t),e.removeEventListener(on,t),e.removeEventListener(an,t))},je=e=>"radio"===e,ke=e=>"checkbox"===e;const En={isValid:!1,value:""};Fe=e=>Le(e)?e.reduce((e,{ref:{checked:t,value:n}})=>t?{isValid:!0,value:n}:e,En):En,Be=e=>[...e].filter(({selected:e})=>e).map(({value:e})=>e),He=e=>"file"===e,qe=e=>"select-multiple"===e,Ve=e=>""===e;const gn={value:!1,isValid:!1},hn={value:!0,isValid:!0};Ue=e=>{if(Le(e)){if(e.length>1){const t=e.filter(({ref:{checked:e}})=>e).map(({ref:{value:e}})=>e);return{value:t,isValid:!!t.length}}const{checked:t,value:n,attributes:r}=e[0].ref;return t?r&&!Ce(r.value)?Ce(n)||Ve(n)?hn:{value:n,isValid:!0}:hn:gn}return gn},We=e=>Object.values(e).reduce((t,{ref:n,ref:{name:r}})=>Object.assign(Object.assign({},t),{[r]:b(e,n)}),{}),ze=e=>Pe(e)&&!Object.keys(e).length,Ge=(e,t,n)=>Pe(e)&&e.type===t&&e.message===n,Xe=(e,t,n)=>{const r=t.split(/[,[\].]+?/).filter(Boolean).reduce((e,t)=>Re(e)?e:e[t],e);return Ce(r)||r===e?e[t]||n:r},$e=e=>e instanceof RegExp,Je=e=>{const t=Pe(e)&&!$e(e);return{value:t?e.value:e,message:t?e.message:""}},Ye=e=>"string"==typeof e,Ze=e=>"function"==typeof e,Qe=e=>"boolean"==typeof e,Ke=(e,t,n,r,a)=>{if(!t)return{};const i=n[e];return Object.assign(Object.assign({},i),{types:Object.assign(Object.assign({},i&&i.types?i.types:{}),{[r]:a||!0})})},et=async(e,t,{ref:n,ref:{type:r,value:a,name:i,valueAsNumber:o,valueAsDate:s},options:c,required:l,maxLength:u,minLength:d,min:m,max:p,pattern:f,validate:E})=>{const g=e.current,h={},N=je(r),y=ke(r),O=N||y,A=Ve(a),S=Ke.bind(null,i,t,h),T=(e,r,a,o="maxLength",s="minLength")=>{const c=e?r:a;if(h[i]=Object.assign({type:e?o:s,message:c,ref:n},S(e?o:s,c)),!t)return h};if(l&&(!N&&!y&&(A||Re(a))||Qe(a)&&!a||y&&!Ue(c).isValid||N&&!Fe(c).isValid)){const e=Ye(l)?l:Je(l).message;if(h[i]=Object.assign({type:ln,message:e,ref:O?g[i].options[0].ref:n},S(ln,e)),!t)return h}if(!Re(m)||!Re(p)){let e,n;const{value:i,message:c}=Je(p),{value:l,message:u}=Je(m);if("number"===r||!r&&!isNaN(a)){const t=o||parseFloat(a);Re(i)||(e=t>i),Re(l)||(n=t<l)}else{const t=s||new Date(a);Ye(i)&&(e=t>new Date(i)),Ye(l)&&(n=t<new Date(l))}if((e||n)&&(T(!!e,c,u,"max","min"),!t))return h}if(Ye(a)&&!A&&(u||d)){const{value:e,message:n}=Je(u),{value:r,message:i}=Je(d),o=a.toString().length,s=u&&o>e;if((s||d&&o<r)&&(T(!!s,n,i),!t))return h}if(f&&!A){const{value:e,message:r}=Je(f);if($e(e)&&!e.test(a)&&(h[i]=Object.assign({type:cn,message:r,ref:n},S(cn,r)),!t))return h}if(E){const e=b(g,n),r=O&&c?c[0].ref:n;if(Ze(E)){const n=v(await E(e),r);if(n&&(h[i]=Object.assign(Object.assign({},n),S("validate",n.message)),!t))return h}else if(Pe(E)){const n=Object.entries(E),a=await new Promise(a=>{n.reduce(async(o,[s,c],l)=>{if(!ze(await o)&&!t||!Ze(c))return a(o);let u;const d=v(await c(e),r,s);return d?(u=Object.assign(Object.assign({},d),S(s,d.message)),t&&(h[i]=u)):u=o,n.length-1===l?a(u):u},{})});if(!ze(a)&&(h[i]=Object.assign({ref:r},a),!t))return h}}return h};const bn=(e,t)=>Le(e.inner)?e.inner.reduce((e,{path:n,message:r,type:a})=>Object.assign(Object.assign({},e),e[n]&&t?{[n]:Ke(n,t,e,a,r)}:{[n]:e[n]||Object.assign({message:r,type:a},t?{types:{[a]:r||!0}}:{})}),{}):{[e.path]:{message:e.message,type:e.type}};tt=(e,t,n)=>Ce(e[t])?Xe(e,t,n):e[t],nt=e=>Re(e)||!fn(e);const vn=(e,t)=>{const n=(t,n,r)=>{const a=r?`${e}.${n}`:`${e}[${n}]`;return nt(t)?a:vn(a,t)};return Le(t)?t.map((e,t)=>n(e,t)):Object.entries(t).map(([e,t])=>n(t,e,!0))};rt=(e,t)=>y(vn(e,t)),at=(e,t,n,r)=>{let a;return ze(e)?a=void 0:Ce(e[t])?(a=Xe(Me(e),t),Ce(a)||rt(t,a).forEach(e=>n.add(e))):(n.add(t),a=e[t]),Ce(a)?Pe(r)?tt(r,t):r:a},it=({hasError:e,isBlurEvent:t,isOnSubmit:n,isReValidateOnSubmit:r,isOnBlur:a,isReValidateOnBlur:i,isSubmitted:o})=>n&&r||n&&!o||a&&!t&&!e||i&&!t&&e||r&&o,ot=(e,t)=>[...e].reduce((e,n)=>!!((e,t)=>e.startsWith(`${t}[`))(t,n)||e,!1);const Nn=e=>{for(const t in e){const n=e[t],r=Le(n);!Pe(n)&&!r||n.ref||Nn(n),(Ce(n)||ze(n)||r&&!e[t].filter(Boolean).length)&&delete e[t]}return e},yn=(e,t)=>(t.forEach(t=>{g(e,t,void 0)}),Nn(e));st=e=>({isOnSubmit:!e||e===nn.onSubmit,isOnBlur:e===nn.onBlur,isOnChange:e===nn.onChange});const{useRef:On,useState:An,useCallback:Sn,useEffect:Tn}=P,In=(0,P.createContext)(null);return function(e){e.PHONE="Phone",e.TEXT_MESSAGE="TextMessage",e.EMAIL="Email"}(ct||(ct={})),function(e){e.AM="AM",e.PM="PM",e.Evening="Evening",e.AllDay="AllDay",e.Item0810="Item0810",e.Item1012="Item1012",e.Item1315="Item1315",e.Item1517="Item1517",e.Item1719="Item1719",e.Item1921="Item1921"}(lt||(lt={})),function(e){e.EMAIL="Email",e.TEXT_MESSAGE="TextMessage",e.PHONE="Phone"}(ut||(ut={})),dt=/^[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+((\.)+[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+)*@([a-z0-9]+([\-][a-z0-9])*)+([\.]([a-z0-9]+([\-][a-z0-9])*)+)+$/i,mt=/^[0-9]\d{2}-\d{3}-\d{4}$/i,pt=function(e){var t,n=[];return w.Utils.getFlowType()===w.EFlowType.INTERNET&&(t=523),w.Utils.getFlowType()===w.EFlowType.BUNDLE&&(t=508),Object.keys(e).map(function(t){var r=e[t];n.push({id:r.ref.name,error:r.type})}),w.Omniture.useOmniture().trackError(n.map(function(e){return{code:e.id,type:w.Omniture.EErrorType.Validation,layer:w.Omniture.EApplicationLayer.Frontend,description:e.error}}),t),n},ft=function(e,t){var n,r;switch(e){case ct.EMAIL:return null==t?void 0:t.email;case ct.PHONE:return(null===(n=null==t?void 0:t.primaryPhone)||void 0===n?void 0:n.phoneNumber)&&I(null===(r=null==t?void 0:t.primaryPhone)||void 0===r?void 0:r.phoneNumber);case ct.TEXT_MESSAGE:return(null==t?void 0:t.textMessage)&&I(null==t?void 0:t.textMessage);default:return""}},Et=u(419),gt=function(e){var t=R(R({},ht),e),n=t.label,r=t.value,a=t.handleChange,i=t.subLabel,o=t.checked,s=O().register;return P.createElement("div",{className:"flexBlock flexCol-xs margin-15-bottom"},P.createElement("label",{htmlFor:"additionalPhoneNumber",className:"installation-form-label"},P.createElement("span",{className:"txtBold block"},P.createElement(Et.FormattedMessage,{id:n})),i?P.createElement("span",{className:"txtItalic block txtNormal"},P.createElement(w.FormattedHTMLMessage,{id:i})):null),P.createElement("div",{className:"flexCol margin-5-top"},P.createElement("label",{className:"graphical_ctrl graphical_ctrl_checkbox txtNormal"},P.createElement(w.FormattedHTMLMessage,{id:r+"_LABEL"}),P.createElement("input",{type:"checkbox",ref:s,id:n,name:n,defaultChecked:o,onChange:function(e){return a(e)}}),P.createElement("span",{className:"ctrl_element chk_radius"}))))},ht={checked:!1},bt=function(e){var t=R(R({},vt),e),n=t.label,r=t.value,a=t.handleChange,i=t.checked,o=t.requiredInput,s=O().register;return n?P.createElement("label",{className:"graphical_ctrl pointer ctrl_radioBtn margin-10-bottom"},P.createElement("label",{className:"txtBold block",htmlFor:"option_".concat(r)},P.createElement(Et.FormattedMessage,{id:r})),P.createElement("input",{type:"radio",id:"option_".concat(r),ref:s({required:o}),name:n,value:r,checked:i,onChange:function(e){return a(e)}}),P.createElement("span",{className:"ctrl_element"}),"OTHER"===r&&!0===i?P.createElement("span",{className:"topArrow text-left otherOption","aria-hidden":"true"}):null):null},vt={checked:!1,requiredInput:!1},Nt=function(e){var t=R(R({},yt),e),n=t.label,r=t.required,a=t.value,i=t.subLabel,o=t.handleChange,s=t.requiredInput,c=t.maxLength,l=O().register,u=f(P.useState((c||0)-(a||"").length),2),d=u[0],m=u[1];return P.createElement("div",{className:"flexBlock flexCol-xs margin-15-bottom"},P.createElement("label",{htmlFor:n,className:"installation-form-label"},P.createElement("span",{className:"txtBold"},P.createElement(Et.FormattedMessage,{id:n})),r?P.createElement("span",{className:"txtNormal"},"(optional)"):"",i?P.createElement("span",{className:"txtItalic block txtNormal"},P.createElement(w.FormattedHTMLMessage,{id:i})):null),P.createElement("div",{className:"flexCol"},P.createElement("textarea",{ref:l({required:s}),id:n,name:n,defaultValue:a,maxLength:c,className:"brf3-textarea form-control",onChange:function(e){m((c||0)-(e.currentTarget.value||"").length),o(e)}}),P.createElement("p",null,P.createElement(Et.FormattedMessage,{id:n+"_DESCRIPTION",values:{max:c,count:d}}))))},yt={required:!1,requiredInput:!1,value:"",subLabel:""},Ot=function(e){var t=R(R({},At),e),n=t.label,r=t.subLabel,a=t.handleChange,i=t.containerClass,o=t.extention,s=t.optionalExtenstion,c=t.requiredInput,l=t.requiredPattern,u=t.value,d=t.subValue,m=O(),p=m.register,f=m.errors;return P.createElement("div",{className:"flexBlock flexCol-xs margin-15-bottom flexWrap ".concat(i)},P.createElement("label",{htmlFor:"additionalPhoneNumber",className:"installation-form-label ".concat(c?"form-required":""," ").concat(f&&f[n]?"error":"")},P.createElement("span",{className:"txtBold block"},P.createElement(Et.FormattedMessage,{id:n})),r?P.createElement("span",{className:"txtItalic block txtNormal","aria-label":function(e){switch(e){case"TELEPHONE_FORMAT":case"PREFERED_PHONE_FORMAT":case"PREFERED_TEXT_MESSAGE_FORMAT":case"Phone_FORMAT":case"TextMessage_FORMAT":return Ne.getLocalizedString("TELEPHONE_FORMAT_ARIA");default:return Ne.getLocalizedString(e)}}(r)},P.createElement(w.FormattedHTMLMessage,{id:r})):null),P.createElement("div",{className:"flexCol relative ".concat(f&&f[n]?"has-error":"")},P.createElement("span",{className:"topArrow text-left hide","aria-hidden":"true"}),P.createElement("input",{type:"text",ref:p({required:c,pattern:l}),className:"brf3-virgin-form-input form-control",id:n,name:n,title:n,defaultValue:u,onBlur:a,onChange:function(e){return a(e)}}),f&&f[n]?P.createElement("span",{className:"error margin-5-top"},P.createElement("span",{className:"virgin-icon icon-warning margin-15-right","aria-hidden":!0},P.createElement("span",{className:"volt-icon path1"}),P.createElement("span",{className:"volt-icon path2"})),P.createElement("span",{className:"txtSize12"},P.createElement(w.FormattedHTMLMessage,{id:"pattern"!==f[n].type?"INLINE_ERROR_required":"INLINE_ERROR_".concat(n,"_pattern")}))):null),o?P.createElement("div",{className:"flexCol brf3-virgin-form-subInput fill-sm"},P.createElement("div",{className:"flexBlock flexCol-xs"},P.createElement("label",{htmlFor:"extension",className:"installation-form-label"},P.createElement("span",{className:"txtBold block"},P.createElement(Et.FormattedMessage,{id:o})),s?P.createElement("span",{className:"txtItalic block txtNormal"},P.createElement(Et.FormattedMessage,{id:"OPTIONAL_LABEL"})):null),P.createElement("div",{className:"flexCol"},P.createElement("input",{type:"text",ref:p,className:"brf3-virgin-form-input form-control",id:o,name:o,title:o,maxLength:10,defaultValue:d,onBlur:a,onChange:function(e){return a(e)}})))):null)},At={requiredInput:!1,requiredPattern:/.*/i,containerClass:"",value:"",subValue:""},St=function(e){var t=e.legend,n=e.required,r=e.accessibleLegend,a=e.legendAdditionalClass;return t?P.createElement("legend",{className:"installation-form-label ".concat(n?"form-required":""," ").concat(r?"sr-only":""," ").concat(a)},P.createElement(Et.FormattedMessage,{id:t})):null},Tt=function(e){var t=e.className,n=e.children,r=e.legend,a=e.accessibleLegend,i=e.legendAdditionalClass,o=e.required,s=e.additionalClass;return P.createElement("fieldset",{className:"margin-15-bottom ".concat(t)},a?P.createElement(P.Fragment,null,P.createElement(St,{legend:r,required:o,accessibleLegend:a,legendAdditionalClass:i}),n):P.createElement("div",{className:"flexBlock flexCol-xs ".concat(s)},P.createElement(St,{legend:r,required:o,accessibleLegend:a,legendAdditionalClass:i}),n))},function(e){e.ERROR="icon-warning",e.NOTE="icon-info",e.VALIDATION="icon-Big_check_confirm",e.INFO="icon-BIG_WARNING"}(It||(It={})),xt=function(e){var t=R(R({},Dt),e),n=t.iconType,r=t.heading,a=t.message,i=t.messages,o=t.iconSizeCSS;return P.createElement(w.Components.Container,null,P.createElement(w.Components.Panel,{className:"flexBlock pad-20-left pad-20-right pad-25-top pad-25-bottom margin-15-bottom txtBlack"},P.createElement("span",{className:"virgin-icon ".concat(n," ").concat(o," txtSize36"),"aria-hidden":!0},P.createElement("span",{className:"virgin-icon path1"}),P.createElement("span",{className:"virgin-icon path2"})),P.createElement("div",{id:"IstallationMessageBanner",className:"flexCol pad-15-left content-width valign-top pad-0-xs"},P.createElement("h4",{className:"virginUltraReg txtSize18 txtDarkGrey1 no-margin-top txtUppercase"},P.createElement(w.FormattedHTMLMessage,{id:r})),a?P.createElement("p",{className:"txtSize14 txtGray4A sans-serif no-margin"},P.createElement(w.FormattedHTMLMessage,{id:a})):null,i?P.createElement("ul",null,i&&i.map(function(e){return P.createElement("li",{className:"error"},P.createElement("a",{id:"message_".concat(e.id),href:"#".concat(e.id),className:"txtRed txtBold txtUnderline",title:e.id},P.createElement(Et.FormattedMessage,{id:e.id})),P.createElement("span",{className:"txtDarkGrey"}," - ","required"===e.error?P.createElement(Et.FormattedMessage,{id:"INLINE_ERROR_required"}):P.createElement(Et.FormattedMessage,{id:"INLINE_ERROR_"+e.id+"_"+e.error})))})):null)))},Dt={iconType:It.INFO,iconSizeCSS:"txtSize36"},function(e){e.H1="h1",e.H2="h2",e.H3="h3",e.H4="h4",e.H5="h5",e.H6="h6"}(_t||(_t={})),Ct=function(e){var t=R(R({},Rt),e),n=t.tag,r=t.additionalClass,a=t.content,i=t.description,o=n||"h2";return P.createElement(P.Fragment,null,P.createElement(o,{className:"virginUltra txtBlack txtCapital noMargin ".concat(r)},P.createElement(Et.FormattedMessage,{id:a})),i?P.createElement(P.Fragment,null,P.createElement("span",{className:"spacer10 col-xs-12 clear"}),P.createElement("p",{className:"noMargin"},P.createElement(w.FormattedHTMLMessage,{id:i}))):null)},Rt={additionalClass:"",description:""},Lt=function(e){function t(t){var n=e.call(this,t)||this;return n.headingProps={tag:_t.H2,classNames:"txtSize28 txtSize24-xs",content:"INSTALLATION_PAGE_HEADING"},n}return d(t,e),t.prototype.render=function(){return P.createElement(P.Fragment,null,P.createElement(w.Components.Container,null,P.createElement(w.Components.BRF3Container,null,P.createElement("span",{className:"spacer5 flex col-12"}),P.createElement(Ct,R({},this.headingProps)),P.createElement("span",{className:"spacer25 flex col-12 clear"}))),P.createElement(xt,{iconType:It.INFO,heading:"INSTALLATION_HEADING",message:"INSTALLATION_MESSAGE"}),Object.keys(this.props.errors).length?P.createElement(xt,{iconType:It.ERROR,heading:"ERRORS_HEADING",messages:pt(this.props.errors)}):null)},t}(P.PureComponent),Pt=function(){var e,t,n,r=(0,M.useSelector)(function(e){return null==e?void 0:e.contactInformation}),a=(0,M.useSelector)(function(e){return null==e?void 0:e.additionalDetails}),i=f(P.useState(ct.PHONE),2),o=i[0],s=i[1],c=O().setValue,l=function(e){var t=e.target,n=t.value,r=t.name;switch(n){case ct.PHONE:case ct.EMAIL:case ct.TEXT_MESSAGE:s(n)}switch(r){case ct.PHONE+"_LABEL":case ct.TEXT_MESSAGE+"_LABEL":case"ADDITIONAL_PHONE_NUMBER":c(r,I(n),!0);break;case ct.PHONE+"_EXT":case"ADDITIONAL_PHONE_EXT":c(r,x(n),!0);break;case"SUPERINTENDANT_PHONE":c(r,I(n),!0);break;case ct.EMAIL+"_LABEL":c(r,n,!0)}};return P.useEffect(function(){s((null==r?void 0:r.preferredContactMethod)?r.preferredContactMethod:ct.PHONE)},[r]),n={tag:_t.H2,additionalClass:"txtSize22 txtSize24-xs",content:"CONTACT_INFORMATION"},P.createElement("div",{className:"margin-30-bottom",id:"section2"},P.createElement(Ct,R({},n)),P.createElement("span",{className:"spacer10 visible-xs"}),P.createElement("div",{className:"pad-25-top no-pad-xs"},P.createElement(Tt,{legend:"PREFERED_METHOD_OF_CONTACT",required:!0,additionalClass:"flexWrap",accessibleLegend:!1},P.createElement("div",{className:"flexCol lineHeight18"},P.createElement("div",{className:"spacer15 visible-xs"}),T(ct,function(e){return P.createElement(bt,{label:"PREFERED_METHOD_OF_CONTACT",value:e,handleChange:l,checked:e===o})})),T(ct,function(e){var t;return P.createElement(Ot,{requiredInput:o===e,label:e+"_LABEL",containerClass:"sub-option flex-wrap ".concat(e===o?"show":"hide"),subLabel:e+"_FORMAT",extention:e===ct.PHONE&&e+"_EXT",optionalExtenstion:!0,requiredPattern:e===ct.EMAIL?dt:mt,value:ft(e,r),subValue:null===(t=null==r?void 0:r.primaryPhone)||void 0===t?void 0:t.phoneExtension,handleChange:function(e){return l(e)}})})),P.createElement(Tt,{legend:"ADDITIONAL_PHONE_NUMBER",required:!1,accessibleLegend:!0},P.createElement(Ot,{requiredInput:!1,label:"ADDITIONAL_PHONE_NUMBER",subLabel:"TELEPHONE_FORMAT",extention:"ADDITIONAL_PHONE_EXT",optionalExtenstion:!0,requiredPattern:mt,value:null===(e=null==r?void 0:r.additionalPhone)||void 0===e?void 0:e.phoneNumber,subValue:null===(t=null==r?void 0:r.additionalPhone)||void 0===t?void 0:t.phoneExtension,handleChange:function(e){return l(e)}}),P.createElement(Ot,{label:"APPARTMENT",value:null==a?void 0:a.apartment,handleChange:function(e){return l(e)}}),P.createElement(Ot,{label:"ENTRY_CODE",value:null==a?void 0:a.entryCode,handleChange:function(e){return l(e)}}),P.createElement(Ot,{label:"SUPERINTENDANT_NAME",value:null==a?void 0:a.superintendantName,handleChange:function(e){return l(e)}}),P.createElement(Ot,{label:"SUPERINTENDANT_PHONE",requiredPattern:mt,value:null==a?void 0:a.superintendantPhone,handleChange:function(e){return l(e)}}),P.createElement(gt,{label:"INFORMED_SUPERINTENDANT",value:"YES",checked:null==a?void 0:a.informedSuperintendant,handleChange:function(e){return l(e)}}),P.createElement(Nt,{label:"SPECIAL_INSTRUCTIONS",subLabel:"SPECIAL_INSTRUCTIONS_SUBLABEL",value:null==a?void 0:a.specialInstructions,maxLength:200,handleChange:function(e){return l(e)}}))))},Mt=P.memo(function(e){var t,n=e.handleChange,r=e.preferredDate,a=e.checked,i=O().register;return P.createElement(P.Fragment,null,P.createElement("label",{id:"dateAndTime"+r.date,className:"graphical_ctrl pointer ctrl_radioBtn margin-10-bottom"},P.createElement("input",{type:"radio",ref:i({required:!0}),id:"timeOption"+r.date,name:"dateAndTime",value:JSON.stringify(r),onChange:function(e){return n(e)},checked:a.date===r.date}),P.createElement("label",{className:"block no-margin",htmlFor:"timeOption"+r.date},Boolean(r.date)?P.createElement(Et.FormattedDate,{value:r.date,year:"numeric",weekday:"long",month:"long",day:"2-digit",timeZone:"UTC"}):"No Appointment Details"),Boolean(r.timeSlots.length)?P.createElement("span",{className:"txtNormal block"},P.createElement(Et.FormattedMessage,{id:null===(t=r.timeSlots.find(function(e){return e.isAvailable}))||void 0===t?void 0:t.intervalType})):null,P.createElement("span",{className:"ctrl_element"})))}),wt=function(e){function t(t){return e.call(this,t)||this}return d(t,e),t.prototype.componentDidMount=function(){this.props.initSlickSlider()},t.prototype.render=function(){var e=this.props,t=e.availableDates,n=e.selectDate,r=e.selectedDateTime;return P.createElement("div",{className:"flexBlock margin-15-bottom sub-option relative timeslot-picker"},P.createElement("div",{className:"select-timeslot fill"},t&&t.map(function(e,t){return P.createElement("div",{className:""},P.createElement("div",{className:e.timeSlots[0].intervalType===lt.AllDay?"allDayContainer":"day-container"},P.createElement("label",{htmlFor:"dayIndex_"+t,className:"virginUltra sans-serif-xs txtBold-xs txtSize16 txtBlack lineHeight1-3 margin-15-bottom"},P.createElement(Et.FormattedDate,{value:S(e.date),weekday:"long",timeZone:"UTC"}),P.createElement("br",{className:"hidden-m"}),P.createElement("span",{className:"d-sm-none d-md-none d-lg-none d-xl-none"},", "),P.createElement(Et.FormattedDate,{value:S(e.date),year:"numeric",month:"short",day:"2-digit",timeZone:"UTC"})),P.createElement("ul",{className:"noMargin list-unstyled timeItem","aria-labelledby":"mondayList"},e.timeSlots.map(function(t){var a=r.timeSlots[0].intervalType===t.intervalType&&r.date===e.date;return P.createElement("li",{className:"txtBlue ".concat(a?"selected":"")},P.createElement("button",{id:"slot_".concat(t.intervalType),onClick:function(r){return n(r,e.date,t)},className:"btn btn-link ".concat(t.intervalType===lt.AllDay?"flexCol flexJustify":""," ").concat(t.isAvailable?"":"disabled"," ").concat(t.isSelected?"selected":""),tabIndex:0},P.createElement(w.FormattedHTMLMessage,{id:t.intervalType})))}))))})))},t.displayName="TimeSlots",t}(P.Component),jt=w.Components.Visible,kt=function(e){function t(t){var n=e.call(this,t)||this;return n.handleChange=function(e){var t=e.target.value;"OTHER"===t?n.setState({showTimeSlots:!0}):n.setState({showTimeSlots:!1,selectedDateTime:JSON.parse(t)})},n.selectDate=function(e,t,r){e.preventDefault();var a=E([],f(n.state.preferredDates),!1);n.state.preferredDates[0].date===t&&n.state.preferredDates[0].timeSlots[0].intervalType===r.intervalType?n.setState({preferredDates:n.state.preferredDates,selectedDateTime:n.state.preferredDates[0],showTimeSlots:!1,showOther:!1}):(a[1]={date:t,timeSlots:[R(R({},r),{isSelected:!0})]},n.setState({preferredDates:a,selectedDateTime:a[1],showTimeSlots:!1,showOther:!1}))},n.changeBtn=function(e){e.preventDefault(),n.setState({showOther:!0,showTimeSlots:!0,preferredDates:[n.state.preferredDates[0]]})},n.state={showTimeSlots:!1,selectedDateTime:null,preferredDates:[],showOther:!0},n.handleChange.bind(n),n.changeBtn.bind(n),n}return d(t,e),t.prototype.componentDidUpdate=function(e){var t,n,r;this.props.availableDates&&this.props.availableDates.length&&JSON.stringify(this.props.availableDates)!==JSON.stringify(e.availableDates)&&(t=(r=(n=this.props.availableDates).filter(function(e){return e.timeSlots.find(function(e){return!0===e.isSelected})})).length>0?r:[n[0]],this.setState({preferredDates:t,selectedDateTime:t[0].date?t[0]:null,showOther:!(t.length>1)}))},t.prototype.render=function(){var e=this,t=this.props,n=t.installationAddress,r=t.availableDates,a=t.initSlickSlider,i=this.state,o=i.showTimeSlots,s=i.selectedDateTime,c=i.showOther,l=i.preferredDates,u={tag:_t.H2,additionalClass:"txtSize22 txtSize24-xs",content:"INSTALLATION_DETAILS",description:"INSTALLATION_DETAILS_DESC"};return P.createElement("div",{className:"margin-30-bottom",id:"section1"},P.createElement(Ct,R({},u)),P.createElement("span",{className:"spacer10 flex col-12 clear"}),P.createElement("p",{className:"noMargin txtItalic"},P.createElement(Et.FormattedMessage,{id:"REQUIRED_INFO_FLAG"})),P.createElement("div",{className:"pad-15-top"},P.createElement(Tt,{legend:"DATE_AND_TIME_LABEL",required:!0,accessibleLegend:!1,additionalClass:"flex-wrap"},P.createElement("div",{className:"spacer10 visible-xs"}),P.createElement("div",{className:"flexCol lineHeight18"},l&&l.length&&l.map(function(t){return P.createElement(Mt,{handleChange:e.handleChange,preferredDate:t,checked:o||s})}),P.createElement(jt,{when:c,placeholder:P.createElement("div",{className:"pad-35-left relative changeBtn"},P.createElement("button",{id:"CHANGE_BTN",className:"btn btn-link pad-0 txtSize14 txtUnderline txtVirginBlue",onClick:function(t){return e.changeBtn(t)}},"Change"))},P.createElement(bt,{handleChange:this.handleChange,requiredInput:!0,checked:o,label:"dateAndTime",value:"OTHER"}))),o?P.createElement(wt,{selectDate:this.selectDate,availableDates:r,initSlickSlider:a,selectedDateTime:s}):null),P.createElement(jt,{when:Boolean(s)},s&&"OTHER"!==s?P.createElement(Tt,{legend:"ESTIMATED_DURATION",required:!1,accessibleLegend:!1},P.createElement("div",{className:"flexCol"},P.createElement("span",{className:"block"},P.createElement(Et.FormattedMessage,{id:s.timeSlots[0].duration})),P.createElement("span",{className:"block"},P.createElement(Et.FormattedMessage,{id:"ARRIVAL_OF_TECHNICIAN"})))):null),P.createElement(jt,{when:Boolean(n)},P.createElement(Tt,{legend:"SHIPPING_INSTALLATION_ADDRESS",required:!1,accessibleLegend:!1},P.createElement("div",{className:"flexCol"},P.createElement("span",{className:"block"},P.createElement(jt,{when:(0,w.ValueOf)(n,"apartmentNumber",!1)},(0,w.ValueOf)(n,"apartmentNumber","")," - "),(0,w.ValueOf)(n,"address1","")," ",(0,w.ValueOf)(n,"address2","")," ",(0,w.ValueOf)(n,"streetType",""),", ",(0,w.ValueOf)(n,"city",""),", ",(0,w.ValueOf)(n,"province",""),", ",(0,w.ValueOf)(n,"postalCode","")),P.createElement("span",{className:"margin-10-top"},P.createElement(w.FormattedHTMLMessage,{id:"CONTACT_US_NOTE"})))))))},t}(P.Component),Ft=(0,M.connect)(function(e){return{installationAddress:e.installationAddress,availableDates:e.availableDates,duration:e.duration}},function(e){return{initSlickSlider:function(){return e(Y())}}})(kt),Bt=null,(Ht=function(e){var t,n=P.useRef(null),r=O().handleSubmit,a=(0,M.useDispatch)();return Bt=function(){n.current.click()},t=function(e){return n=function(){return function(t,n){function r(r){return function(l){return function(r){if(a)throw new TypeError("Generator is already executing.");for(;c&&(c=0,r[0]&&(s=0)),s;)try{if(a=1,i&&(o=2&r[0]?i.return:r[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,r[1])).done)return o;switch(i=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,i=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==r[0]&&2!==r[0])){s=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){s.label=r[1];break}if(6===r[0]&&s.label<o[1]){s.label=o[1],o=r;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(r);break}o[2]&&s.ops.pop(),s.trys.pop();continue}r=n.call(t,s)}catch(e){r=[6,e],i=0}finally{a=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,l])}}var a,i,o,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},c=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return c.next=r(0),c.throw=r(1),c.return=r(2),"function"==typeof Symbol&&(c[Symbol.iterator]=function(){return this}),c}(this,function(t){return e.preventDefault(),r(function(e){a(V(e))})(e),[2]})},new((t=void 0)||(t=Promise))(function(r,a){function i(t){try{s(n.next(t))}catch(e){a(e)}}function o(t){try{s(n.throw(t))}catch(e){a(e)}}function s(e){var n;e.done?r(e.value):(n=e.value,n instanceof t?n:new t(function(e){e(n)})).then(i,o)}s((n=n.apply(undefined,[])).next())});var t,n},P.createElement("form",{id:"AppointmentForm",onSubmit:t},P.createElement("div",{className:"spacer45 hidden-m"}),P.createElement("div",{className:"spacer20 d-block d-sm-none"}),P.createElement(Ft,null)," ",P.createElement(Pt,null)," ",P.createElement("button",{ref:n,type:"submit","aria-hidden":"true",style:{display:"none"}}))}).useSubmitRef=function(){return Bt},qt=Ht,Vt=j.CommonFeatures.BasePipe,Ut=function(e){function t(n){var r=e.call(this,n)||this;return t.instance=r,r}return d(t,e),t.Subscriptions=function(e){var t;return(t={})[w.Actions.onContinue.toString()]=function(t){var n=qt.useSubmitRef();n&&n(),e.dispatch(w.Actions.setWidgetStatus(w.EWidgetStatus.RENDERED))},t},t}(Vt),Wt=w.Components.RestrictionModal,zt=w.Actions.widgetRenderComplete,Gt=function(e){var t=(0,M.useDispatch)(),n=O().errors;return P.useEffect(function(){t(zt(w.EWidgetName.APPOINTMENT))},[]),P.createElement("main",{id:"mainContent"},P.createElement("span",{className:"flex spacer30 col-12","aria-hidden":"true"}),P.createElement(Wt,{id:"APPOINTMENT_RESTRICTION_MODAL"}),P.createElement(Lt,{errors:n}),P.createElement(w.Components.Container,null,P.createElement(w.Components.Panel,{className:"pad-25-left pad-25-right clearfix"},P.createElement(qt,null))))},Xt=w.Components.ApplicationRoot,$t=function(e){var t=function({mode:e=nn.onSubmit,reValidateMode:t=nn.onChange,validationSchema:n,defaultValues:r={},submitFocusError:a=!0,validateCriteriaMode:i}={}){function o(e){ze(l.current)||(Le(e)?e:[e]).forEach(e=>te(l.current[e],!0))}function s(e,t={}){if(!e.name)return;const{name:r,type:a,value:i}=e,o=Object.assign({ref:e},t),s=l.current,c=je(a)||ke(a);let u,d=s[r],m=!0,p=!1;if(c?d&&Le(d.options)&&d.options.find(({ref:e})=>i===e.value):d)s[r]=Object.assign(Object.assign({},d),t);else{if(a){const n=function(e){const t=new MutationObserver(()=>{h(e)&&(t.disconnect(),te(o))});return t.observe(window.document,{childList:!0,subtree:!0}),t}(e);d=c?Object.assign({options:[...d&&d.options||[],{ref:e,mutationWatcher:n}],ref:{type:a,name:r}},t):Object.assign(Object.assign({},o),{mutationWatcher:n})}else d=o;s[r]=d,ze(A.current)||(u=tt(A.current,r),m=Ce(u),p=ot(L.current,r),m||p||G(r,u)),n&&B.current.isValid?K():ze(t)||(E.current.add(r),!w&&B.current.isValid&&U(d).then(e=>{const t=y.current;ze(e)?v.current.add(r):y.current=!1,t!==y.current&&V()})),O.current[r]||p&&m||(O.current[r]=m?b(s,d.ref):u),a&&function({field:e,handleChange:t,isRadioOrCheckbox:n}){const{ref:r}=e;r.addEventListener&&(r.addEventListener(n?on:sn,t),r.addEventListener(an,t))}({field:c&&d.options?d.options[d.options.length-1]:d,isRadioOrCheckbox:c,handleChange:C.current})}}function c(e,t){if(!j&&e)if(Ye(e))s({name:e},t);else{if(!Pe(e)||!("name"in e))return t=>t&&s(t,e);s(e,t)}}const l=On({}),u="all"===i,d=On({}),m=On({}),p=On(new Set),f=On(new Set),E=On(new Set),v=On(new Set),y=On(!0),O=On({}),A=On(r),S=On(!1),T=On(!1),I=On(!1),x=On(!1),D=On(0),_=On(!1),C=On(),R=On({}),L=On(new Set),[,P]=An(),{isOnBlur:M,isOnSubmit:w}=On(st(e)).current,j=typeof window===rn,k=typeof document!==rn&&!j&&!Ce(window.HTMLElement),F=k&&"Proxy"in window,B=On({dirty:!F,isSubmitted:w,submitCount:!F,touched:!F,isSubmitting:!F,isValid:!F}),{isOnBlur:H,isOnSubmit:q}=On(st(t)).current;A.current=A.current?A.current:r;const V=Sn(()=>{S.current||P({})},[]),U=Sn(et.bind(null,l,u),[]),W=Sn(N.bind(null,n,u),[n]),z=Sn((e,t,r,a)=>{let i=r||function({errors:e,name:t,error:n,validFields:r,fieldsWithValidation:a}){const i=ze(n),o=ze(e),s=Xe(n,t),c=Xe(e,t);return!(i&&r.has(t)||c&&c.isManual)&&(!!(o!==i||!o&&!c||i&&a.has(t)&&!r.has(t))||s&&!Ge(c,s.type,s.message))}({errors:d.current,error:t,name:e,validFields:v.current,fieldsWithValidation:E.current});if(ze(t)?((E.current.has(e)||n)&&(v.current.add(e),i=i||Xe(d.current,e)),d.current=yn(d.current,[e])):(v.current.delete(e),i=i||!Xe(d.current,e),g(d.current,e,t[e])),i&&!a)return V(),!0},[V,n]),G=Sn((e,t)=>{const n=l.current[e];if(!n)return!1;const r=n.ref,{type:a}=r,i=n.options,o=k&&r instanceof window.HTMLElement&&Re(t)?"":t;return je(a)&&i?i.forEach(({ref:e})=>e.checked=e.value===o):He(a)?o instanceof FileList||""===o?r.files=o:r.value=o:qe(a)?[...r.options].forEach(e=>e.selected=o.includes(e.value)):ke(a)&&i?i.length>1?i.forEach(({ref:e})=>e.checked=o.includes(e.value)):i[0].ref.checked=!!o:r.value=o,a},[k]),X=e=>{if(!l.current[e]||!B.current.dirty)return!1;const t=ot(L.current,e);let n=O.current[e]!==b(l.current,l.current[e].ref);if(t){const t=e.substring(0,e.indexOf("["));n=function(e,t){let n=!1;if(!Le(e)||!Le(t)||e.length!==t.length)return!0;for(let r=0;r<e.length&&!n;r++){const a=e[r],i=t[r];if(!i||Object.keys(a).length!==Object.keys(i).length){n=!0;break}for(const e in a)if(!i[e]||a[e]!==i[e]){n=!0;break}}return n}(Me(We(l.current))[t],Xe(A.current,t))}const r=t?x.current!==n:f.current.has(e)!==n;return n?f.current.add(e):f.current.delete(e),x.current=t?n:!!f.current.size,r},$=Sn((e,t)=>{if(G(e,t),X(e)||!Xe(m.current,e)&&B.current.touched)return!!g(m.current,e,!0)},[G]),J=Sn(async(e,t,n)=>{const r=l.current[e];if(!r)return!1;t&&V();const a=await et(l,u,r);return z(e,a,!1,n),ze(a)},[V,z,u]),Y=Sn(async(e,t)=>{const{errors:r}=await N(n,u,Me(We(l.current))),a=y.current;if(y.current=ze(r),Le(e))e.forEach(e=>{r[e]?g(d.current,e,r[e]):yn(d.current,[e])}),V();else{const n=e,i=Xe(r,n)?{[n]:Xe(r,n)}:{};z(n,i,t||a!==y.current)}return ze(d.current)},[V,z,u,n]),Z=Sn(async(e,t)=>{const r=e||Object.keys(l.current);if(n)return Y(r,t);if(Le(r)){const e=await Promise.all(r.map(async e=>await J(e,!1,!0)));return V(),e.every(Boolean)}return await J(r,t)},[Y,J,V,n]),Q=Sn((e,t,n)=>{const r=$(e,t)||T.current||p.current.has(e);if(n)return Z(e,r);r&&V()},[V,$,Z]);C.current=C.current?C.current:async({type:e,target:t})=>{const r=t?t.name:"",a=l.current,i=d.current,o=a[r],s=Xe(i,r);let c;if(!o)return;const f=e===an,E=it({hasError:!!s,isBlurEvent:f,isOnSubmit:w,isReValidateOnSubmit:q,isOnBlur:M,isReValidateOnBlur:H,isSubmitted:I.current}),h=X(r);let b=T.current||p.current.has(r)||h;if(f&&!Xe(m.current,r)&&B.current.touched&&(g(m.current,r,!0),b=!0),E)return b&&V();if(n){const{errors:e}=await N(n,u,Me(We(a))),t=ze(e);c=Xe(e,r)?{[r]:Xe(e,r)}:{},y.current!==t&&(b=!0),y.current=t}else c=await et(l,u,o);!z(r,c)&&b&&V()};const K=Sn(()=>{const e=ze(A.current)?We(l.current):A.current;W(Me(e)).then(({errors:e})=>{const t=y.current;y.current=ze(e),t&&t!==y.current&&V()})},[V,W]),ee=Sn(e=>{d.current=yn(d.current,[e]),m.current=yn(m.current,[e]),O.current=yn(O.current,[e]),[f,E,v,p].forEach(t=>t.current.delete(e)),(B.current.isValid||B.current.touched)&&V(),n&&K()},[V]),te=Sn((e,t)=>{e&&(Ce(C.current)||function(e,t,n,r){if(!n)return;const{ref:a,ref:{name:i,type:o},mutationWatcher:s}=n;if(!o)return;const c=e[i];if((je(o)||ke(o))&&c){const{options:n}=c;Le(n)&&n.length?(n.forEach(({ref:e},a)=>{if(e&&h(e)||r){const r=e.mutationWatcher;we(e,t),r&&r.disconnect(),n.splice(a,1)}}),n&&!n.length&&delete e[i]):delete e[i]}else(h(a)||r)&&(we(a,t),s&&s.disconnect(),delete e[i])}(l.current,C.current,e,t),ee(e.ref.name))},[ee]),ne=({name:e,type:t,types:n,message:r,preventRender:a})=>{const i=l.current[e];Ge(d.current[e],t,r)||(g(d.current,e,{type:t,types:n,message:r,ref:i?i.ref:{},isManual:!0}),a||V())},re=Sn(e=>async t=>{let r,i;t&&(t.preventDefault(),t.persist());const o=l.current;B.current.isSubmitting&&(_.current=!0,V());try{if(n){i=We(o);const{errors:e,values:t}=await W(Me(i));d.current=e,r=e,i=t}else{const{errors:e,values:t}=await Object.values(o).reduce(async(e,t)=>{if(!t)return e;const n=await e,{ref:r,ref:{name:a}}=t;if(!o[a])return Promise.resolve(n);const i=await U(t);return i[a]?(g(n.errors,a,i[a]),v.current.delete(a),Promise.resolve(n)):(E.current.has(a)&&v.current.add(a),n.values[a]=b(o,r),Promise.resolve(n))},Promise.resolve({errors:{},values:{}}));r=e,i=t}if(ze(r))d.current={},await e(Me(i),t);else{if(a)for(const e in l.current)if(Xe(r,e)){const t=l.current[e];if(t){if(t.ref.focus){t.ref.focus();break}if(t.options){t.options[0].ref.focus();break}}}d.current=r}}finally{I.current=!0,_.current=!1,D.current=D.current+1,V()}},[V,a,U,W,n]);Tn(()=>()=>{S.current=!0,l.current&&Object.values(l.current).forEach(e=>te(e,!0))},[te]),n||(y.current=v.current.size>=E.current.size&&ze(d.current));const ae={dirty:x.current,isSubmitted:I.current,submitCount:D.current,touched:m.current,isSubmitting:_.current,isValid:w?I.current&&ze(d.current):ze(l.current)||y.current};return{watch:function(e,t){const n=Ce(t)?Ce(A.current)?{}:A.current:t,r=We(l.current),a=p.current;if(F&&(B.current.dirty=!0),Ye(e))return at(r,e,a,n);if(Le(e))return e.reduce((e,t)=>{let i;return i=ze(l.current)&&Pe(n)?tt(n,t):at(r,t,a,n),Object.assign(Object.assign({},e),{[t]:i})},{});T.current=!0;const i=!ze(r)&&r||t||A.current;return e&&e.nest?Me(i):i},control:{register:c,unregister:o,setValue:Q,triggerValidation:Z,formState:ae,mode:{isOnBlur:M,isOnSubmit:w},reValidateMode:{isReValidateOnBlur:H,isReValidateOnSubmit:q},errors:d.current,fieldsRef:l,resetFieldArrayFunctionRef:R,fieldArrayNamesRef:L,isDirtyRef:x,readFormStateRef:B,defaultValuesRef:A},handleSubmit:re,setValue:Q,triggerValidation:Z,getValues:Sn(e=>{const t=We(l.current),n=ze(t)?A.current:t;return e&&e.nest?Me(n):n},[]),reset:Sn(e=>{for(const t of Object.values(l.current))if(t&&t.ref&&t.ref.closest)try{t.ref.closest("form").reset();break}catch(Oe){}e&&(A.current=e),Object.values(R.current).forEach(t=>Ze(t)&&t(e)),d.current={},l.current={},m.current={},v.current=new Set,E.current=new Set,O.current={},p.current=new Set,f.current=new Set,T.current=!1,I.current=!1,x.current=!1,y.current=!0,D.current=0,V()},[V]),register:Sn(c,[A.current]),unregister:Sn(o,[te]),clearError:Sn(function(e){Ce(e)?d.current={}:yn(d.current,Le(e)?e:[e]),V()},[]),setError:Sn(function(e,t="",n){Ye(e)?ne(Object.assign({name:e},Pe(t)?{types:t,type:""}:{type:t,message:n})):Le(e)&&(e.forEach(e=>ne(Object.assign(Object.assign({},e),{preventRender:!0}))),V())},[]),errors:d.current,formState:F?new Proxy(ae,{get:(e,t)=>t in e?(B.current[t]=!0,e[t]):{}}):ae}}();return P.createElement(Xt,null,P.createElement(A,R({},t)," ",P.createElement(Gt,null)))},Jt=w.Actions.setWidgetProps,Yt=w.Actions.setWidgetStatus,Zt=M.Provider,Qt=function(e){function t(t,n,r,a){var i=e.call(this)||this;return i.store=t,i.params=n,i.config=r,i.pipe=a,i}return d(t,e),t.prototype.init=function(){this.pipe.subscribe(Ut.Subscriptions(this.store)),this.store.dispatch(Jt(this.config)),this.store.dispatch(Jt(this.params.props)),this.store.dispatch(Yt(w.EWidgetStatus.INIT))},t.prototype.destroy=function(){this.pipe.unsubscribe(),this.store.destroy()},t.prototype.render=function(e){var t=this.store;e.render(P.createElement(w.ContextProvider,{value:{config:this.config}},P.createElement(Zt,{store:t},P.createElement($t,null))))},m([(0,j.Widget)({namespace:"Ordering"}),p("design:paramtypes",[_e,j.ParamsProvider,ee,Ut])],t)}(j.ViewWidget),Kt=Qt,D}()});
//# sourceMappingURL=widget.js.map