import { Actions, Components, FormattedHTMLMessage, Omniture } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { Localization } from "../../Localization";
import { IStoreState } from "../../models";

const {
  Modal
} = Components;

interface IComponentProps {
}

interface IComponentDispatches {
  onContinueClick: () => void;
  closeLightbox: () => void;
}

export const ModalId: string = "APPLICATION_LOGOUT";

const Component: React.FC<IComponentProps & IComponentDispatches> = ({
  onContinueClick,
  closeLightbox
}) => <Modal
  modalId={ModalId}
  onShown={() => {
    Omniture.useOmniture().trackFragment({
      id: "logoutLightbox",
      s_oAPT: {
        actionId: 104
      },
      s_oPRM: Localization.getLocalizedString("APPLICATION_LOGOUT_TITLE"),
      s_oLBC: Localization.getLocalizedString("APPLICATION_LOGOUT_TEXT")
    });
  }}
  title={<FormattedMessage id="APPLICATION_LOGOUT_TITLE" />}>
  <div className="pad-30">
    <FormattedHTMLMessage id="APPLICATION_LOGOUT_TEXT" />
  </div>
  <div className="spacer1 bgGrayLight6" aria-hidden="true"></div>
  <div className="bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg">
    <button id="APP_LOGOUT_CONTINUE" className="btn btn-primary fill-xs" onClick={onContinueClick}><FormattedMessage id="APPLICATION_LOGOUT_CONTINUE" /></button>
    <div className="vSpacer15" aria-hidden="true"></div>
    <button id="APP_LOGOUT_CLOSE" className="btn btn-default fill-xs" onClick={closeLightbox}><FormattedMessage id="APPLICATION_LOGOUT_CLOSE" /></button>
  </div>
</Modal>;

export const ApplicationLogoutLightbox = connect<IComponentProps, IComponentDispatches>(
  ({ }: IStoreState) => ({}),
  (dispatch) => ({
    onContinueClick: () => {
      Omniture.useOmniture().trackAction({
        id: "logoutLightbox",
        s_oAPT: {
          actionId: 647
        },
        s_oBTN: {
          ref: "APP_LOGOUT_CONTINUE"
        }
      });
      dispatch(Actions.applicationLogout());
    },
    closeLightbox: () => {
      Omniture.useOmniture().trackAction({
        id: "logoutLightbox",
        s_oAPT: {
          actionId: 647
        },
        s_oBTN: {
          ref: "APP_LOGOUT_CLOSE"
        }
      });
      dispatch(Actions.closeLightbox(ModalId));
    },
  })
)(Component);
