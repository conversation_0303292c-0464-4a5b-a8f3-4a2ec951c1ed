import { AjaxServices, CommonFeatures, AjaxOptions } from "bwtk";
import { Observable } from "rxjs";
import { Volt, Models } from "../Models";
export declare abstract class BaseClient extends CommonFeatures.BaseClient {
    private config;
    constructor(ajax: AjaxServices, config: Models.IBaseConfig);
    private get useMockData();
    private get mockdata();
    get<T>(path: string, _data?: any, _options?: AjaxOptions | undefined): Observable<T>;
    put<T>(path: string, _data: any, _options?: AjaxOptions | undefined): Observable<T>;
    post<T>(path: string, _data: any, _options?: AjaxOptions | undefined): Observable<T>;
    del(path: string, _options?: AjaxOptions | undefined): any;
    action<T>(action: Volt.IHypermediaAction): Observable<T>;
    get options(): {
        url: string;
        cache: boolean;
        credentials: RequestCredentials;
        headers: {
            [key: string]: string;
        };
    };
}
