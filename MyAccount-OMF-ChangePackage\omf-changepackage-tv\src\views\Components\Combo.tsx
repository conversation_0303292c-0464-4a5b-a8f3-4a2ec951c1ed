import { Components, Omniture, ValueOf, Volt } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { connect } from "react-redux";
import { IStoreState, ITVChannel } from "../../models";
import { toggleSelection } from "../../store";
import { sortOfferings } from "../../utils/Characteristics";
import Channel from "./Channel";
import Price from "./Price";

const {
  Visible
} = Components;

interface IComponentProps extends Volt.IProductOffering {
}

interface IComponentConnectedProps {
}

interface IComponentDispatches {
  onActionClick: (action: Volt.IHypermediaAction) => void;
}

export const Component: React.FC<IComponentProps & IComponentConnectedProps & IComponentDispatches> = ({
  id,
  name,
  imagePath,
  isAlreadyIncludedIn,
  isSelectable,
  isSelected,
  isCurrent,
  isDisabled,
  regularPrice,
  shortDescription,
  longDescription,
  promotionDetails,
  offeringAction,
  childOfferings,
  onActionClick
}) => {
  const [expanded, Expand] = React.useState(false);
  const intl = useIntl();
  React.useEffect(() => {
    expanded &&
            Omniture.useOmniture().trackAction({
              id: "showChannelsClick",
              s_oAPT: {
                actionId: 648
              },
              s_oEPN: "Show Channel"
            });
  }, [expanded]);
  const haveChildren = ValueOf(childOfferings, "length", 0) > 0;
  return <div className={`bell-tv-package bell-tv-movie-pack noBorder ${isSelected ? "selected" : ""} ${isDisabled ? "disabled" : ""}`} id={id}>
    <div className="bell-tv-package-body flexRow">
      <div className="bell-tv-package-left flexGrow flexCol">
        <label id={`combo_${id}`} onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          if (!isSelectable || isDisabled) return;
          else onActionClick(offeringAction);
        }} className="bell-tv-package-checkbox graphical_ctrl graphical_ctrl_checkbox txtSize15 block">
          <input type="checkbox" name="packages" checked={isSelected} disabled={isDisabled || !isSelectable} />
          <span className="block txtSize16 pad-5-left txtBlack">{name}</span>
          <Visible when={isSelectable && isDisabled && !Boolean(isAlreadyIncludedIn)}>
            <span className="block bell-tv-channel-tile-description txtSize12"><dfn><FormattedMessage id="Already selected" /></dfn></span>
          </Visible>
          <Visible when={Boolean(isAlreadyIncludedIn)}>
            <span className="block bell-tv-channel-tile-description txtSize12"><dfn><FormattedMessage id="Already included in" values={{ name: isAlreadyIncludedIn }} /></dfn></span>
          </Visible>
          <Price regularPrice={regularPrice} promotionDetails={promotionDetails} />
          <span className="ctrl_element chk_radius borderGrayLight7"></span>
        </label>
        <ul className="flexRow flexWrap bell-tv-individual-channels virgin-channel-block">
          {
            ValueOf<Array<ITVChannel>>(childOfferings, undefined, [])
              .slice(0, 3)
              .map(
                channel => <li>
                  <img src={ValueOf(channel, "imagePath", "")} alt={ValueOf(channel, "name", "")} title={ValueOf(channel, "name", "")} />
                </li>
              )
          }
        </ul>
        <div className="flexGrow" aria-hidden="true" />
        <div className="spacer15" aria-hidden="true" />
        <Visible when={haveChildren}>
          <div className=" flexBlock flexRow">
            <button id={`View_all_channels_${id}`} onClick={() => Expand(!expanded)} className="btn btn-link no-pad links-blue-on-bg-white txtDecorationNoneHover txtSize14" aria-controls={`View_all_channels_${id}`} aria-expanded={expanded} aria-label={`${intl.formatMessage({id: "Show channels"})} ${intl.formatMessage({id: "FOR_TEXT"})} ${name}`}>
              <span className={`volt-icon links-blue-on-bg-white margin-5-top ${expanded ? "icon-Collapse" : "icon-Expand"}`}>
                <span className="volt-icon path1 icon-Collapse"></span><span className="volt-icon path2 icon-Collapse"></span>
              </span>
              <span className="sans-serif margin-10-left"><FormattedMessage id="Show channels" /></span>
            </button>
          </div>
        </Visible>
      </div>
      <div className="spacer10 flexStatic d-block d-sm-none" aria-hidden="true" />
      <div className="bell-tv-package-right flexStatic block-xs">
        <Visible when={Boolean(imagePath)}>
          <img src={imagePath} alt={name} />
        </Visible>
      </div>
    </div>
    <Visible when={expanded}>
      <div className="bell-tv-package-footer bgGrayLight4 pad-30 pad-15-left-right-xs expanded" role="region" aria-hidden={!expanded}>
        <div className="bell-tv-package-filters-row no-pad">
          <Visible when={Boolean(longDescription || shortDescription)}>
            <p dangerouslySetInnerHTML={{ __html: longDescription || shortDescription }} />
            <hr />
          </Visible>
          <div className="bell-tv-channels bell-tv-channel-picker flexRow">
            {
              sortOfferings(ValueOf(childOfferings, undefined, [])).map((channel: ITVChannel) => <Channel key={channel.id} {...channel} isSelectable={false} />)
            }
          </div>
        </div>
      </div>
    </Visible>
  </div>;
};

var Combo = connect<IComponentConnectedProps, IComponentDispatches, IComponentProps>(
  ({ }: IStoreState) => ({}),
  (dispatch) => ({
    onActionClick: (action: Volt.IHypermediaAction) => dispatch(toggleSelection(action))
  })
)(Component);

export default Combo;
