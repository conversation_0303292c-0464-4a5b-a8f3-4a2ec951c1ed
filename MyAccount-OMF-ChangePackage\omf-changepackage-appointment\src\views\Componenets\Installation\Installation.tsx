import * as React from "react";
import { Heading, HeadingTags } from "../Header";
import { FormattedMessage } from "react-intl";
import { Components, ValueOf, FormattedHTMLMessage } from "omf-changepackage-components";
import { Fieldset, RadioBtn } from "../FormElements";
import { DateAndTime, TimeSlots } from "./DateAndTime";
import { IInstallationAddress, IAvailableDates, ITimeSlots } from "../../../models";
import { getSelectedDate } from "../../../utils/AppointmentUtils";

const { Visible } = Components;

export interface IInstallationProps {
  // preferredDate?: IAvailableDates;
  installationAddress?: IInstallationAddress;
  availableDates?: Array<IAvailableDates>;
  duration: any;
}

export interface IInstallationDispatches {
  initSlickSlider: Function;
}

interface IInstallationState {
  showTimeSlots: boolean;
  selectedDateTime: IAvailableDates | null | "OTHER";
  showOther: boolean;
  preferredDates: Array<IAvailableDates>;
}

export class Component extends React.Component<IInstallationProps & IInstallationDispatches, IInstallationState> {
  constructor(props: any) {
    super(props);
    this.state = {
      showTimeSlots: false,
      selectedDateTime: null,
      preferredDates: [],
      showOther: true
    };
    this.handleChange.bind(this);
    this.changeBtn.bind(this);
  }

  handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    switch (value) {
      case "OTHER":
        // If selection is other show timeslots
        this.setState({
          showTimeSlots: true,
          // selectedDateTime: value as any
        });
        break;
      default:
        this.setState({
          showTimeSlots: false,
          selectedDateTime: JSON.parse(value) as any
        });
        break;
    }
  };

  selectDate = (e: any, day: string, interval: ITimeSlots) => {
    e.preventDefault();
    // Clone preferedDates Object
    const newPreferedDates = [...this.state.preferredDates];
    // Compare if selected date is not same as prefered date from calendar
    if (this.state.preferredDates[0].date === day &&
      this.state.preferredDates[0].timeSlots[0].intervalType === interval.intervalType) {
      // if same, select the default prefered date again
      this.setState({
        preferredDates: this.state.preferredDates,
        selectedDateTime: this.state.preferredDates[0],
        showTimeSlots: false,
        showOther: false
      });
    } else {
      newPreferedDates[1] = { date: day, timeSlots: [{ ...interval, isSelected: true }] };
      this.setState({
        preferredDates: newPreferedDates,
        selectedDateTime: newPreferedDates[1],
        showTimeSlots: false,
        showOther: false
      });
    }
  };

  changeBtn = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    this.setState({
      showOther: true,
      showTimeSlots: true,
      // selectedDateTime: "OTHER",
      // Remove second date selected
      preferredDates: [this.state.preferredDates[0]]
    });
  };

  componentDidUpdate(props: IInstallationProps) {
    if (
      this.props.availableDates && this.props.availableDates.length && JSON.stringify(this.props.availableDates) !== JSON.stringify(props.availableDates)
    ) {
      // let preferredDate: Array<IAvailableDates> = getPreferedDates(this.props.availableDates);
      const selectedDate: Array<IAvailableDates> = getSelectedDate(this.props.availableDates);
      // preferredDate = mergeArrays(...preferredDate, ...selectedDate);
      this.setState({
        preferredDates: selectedDate,
        selectedDateTime: selectedDate[0].date ? selectedDate[0] : null,
        showOther: selectedDate.length > 1 ? false : true
      });
    }
  }

  render() {
    const { installationAddress, availableDates, initSlickSlider } = this.props;
    const { showTimeSlots, selectedDateTime, showOther, preferredDates } = this.state;
    const headingProps = {
      tag: HeadingTags.H2,
      additionalClass: "txtSize22 txtSize24-xs",
      content: "INSTALLATION_DETAILS",
      description: "INSTALLATION_DETAILS_DESC"
    };

    return (
      <div className="margin-30-bottom" id="section1">
        <Heading {...headingProps} />
        <span className="spacer10 flex col-12 clear"></span>
        <p className="noMargin txtItalic"><FormattedMessage id="REQUIRED_INFO_FLAG" /></p>
        <div className="pad-15-top">
          <Fieldset legend={"DATE_AND_TIME_LABEL"} required={true} accessibleLegend={false} additionalClass={"flex-wrap"}>
            <div className="spacer10 visible-xs"></div>
            <div className="flexCol lineHeight18">
              {
                preferredDates && preferredDates.length && preferredDates.map(date => <DateAndTime
                  handleChange={this.handleChange}
                  preferredDate={date}
                  checked={showTimeSlots || (selectedDateTime as IAvailableDates)}
                />)
              }
              <Visible when={showOther}
                placeholder={
                  /** Show Change button if Other is not there and date is selected */
                  <div className="pad-35-left relative changeBtn">
                    <button id="CHANGE_BTN" className="btn btn-link pad-0 txtSize14 txtUnderline txtVirginBlue" onClick={(e) => this.changeBtn(e)}>Change</button>
                  </div>
                }>
                {/** Show other button and hide the second prefered date */}
                <RadioBtn
                  handleChange={this.handleChange}
                  requiredInput={true}
                  checked={showTimeSlots}
                  label={"dateAndTime"}
                  value={"OTHER"} />
              </Visible>

            </div>
            {
              showTimeSlots ? <TimeSlots selectDate={this.selectDate} availableDates={availableDates} initSlickSlider={initSlickSlider} selectedDateTime={selectedDateTime as IAvailableDates} /> : null
            }
          </Fieldset>
          <Visible when={Boolean(selectedDateTime)}>
            {
              selectedDateTime && selectedDateTime !== "OTHER" ?
                <Fieldset legend={"ESTIMATED_DURATION"} required={false} accessibleLegend={false}>
                  <div className="flexCol">
                    <span className="block"><FormattedMessage id={selectedDateTime.timeSlots[0].duration} /></span>
                    <span className="block"><FormattedMessage id="ARRIVAL_OF_TECHNICIAN" /></span>
                  </div>
                </Fieldset> : null
            }
          </Visible>
          <Visible when={Boolean(installationAddress)}>
            <Fieldset legend={"SHIPPING_INSTALLATION_ADDRESS"} required={false} accessibleLegend={false}>
              <div className="flexCol">
                <span className="block">
                  <Visible when={ValueOf(installationAddress, "apartmentNumber", false)}>
                    {ValueOf(installationAddress, "apartmentNumber", "")}&nbsp;-&nbsp;
                  </Visible>
                  {ValueOf(installationAddress, "address1", "")}&nbsp;
                  {ValueOf(installationAddress, "address2", "")}&nbsp;
                  {ValueOf(installationAddress, "streetType", "")},&nbsp;
                  {ValueOf(installationAddress, "city", "")},&nbsp;
                  {ValueOf(installationAddress, "province", "")},&nbsp;
                  {ValueOf(installationAddress, "postalCode", "")}
                </span>
                <span className="margin-10-top"><FormattedHTMLMessage id="CONTACT_US_NOTE" /></span>
              </div>
            </Fieldset>
          </Visible>
        </div>
      </div>
    );
  }
}
