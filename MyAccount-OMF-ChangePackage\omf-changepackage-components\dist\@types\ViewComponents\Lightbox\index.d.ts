import * as React from "react";
import { Models } from "../../Models";
export interface ILightboxContainerProps extends Models.IBaseComponentProps {
    title: any;
    modalId: string;
    flexDisplay?: boolean;
    size?: string;
    permanent?: boolean;
    containerClass?: Array<string>;
    onShow?: () => void;
    onShown?: () => void;
    onHide?: () => void;
    onHidden?: () => void;
    onClose?: Function | undefined;
    onDismiss?: () => void;
    children?: React.ReactNode;
}
export interface ILightboxConnectedProps {
    lightboxData?: any;
}
export interface ILightboxContainerDispatches {
    onCloseLightbox: (modalId: string) => void;
    clearLightboxData: () => void;
}
export declare class Component extends React.Component<ILightboxContainerProps & ILightboxConnectedProps & ILightboxContainerDispatches> {
    static defaultProps: {
        className: string;
        size: string;
    };
    componentDidMount(): void;
    onClose(): void;
    render(): React.JSX.Element;
    componentWillUnmount(): void;
}
export declare const LightboxContainer: import("react-redux").ConnectedComponent<typeof Component, {
    lightboxData?: any;
    ref?: React.LegacyRef<Component> | undefined;
    key?: React.Key | null | undefined;
    title: any;
    modalId: string;
    flexDisplay?: boolean | undefined;
    size?: string | undefined;
    permanent?: boolean | undefined;
    containerClass?: Array<string> | undefined;
    onShow?: (() => void) | undefined;
    onShown?: (() => void) | undefined;
    onHide?: (() => void) | undefined;
    onHidden?: (() => void) | undefined;
    onClose?: Function | undefined;
    onDismiss?: (() => void) | undefined;
    children?: React.ReactNode;
    id?: string | undefined;
    className?: string | undefined;
    style?: {
        [key: string]: string;
    } | undefined;
    context?: React.Context<import("react-redux").ReactReduxContextValue<any, import("redux").UnknownAction> | null> | undefined;
    store?: import("redux").Store | undefined;
}>;
