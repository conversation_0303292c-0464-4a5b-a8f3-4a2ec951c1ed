import * as React from "react";
import { ITVChannel } from "../models";
// import { useHistory } from "./History";
import { toCharacteristicsJSON, getSupportdGenres } from "./Characteristics";
import { iterationCopy } from "./deepCopy";

export interface IFilterState {
  genre: Array<string>;
  language: Array<string>;
  // is4K: boolean;
  isDontHave: boolean;
  isHave: boolean;
  // sort
  sortBy: string;
  sortOrder: "asc" | "desc";
}

export interface IFilteredContentFilters {
  genres: Array<string>;
  languages: Array<string>;
}

export interface IFilteredContent {
  filters: IFilteredContentFilters;
  channels: Array<ITVChannel>;
}

export interface IFilterDispatcher {
  toggleGenre: (genre: string) => void;
  setGenre: (genre?: string) => void;
  toggleLanguage: (language: string) => void;
  setLanguage: (language: string) => void;
  // toggleIs4K: () => void;
  toggleDontHave: () => void;
  toggleHave: () => void;
  reset: () => void;
  // sort
  setSort: (prop: string) => void;
  // static
  hasGenre: (genre?: string) => boolean;
  onlyGenre: (genre: string) => boolean;
  hasLanguage: (genre: string) => boolean;
  hasDontHave: () => boolean;
  hasHave: () => boolean;
  whichSort: () => string;
  selectedGenre: () => string;
  // utility
  getState: () => IFilterState;
  setState: (state: IFilterState) => void;
}

const defaultState: IFilterState = {
  genre: [],
  language: [],
  // is4K: false,
  isDontHave: false,
  isHave: false,
  sortBy: "name",
  sortOrder: "asc"
};

function buildDefaultFilters(): IFilterState {
  // const history = useHistory();
  // debugger;
  return iterationCopy(defaultState);
}

function toggleArrayItem(arr: Array<string>, item: string): Array<string> {
  const index = arr.indexOf(item);
  if (index > -1) arr.splice(index, 1);
  else arr.push(item);
  return arr;
}

export function useFilterDispatcher(defaultFilter?: IFilterState): [IFilterState, IFilterDispatcher] {
  const [filter, setFilter] = React.useState(defaultFilter || buildDefaultFilters());
  const dispatcher: IFilterDispatcher = {
    toggleGenre: (genre: string) => setFilter({ ...filter, genre: toggleArrayItem(filter.genre, genre) }),
    setGenre: (genre?: string) => setFilter({ ...filter, genre: genre ? [genre] : [] }),
    toggleLanguage: (language: string) => setFilter({ ...filter, language: toggleArrayItem(filter.language, language) }),
    setLanguage: (language: string) => setFilter({ ...filter, language: [language] }),
    // toggleIs4K: () => setFilter({ ...filter, is4K: !filter.is4K }),
    toggleDontHave: () => setFilter({ ...filter, isDontHave: !filter.isDontHave }),
    toggleHave: () => setFilter({ ...filter, isHave: !filter.isHave }),
    reset: () => setFilter(buildDefaultFilters()),
    // Sort
    setSort: (prop: string) => setFilter({
      ...filter, sortBy: prop, sortOrder:
                filter.sortBy === prop ? (
                  filter.sortOrder === "desc" ? "asc" : "desc"
                ) : "asc"
    }),
    // Static
    hasGenre: (genre?: string) => genre ? filter.genre.indexOf(genre) > -1 : filter.genre.length === 0,
    onlyGenre: (genre: string) => filter.genre.indexOf(genre) > -1 && filter.genre.length === 1,
    hasLanguage: (language: string) => filter.language.indexOf(language) > -1,
    hasDontHave: () => filter.isDontHave,
    hasHave: () => filter.isHave,
    whichSort: () => filter.sortBy + filter.sortOrder,
    selectedGenre: () => filter.genre[0] || "All",
    // utility
    getState: () => (iterationCopy(filter)),
    setState: (state) => setFilter(iterationCopy(state))
  };
  return [filter, dispatcher];
}

export function usePrevious(value: any): any {
  const ref = React.useRef(null);
  React.useEffect(() => {
    ref.current = value;
  }, [value]);
  return ref.current;
}

export function useChannelsFilter(channels: Array<ITVChannel>, defaultFilter?: IFilterState): [
  IFilteredContent,
  IFilterDispatcher
] {
  const [content, setContent] = React.useState<IFilteredContent>({ filters: {} as IFilteredContentFilters, channels: [] });
  const [filter, dispatcher] = useFilterDispatcher(defaultFilter || buildDefaultFilters());
  const _channels = usePrevious(channels);
  React.useEffect(() => {
    let filters: IFilteredContentFilters = content.filters;
    if (
      !filters.genres ||
            !filters.languages ||
            channels.length !== _channels.length
    ) {
      filters = {
        genres: getSupportdGenres(channels),
        languages: ["English", "French", "Other"] // getSupportdLanguages(channels)
      };
      // channels.reduce(
      //     (acc, channel) => {
      //         const { language, genre } = toCharacteristicsJSON(channel.characteristics);
      //         if (Boolean(genre) && acc.genres.indexOf(genre) < 0) acc.genres.push(genre);
      //         if (Boolean(language) && acc.languages.indexOf(language) < 0) acc.languages.push(language);
      //         return acc;
      //     }, {
      //         genres: [],
      //         languages: []
      //     } as IFilteredContentFilters
      // );
    }
    const filtered = channels.filter(
      channel => {
        const { isSelected, isCurrent } = channel;
        const { language, genre } = toCharacteristicsJSON(channel.characteristics);
        let test: boolean = true;
        if (filter.isDontHave) test = !(isSelected || isCurrent);
        else if (filter.isHave) test = (isSelected || isCurrent);
        if (test && filter.genre.length > 0) test = !!filter.genre.find(g => (genre || "").indexOf(g) > -1);
        if (test && filter.language.length > 0) {
          if (filter.language.indexOf("Other") > -1) test = !/(english|french)/i.test(language || "");
          else test = !!filter.language.find(l => (language || "").indexOf(l) > -1);
        }
        return test;
      }
    ).sort(
      (a: any, b: any) => {
        const testA: string = a[filter.sortBy] || toCharacteristicsJSON(a.characteristics)[filter.sortBy] || "";
        const testB: string = b[filter.sortBy] || toCharacteristicsJSON(b.characteristics)[filter.sortBy] || "";
        return testA.localeCompare(testB, undefined, { numeric: true, sensitivity: "base" }) * (filter.sortOrder === "desc" ? -1 : 1);
        //  return testA.localeCompare(testB) * (filter.sortOrder === "desc" ? -1 : 1);
      }
    );
    setContent({ filters, channels: filtered });
  }, [filter, channels]);

  return [content, dispatcher];
}

export const handlePropFilter = (e: any, filter: any, callback: Function) => {
  if (e.keyCode === 32 || e.type !== "keydown") {
    e.preventDefault();
    e.stopPropagation();
    callback(filter);
  }
};

export const handleVoidFilter = (e: any, callback: Function) => {
  if (e.keyCode === 32 || e.type !== "keydown") {
    e.preventDefault();
    e.stopPropagation();
    callback();
  }
};
