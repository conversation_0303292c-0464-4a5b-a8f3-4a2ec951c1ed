@import "includes/mixins";
.bell-tv-line-up {
    @import "partials/tvsc-posters";
    @import "partials/tvsc-slider";
    @import "partials/tvsc-equipment-list";
    @import "partials/tvsc-service-tiles";
    @import "partials/tvsc-support-links";
    .section-mylineup-packages {
        .section-packages-listing-and-total {
            .section-packages-listing {
                .disabled>a {
                    cursor: default;
                    &:hover {
                        text-decoration: none;
                    }
                }
                table>div>tbody>tr>th,
                table>tbody>tr>th:not(.noPadding) {
                    padding-left: 15px;
                    @media #{$media-mobile} {
                        padding-left: 0;
                    }
                }
                table>div {
                    display: inherit;
                    width: 100%;
                }
                .txtGray:before {
                    color: #a1a5a6;
                }
                .icon-bold-online-builder {
                    margin-left: 10px;
                    &:before {
                        top: 11px;
                        left: 2px;
                    }
                }
                .package-section-change {
                    width: 44px;
                    height: 44px;
                    display: inline-block;
                    position: relative;
                    top: -15px;
                    margin-bottom: -20px;
                    text-decoration: none;
                }
            }
            .section-packages-total {
                table th {
                    padding-right: 20px;
                }
            }
        }
        .section-tv-programming,
        .section-channel-lineup {
            .section-icon {
                margin-right: 20px;
                >i {
                    font-size: 50px;
                }
            }
            .section-content {}
        }
    }
    .banner-package-section table tr>td {
        min-width: 120px;
    }
    .section-ad-banner {
        .panel-body {
            padding: 0
        }
        img {
            max-width: 100%!important;
            height: auto;
        }
    }
    ._ad_Blocked {
        .hide.content-fallback {
            display: block!important
        }
    }
}

#grandfathered-review-modal .flexRow {
    display: flex;
    flex-direction: row;
}

.collapsable-container {
    display: none;
}

.collapsable-container.expanded {
    display: block;
}