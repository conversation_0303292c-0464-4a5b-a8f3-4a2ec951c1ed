import * as React from "react";
import { FormattedMessage } from "react-intl";
import { Volt } from "omf-changepackage-components";

interface IComponentProps {
  message: Volt.IMessage;
}

const Message: React.FunctionComponent<IComponentProps> = ({ message }) => (
  <div className="bgWhite flexBlock pad-20-left pad-20-right pad-25-top pad-25-bottom margin-15-bottom txtBlack">
    <span className="virgin-icon icon-warning txtSize36">
      <span className="virgin-icon path1 yellowIcon"></span>
      <span className="volt-icon path2"></span>
    </span>
    <div className="flexCol pad-15-left content-width valign-top pad-0-xs">
      <p className="virginUltraReg txtSize18 txtDarkGrey1 no-margin-top txtUppercase accss-info-text-header">
        <FormattedMessage id="HEADS_UP" />
      </p>
      <p className="txtSize14 txtGray4A sans-serif no-margin" dangerouslySetInnerHTML={{ __html: message.messageBody }} />
    </div>
  </div>
);

export default Message;