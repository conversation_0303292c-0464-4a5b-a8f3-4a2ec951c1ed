@import "mixins";

/* Bell cart module
 * 
 * Sticky panel attached to
 * the bottom of the screen
*/

.virgin-dockbar {
    bottom: 0;
    z-index: 999;
    position: fixed;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    @media #{$media-mobile} {
        position: relative;
    }
    .continue-button {
        @media #{$media-mobile} {
            bottom: 0;
            z-index: 999;
            position: fixed;
            .virgin-dockbar-row {
                height: 80px !important;
            }
        }
    }
    .virgin-dockbar-panel {
        align-items: center;
        @media #{$media-tab-mobile} {
            width: 100%;
            padding: 0;
            margin: 0;
        }
        .virgin-dockbar-row {
            padding: 15px;
            display: flex;
            flex-direction: column;
            @media #{$media-tablet} {
                padding: 17px;
            }
            @media #{$media-desktop} {
                padding: 20px;
            }
            @media #{$media-mobile} {
                flex-direction: row;
                align-items: center;
                // height: 50px;
            }
            .bgOrange {
                border: none;
                background-color: $virginOrange;
                color: $colorWhite;
                padding: 13px 30px;
                border-radius: 0px;
                font-size: 16px;
            }
            &.tv-cart-continue-row {
                @media #{$media-mobile} {
                    flex-grow: 1;
                }
            }
            &.tv-cart-mobile-back {
                @media #{$media-mobile} {
                    flex-grow: 1;
                }
            }
            .txtCurrency {
                line-height: 1;
            }
        }
        .virgin-dockbar-separator {
            background-color: #D4D4D4;
            margin: 0 20px;
            @media #{$media-tablet} {
                margin: 0 15px;
            }
            height: 50px;
            width: 1px;
            @media #{$media-mobile} {
                margin: 15px 0;
                height: 1px;
                width: 100%;
            }
        }
    }
}

.tv-cart-continue {
    position: relative;
    .counter {
        position: absolute;
        background: #B4212A;
        width: 26px;
        line-height: 26px;
        border-radius: 50%;
        top: -7px;
        right: -8px;
        -webkit-box-shadow: 1px 1px 2px 1px rgba(0, 0, 0, 0.3);
        -moz-box-shadow: 1px 1px 2px 1px rgba(0, 0, 0, 0.3);
        box-shadow: 1px 1px 2px 1px rgba(0, 0, 0, 0.3);
    }
}

.bell-tv-navigator-menu {
    .bell-tv-cart-panel {
        display: none;
        @media #{$media-mobile} {
            display: block;
        }
    }
}

.bell-tv-navigator-tray-menu {
    .bell-tv-cart .bell-tv-cart-panel {
        display: block;
    }
}