import { Init, <PERSON><PERSON>y<PERSON>ll, RenderWidget, ServiceLocator, CommonServices, ApplicationError } from "bwtk";
import { Loader, WidgetMap } from "./controllers";
import { jsonToConfig } from "./utils";

export const loader = new Loader();

interface IWidgets { container: string; widget: string; props: any; localization: string; }
interface IConnectData { widgets: Array<IWidgets>; store: any; localization: any; pipe: any; }

// We needthis for Omniture:
const _connectData: IConnectData = { widgets: [], store: null, localization: null, pipe: null };

/**
 * Initilize widget bundle
 * and instantiate all required components
 * @export
 * @param {Array<IWidgets>} [widgets=[]] Array containing widgets details. e.g.: { container: string; widget: string; props: any; localization: string; }
 * @param {*} config Object containing relevant widget config values. e.g.: {"[namespace]": {"[widget-name]:": {"configValue1": value, "configValue2": value, ...}}}
 * @param {(action: any) => void} listener? Optional global store listener function
 */
export function initialize(
  widgets: Array<IWidgets> = [],
  config: any,
  listener?: (action: any) => void) {
  loader.toggle(true);

  // Decode widget config object and
  //  init BWTK with combined config
  Init({ ...jsonToConfig(config), ...WidgetMap});
  const serviceLocator = ServiceLocator.instance;
  const store = _connectData.store = serviceLocator.getService(CommonServices.Store);
  const localization = _connectData.localization = serviceLocator.getService(CommonServices.Localization);
  _connectData.pipe = serviceLocator.getService(CommonServices.EventStream);
  _connectData.widgets = widgets;

  // Uncomment in case Localization requires credentials to complete API request
  // serviceLocator.addServiceInterceptor(CommonServices.Localization, ["client"], function (invocation: any, proceed: any) {
  //     let result = proceed();
  //     result.config.options.credentials = "include";
  //     return result;
  // });

  // Preload localization bundles before rendering widget(s)
  localization
    .preloadLocaleData(widgets.reduce((acc, item) => ({ ...acc, ...{ [item.widget]: item.localization } }), {}))
    .then(() => {
      widgets
        .filter(widget => Boolean(widget.container))
        .forEach(({ container, widget, props = {} }) => {
          const _container = document.getElementById(container) as HTMLDivElement;
          RenderWidget(widget, _container, props || {});
        });

      // Attache global store listeners to handle
      //  1) loading sequences
      //  2) error sequences
      store.createGlobalActionListener(function (action: any) {
        const { type, payload, meta } = action;
        switch (type) {
          case "FLOW_CONTINUE":
          case "HISTORY_BACK":
          case "HISTORY_FORWARD":
          case "HISTORY_GO":
            loader.toggle(true);
            break;
          case "SHOW_HIDE_LOADER":
            loader.toggle(payload, meta.source);
            break;
          case "ERROR_OCCURED":
          case "OPEN_LIGHTBOX":
            loader.toggle(false);
            break;
          case "LOCALIZATION_ERROR":
            const errorContainer = document.getElementById("errorContainer") as HTMLDivElement;
            loader.toggle(false);
            widgets
              .filter(widget => Boolean(widget.container))
              .forEach(({ container }) => {
                const _container = document.getElementById(container) as HTMLDivElement;
                _container.style.display = "none";
              });
            errorContainer.style.display = "block";
            break;
          default:
            // No action needed for unhandled action types
            break;
        }
        listener &&
                    listener(action);
      });
    })
    .catch((errors: any) => {
      store.dispatch({
        type: "LOCALIZATION_ERROR",
        payload: new ApplicationError("Localization request failed")
      });
      console["error"](`Could not load localization packages: ${errors.map((e: any) => e.widget).join(", ")}`);
    });
}
/**
 * Store getter
 * @export
 * @returns Store service instance
 */
export function store() {
  return _connectData.store;
}
/**
 * Localization getter
 * @export
 * @returns Localization service instance
 */
export function localization() {
  return _connectData.localization;
}
/**
 * Pub-Sub Pipe getter
 * @export
 * @returns Pipe service instance
 */
export function pipe() {
  return _connectData.pipe;
}
/**
 * Destroys all widget instances,
 * currently available on page
 * @export
 */
export const destroy = DestroyAll;
