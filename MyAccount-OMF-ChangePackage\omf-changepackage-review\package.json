{"name": "omf-changepackage-review", "version": "0.1.0", "description": "Virgin ordering flow review and confirmation widget", "main": "dist/widget.js", "private": true, "scripts": {"dev": "webpack -w", "build": "webpack", "build:prod": "webpack --env -p", "build:dev": "webpack --env -d", "clean": "rm -rf ./node_modules & rm -rf ./dist & rm -rf ./package-lock.json & rm -rf ./yarn.lock", "lint": "tslint -p src/tsconfig.json -c tslint.json -t stylish --fix && tsc -p src --pretty --noEmit", "test": "react-scripts test"}, "keywords": [], "author": "IBM", "license": "MIT", "devDependencies": {"@babel/preset-env": "^7.7.7", "@babel/preset-react": "^7.7.4", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.4.0", "@types/jest": "^24.0.17", "@types/node": "12.7.1", "@types/react-router-dom": "^5.1.3", "@types/redux-mock-store": "^1.0.1", "@types/redux-persist": "^4.3.1", "@types/reflect-metadata": "^0.1.0", "babel-jest": "^30.0.5", "bwtk": "git+https://gitlab.int.bell.ca/uxp/bwtk.git#v6.1.0", "husky": "4.3.8", "jest-config": "^30.0.5", "omf-changepackage-components": "file:../omf-changepackage-components", "react-router-dom": "^5.1.2", "redux-mock-store": "^1.5.4", "redux-persist": "^6.0.0", "reflect-metadata": "^0.1.13", "reselect": "^4.0.0", "ts-jest": "^29.4.1"}, "peerDependencies": {"bwtk": "*", "omf-changepackage-components": "*", "react": "*", "react-dom": "*", "react-intl": "*", "react-redux": "*", "redux": "*", "redux-actions": "*", "redux-observable": "*", "rxjs": "*"}, "husky": {"hooks": {"pre-commit": "node pre-commit.js"}}}