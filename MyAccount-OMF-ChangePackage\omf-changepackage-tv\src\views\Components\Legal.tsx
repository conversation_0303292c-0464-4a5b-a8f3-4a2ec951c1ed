import { Components, FormattedHTMLMessage, Omniture } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";

interface IComponentProps {
  pageName: string;
  label: string;
  content: string;
}

export const Footer: React.FC<IComponentProps> = ({
  pageName,
  label,
  content
}) => {
  const [expanded, toggleState] = React.useState(false);
  React.useEffect(() => {
    expanded &&
            Omniture.useOmniture().trackAction({
              id: "ligalStuffClick",
              s_oAPT: {
                actionId: 648
              },
              s_oEPN: "Legal Stuff"
            });
  }, [expanded]);
  return <div className="virginUltraReg more-info pad-15-top accss-focus-outline-override-grey-bg" id="moreInfo">
    <button id="Legal" className="btn btn-link noUnderlineAll noPaddingImp links-blue-on-bg-gray" onClick={() => toggleState(!expanded)} aria-expanded={expanded}>
      <span className={`volt-icon ${expanded ? "icon-collapse_m" : "icon-expand_m"}`} aria-hidden="true" />&nbsp;&nbsp;
      <FormattedMessage id={label} />
    </button>
    <div className="spacer30" aria-hidden="true" />
    <Components.Visible when={expanded}>
      <div className="moreInfoBox">
        <button id="legal_close" type="button" onClick={() => toggleState(false)} className="close moreInfoLink x-inner txtDarkGrey txtSize18" aria-label="close">
          <span className="virgin-icon icon-big_X" aria-hidden="true" />
        </button>
        <FormattedHTMLMessage id={content} />
      </div>
    </Components.Visible>
  </div>;
};
