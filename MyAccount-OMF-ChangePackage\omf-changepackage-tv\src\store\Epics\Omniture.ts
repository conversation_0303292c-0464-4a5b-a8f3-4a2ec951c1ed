import { Injectable } from "bwtk";
import { Actions, EWidgetStatus, Omniture, ValueOf, Utils, EFlowType } from "omf-changepackage-components";
import { combineEpics, Epic, ofType } from "redux-observable";
import { mergeMap, filter, catchError , of } from "rxjs";

import { IStoreState } from "../../models";

const {
  omniPageLoaded,
  omniPageSubmit
} = Actions;

// function catalofToPRD(catalog: ): Array<Omniture.IProduct> {

// }

@Injectable
export class OmnitureEpics {
  widgetState: EWidgetStatus = EWidgetStatus.INIT;

  combineEpics() {
    return combineEpics(
      this.pageLoadedEpic,
      this.pageSubmitEpic
      // this.internetPageSubmitEpic
    );
  }

  /**
     * The only Omniture dependecy for pageload
     * on Internet changepackage page is account details
     * so we wait for those to come from API and then
     * fire the beakon
     * @readonly
     * @private
     * @type {UserAccountEpic}
     * @memberof OmnitureEpics
     */
  private get pageLoadedEpic(): UserAccountEpic {
    return (action$: any, store) =>
      action$.pipe(
        ofType(omniPageLoaded.toString()),
        filter(({ payload }: ReduxActions.Action<any>) => Boolean(payload)),
        mergeMap(({ payload: { name, data = {} } }: ReduxActions.Action<any>) => {
          // const { accountDetails } = store.getState();
          const omniture = Omniture.useOmniture();
          const callPayload: Omniture.IProps = {
            id: `${name}Pageload`,
            s_oSS1: "~",
            s_oSS2: "~",
            s_oSS3: "~",
            s_oPGN: "Setup your service:" + name,
            s_oAPT: "~",
            ...data
          };
          // if (accountDetails.length > 0 && !callPayload.s_oPLE) {
          //     callPayload.s_oPLE = {
          //         type: Omniture.EMessageType.Information,
          //         content: ""
          //     };
          // }
          if (Utils.getFlowType() === EFlowType.TV && !callPayload.s_oAPT) {
            callPayload.s_oAPT = {
              actionId: 394,
              actionresult: 1
            };
          }
          // omniture.trackFragment(callPayload);
          Utils.getFlowType() === EFlowType.TV ? null : omniture.trackFragment(callPayload);
          return of();
        }),
        catchError((error: Response) => of())
      );
  }


  private get pageSubmitEpic(): UserAccountEpic {
    return (action$: any, store) =>
      action$.pipe(
        ofType(omniPageSubmit.toString()),
        mergeMap(() => {
          const { catalog } = store.value as IStoreState;
          const omniture = Omniture.useOmniture();
          omniture.trackAction({
            id: "tvPageSubmit",
            s_oAPT: {
              actionId: 647
            },
            s_oBTN: "Continue",
            s_oPRD: catalog.index
              .filter(prd => prd.isSelected)
              .map(
                prd => ({
                  category: prd.displayGroupKey,
                  name: prd.name,
                  sku: "",
                  quantity: "1",
                  price: ValueOf<string>(prd, "regularPrice.price", "0"),
                  promo: ValueOf<string>(prd, "promotionDetails.promotionalPrice.price", "")
                })
              )
          });
          return of();
        }),
        catchError((error: Response) => of())
      );
  }
}

type UserAccountEpic = Epic<any, any, IStoreState, any>;
