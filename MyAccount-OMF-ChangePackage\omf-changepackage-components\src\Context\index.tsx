import * as React from "react";
import { Models } from "../Models";

/**
 * React Widget context provider
 * allows to propagate immutable values
 * through the widget componets
 * @export
 * @const WidgetContext
 * @extends {React.Context}
 */
export const WidgetContext = React.createContext<Models.IWidgetContext<Models.IBaseConfig | any>>({} as Models.IWidgetContext<Models.IBaseConfig>);

export const ContextProvider: React.ProviderExoticComponent<React.ProviderProps<Models.IWidgetContext<Models.IBaseConfig | any>>> = WidgetContext.Provider;
export const Context: React.ExoticComponent<React.ConsumerProps<Models.IWidgetContext<Models.IBaseConfig | any>>> = WidgetContext.Consumer;
export function withContext<T>(
  Component: any
): React.FC<T> {
  return function (props: T) {
    return (<Context>
      {(context: Models.IWidgetContext<any>) => <Component {...props} {...context} />}
    </Context>);
  };
}
