﻿/*Latest update:Dec.31.2018

Rules:
1. Please keep the fonts grouped to<PERSON><PERSON>er at the top of the file.
2. Please keep the media queries grouped at the bottom of the file.

VirginUltra font*/
@font-face {font-family:'VMUltramagneticNormalRegular';
src:url('../../core/fonts/vmultramagnetic-normal-webfont.eot');
src:url('../../core/fonts/vmultramagnetic-normal-webfont.eot?#iefix') format('embedded-opentype'),url('../../core/fonts/vmultramagnetic-normal-webfont.ttf') format('truetype')}
@font-face {font-family:'BigUltramagneticBold';
src:url('../../core/fonts/vmultramagnetic-bold-webfont.eot');
src:url('../../core/fonts/vmultramagnetic-bold-webfont.eot?#iefix') format('embedded-opentype'),url('../../core/fonts/vmultramagnetic-bold-webfont.ttf') format('truetype')}
.virginUltra {font-family:"BigUltramagneticBold", Helvetica, Arial, sans-serif;
font-weight:normal}
.virginUltraReg {font-family:"VMUltramagneticNormalRegular", Helvetica, Arial, sans-serif;
font-weight:normal}
@font-face {font-family:'virgin-icons';
src:url('../../core/fonts/virgin-icons.eot?qkura8');
src:url('../../core/fonts/virgin-icons.eot?qkura8#iefix') format('embedded-opentype'), url('../../core/fonts/virgin-icons.ttf?qkura8') format('truetype'), url('../../core/fonts/virgin-icons.woff?qkura8') format('woff'), url('../../core/fonts/virgin-icons.svg?qkura8#icomoon') format('svg');
font-weight:normal;
font-style:normal}
.vm-default-page-font {font-family:"Helvetica Neue",Helvetica,Arial,sans-serif}
@font-face {font-family:'icomoon';
src:url('../../core/fonts/virgin-icons.eot?dhg6y8');
src:url('../../core/fonts/virgin-icons.eot?dhg6y8#iefix') format('embedded-opentype'), url('../../core/fonts/virgin-icons.ttf?dhg6y8') format('truetype'), url('../../core/fonts/virgin-icons.woff?dhg6y8') format('woff'), url('../../core/fonts/virgin-icons.svg?dhg6y8#icomoon') format('svg');
font-weight:normal;
font-style:normal}
/*Virgin Icon fonts*/
[class^="icon-"], [class*=" icon-"] {/* use !important to prevent issues with browser extensions that change fonts */
font-family:'virgin-icons' !important;
speak:none;
font-style:normal;
font-weight:normal;
font-variant:normal;
text-transform:none;
line-height:1;
/* Better Font Rendering =========== */
-webkit-font-smoothing:antialiased;
-moz-osx-font-smoothing:grayscale}
.sans-serif{font-family:"Helvetica",Arial, sans-serif;letter-spacing:0}

/*Virgin Icon Font paths*/
.icon-VM-logo .path1:before {
  content: "\e959";
  color: #fff;
}
.icon-VM-logo .path2:before {
  content: "\e95a";
  color: #c00;
  margin-left: -1.9658203125em;
}
.icon-VM-logo .path3:before {
  content: "\e95b";
  color: #000;
  margin-left: -1.9658203125em;
}
.icon-VM-logo .path4:before {
  content: "\e95c";
  color: #000;
  margin-left: -1.9658203125em;
}
.icon-VM-logo .path5:before {
  content: "\e95d";
  color: #000;
  margin-left: -1.9658203125em;
}
.icon-VM-logo .path6:before {
  content: "\e95e";
  color: #000;
  margin-left: -1.9658203125em;
}
/*Delete search icon*/
.icon-delete_in_search-solid .path1:before {
  content: "\e972";
  color: #fff;
}
.icon-delete_in_search-solid .path2:before {
  content: "\e973";
  color: #333;
  margin-left: -1em;
}
/*END: Virgin Icon Font paths*/

.icon-check:before {content:"\e900";
color:#329c2e;
font-size:22px}
.icon-check-reg:before {content:"\e900";
color:#2c9e25;
font-size:22px}
.icon-check-lg:before {content:"\e900";
color:#329c2e;
font-size:34px}
.icon-check-sm:before {content:"\e900";
color:#329c2e;
font-size:16px}
.icon-exclamation-alert:before {content:"\e901";
color:#E99E00;
font-size:22px}
.icon-exclamation-alert:before {content:"\e901";
color:#E99E00;
font-size:22px}
.icon-exclamation-alert-lg:before {content:"\e901";
color:#E99E00;
font-size:34px}
.icon-exclamation-alert-sm:before {content:"\e901";
color:#f5bf3d;
font-size:16px}
.icon-exclamation-error:before {content:"\e901";
color:#cc0000;
font-size:22px}
.icon-exclamation-error-sm:before {content:"\e901";
color:#cc0000;
font-size:16px}
.icon-exclamation-error-lg:before {content:"\e901";
color:#cc0000;
font-size:34px}
.icon-iIcon:before {content:"\e902";
color:#257fa3;
font-size:22px}
.icon-iIcon-sm.alignIconWithText:before {vertical-align:-20%}
.icon-iIcon-lg:before {content:"\e902";
color:#257fa3;
font-size:34px}
.icon-iIcon-sm:before {content:"\e902";
color:#257fa3;
font-size:16px}
.icon-modal-close:before {content:"\e903";
color:#333;
border:1px solid #fff;
border-radius:50%;
font-size:25px;
box-shadow:-1px 2px 2px #9e9e9e;
background-color:#fff}
.icon-retry:before {content:"\e904";
color:#000;
font-size:18px}
.icon-retry.alignIconWithText:before {vertical-align:-20%}
.icon-email-envelopeIcon:before {content:"\e905";
font-size:48px;
color:#cd2027}
.icon-expand:before {content:"\e906";font-size:33px}
.icon-collapse:before {content:"\e907";font-size:33px}
.icon-close-btn:before {content: "\e908";font-size:26px}

.icon-sort-descend:before {
  content: "\e909";
}
.icon-sort-ascend:before {
  content: "\e90a";
}
.icon-print-btn:before {
  content: "\e90b";
}
.icon-next-btn:before {
  content: "\e90c";
}
.icon-prev-btn:before {
  content: "\e90d";
}
.icon-more-details:before {
  content: "\e90e";
}
.icon-info-solid:before {
  content: "\e90f";
}
.icon-plus-solid:before {
  content: "\e910";
}
.icon-close-solid:before {
  content: "\e910";
  display:inline-block;
  -ms-transform: rotate(45deg); /* IE 9 */
  -webkit-transform: rotate(45deg); /* Safari */
  transform: rotate(45deg);
}
.icon-minus-solid:before {
  content: "\e911";
}
.icon-exclam-outline:before {
  content: "\e912";
}
.icon-email:before {
  content: "\e913";
}
.icon-custom-select:before {
  content: "\e914";
}
.icon-close-btn-big:before, .icon-x-close:before {
  content: "\e915";
}
.icon-home-phone:before {
  content: "\e9bd";
}   
.icon-internet:before {
  content: "\e9be";
}
.icon-mobile:before {
  content: "\e9bf";
}
.icon-tablet:before {
  content: "\e9c0";
}
.icon-virgin-current-bill:before {
  content: "\e916";
}
.icon-download:before {
  content: "\e917";
}
.icon-email2:before {
  content: "\e918";
}
.icon-virgin-multiple:before {
  content: "\e919";
}
.icon-virgin-collapse:before {
  content: "\e91a";
}
.icon-virgin-expand:before {
  content: "\e91b";
}
.icon-virgin-detailed-bill:before {
  content: "\e91c";
}
.icon-virgin-i-icon:before {
  content: "\e91d";
}
.icon-virgin-wifi:before {
  content: "\e91e";
}
.icon-sort-dec-virgin:before {
  content: "\e91f";
}
.icon-sort-asc-virgin:before {
  content: "\e920";
}
.icon-profile:before {
  content: "\e921";
}
.icon-next-btn2:before {
  content: "\e922";
}
.icon-prev-btn2:before {
  content: "\e922";
  display:inline-block;
  -ms-transform: rotate(180deg); /* IE 9 */
  -webkit-transform: rotate(180deg); /* Safari */
  transform: rotate(180deg);
}
.icon-location:before {
  content: "\e923";
}
.icon-hamburger-menu:before {
  content: "\e924";
}
.icon-check-symbol:before {
  content: "\e925";
}
.icon-error-symbol:before {
  content: "\e926";
}
.icon-triangle-down:before {
  content: "\e927";
}
.icon-envelope-bold:before {
  content: "\e928";
}
.icon-virgin-scroll-left:before {
  content: "\e929";
}
.icon-virgin-scroll-right:before {
  content: "\e92a";
}
.icon-calendar:before {
  content: "\e92b";
}
.icon-daily-usage-graph:before {
  content: "\e92c";
}
.icon-data-calculator-chart:before {
  content: "\e92d";
}
.icon-infinity-simbol:before {
  content: "\e92e";
}
.icon-light-bulb:before {
  content: "\e92f";
}
.icon-lock:before {
  content: "\e930";
}
.icon-planet-world:before {
  content: "\e931";
}
.icon-search:before {
  content: "\e932";
}
.icon-timer-clock:before {
  content: "\e933";
}
.icon-usage-details:before {
  content: "\e934";
}
.icon-activate:before {
  content: "\e935";
}
.icon-email3:before {
  content: "\e936";
}
.icon-facebook:before {
  content: "\e937";
}
.icon-find-store:before {
  content: "\e938";
}
.icon-instagram:before {
  content: "\e939";
}
.icon-maple-leaf:before {
  content: "\e93a";
}
.icon-member-silhouette:before {
  content: "\e93b";
}
.icon-tools-support:before {
  content: "\e93c";
}
.icon-top-up:before {
  content: "\e93d";
}
.icon-twitter:before {
  content: "\e93e";
}
.icon-upgrade-phone:before {
  content: "\e93f";
}
.icon-why-virgin:before {
  content: "\e940";
}
.icon-youtube:before {
  content: "\e941";
}
.icon-laptop2:before {
  content: "\e942";
}
.icon-hyperlink-chain:before {
  content: "\e943";
}
.icon-unlink-account:before {
  content: "\e944";
}
.icon-tv-screen2:before {
  content: "\e948";
}
.icon-trash-icon:before {
  content: "\e947";
}
.icon-home-phone2:before {
  content: "\e946";
}
.icon-edit-small:before {
  content: "\e945";
}
.icon-package:before {
  content: "\e949";
}
.icon-echat:before {
  content: "\e94a";
}
.icon-youtube2:before {
  content: "\e94b";
}
.icon-video-game-controller:before {
  content: "\e94c";
}
.icon-travel:before {
  content: "\e94d";
}
.icon-text-911:before {
  content: "\e94e";
}
.icon-smart-watch:before {
  content: "\e94f";
}
.icon-rate-plan:before {
  content: "\e950";
}
.icon-password:before {
  content: "\e951";
}
.icon-my-buddies:before {
  content: "\e952";
}
.icon-light:before {
  content: "\e953";
}
.icon-headset:before {
  content: "\e954";
}
.icon-credit-card:before {
  content: "\e955";
}
.icon-caller-id:before {
  content: "\e956";
}
.icon-add-ons:before {
  content: "\e957";
}
.icon-check-mark:before {
  content: "\e958";
}
.icon-star:before {
  content: "\e60f";
}
.icon-paper-plane:before {
  content: "\e6ce";
}
.icon-close-thin:before {
  content: "\e624";
}
.icon-world:before {
  content: "\e95f";
}
.icon-Speech_bubble:before {
  content: "\e960";
}
.icon-bill-2:before {
  content: "\e961";
}
.icon-BigrightArrow:before {
  content: "\e962";
}
.icon-ArrowDowncopy:before {
  content: "\e963";
}
.icon-steve_val:before {
  content: "\e965";
}
.icon-store_locator:before {
  content: "\e966";
}
.icon-Triangle:before {
  content: "\e967";
}
.icon-user_profile-2:before {
  content: "\e968";
}
.icon-delete_in_search:before {
  content: "\e969";
}
.icon-home_phone-2:before {
  content: "\e96a";
}
.icon-internet-2:before {
  content: "\e96b";
}
.icon-magnifyingglass:before {
  content: "\e96c";
}
.icon-mobilephone:before {
  content: "\e96d";
}
.icon-mobility-view-more:before {
  content: "\e96e";
}
.icon-mobility_bill:before {
  content: "\e96f";
}
.icon-make-a-payment:before {
  content: "\e964";
}
.icon-steve_val-2:before {
  content: "\e970";
}
.icon-turbo-hub:before {
  content: "\e971";
}
.icon-my-bill:before {
  content: "\e974";
}

.icon-circle-silhouette:before {
  content: "\ea4d";
}
.icon-circle-multiple-homes:before {
  content: "\ea4f";
}
.icon-circle-location:before {
  content: "\ea50";
}
.icon-circle-home:before {
  content: "\ea51";
}
.icon-circle-cellphone-chart-up:before {
  content: "\ea52";
}
.icon-circle-cellphone:before {
  content: "\ea53";
}
.icon-circle-building:before {
  content: "\ea54";
}
.icon-cicle-clock:before {
  content: "\ea57";
}
.icon-Product_Catalog:before {
  content: "\ea58";
}
.icon-New_Customer:before {
  content: "\ea59";
}
.icon-Filter:before {
  content: "\ea5a";
}
.icon-Internet:before {
  content: "\ea61";
}
.icon-SatTV:before {
  content: "\ea60";
}
.icon-FibeTV:before {
  content: "\ea5f";
}
.icon-homephone:before {
  content: "\ea5e";
}
.icon-bell:before {
  content: "\ea5d";
}
.icon-HomeBundle:before {
  content: "\ea5c";
}

.icon-Clock-outlined:before {
  content: "\ea6a";
}
.icon-home-outlined:before {
  content: "\ea69";
}
.icon-location-outlined:before {
    content: "\ea68";
}
.icon-cellphone-outlined:before {
  content: "\ea67";
}
.icon-Silhouette-outlined:before {
  content: "\ea66";
}
.icon-cellphone-chart-up-outlined:before {
  content: "\ea65";
}
.icon-building-outlined:before {
  content: "\ea64";
}

.icon-AccountMigration:before {
  content: "\ea75";
}
.icon-AddLine:before {
  content: "\ea74";
}
.icon-corporate-circled:before {
  content: "\ea73";
}
.icon-HardwareUpgrade:before {
  content: "\ea72";
}
.icon-AccountManagement:before {
  content: "\ea70";
}
.icon-location-circled:before {
  content: "\ea71";
}
.icon-mobility-circled:before {
  content: "\ea6f";
}
.icon-Personal:before {
  content: "\ea6e";
}
.icon-phone-increase-circled:before {
  content: "\ea6d";
}
.icon-ProductCatalogue:before {
  content: "\ea6c";
}
.icon-Search:before {
  content: "\ea6b";
}
.icon-Audio:before {
  content: "\e975";
}
.icon-Cases:before {
  content: "\e976";
}
.icon-CCAndBatteries:before {
  content: "\e977";
}
.icon-Essentials:before {
  content: "\e978";
}
.icon-HandsFree:before {
  content: "\e979";
}
.icon-IOT:before {
  content: "\e97a";
}
.icon-MobileDevices:before {
  content: "\e97b";
}
.icon-SaleAndClearance:before {
  content: "\e97c";
}
.icon-ScreenProtection:before {
  content: "\e97d";
}
.icon-Wearables:before {
  content: "\e97e";
}
.icon-turbo_stick:before {
  content: "\eaa0";
}
.icon-tablet-landscape:before {
  content: "\eaa1";
}
.icon-mobility-promotion:before {
    content: "\e97f";
}
.icon-order-dashboard:before {
    content: "\e980";
}
.icon-order-search:before {
    content: "\e981";
}
.icon-product-catalogue:before {
    content: "\e982";
}
.icon-add_device:before {
    content: "\e983";
}
.icon-new_customer:before {
    content: "\e984";
}
.icon-upgrade_device:before {
    content: "\e985";
}
.icon-a1_effective_date:before {
    content: "\e986";
}
.icon-a2_payment_confirmation:before {
    content: "\e987";
}
.icon-a3_payment_received:before {
    content: "\e988";
}
.icon-a4_promote_activity_online_mobility:before {
    content: "\e989";
}
.icon-a6_promote_activity_online_internet:before {
    content: "\e98a";
}
.icon-a8_promote_activity_online_payment:before {
    content: "\e98b";
}
.icon-a10_appointment_online_details:before {
    content: "\e98c";
}
.icon-a11_appointment_tv_installation:before {
    content: "\e98d";
}
.icon-a12_appointment_modem_installation:before {
    content: "\e98e";
}
.icon-a14_speed_boost_eligible:before {
    content: "\e98f";
}
.icon-a15_date_first_bill_available:before {
    content: "\e990";
}
.icon-a16_bill_is_ready:before {
    content: "\e991";
}
.icon-a18_roaming_travel_pass:before {
    content: "\e992";
}
.icon-a19_travel_section:before {
    content: "\e993";
}
.icon-a22_HUG_eligible:before {
    content: "\e994";
}
.icon-a23_partial_HUG_eligible:before {
    content: "\e995";
}
.icon-a24_add_a_line:before {
    content: "\e996";
}
.icon-a27_mobility_modification:before {
    content: "\e997";
}
.icon-a28_credit_card_expiration_date:before {
    content: "\e998";
}
.icon-a29_promo_soon_ending:before {
    content: "\e999";
}
.icon-a30_bill_update:before {
    content: "\e99a";
}
.icon-a31_pay_your_bill_now:before {
    content: "\e99b";
}
.icon-a33_ebill:before {
    content: "\e99c";
}
.icon-a34_service_cancelled:before {
    content: "\e99d";
}
.icon-a35_bill_with_loyalty_price:before {
    content: "\e99e";
}
.icon-a37_equipment_shipped:before {
    content: "\e99f";
}
.icon-a38_equipment_received:before {
    content: "\e9a0";
}
.icon-a39_equipment_to_be_return:before {
    content: "\e9a1";
}
.icon-a40_equipment_return:before {
    content: "\e9a2";
}
.icon-a50_generic_exclamation:before {
    content: "\e9a3";
}
.icon-i1_effective_date:before {
    content: "\e9a4";
}
.icon-i2_payment_confirmation:before {
    content: "\e9a5";
}
.icon-i3_payment_received:before {
    content: "\e9a6";
}
.icon-i4_promote_activity_online_mobility:before {
    content: "\e9a7";
}
.icon-i6_promote_activity_online_internet:before {
    content: "\e9a8";
}
.icon-i8_promote_activity_online_payment:before {
    content: "\e9a9";
}
.icon-i10_appointment_online_details:before {
    content: "\e9aa";
}
.icon-i11_appointment_tv_installation:before {
    content: "\e9ab";
}
.icon-i12_appointment_modem_installation:before {
    content: "\e9ac";
}
.icon-i14_speed_boost_eligible:before {
    content: "\e9ad";
}
.icon-i15_date_first_bill_available:before {
    content: "\e9ae";
}
.icon-i16_bill_is_ready:before {
    content: "\e9af";
}
.icon-i18_roaming_travel_pass:before {
    content: "\e9b0";
}
.icon-i19_travel_section:before {
    content: "\e9b1";
}
.icon-i22_HUG_eligible:before {
    content: "\e9b2";
}
.icon-i23_partial_HUG_eligible:before {
    content: "\e9b3";
}
.icon-i24_add_a_line:before {
    content: "\e9b4";
}
.icon-i27_mobility_modification:before {
    content: "\e9b5";
}
.icon-i28_credit_card_expiration_date:before {
    content: "\e9b6";
}
.icon-i29_promo_soon_ending:before {
    content: "\e9b7";
}
.icon-i30_bill_update:before {
    content: "\e9b8";
}
.icon-i31_pay_your_bill_now:before {
    content: "\e9b9";
}
.icon-i33_ebill:before {
    content: "\e9ba";
}
.icon-i34_service_cancelled:before {
    content: "\e9bb";
}
.icon-i35_bill_with_loyalty_price:before {
    content: "\e9bc";
}
.icon-i37_equipment_shipped:before {
    content: "\e9c1";
}
.icon-i38_equipment_received:before {
    content: "\e9c2";
}
.icon-i39_equipment_to_be_return:before {
    content: "\e9c3";
}
.icon-i40_equipment_return:before {
    content: "\e9c4";
}
.icon-i42_tv_promo:before {
    content: "\e9c5";
}
.icon-i50_generic_exclamation:before {
    content: "\e9c6";
}
.icon-a43_internet_promo:before {
    content: "\e9c7";
}
.icon-08_offres_et_rabais:before {
    content: "\e9c8";
}

.icon-sales-clearance_VM:before {
    content: "\e9c9";
}
.icon-customer_digital_pin:before {
    content: "\e986";
}
.icon-digital_pin_inverted:before {
    content: "\e9cb";
}
.icon-a1_effective_date:before {
    content: "\e9eb";
}
.icon-a2_payment_confirmation:before {
    content: "\e9ec";
}
.icon-a3_payment_received:before {
    content: "\e9ed";
}
.icon-a4_promote_activity_online_mobility:before {
    content: "\e9ee";
}
.icon-a6_promote_activity_online_internet:before {
    content: "\e9ef";
}
.icon-a8_promote_activity_online_payment:before {
    content: "\e9f0";
}
.icon-a10_appointment_online_details:before {
    content: "\e9f1";
}
.icon-a11_appointment_tv_installation:before {
    content: "\e9f2";
}
.icon-a12_appointment_modem_installation:before {
    content: "\e9f3";
}
.icon-a14_speed_boost_eligible:before {
    content: "\e9f4";
}
.icon-a15_date_first_bill_available:before {
    content: "\e9f5";
}
.icon-a16_bill_is_ready:before {
    content: "\e9f6";
}
.icon-a18_roaming_travel_pass:before {
    content: "\e9f7";
}
.icon-a19_travel_section:before {
    content: "\e9f8";
}
.icon-a22_HUG_eligible:before {
    content: "\e9f9";
}
.icon-a23_partial_HUG_eligible:before {
    content: "\e9fa";
}
.icon-a24_add_a_line:before {
    content: "\e9fb";
}
.icon-a27_mobility_modification:before {
    content: "\e9fc";
}
.icon-a28_credit_card_expiration_date:before {
    content: "\e9fd";
}
.icon-a29_promo_soon_ending:before {
    content: "\e9fe";
}
.icon-a30_bill_update:before {
    content: "\e9ff";
}
.icon-a31_pay_your_bill_now:before {
    content: "\ea00";
}
.icon-a33_ebill:before {
    content: "\ea01";
}
.icon-a34_service_cancelled:before {
    content: "\ea02";
}
.icon-a35_bill_with_loyalty_price:before {
    content: "\ea03";
}
.icon-a37_equipment_shipped:before {
    content: "\ea04";
}
.icon-a38_equipment_received:before {
    content: "\ea05";
}
.icon-a39_equipment_to_be_return:before {
    content: "\ea06";
}
.icon-a40_equipment_return:before {
    content: "\ea07";
}
.icon-a43_internet_promo:before {
    content: "\ea08";
}
.icon-a50_generic_exclamation:before {
    content: "\ea09";
}

/* Virgin background - changes at mobile widths to white background (no image) and different Title section (.titleTabMob)- see media query. */
.bgVirginRegistrationImg {background:#000;
background-image:url(../../content/img/background1.jpg);
background-position:center 0;
background-repeat:no-repeat}
.titleTabMob {background-image:none;
font-size:48px;
text-align:left;
line-height:50px;
padding:70px 0}

/*header helpers*/
.virginLogo {width:85px}
.flex-box-wrapper {display: flex; display: -webkit-flex;flex-wrap: wrap;-webkit-flex-wrap: wrap;}
.v-resp-header{
    height:75px;
    background-color: #2D2D2D;
}
@media screen and (max-width: 767px){
    .v-resp-header{
         height:54px; 
    } 
}

/*Padding*/
.pad-bottom {padding-bottom:20px}
.pad-top {padding-top:20px}
.no-pad {padding:0}
.no-pad-left{padding-left:0}
.no-pad-right{padding-right:0}
.no-pad-top{padding-top:0}
.no-pad-bottom{padding-bottom:0}
.pad-5{padding:5px}
.pad-5-left{padding-left:5px}
.pad-5-right{padding-right:5px}
.pad-5-top{padding-top:5px}
.pad-5-bottom{padding-bottom:5px}
.pad-10{padding:10px}
.pad-10-left-imp{padding-left:10px !important}
.pad-10-right-imp{padding-right:10px !important}
.pad-10-left{padding-left:10px}
.pad-10-right{padding-right:10px}
.pad-10-top{padding-top:10px}
.pad-10-bottom{padding-bottom:10px}
.pad-15{padding:15px}
.pad-15-top{padding-top:15px}
.pad-15-bottom{padding-bottom:15px}
.pad-15-left{padding-left:15px}
.pad-15-right{padding-right:15px}
.pad-20{padding:20px}
.pad-20-left{padding-left:20px}
.pad-20-right{padding-right:20px}
.pad-20-top{padding-top:20px}
.pad-20-bottom{padding-bottom:20px}
.pad-25-left{padding-left:25px}
.pad-25-right{padding-right:25px}
.pad-25-top{padding-top:25px}
.pad-25-bottom{padding-bottom:25px}
.pad-30{padding:30px}
.pad-30-top{padding-top:30px}
.pad-30-bottom{padding-bottom:30px}
.pad-30-left{padding-left:30px}
.pad-30-right{padding-right:30px}
.pad-40{padding:40px}
.pad-40-left{padding-left:40px}
.pad-40-right{padding-right:40px}
.pad-40-left{padding-left:40px}
.pad-40-top{padding-top:40px}
.pad-40-bottom {padding-bottom:40px}
.pad-60{padding:60px}
.pad-60-top{padding-top:60px}
.pad-60-bottom{padding-bottom:60px}
.pad-60-left{padding-left:60px}
.pad-60-right{padding-right:60px}
.pad-10{padding:10px}
.pad-20{padding:20px}
.pad-30{padding:30px}
.pad-40{padding:40px}
.vPaddingL06{padding-left:6px}
.vPaddingL10 {padding-left:10px} 
.vPaddingL20 {padding-left:20px}
.vPaddingL30 {padding-left:30px}
.vPaddingR85 {padding-right:85px}
.vPaddingTB30 {padding-top:30px; padding-bottom:30px; }
.paddingR20 {padding-right:20px}
.paddingR30 {padding-right:30px}
.paddingL30 { padding-left:30px}
.pad-20lg-30xs{padding:30px}
.paddingL26-md-lg{padding-left:26px} 
/*padding ends */

/*Margins*/
.no-margin {margin:0}
.no-margin-bottom {margin-bottom:0}
.no-margin-top {margin-top:0}
.no-margin-right {margin-right:0}
.no-margin-left {margin-left:0}
.margin-2-top { margin-top: 3px; } 
.margin-3-top { margin-top: 3px; }  
.margin-5-left{margin-left:5px}
.margin-5-right{margin-right:5px}
.margin-5-top{margin-top:5px}
.margin-5-bottom{margin-bottom:5px}
.margin-7-right{margin-right:7px}
.margin-7-left{margin-left:7px}
.margin-8-right{margin-right:8px}
.margin-8-left{margin-left:8px}
.margin-10-top{margin-top:10px}
.margin-10-bottom{margin-bottom:10px}
.margin-10-right{margin-right:10px}
.margin-10-left{margin-left:10px}
.margin-12-left{margin-right:12px}
.margin-12-right{margin-right:12px}
.margin-15-left{margin-left:15px}
.margin-15-right{margin-right:15px}
.margin-15-top{margin-top:15px}
.margin-15-bottom{margin-bottom:15px}
.margin-20-top{margin-top:20px}
.margin-20-bottom{margin-bottom:20px}
.margin-20-right{margin-right:20px}
.margin-right-20{margin-right:20px}
.margin-20-left{margin-left: 20px}
.margin-30-left{margin-left:30px}
.margin-30-right{margin-right:30px}
.margin-40-left{margin-left:40px}
.margin-40-right{margin-right:40px}
.margin-60-left{margin-left:60px}
.margin-60-right{margin-right:60px}


/*spacing helpers */
.spacer10 {height:10px}
.spacer30 {height:30px}
.spacer35 {height:35px}
.spacer45 {height:45px}
.spacer60 {height:60px}
.spacer75 {height:75px}
.spacer90 {height:90px}
.spacer170 {height:170px}
.spacer250 {height:250px}
.vSpacer150 {width:150px}
.vSpacer160 {width:160px}

/*margin helpers */
.noMarginAll{margin:0}
.noMarginTop{margin-top:0}
.noMarginBottom{margin-bottom:0}
.marginTop05 {margin-top:5px}
.marginTop07 {margin-top:7px}
.marginR-40{margin-right:-40px}
.marginCompensator{margin-left:-21px}

/*bgcolors*/
.bgVirginGrayDark {background-color:#2D2D2D}
.bgVirginGrayMed {background-color:#666}
.bgVirginBlue {background-color:#00b4e1}
.bgGray20 {background-color:#efefef}
.bgGrayVirginModalHeader{background-color:#dbdbdb}
.bg-purewhite { background-color:#fff;}

.bg-vm-red{background-color:#cc0000}
.bg-vm-jetGray{background-color:#333333}  
.bg-vm-cyanCorn{background-color:#2390b9}
.bg-vm-lightGreen{background-color:#a1e67c}

.bg-gray-ba { background-color: #babec2 }
.border-gray-ba { border-color: #babec2 }
.bg-timberwolf-vm { background-color: #d7d7d7}
.borderGrayLight6 {border: 1px solid #d7d7d7;}

/*textcolors*/
.txtGrayLight2{color:#999}
.txtRedV, .txtRedV a, a.txtRedV :active,  a.txtRedV:visited,  a.txtRedV:hover {color:#CC0000}
.txtGrayDark{color:#333}
.txtVirginBlue {color:#2390b9} 
.txt-vm-isabelGray{color:#efefef}
.txt-vm-whiteSmoke{color:#f4f4f4}
.txt-vm-timberwolf{color:#d7d7d7}
.txt-vm-cyanCorn{color:#2390b9}
.txt-vm-lightGreen{color:#a1e67c}
.txt-vm-whitepearl, .txt-vm-whitepearl a, a.txt-vm-whitepearl :active,  a.txt-vm-whitepearl:visited {color:#ffffff}
 a.txt-vm-whitepearl:hover {color: #d7d7d7;}   

/*text helpers*/
.txtSize22{font-size:22px}
.txtUnderline{text-decoration:underline;text-decoration-skip:ink}
.flipFloatLR {float:left} 
.flipTxtAlignRL {text-align:left}
.inlineBlock {display:inline-block}
.wordBreak {word-break:break-all}
.align-self-center{-webkit-align-self: center;  align-self: center;}
.txt-overflow-ellipsis{white-space: nowrap;}
.txt-align-center {text-align:center}
.cursor-pointer{cursor: pointer;}

/*text size*/
.h1{font-size:41px;line-height:45px; text-transform:uppercase}
.h2{font-size:28px;line-height:32px; text-transform:uppercase}
.h3{font-size:26px;line-height:30px; text-transform:uppercase}
.h4{font-size:22px;line-height:26px; text-transform:uppercase}
.h5{font-size:18px;line-height:22px;}
.h6{font-size:14px;line-height:18px;}
.txtSize10{font-size:10px}


/*borders helper*/
.border-container{border:1px solid #dcdcdc}
.border-table-line{border:1px solid #d7d7d7}
.border-bottom-container{border-bottom:1px solid #dcdcdc}
.border-v-dark-gray {border:1px solid #2D2D2D}
.noRadius {border-radius:0}
.noBorder{border:0}
.border-allRound{-webkit-border-radius:50%; -moz-border-radius:50%; border-radius:50%;}
.border-pillStyle {border: 5px solid #FFF; box-shadow: 0 0 2px #000;}

/*table */

.table{display: table; margin-bottom: 0; table-layout: fixed; width: 100%;}
.table-cell { display: table-cell; float: none;}

/* footer */
.footer {margin-top:-20px}
ul.footerList {padding-left:0}
ul.footerList li {display:inline-block;
padding-right:25px}
ul.footerList li a {text-decoration:none;
color:#999}
ul.footerList li a:hover {color:#cbcbcb}
/* END footer */

/*links helper*/
a, a.txtUnderline, a.btn.txtUnderline {color:#2390b9;}
a.txtWhite, a.txtWhite.txtUnderline{color:#fff}
a.txtWhite:hover, a.txtWhite:focus, a.txtWhite.txtUnderline:hover, a.txtWhite.txtUnderline:focus{color:#d7d7d7}
a.txtWhite.txtUnderline{text-decoration:underline;text-decoration-skip:ink}
a:hover,a:focus, a.txtUnderline:hover, a.btn.txtUnderline:hover, a.txtUnderline:focus, a.btn.txtUnderline:focus  {color:#0d5f7d;
text-decoration:underline;text-decoration-skip:ink}
.txtRed, .txtRed a, .txtRed a:active, .txtRed a:visited, .txtRed a:hover {color:#CC0000}
.txtNoUnderline, a:hover.txtNoUnderline, a.txtNoUnderline:hover,.txtHoverUnderline, .txtHoverUnderline a, .txtHoverUnderline a:active, .txtHoverUnderline a:visited, .txtHoverUnderline a:link{text-decoration:none}
a:hover.txtRed,a:focus.txtRed {color:#CC0000;
text-decoration:underline;text-decoration-skip:ink}


/* panels */
.panel {border-radius:0;
-webkit-box-shadow:0 0 0 rgba(0,0,0,.05);
box-shadow:0 0 0 rgba(0,0,0,.05)}
.panel-default {border-color:#eaeaea}
.panel-body-no-paddingLR-md-lg{padding:20px 0}
.panel-default.bgRadioPanel {background:#fff;
color:#333;
cursor:pointer}
.panel-default.bgRadioPanel.active {background:#333;
color:#fff}

/* modals */
.modal-header {border-top-left-radius:6px;
border-top-right-radius:6px}
.tooltip-dialog .modal-header {
border-top-left-radius:0px;
border-top-right-radius:0px}
.modal-content{border-radius:0px}
.modal-header .close {margin-top:-26px;
margin-right:-20px}
.modal-lg .modal-header .close {margin-top:-26px;
margin-right:-37px}

.modal-lg .modal-header .close.x-inner,.modal-header .close.x-inner {
    margin-top:4px;
    margin-right:0px
}
.modal-dialog.modal-md {width:645px}
.modal .modal-md .close x-inner{top:8px;}
.modal-header.bgGrayVirginModalHeader{padding-top:24px;}
.modal-header.bgGrayVirginModalHeader .close.x-inner{top:8px}
.modal-body, .modal-header, .modal-footer {padding:20px 30px}
.modal.modal-tooltip {z-index:99999;bottom:auto;padding:0px 0px 0px 15px}
.modal.modal-tooltip .modal-body {padding:0px 30px 25px 30px}
.modal .modal-dialog .modal-content .modal-header.bgGrayVirginModalHeader{background-color: #ececec;}

.modalSelectNav {width:90%;
margin:auto}
.modal.modal-tooltip.vm-mobile .close{position:absolute;padding:20px;line-height:0;margin:0;right:0;top:0;}
.modal.modal-tooltip.vm-mobile .close:focus{color:#fff;}
.close {opacity:1}
.close:hover,.close:focus {opacity:1}
.modal-footer {text-align:left;
border-top:none}
.modal-footer .btn + .btn {margin-bottom:0;
margin-left:0}
button.close:focus {outline:1px dotted; outline-color:#2390b9}

/* tooltip */
.tooltip-inner {max-width:320px;
padding:20px 40px;
color:#555;
text-align:left;
font-size: 14px;
background-color:#FFF;
border-radius:0;
-webkit-box-shadow:0px 0px 5px 5px rgba(0,0,0,0.2);
-moz-box-shadow:0px 0px 5px 5px rgba(0,0,0,0.2);
box-shadow:0px 0px 5px 5px rgba(0,0,0,0.2)}
.tooltip{width:320px}
.tooltip.top .tooltip-arrow {border-top-color:#FFF}
.tooltip.top-left .tooltip-arrow {border-top-color:#FFF}
.tooltip.top-right .tooltip-arrow {border-top-color:#FFF}
.tooltip.right .tooltip-arrow {border-right-color:#FFF}
.tooltip.left .tooltip-arrow {border-left-color:#FFF}
.tooltip.bottom .tooltip-arrow {border-bottom-color:#FFF}
.tooltip.bottom-left .tooltip-arrow {border-bottom-color:#FFF}
.tooltip.bottom-right .tooltip-arrow {border-bottom-color:#FFF}
.tooltip.in {filter:alpha(opacity=100);
opacity:1}

.col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-xs-1, .col-xs-10, .col-xs-11, .col-xs-12, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9 {padding-right:0;
padding-left:0}
ol, ul {padding-left:17px}


/* checkbox switch */
.form-switch-container { width: 120px; border: 1px solid #d4d4d4; height: 30px; border-radius: 15px; font-size: 12px; }
.form-switch-toggle > span, .form-switch-toggle > span { color: #fff; }
.form-switch-toggle span span, .form-switch-toggle label, .form-switch-toggle span span, .form-switch-toggle label { color: #fff;  }

.form-switch-toggle span span, .form-switch-toggle input:checked ~ span span:first-child, .form-switch-toggle.form-switch-candy label { color: #00549a; }
.form-switch-toggle input ~ span span:first-child, .form-switch-toggle input:checked ~ span span:nth-child(2), .form-switch-candy input:checked + label { color: #fff; }

.form-switch-toggle a,
.form-switch-toggle span span { display: none; }

.form-switch-toggle { display: block; height: 29px;   cursor: pointer; position: relative; overflow: visible; padding: 0px; margin-left: 0px; margin-bottom: 0; }
.form-switch-toggle * { box-sizing: border-box; }
.form-switch-toggle a { display: block; transition: all 0.3s ease-out 0s; }

.form-switch-toggle label,
.form-switch-toggle > span { line-height: 30px; vertical-align: middle; }

.form-switch-toggle label { font-weight: 700; margin-bottom: 2px; max-width: 100%; color: #555; }

/* .form-switch-toggle input:focus ~ a, .form-switch-toggle input:focus + label { outline: 1px dotted rgb(136, 136, 136); } */
.form-switch-toggle input { position: absolute; opacity: 0; z-index: 5; }
    .form-switch-toggle input:checked ~ a { right: 2%; }
.form-switch-toggle > span { position: absolute; left: -100px; width: 100%; margin: 0px; padding-right: 100px; text-align: left; }
    .form-switch-toggle > span span { position: absolute; /*top: 5px;*/ left: 2px; z-index: 5; display: block; width: 50%; margin-left: 100px; text-align: center; }
        .form-switch-toggle > span span:last-child { left: 50%; }
.form-switch-toggle a { position: absolute; right: 48%; top: 48%; z-index: 4; display: block; width: 50%; height: 24px; border-radius: 12px; padding: 0px; transform: translateY(-48%); }

.form-red-onoffswitch {
    position: relative; 
    width: 70px;
    -webkit-user-select:none; 
    -moz-user-select:none; 
    -ms-user-select: none;
}
.form-red-onoffswitch-checkbox {
    position: absolute;
    height: 28px;
    width: 70px;
    opacity: 0;
}
.form-red-onoffswitch-label {
    display: block; overflow: hidden; cursor: pointer;
    border-radius: 50px;
}
.form-red-onoffswitch-inner {
    display: block; width: 200%; margin-left: -100%;

}
.form-red-onoffswitch-inner:before, .form-red-onoffswitch-inner:after {
    display: block; 
    float: left; 
    width: 50%; 
    height: 30px; 
    padding: 0; 
    line-height: 30px;
    font-size: 12px; 
    color: white; 
    font-family: Trebuchet, Arial, sans-serif; 
    font-weight: bold;
    box-sizing: border-box;
}
.form-red-onoffswitch-inner:before {
    content: "YES";
    padding-left: 13px;
    background-color: #cc0000; 
    color: #FFFFFF;
}
.form-red-onoffswitch-inner:after {
    content: "NO";
    padding-right: 14px;
    background-color: #000000; 
    color: #FFFFFF;
    text-align: right;
}
.form-red-onoffswitch-switch {
    display: block; width: 24px; margin: 3px;
    background: #D4D4D4;
    position: absolute; top: 0; bottom: 0;border-radius: 50px;
    transition: all 0.3s ease-in 0s; 
}
.form-red-onoffswitch-checkbox:checked + .form-red-onoffswitch-label .form-red-onoffswitch-inner {
    margin-left: 0;
}
.form-red-onoffswitch-checkbox:checked + .form-red-onoffswitch-label .form-red-onoffswitch-switch {
    right: 0px; 
    background: #fff;
}
/* checkbox swtich ends*/

.valign-top {vertical-align: top;}
.V-align-middle {vertical-align: middle !important;}

/*progress steps*/
.vsteps-views{height:107px;}
.progressive-steps .icon-circle-xsmall { color: #fff; display: inline-block; position: relative; border: 2px solid #D7D7D7; border-radius: 50%; }
.progressive-steps .icon-circle-xsmall.icon-circle_solid { background: #333; }
.progressive-steps .icon-circle-xsmall { height: 13px; width: 13px; }

.progressive-steps.complete .icon-circle-xsmall, .progressive-steps.active .icon-circle-xsmall { height: 24px; width: 24px; border: 5px solid #fff; }
.progressive-steps.complete .icon-circle-xsmall { background: #fff; }
.progressive-steps.complete .icon-check-mark:before { font-size: 7px; top: -2px; position: relative; }
.progressive-steps.next .icon-circle-xsmall { height: 30px; width: 30px; }
.progressive-steps .icon.icon-check::before { font-size: 11px; }
.progressive-steps .sm-tick-icon .icon-check::before { font-size: 15px; color: #fff; }  
.progressive-steps-line { position: relative; top: 17px; margin: 0 100px; height: 2px; }
.progressive-steps-line.one-step { margin: 0 200px; }
.progressive-steps-line.two-steps { margin: 0 25%; }
.progressive-steps-line.three-steps { margin: 0 17%; }
.progressive-steps-line.four-steps { margin: 0 12.5%; }
.progressive-steps-line.five-steps { margin: 0 10%; }
.progressive-steps-line.six-steps { margin: 0 8%; } 
  
.progressive-mobile-progressbar { display: none; width: 100%; margin: 0px 0 0 0; }
.progressive-mobile-bar-left { background-color: #fff; height: 4px; }
.progressive-mobile-bar-right { background-color: #babec2; height: 2px; margin-top: 1px; }
.progressive-steps.left-side .icon-circle-xsmall { position: relative; left: -8px; top: -10px; height: 15px; width: 15px; overflow: hidden; }
.progressive-steps.right-side .icon-circle-xsmall { position: relative; right: -8px; top: -9px; height: 15px; width: 15px; overflow: hidden; }
.progressive-steps.active.mobile .icon-circle-xsmall { position: relative; top: -16px; }
 
/*progress for 6 steps only */
.progressive-steps-progress-1-of-6 { position: relative; top: 15px; margin: 0 8%; width: 0; height: 2px; }
.progressive-steps-progress-2-of-6 { position: relative; top: 15px; margin: 0 8%; width: calc(100% / 6); height: 2px; }
.progressive-steps-progress-3-of-6 { position: relative; top: 15px; margin: 0 8%; width: calc(100% / 3); height: 2px; }
.progressive-steps-progress-4-of-6 { position: relative; top: 15px; margin: 0 8%; width: calc(100% / 2); height: 2px; }
.progressive-steps-progress-5-of-6 { position: relative; top: 15px; margin: 0 8%; width: calc(100% / 1.5); height: 2px; }
.progressive-steps-progress-6-of-6 { position: relative; top: 15px; margin: 0 8%;  width: auto; height: 2px; }

/*progress for 5 steps only */
.progressive-steps-progress-1-of-5 { position: relative; top: 15px; margin: 0 10%; width: 0; height: 2px; }
.progressive-steps-progress-2-of-5 { position: relative; top: 15px; margin: 0 10%; width: calc(100% / 5); height: 2px; }
.progressive-steps-progress-3-of-5 { position: relative; top: 15px; margin: 0 10%; width: calc(100% / 2.5); height: 2px; }
.progressive-steps-progress-4-of-5 { position: relative; top: 15px; margin: 0 10%; width: calc(100% / 1.66); height: 2px; }
.progressive-steps-progress-5-of-5 { position: relative; top: 15px; margin: 0 10%; width: auto; height: 2px; } 

/*progress for 4 steps only */
.progressive-steps-progress-1-of-4 { position: relative; top: 15px; margin: 0 12.5%; width: 0; height: 2px; }
.progressive-steps-progress-2-of-4  { position: relative; top: 15px; margin: 0 12.5%; width: calc(100% / 3.9); height: 2px; }
.progressive-steps-progress-3-of-4  { position: relative; top: 15px; margin: 0 12.5%; width: calc(100% / 2); height: 2px; }
.progressive-steps-progress-4-of-4  { position: relative; top: 15px; margin: 0 12.5%; width: auto; height: 2px; } 


/*progress for 3 steps only */
.progressive-steps-progress-1-of-3 { position: relative; top: 15px; margin: 0 17%; width: 0; height: 2px; }
.progressive-steps-progress-2-of-3  { position: relative; top: 15px; margin: 0 17%; width: calc(100% / 3); height: 2px; }
.progressive-steps-progress-3-of-3  { position: relative; top: 15px; margin: 0 17%; width: auto; height: 2px; } 

/*progress for 2 steps only */
.progressive-steps-progress-1-of-2 { position: relative; top: 15px; margin: 0 25%; width: 0; height: 2px; } 
.progressive-steps-progress-2-of-2  { position: relative; top: 15px; margin: 0 25%; width: auto; height: 2px; } 
 
/*accordion with scroll*/
.accordion-scrollable-2[aria-expanded="false"] { display: block; height: 55px !important; overflow-y: auto; }
.accordion-scrollable-2[aria-expanded="true"] { display: block; height: 200px !important; overflow-y: auto; }
.accordion-scrollable-2 { display: block; height: 55px; overflow-y: auto;  -moz-transition: height 0.5s ease; -webkit-transition: height 0.5s ease; -o-transition: height 0.5s ease; transition: height 0.5s ease;} 
.expand-info-toggle[aria-expanded="true"] .icon-expand-small ,.expand-info-toggle[aria-expanded="false"] .icon-collapse-small { display: none; }
.expand-info-toggle[aria-expanded="true"] .icon-collapse-small,.expand-info-toggle[aria-expanded="false"] .icon-expand-small { display: inline-block;}
   
/*pagination*/  
.pagination > li > a, .pagination > li > span { border-radius: 50%; border: 0; background: none; } 
.pagination>li>a, .pagination>li>span{color:#2390B9}
.pagination > .active > a, .pagination > .active > a:focus, .pagination > .active > a:hover, .pagination > .active > span, .pagination > .active > span:focus, .pagination > .active > span:hover {  background-color: #cc0000; border-color: #cc0000; color: #fff; }
.pagination > li:first-child > a, .pagination > li:first-child > span, .pagination > li:last-child > a, .pagination > li:last-child > span { border-top-left-radius: 50%; border-bottom-left-radius: 50%; background: none; }
.pagination > li:first-child > a:focus, .pagination > li:first-child > span:focus, .pagination > li:last-child > a:focus, .pagination > li:last-child > span:focus { background: none; }
.pagination > li:first-child > a:hover, .pagination > li:first-child > span:hover, .pagination > li:last-child > a:hover, .pagination > li:last-child > span:hover { background: none; }


/*scrollbar*/
.scrollAdjust::-webkit-scrollbar { width: 8px; }
/* Track */
.scrollAdjust::-webkit-scrollbar-track { -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); -webkit-border-radius: 8px; border-radius: 8px; background: #e1e1e1; }

/* Handle */
.scrollAdjust::-webkit-scrollbar-thumb { height: 40px; -webkit-border-radius: 8px; border-radius: 8px; background: #CC0000; -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5); }
.scrollAdjust { overflow: scroll; overflow-x: hidden; }



@media (max-width:1239px) {
    .progressive-steps-line { margin: 0 80px; } 
    .progressive-steps-progress-2 { margin: 0 80px; width: 160px; }
    .progressive-steps-progress-3 { margin: 0 80px; width: 320px; }
    .progressive-steps-progress-4 { margin: 0 80px; width: 480px; }
    .progressive-steps-progress-5 { margin: 0 80px; width: 640px; }
    .progressive-steps-progress-6 { margin: 0 80px; width: 800px; } 
}


/*Liquid layouts tablet to match Bell layouts*/
.container12 { max-width: 1199px;      margin: 0 auto; padding:0;}
.container.liquid-container12 { max-width: 1200px; }

@media (min-width:520px) {
    .container12.liquid-container12 { width: 100%; padding: 0 15px; }
}

@media (min-width:768px) {
    .container12.liquid-container12 { width: 100%; padding: 0 15px; }
    .container-fullWidth-xs {
        width: 100%;
    } 
}

@media (min-width:992px) {
    .container12.liquid-container12 { width: 100%; padding: 0 15px; }
}

@media (min-width:1200px) {
    .container12.liquid-container12 { width: 100%; }
}

@media (min-width:1240px) {
    .container12.liquid-container12, .container12 {width: 1200px;padding: 0;}
}

/*progress steps End*/

/* form controls */
.form-control {border-radius:0;
padding:18px 12px;
color:#333}
.has-error .form-control:focus {border-color:#fe0000}
.has-error .form-control {border-color:#fe0000}
label {font-weight:normal}
.radio {margin-bottom:15px}
.has-error .checkbox, .has-error .checkbox-inline, .has-error .control-label, .has-error .help-block, .has-error .radio, .has-error .radio-inline, .has-error.checkbox label, .has-error.checkbox-inline label, .has-error.radio label, .has-error.radio-inline label {color:#fe0000}
.help-block {color:#333}
.variableWidthInput {width:100%}
.variableWidthInput2 {width:88%}
.form-control:focus {
  border-color: #34a8d6;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset, 0 0 8px rgba(52, 168, 214, 0.6);
}

/* Buttons */
.btn {border-radius:4px;
font-size:16px;
font-weight:bold;
padding:9px 30px}
.btn-primary {color:#fff;
background-color:#cc0000;
border:2px solid #cc0000}
.btn-primary:focus,.btn-primary.focus {color:#fff;
background-color:#eb0000;
border-color:#eb0000}
.btn-primary:hover {color:#fff;
background-color:#eb0000;
border-color:#eb0000}
.btn-primary:active,.btn-primary.active,.open > .dropdown-toggle.btn-primary {color:#fff;
background-color:#eb0000;
border-color:#eb0000}
.btn-primary:active:hover,.btn-primary.active:hover,.open > .dropdown-toggle.btn-primary:hover,.btn-primary:active:focus,.btn-primary.active:focus,.open > .dropdown-toggle.btn-primary:focus,.btn-primary:active.focus,.btn-primary.active.focus,.open > .dropdown-toggle.btn-primary.focus {color:#fff;
background-color:#eb0000;
border-color:#eb0000}
.btn-primary:active,.btn-primary.active,.open > .dropdown-toggle.btn-primary {background-image:none}
.btn-primary.disabled:hover,.btn-primary[disabled]:hover,fieldset[disabled] .btn-primary:hover,.btn-primary.disabled:focus,.btn-primary[disabled]:focus,fieldset[disabled] .btn-primary:focus,.btn-primary.disabled.focus,.btn-primary[disabled].focus,fieldset[disabled] .btn-primary.focus {background-color:#eb0000;
border-color:#eb0000}
.btn-primary .badge {color:#337ab7;
background-color:#fff}
.btn-default {color:#333333;
background:none;
border-color:#333333;
border:2px solid #333333}
.btn-default:focus,.btn-default.focus {color:#333;
background-color:#999;
border-color:#999}
.btn-default:hover {color:#333333;
background-color:#999;
border-color:#999}
.btn-default:active,.btn-default.active,.open > .dropdown-toggle.btn-default {color:#333;
background-color:#eeeeee;
border-color:#eeeeee}
.btn-default:active:hover,.btn-default.active:hover,.open > .dropdown-toggle.btn-default:hover,.btn-default:active:focus,.btn-default.active:focus,.open > .dropdown-toggle.btn-default:focus,.btn-default:active.focus,.btn-default.active.focus,.open > .dropdown-toggle.btn-default.focus {color:#333;
background-color:#d4d4d4;
border-color:#8c8c8c}
.btn-default:active,.btn-default.active,.open > .dropdown-toggle.btn-default {background-image:none}
.btn-default.disabled:hover,.btn-default[disabled]:hover,fieldset[disabled] .btn-default:hover,.btn-default.disabled:focus,.btn-default[disabled]:focus,fieldset[disabled] .btn-default:focus,.btn-default.disabled.focus,.btn-default[disabled].focus,fieldset[disabled] .btn-default.focus {background-color:#fff;
border-color:#ccc}
.btn-default .badge {color:#fff;
background-color:#333}
.btn.btn-topNav {font-size:14px}
.btn-topNav:hover, .btn-topNav:focus {color:#34A8D6}
.btn-topNav {padding:0 20px;
border-right:1px solid #414141}
.topNav > .btn-topNav:first-child {border-left:1px solid #414141}
.btn.btn-modalSubNav {border-radius:6px;
font-size:13px;
padding:1px 15px;
font-weight:bold;
color:#2390b9;
background-color:#e2e2e2}
.btn.btn-modalSubNav:hover, .btn.btn-modalSubNav.active, .btn.btn-modalSubNav:focus {color:#FFF;
background-color:#727272;
-webkit-box-shadow:inset 0px 2px 13px 0px rgba(0,0,0,0.75);
-moz-box-shadow:inset 0px 2px 13px 0px rgba(0,0,0,0.75);
box-shadow:inset 0px 2px 13px 0px rgba(0,0,0,0.75);
outline:none}
.variableWidthButton {float:left;
width:auto}
.btn-transparent, .btn-transparent:active, .btn-transparent:visited, .btn-transparent:focus {
    display: inline-block;
    padding: 9px 30px;
    border-radius: 4px;
    border: 2px solid #fff;
    color: #fff;
    font-size: 16px;
    font-weight:bold;
    line-height: 1em;
    cursor: pointer;
    background: transparent;
    text-decoration: none;
    line-height: inherit;
}
.btn-transparent:hover, .btn-transparent:focus {
    color: #000;
    background-color: #fff;
    border-color: #fff;
    text-decoration: none;
}
.btn-primary-white, .btn-primary-white:active, .btn-primary-white:visited, .btn-primary-white:focus {
    display: inline-block;
    padding: 9px 30px;
    border-radius: 4px;
    border: 2px solid #fff;
    color: #000;
    font-size: 16px;
    font-weight:bold;
    line-height: 1em;
    cursor: pointer;
    background: #fff;
    text-decoration: none;
    line-height: inherit;
}
.btn-primary-white:hover, .btn-primary-white:focus {
    color: #333;
    background-color: #d7d7d7;
    border-color: #d7d7d7;
    text-decoration: none;
}
.btn.btn-primary.disabled, .btn.btn-default.disabled, .btn-transparent.disabled, .btn-primary-white.disabled, .btn-transparent.disabled:hover, .btn-primary-white.disabled:hover
.btn.btn-transparent.disabled:focus, .btn.btn-primary-white.disabled:focus{
background-color:transparent;
border-color:#9c9c9c;
color:#9c9c9c;
pointer-events: none;}


.container {padding-right:0;
padding-left:0}

.vm-container.container{padding:0 20px;}

/* show/hide password button */
.maskUnMaskPwsBtn {right:12px;
top:7px;
border:medium none;
background:#bbbec3;
width:auto;
height:25px;
color:#000;
border-radius:5px;
font-size:11px}

/*Virgin header and footer Styles*/
html {min-height:100%;
position:relative}
.footerVM {bottom:0;
left:0}
.footerVM a:hover {color:#cbcbcb}
.vm-copyRightLine {text-align:right}

/*modal window vertical cetnering*/
.modal {text-align:center;padding:0 10px;z-index:1100}
.modal:before {content:'';
display:inline-block;
height:100%;
vertical-align:middle;
margin-right:-4px}
.modal-dialog {display:inline-block;
text-align:left;
vertical-align:middle}

/*modal window vertical centering*/
.vm-panel-body {padding:20px 30px;
content:" ";
display:table;
width:100%}
.vm-loader {width:100%;
height:100%;
background-image:url(../../content/img/vmSpinner.gif);
background-position:center center;
background-repeat:no-repeat}
select{padding:9px;
border:1px solid #ccc}

/*fix for firefox input elements which show no text at all using native bootstrap.*/
.firefoxFix {height:38px; 
padding:6px 12px}


.modal.modal-tooltip .modal-header{border-bottom:0px}

/*hamburger icon */
.navbar, .navbar-inverse {border-radius:0;
border:none;
margin-bottom:0;
width:80%}
.navbar-nav{float:right}
.nav li {display:inline}
.navbar-header{height:60px}
.navbar-inverse{background-color:#2D2D2D!important;
float:right}
.navbar-inverse .navbar-nav_bvr > li > a{color:#fff}
.navbar-inverse .navbar-nav_bvr > li > a:hover {color:#2390b9}
.bvr_spacer60_xs35{height:60px}

@media screen and (max-width:1239px){
.modal.modal-tooltip {position:fixed;
height:100%;
top:50%;
left:50%;
-webkit-transform:translate(-50%, -50%);
transform:translate(-50%, -50%);
width:100%;
overflow: hidden;
}

.modal.modal-tooltip .tooltip-dialog{margin:auto;-webkit-transform:translate(0%, -50%);transform:translate(0%, -50%);position:relative;bottom:50%;width:350px}
.hidden-tooltip-target-xs {display:none}
.hidden-tooltip-target-lg {display:inline-block}
}

/*New svg loader using an svg spinner: January 18 2016*/
.loading-indicator-circle {
    display: inline-block;
    width: 37px;
    height: 37px;
    margin-right:10px;
    vertical-align: middle;
    -webkit-animation:spin 1.1s linear infinite;
    -moz-animation:spin 1.1s linear infinite;
    animation:spin 1.1s linear infinite;
}
@-moz-keyframes spin { 100% { -moz-transform: rotate(360deg); } }
@-webkit-keyframes spin { 100% { -webkit-transform: rotate(360deg); } }
@keyframes spin { 100% { -webkit-transform: rotate(360deg); transform:rotate(360deg); } }

.loader-fixed {
    width:350px;
    left: 50%;
    margin-left: -175px;
    position: fixed;
    top: 45%;
    padding:20px;
    z-index: 99999;
    -webkit-box-shadow:0 0 40px rgba(0,0,0,0.4);
    -moz-box-shadow:0 0 40px rgba(0,0,0,0.4);
    box-shadow:0 0 40px rgba(0,0,0,0.4);
}
/*Solid icons with content dark background*/
.icon.icon-bgWhite:before {
    left:-1px;
    position: relative;
    top: -0.2em;
}
.icon.icon-bgWhite span {
    display:inline-block;
    float:left;
    width:0.8em;
    height:0.8em;
    background-color:#fff;
    border-radius:50%;
    margin-right:-1em;
    margin-top:0.2em
}
/*End*/

/*Tabs*/
ul.tabs {
    display: table;
    table-layout: fixed;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}
ul.tabs li.active_tabs {
    background-color: #333;
    z-index: 2;
}
ul.tabs li.active_tabs label {
    position: relative;
    top: -5px;
}
ul.tabs li.active_tabs .active-tab-top {
    background-color: #333;
    display: block;
    height: 10px;
    left: 0px;
    opacity: 1;
    position: absolute;
    top: -10px;
    width: 100%;
    z-index: -1;
}
ul.tabs li.active_tabs::before {
    content: "";
    height: 100%;
    opacity: 1;
    position: absolute;
    right: -10px;
    top: 0;
    width: 10px;
    content: "";
    position: absolute;
    top: 0px;
}
.active_tabs::after {
    content: "";
    position: absolute;
    bottom: -15px;
    border-width: 15px 15px 0;
    border-style: solid;
    border-color: #333 transparent;
    display: block;
    width: 0;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
}
ul.tabs li {
    border-right: 2px solid #fff;
    text-align: center;
}
ul.tabs li.last-active-tab {
    border-right: 0px solid transparent;
}
ul.tabs li{
    cursor: pointer;
    padding: 15px 20px;
    background-color: #666;
    color: #fff;
    font-size: 16px;
    display: table-cell;
    vertical-align: middle;
    float: none;
    position: relative;
}

ul.tabs li> i {
    font-size: 55px;
    vertical-align: middle;
    margin: 0 auto;
    display: table;
}
.tabH {
    display: inline-block;
}
ul.tabs li .tabH a:hover, ul.tabs li .tabH a:focus{
    color:#fff;
    text-decoration:none
}
.tab-content {
    display: none;
}
.tab-content.first {
    display: block;
}
#secondaryNav #tabs li a:focus{color:#fff;background-color:#42caf1}
#secondaryNav #tabs li.active a:focus{color:#000;background-color:#efefef}

/*Custom hamburger select form mobile devices*/
select.custom-selection {
    -webkit-appearance:none;
    -moz-appearance:none;
    cursor:pointer;
    color:#fff;
    background-color:#333;
    padding:15px;
    padding-top: 10px;
    padding-bottom: 10px;
    height:50px;
    font-size:15px;
}
option.tab_selection{
    background-color:#333;
    color:#fff;
    font-size:14px
}

div.selection-box {position:relative;}
div.selection-box:after {
    font-family: "bell-icon";
    content:'\e618';
    font-size:21px;
    background-color:#333;
    color:#fff;
    right:0px; top:1px;
    padding:10px 18px;
    height:48px;
    position:absolute;
    pointer-events:none;
}
div.selection-box:before {
    content:'';
    position:absolute;
    pointer-events:none;
    display:block;
}
div.selection-box.search-arrow-down:after {
    content: "\e618";
}
/*END custom hamburger select form mobile devices END*/

.txtTab{font-size:26px;text-transform:uppercase}

/*Skip to main content for accessibility*/
header .skip-to-main-link {
    display: inline-block;
    padding: 9px 12px;
    position: absolute;
    top: -50px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    text-decoration: underline;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    transition: top .3s ease-out;
    z-index: 3000;
    font-size: 13px;
    background-color: #efefef;
    color:#212121
}
header .skip-to-main-link:focus {
    top: 0;
}
footer .skip-to-main-link{
    display: inline-block;
    padding: 9px 12px;
    position: absolute;
    left: -300px;
    text-decoration: underline;
    border-bottom-right-radius: 5px;
    transition: left .3s ease-out;
    background-color: #efefef;
    z-index: 3000;
    font-size: 13px;
    color:#212121
}
footer .skip-to-main-link:hover{
    color:#212121
}
footer .skip-to-main-link:focus {
    left: 0;
}
.hidden-tooltip-target-lg{display:none}
.hidden-tooltip-target-xs{display:inline-block}
.overflow-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/*for custom radio button and checkboxs*/

.graphical_ctrl {
    position: relative;
    padding-left: 35px;
}
.graphical_ctrl input {
    position: absolute;
    width: 48px;
    z-index: -1;
    height: 48px;
    opacity: 0;
    top: -16px;
    left: -9px;
}
.ctrl_element {
    position: absolute;
    top: -3px;
    left: 0;
    height: 23px;
    width: 23px;
    background: #fff;
    box-shadow: inset 0 0px 2px 0 rgba(0,0,0,0.3);
    /*box-shadow: inset 0 1px 1px 1px rgba(0, 0, 1, .15);*/
    border:1px solid #D9D9D9;
}
/*color-profile pills position fix*/
.pill_size_fix { 
    height: 23px;
    width: 23px; 
}
.ctrl_radioBtn .ctrl_element {
    border-radius: 50%;
}
.graphical_ctrl input:checked:focus ~ .ctrl_element {
  outline-width: 1px;
  outline-style: dotted;
  outline-color: #ccc;
}

.graphical_ctrl input[type="checkbox"]:focus ~ .ctrl_element{
  outline-width: 1px;
  outline-style: dotted;
  outline-color: #ccc;
}

.graphical_ctrl input:checked ~ .ctrl_element {
    background: #cc0000;
    border: 1px solid #cc0000;
}
.graphical_ctrl.ctrl_radioBtn input:checked ~ .ctrl_element {
    background: #fff;
    border: 1px solid #c9c9c9;
}
/*.graphical_ctrl.ctrl_radioBtn input:checked:focus ~ .ctrl_element {
    background: #fff;
}*/
.graphical_ctrl.graphical_ctrl_checkbox input:checked:focus ~ .ctrl_element {
    background: #cc0000;
}

.graphical_ctrl input:disabled ~ .ctrl_element, .graphical_ctrl input:checked:disabled ~ .ctrl_element {
    background: #e6e6e6;
    opacity: 0.6;
    border: 1px solid #e6e6e6;
    pointer-events: none;
}
.ctrl_element:after {
    content: '';
    position: absolute;
    display: none;
}
.graphical_ctrl input:checked ~ .ctrl_element:after {
    display: block;
}
.ctrl_lg .graphical_ctrl_checkbox .ctrl_element:after {
    border-width: 0 3px 3px 0;
}
.graphical_ctrl_checkbox .ctrl_element:after {
    left: 7px;
    top: 1px;
    width: 9px;
    height: 15px;
    border: solid #fff;
    border-width: 0 3px 3px 0;
    display: inline-block;
   -webkit-transform: rotate(45deg);
   -moz-transform: rotate(45deg);
   -o-transform: rotate(45deg); 
   transform: rotate(45deg);
}
.graphical_ctrl_checkbox input:disabled ~ .ctrl_element:after {
    border-color: #e6e6e6;
    pointer-events:none;
    cursor:not-allowed
}
.graphical_ctrl_checkbox input:checked:disabled ~ .ctrl_element:after {
    border-color: #7b7b7b;
}
.ctrl_radioBtn .ctrl_element:after {
    left: 5px;
    top: 5px;
    height: 13px;
    width: 13px;
    border-radius: 50%;
    background: #cc0000;
}
.ctrl_radioBtn input:disabled ~ .ctrl_element:after {
    background: #7b7b7b;
    pointer-events:none;
    cursor:not-allowed
}
.ctrl_lg .graphical_ctrl {
    padding-left: 40px;
}
.ctrl_lg .ctrl_element {
    height: 30px;
    width: 30px;
    top: -5px;
}
.ctrl_lg .ctrl_radioBtn .ctrl_element:after {
    left: 6px;
    top: 6px;
    height: 16px;
    width: 16px;
}
.error-ctrl .ctrl_element {
    border: 1px solid #BD2025;
}
.error-ctrl .graphical_ctrl input:checked ~ .ctrl_element {
    background-color: #BD2025;
    border: 1px solid #BD2025;
}
.error-ctrl .error_radio_lg .ctrl_radioBtn .ctrl_element:after {
    top: 6px;
    left: 6px;
}
.error-ctrl .ctrl_radioBtn .ctrl_element:after {
    top: 5px;
    left: 5px;
}
.chk_radius {
    border-radius: 2px;
    cursor:pointer;
    border: 1px solid #707070;
}
.ctrl_lg .graphical_ctrl_checkbox .ctrl_element:after {
    height: 17px;
    left: 9px;
    top: 3px;
    width: 10px;
}
/*On dark Background*/
label.on-dark-bg, .on-dark-bg label{
color:#fff
}

.graphical_ctrl_checkbox.on-dark-bg .ctrl_element:after {
    border: solid #fff;
    border-width: 0 3px 3px 0;
    display: inline-block;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
}

.graphical_ctrl.on-dark-bg input:checked ~ .ctrl_element:after {
    border: solid #fff;
    border-width: 0 3px 3px 0;
 
}
.graphical_ctrl.on-dark-bg input:checked ~ .ctrl_element {
    background: #cc0000;
    border: 1px solid #cc0000;
}
/*for custom radio button and checkboxs ends*/

/*Colour Pills*/
.colour-pill-appleblue.ctrl_radioBtn .ctrl_element::after,
.colour-pill-applepink.ctrl_radioBtn .ctrl_element::after,
.colour-pill-applered.ctrl_radioBtn .ctrl_element::after,
.colour-pill-applegreen.ctrl_radioBtn .ctrl_element::after,
.colour-pill-yellow.ctrl_radioBtn .ctrl_element::after,
.colour-pill-pink.ctrl_radioBtn .ctrl_element::after,
.colour-pill-lightblue.ctrl_radioBtn .ctrl_element::after,
.colour-pill-darkgreen.ctrl_radioBtn .ctrl_element::after,
.colour-pill-brown.ctrl_radioBtn .ctrl_element::after,
.colour-pill-orange.ctrl_radioBtn .ctrl_element::after,
.colour-pill-green.ctrl_radioBtn .ctrl_element::after,
.colour-pill-purple.ctrl_radioBtn .ctrl_element::after,
.colour-pill-darkblue.ctrl_radioBtn .ctrl_element::after,
.colour-pill-blue.ctrl_radioBtn .ctrl_element::after,
.colour-pill-darkred.ctrl_radioBtn .ctrl_element::after,
.colour-pill-red.ctrl_radioBtn .ctrl_element::after,
.colour-pill-appleblack.ctrl_radioBtn .ctrl_element::after,
.colour-pill-appleyellow.ctrl_radioBtn .ctrl_element::after,
.colour-pill-applejetblack.ctrl_radioBtn .ctrl_element::after,
.colour-pill-black.ctrl_radioBtn .ctrl_element::after,
.colour-pill-applegold.ctrl_radioBtn .ctrl_element::after, .colour-pill-gold.ctrl_radioBtn .ctrl_element::after,
.colour-pill-applerosegold.ctrl_radioBtn .ctrl_element::after, .colour-pill-rosegold.ctrl_radioBtn .ctrl_element::after,
.colour-pill-applesilver.ctrl_radioBtn .ctrl_element::after, .colour-pill-silver.ctrl_radioBtn .ctrl_element::after,
.colour-pill-applewhite.ctrl_radioBtn .ctrl_element::after, .colour-pill-white.ctrl_radioBtn .ctrl_element::after,
.colour-pill-applegrey.ctrl_radioBtn .ctrl_element::after, .colour-pill-grey.ctrl_radioBtn .ctrl_element::after {
    border-radius: 50%;
    border:2px solid #2390b8 !important;
    height: 31px !important;
    width: 31px !important;
    left: -4px;
    top: -4px;  
}
/*Colour pills END*/


/*Message Boxes Start*/
.message-box-wrapper{
  position:fixed;
  top:130px;   
  left:50%;  
  z-index:999;
}
.message-box{ 
  position: relative; 
  left: -50%;
  background: #fff;
  padding: 15px 15px 0px 15px;
  border: 0;    
  border-top-right-radius: 5px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;  
  border-bottom: 5px solid #fff;  
  -webkit-box-shadow: 0px 0px 60px 0px rgba(0,0,0,0.3);
  -moz-box-shadow: 0px 0px 60px 0px rgba(0,0,0,0.3);
  box-shadow: 0px 0px 60px 0px rgba(0,0,0,0.3);
  min-width: 160px;
  max-width: 600px;
}

.message-box-warning{
  border-bottom: 5px solid #e99e00;  

}
.message-box-success{
  border-bottom: 5px solid #2c9e25;
}
.message-box-close-button{
  position: absolute;
  margin-top: -5px;
  margin-right: -5px;
  right: 15px;
  font-size: 9px;
}
.message-box-icon{
  position: absolute;
}
.message-box-text{
  word-wrap: break-word;
  padding-left: 35px;
  padding-right: 10px;
  padding-bottom: 5px;
  padding-top: 5px;
}

.icon-size-medium, .icon-size-medium::before {
    font-size: 36px;
}


@media (max-width: 768px){
.message-box-wrapper{margin-right:-100px;}
}
/*Message Boxes END*/


/*Flex box container and clases for alignment*/
.container-flex-box-wrap {
	display: flex;
	display: -webkit-flex;
	flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
}
.container-flex-box {
    display: flex;
    flex-wrap: nowrap;
}
.middle-align-self{
    align-self:center;
}
.bottom-align-self{
    align-self:flex-end;
}

/*Show/Hide Accordion*/
.new-list-item {
  display:none; 
}
.show-list-item {
  display: none; 
}
.hide-list-item:target + .show-list-item {
  display: inline; 
}
.hide-list-item:target {
  display: none; 
}
.hide-list-item:target ~ .new-list-item {
  display:block; 
}


/*Features Checkboxes*/
.pad-8-left{padding-left: 8px;}
.pad-7-right{padding-right: 7px;}
.borderBlack{border: 1px solid #000;}
.border-2px {border-width: 2px;}
.box-shadow-1{
box-shadow: 0 2px 3px 0 rgba(0,0,0,0.1);
-webkit-box-shadow: 0 2px 3px 0 rgba(0,0,0,0.1);}
.check-select-cta{padding-left:35px}
.checkbox-selection ~ .ctrl_element{top:-5px}
@media (max-width: 999px) {.check-select-cta{padding-left:45px}}
/*END: Features Checkboxes*/


/* media queries keep at the bottom*/
@media screen and (max-width:991px) {
    /*for custom radio button and checkboxs starts*/
     .graphical_ctrl {
        padding-left: 40px;
    }

/*for custom radio button and checkboxs ends*/
.txtTab{font-size:16px;font-family:Helvetica, Arial, sans-serif;text-transform:none}
.pad-20lg-30xs{padding:20px}
.vm-container.container{padding:0}
.bgVirginRegistrationImg {background:#FFF;
background-image:none}
ul.footerList li {display:block}
.vm-copyRightLine {text-align:left}
.titleTabMob {background:#000;
background-image:url(../../content/img/backgroundTabMob.jpg);
background-position-x:right;
background-position-Y:-75px;
background-repeat:no-repeat;
font-size:38px;
text-align:left;
padding:35px 0}
.panel-body, .panel-body-no-paddingLR-md-lg {padding:20px 20px}
.vm-panel-body {padding:20px 0px}
.variableWidthButton {float:left;
width:100%}
.modal:before, .modal-dialog {vertical-align:top}
.modal {padding-top:65px}

.modal.modal-vm:before, .modal-dialog{vertical-align:middle}
.modal.modal-vm {padding-top:0px}

.marginCompensator{margin-left:0}
.paddingL26-md-lg{padding-left:0}
.modal {padding-top:65px}
.Changeorder-xs {-webkit-transform:rotate(180deg);
-moz-transform:rotate(180deg);
-ms-transform:rotate(180deg);
-o-transform:rotate(180deg);
transform:rotate(180deg);
margin-left:0!important}
.Changeorder-xs > [class*="col-"] {-webkit-transform:rotate(-180deg);
-moz-transform:rotate(-180deg);
-ms-transform:rotate(-180deg);
-o-transform:rotate(-180deg);
transform:rotate(-180deg)}
.rotateText{-webkit-transform:rotate(-180deg); 
-moz-transform:rotate(-180deg); 
-ms-transform:rotate(-180deg); 
-o-transform:rotate(-180deg); 
transform:rotate(-180deg);
width:100%;
float:left;
vertical-align:top}
}
@media screen and (max-width:767px) {
.no-pad-LR-xs{padding-left:0;padding-right:0}
    .basic-container, .container.liquid-container.basic-container {
    padding-left: 0;
    padding-right: 0;
}
    .text-center-xs {
    text-align: center;
}
    .pad-h-15-xs {
    padding-left: 15px !important;
    padding-right: 15px !important;
}
    .no-pad-left-xs{padding-left:0}

    .no-side-borders-xs {
    border-left: none;
    border-right: none;
}
.message-block .icon-width-40, .message-block .content-width, .upgrade-my-device .icon-width-40, .md-icon-info-block-full .icon-width-40, .small-icon-info-block-half-full .icon-width-40 {
    width: 100%;
}

.pad-15-left-right-xs{padding-left:15px;padding-right:15px}
.modal-header.bgGrayVirginModalHeader .close.x-inner {
    top: 0px;
}
.modal-header.bgGrayVirginModalHeader {
    padding: 15px;
    border-radius: 0;
}
.sans-serif-xs{font-family:"Helvetica",Arial, sans-serif;letter-spacing:0}
.txtSize18-xs{font-size:18px}
.modal .modal-md .close.x-inner {
    right: -5px;
}
    .modal-body, .modal-header, .modal-footer {
    padding: 20px 15px;
}
.flipFloatLR {float:right}
.modal:before, .modal-dialog {vertical-align:top}
.modal {padding-top:65px}
.panel-body-no-paddingLR-md-lg {padding:20px 30px}
/*hamburger icon*/
.navbar, .navbar-inverse {position:absolute;
right:0;
width:auto}
.nav>li>a{padding:10px 7px}
.bvr_spacer60_xs35{height:35px}
.btn-topNav{border-right:none}
.modal.modal-vm:before, .modal-dialog{vertical-align:top;}
.modal.modal-vm {padding:0px;}
.modal.modal-vm .modal-dialog{margin:0;width:100%;height:100%;position:relative;bottom: 0;background-color:#f4f4f4}
.modal.modal-vm .modal-dialog .modal-body{overflow-y: visible;}
.modal.modal-vm .modal-dialog .modal-content{
border:none;-webkit-box-shadow: none;
-moz-box-shadow: none;
box-shadow: none;}
.modal.modal-tooltip .tooltip-dialog{margin:auto;-webkit-transform:translate(0%, -50%);transform:translate(0%, -50%);position:relative;bottom:50%;width:350px}
}
@media screen and (max-width:520px) {
.txtTab{font-size:14px;font-family:Helvetica, Arial, sans-serif;text-transform:none}
.footerVM .container, .titleTabMob {padding-left:10px}
.virginLogo {margin-left:10px}
.topNav {padding-right:10px}
.flipFloatLR {float:right}
.flipFloatRL {float:left}
.flipTxtAlignRL {text-align:left}
.panel-body {padding:20px 20px}
.vm-panel-body {padding:20px}
.panel-body-no-paddingLR-md-lg {padding:20px 30px}
.titleTabMob {text-align:center;
font-size:26px;
padding:35px 0;
line-height:30px;
background-size:519px 235px;
background-position-y:-15px;
background-repeat:no-repeat}
.variableWidthButton {float:none;
width:100%}
.modal {padding-top:65px}
.modal.modal-tooltip {
position:fixed;
width:100%;
top:50%;
left:50%;
-webkit-transform:translate(-50%, -50%);
transform:translate(-50%, -50%);
padding-right:40px
}
.modal.modal-tooltip .tooltip-dialog{width:100%;margin: auto 20px;}
.hidden-tooltip-target-xs{display:none}
.hidden-tooltip-target-lg{display:inline-block}
}
@media screen and (max-width:443px) {
.col-xxs-10 {width:87%;
float:left}
}
@media (min-width:768px) {
.variableWidthInput {width:160px}
.variableWidthInput2 {width:160px}
.flipFloatLR {float:left}
.flipFloatRL {float:left}
.flipTxtAlignRL {text-align:right}
.panel-body {padding:20px 30px}
}
@media (min-width:992px) {
.variableWidthInput {width:160px}
.variableWidthInput2 {width:160px}
.flipFloatLR {float:left}
.flipFloatRL {float:right}
.flipTxtAlignRL {text-align:right}
.modal-dialog.modal-lg {width:900px}
}
@media (min-width:1200px) {
.variableWidthInput {width:160px}
.variableWidthInput2 {width:160px}
.flipFloatLR {float:left}
.flipTxtAlignRL {text-align:right}

}
@media (max-width:1200px) {
.pad-15-left-xs{padding-left: 15px}
}
@media (max-width:991px) {
.tooltip-dialog .modal-header {
    padding: 15px
}
.hidden-tooltip-target-xs{display:none}
.hidden-tooltip-target-lg{display:inline-block}
header .skip-to-main-link, footer .skip-to-main-link, .skip-to-main-link {
    display:none;
}
}
@media (min-width:992px) {
.modal.modal-tooltip{display:none}
}
.container.liquid-container {
    width: 100%;
    max-width: 992px;
    padding: 0 15px;
}
.container.liquid-container {
    max-width: 1200px;
}
.container.liquid-container.liquid-container-nopad {
    padding: 0 !important;
}


/*to overide out-of-the-box bootstrap*/
.container {padding-right:0;padding-left:0}
@media (min-width:520px) {
.container {width:480px}
}
@media (min-width:768px) {
.container {width:600px}
}
@media (min-width:992px) {
.container {width:980px}
}
@media (min-width:1200px) {
.container {width:980px}
}
@media (min-width: 1240px){
.container.liquid-container, .container {
    width: 1200px;
    padding: 0;
}
}