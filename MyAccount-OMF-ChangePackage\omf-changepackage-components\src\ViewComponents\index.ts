import * as React from "react";
import { ErrorComponent, IErrorComponent } from "./Error";
import { IContainerComponent, ContainerComponent, IPanelComponent, PanelComponent, IBRF3ContainerComponent, BRF3ContainerComponenet } from "./Container";
import { LightboxContainer } from "./Lightbox";
import { RestrictionModalView } from "./Restriction";
import { EllipsisText as EllipsisTextComponent, IEllipsisText } from "./EllipsisText";
import { ApplicationRootComponent, IApplicationRootProps } from "./Application";
import { CurrencyComponent, ICurrencyComponetProps, BellCurrencyComponent, BellCurrencyConnectedProps, BrandedMessageComponent } from "./Localization";
import { ReduxPersistGate } from "./ReduxPersistGate";
import { VisibleComponent } from "./VisibilityContainer";

export namespace Components {
  export const Error: IErrorComponent = ErrorComponent;
  export const Container: IContainerComponent = ContainerComponent;
  export const Panel: IPanelComponent = PanelComponent;
  export const BRF3Container: IBRF3ContainerComponent = BRF3ContainerComponenet;
  export const Modal: typeof LightboxContainer = LightboxContainer;
  export const RestrictionModal: typeof RestrictionModalView = RestrictionModalView;
  export const ApplicationRoot: React.FC<IApplicationRootProps> = ApplicationRootComponent as any;
  export const EllipsisText: IEllipsisText = EllipsisTextComponent;
  export const Currency: React.FC<ICurrencyComponetProps> = CurrencyComponent;
  export const BellCurrency: React.FC<BellCurrencyConnectedProps> = BellCurrencyComponent;
  export const BrandedMessage: React.FC<any> = BrandedMessageComponent;
  export const PersistGate: typeof ReduxPersistGate = ReduxPersistGate;
  export const Visible: typeof VisibleComponent = VisibleComponent;
}
