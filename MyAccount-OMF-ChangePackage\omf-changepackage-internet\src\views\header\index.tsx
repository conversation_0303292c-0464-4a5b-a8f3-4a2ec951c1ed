import * as React from "react";
import { connect } from "react-redux";
import { FormattedMessage, FormattedDate } from "react-intl";
import { ValueOf, Components, Omniture } from "omf-changepackage-components";
import { IStoreState, IAccountDetails } from "../../models";

interface IComponentProps {
  accountDetails: Array<IAccountDetails>;
}

const Component: React.FC<IComponentProps> = ({ accountDetails }) => {
  const [expanded, toggleState] = React.useState(false);
  // Omniture tracking for expand Interent package
  React.useEffect(() => {
    // we only care about when the menu expands
    if (expanded) {
      Omniture.useOmniture().trackAction({
        id: "myCurrentPackageClick",
        s_oAPT: {
          actionId: 648
        },
        s_oEPN: "My current Home Internet package"
      });
    }
  }, [expanded]);
  // ---
  const collapseIcon = expanded ? "icon-Collapse" : "icon-Expand";
  return <section className="bgVirginGradiant accss-focus-outline-override-pad">
    <div className="container liquid-container sans-serif">
      <div className="accordion-group internet-current-package flexCol">
        <div className="accordion-heading col-xs-12 noPaddingImp">
          <a id="accordion_expand_link" href="javascript:void(0)" onClick={() => toggleState(!expanded)} aria-controls="div1-accessible"
            className="accordion-accessible-toggle txtSize18 txtDecorationNoneHover txtWhite flexRow align-items-center accss-width-fit-content"
            aria-expanded={expanded} role="button">
            <span className="sr-only accordion-label" aria-live="polite" aria-atomic="true"
              aria-hidden="true"><FormattedMessage id={expanded ? "Collapse" : "Expand"} /></span>
            <span className={`${collapseIcon} virgin-icon txtSize24 virginRedIcon`}
              aria-hidden="true">
              <span className={`virgin-icon path1 ${collapseIcon}`} />
              <span className={`virgin-icon path2 ${collapseIcon}`} />
            </span>
            <div className="margin-15-left flexCol">
              <span className="txtWhite txtBold txtSize18"><FormattedMessage id="My current Home Internet package" /></span>
              <span className="expand txtWhite txtSize12 no-margin-top" style={{ display: expanded ? "none" : undefined }}><FormattedMessage id="Expand to view details" /></span>
            </div>
          </a>
        </div>
        <div id="div1-accessible"
          className="collapse-accordion-accessible-toggle accordion-body txtWhite col-xs-12 margin-5-top margin-40-left"
          style={{ display: expanded ? "block" : "none" }}>
          <div className="accordion-inner">
            {
              accountDetails.map(({
                Name,
                RegularPrice,
                PromotionDetails
              }) =>
                <div className="col-sm-5">
                  <div className="spacer10" aria-hidden="true" />
                  <div className="flexRow flexEnd">
                    <div className="flexGrow">{Name}</div>
                    <div style={{ whiteSpace: "nowrap" }}><Components.BellCurrency value={ValueOf(RegularPrice, "Price", 0)} /><FormattedMessage id="PER_MO" /></div>
                  </div>
                  <Components.Visible when={!!PromotionDetails}>
                    <div className="spacer5" aria-hidden="true" />
                    <Components.Visible when={ValueOf(PromotionDetails, "Description", false)}>
                      <div className="flexRow">
                        <div className="flexGrow">{ValueOf(PromotionDetails, "Description", "")}</div>
                        <div><Components.BellCurrency value={ValueOf(PromotionDetails, "PromotionalPrice.Price", 0)} /><FormattedMessage id="PER_MO" /></div>
                      </div>
                    </Components.Visible>
                    <Components.Visible when={ValueOf(PromotionDetails, "ExpiryDate", false)}>
                      <div>
                        <FormattedDate value={ValueOf(PromotionDetails, "ExpiryDate", "")} format="yMMMMd" timeZone="UTC">
                          {(expiryDate) => <FormattedMessage id="PromotionExpires" values={{ expiryDate }} />}
                        </FormattedDate>
                      </div>
                    </Components.Visible>
                  </Components.Visible>
                </div>)
            }
          </div>
        </div>
      </div>
    </div>
  </section>;
};

export const Header = connect<IComponentProps>(
  ({ accountDetails }: IStoreState) => ({ accountDetails: accountDetails || [] })
)(Component);
