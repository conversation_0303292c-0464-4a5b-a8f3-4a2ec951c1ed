import { Components , FormattedHTMLMessage } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { IErrorsList } from "../../../models";


export enum EBannerIcons { // Need to be updated after BRF3 is done
  ERROR = "icon-warning",
  NOTE = "icon-info",
  VALIDATION = "icon-Big_check_confirm",
  INFO = "icon-BIG_WARNING"
}

interface ComponentProps {
  iconType?: EBannerIcons | string;
  heading: string;
  message?: string;
  messages?: Array<IErrorsList>;
  iconSizeCSS?: string;
}

export const Banner = (props: ComponentProps) => {
  const privateProps = { ...defaultProps, ...props };
  const { iconType, heading, message, messages, iconSizeCSS } = privateProps;

  return (
    <Components.Container>
      <Components.Panel className={`flexBlock pad-20-left pad-20-right pad-25-top pad-25-bottom margin-15-bottom txtBlack`}>
        <span className={`virgin-icon ${iconType} ${iconSizeCSS} txtSize36`} aria-hidden={true}><span className={`virgin-icon path1`}></span><span className="virgin-icon path2"></span></span>
        <div id="IstallationMessageBanner" className="flexCol pad-15-left content-width valign-top pad-0-xs">
          <h4 className="virginUltraReg txtSize18 txtDarkGrey1 no-margin-top txtUppercase"><FormattedHTMLMessage id={heading} /></h4>
          {
            message ? <p className="txtSize14 txtGray4A sans-serif no-margin"><FormattedHTMLMessage id={message} /></p> : null
          }
          {
            messages ? <ul>
              {
                messages && messages.map(message => <li className="error"><a id={`message_${message.id}`} href={`#${message.id}`} className="txtRed txtBold txtUnderline" title={message.id}><FormattedMessage id={message.id} /></a>
                  <span className="txtDarkGrey">&nbsp;-&nbsp;{message.error === "required" ? <FormattedMessage id="INLINE_ERROR_required" /> : <FormattedMessage id={"INLINE_ERROR_" + message.id + "_" + message.error} />}</span>
                </li>)
              }
            </ul> : null
          }
        </div>
      </Components.Panel>
    </Components.Container>
  );
};

const defaultProps = {
  iconType: EBannerIcons.INFO,
  iconSizeCSS: "txtSize36"
};
