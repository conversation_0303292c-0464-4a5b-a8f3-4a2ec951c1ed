@import "../includes/mixins";

.form-control-select-box.select-account {
  width: 68%;
  &:before {
    background: none;
  }
  &:after {
    content: "\e963";
    color: #555555;
    font-weight: 900;
    font-size: 7px;
    top: 9px;
  }
  @media #{$media-mobile} {
    width: 100%;
  }
}
.my-message {
  align-items: center;
  justify-content: flex-end;
}
.overview-details {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: left;
}
.overview-actions {
  padding-bottom: 25px;
}
.overview-lineup {
  padding: 42px 40px;
  @media #{$media-mobile} {
    padding: 0;
    text-align: left;
  }
}
.overview-actions,
.overview-details {
  padding: 45px 45px;
  @media #{$media-mobile} {
    padding: 0;
    text-align: left;
  }
}
.notification-links {
  display: flex;
  flex-direction: column;
  li {
    @media #{$media-mobile} {
      justify-content: flex-end;
      display: flex;
    }
    span.notification-item {
      @media #{$media-desktop} {
        margin-left: 10px;
      }
      @media #{$media-mobile} {
        margin-right: 10px;
      }
      &:before {
        color: $virginCustomGray;
        background: #fff;
        border-radius: 50%;
      }
    }
  }
}
.virgin-poster-image {
  display: inline-block;
  width: 100%;
  padding: 15px;
  flex-basis: 20%;
  .virgin-poster-detail {
    height: 110px;
    padding: 25px;
    background-color: $colorLightGray;
    font-size: 14px;
    flex-grow: 0;
    overflow: hidden;
    color: $virginGray;
    border-radius: 0 0 3px 3px;
  }
  .virgin-movie-poster {
    width: 100%;
    border-radius: 3px 3px 0 0;
  }
}

.subscriberDropdown {
  background: linear-gradient(180deg, #000000 0%, #1f1f1f 100%);
  .message-centre {
    margin-bottom: 0;
  }
}

.bill-component {
  padding-top: 25px;
  padding-bottom: 25px;
}

.tv-guide-btn {
  align-self: center;
  padding: 11px 18px !important;
  @media #{$media-mobile} {
    width: 100%;
    margin-left: 47px;
    margin-top: 15px;
  }
}

.tv-guide-details {
  padding-left: 25px;
  @media #{$media-mobile} {
    padding-left: 15px;
  }
}
.lineup-bgColour {
  background-color: $virginGrayLigh1;
}
.minus-margin40 {
  margin-top: -40px;
}
.lineup-table {
  border-collapse: collapse;
  table-layout: auto;
  width: 100%;

  td:first-of-type {
    color: $virginIconBlue;
  }
  tr:nth-child(odd) {
    background-color: #eeeeee;
  }
  tr:nth-child(even) {
    background-color: $colorWhite;
  }
  td {
    padding: 14px;
    border: #dfdfdf 1px solid;
    word-break: break-word;
  }
  td:last-of-type{
    width: 15%;
  }
}

#overview-myservice {
  width: 45%;
}
#overview-featured-content {
  width: 55%;
}

.my-services-items {
  .service-item-title {
    flex-basis: 54%;
  }
  .service-item-detail {
    flex-basis: 46%;
  }
}
@media #{$media-mobile} {
  .margin-0-right-xs {
    margin-right: 0;
  }
  .border-radius-0-xs {
    border-radius: 0px !important;
  }
  .flexCol-reverse-xs {
    display: flex;
    flex-direction: column-reverse;
  }
}
.br-3-top {
  border-radius: 3px 3px 0 0;
}
.br-3-bottom {
  border-radius: 0 0 3px 3px;
}
.borderOrange {
  border-color: $virginOrange;
}
.connector-search {
  @media (min-width: 1000px) {
    display: inline-block;
    height: 60px;
    vertical-align: middle;
  }
}
@media #{$media-large-desktop} {
  #overview-myservice {
    width: 39%;
  }
  #overview-featured-content {
    width: 61%;
  }
}
@media #{$media-mobile} {
  #overview-myservice,
  #overview-featured-content {
    width: 100%;
  }
}
@media #{$media-tablet} {
  #overview-myservice,
  #overview-featured-content {
    width: 50%;
  }
}

#changePackageCTA[disabled], .btn[disabled] {
  background-color: $bgGrey;
  border-color: $bgGrey;
  opacity: 1;
}

#pendingOrderFlyout {
  opacity: 1;
  bottom: 70px;
  left: 50%;
  transform: translateX(-50%);
  .arrow {
    width: 0;
    height: 0;
    border-style: solid;
    position: absolute;
    border-width: 15px 15px 0 15px;
    border-color: $colorWhite transparent transparent transparent;
    bottom: -15px;
    left: calc(50% - 7.5px);
  }
  .tooltip-inner {
    padding: 15px;
    border-radius: 3px;
  }
}
// For Lightbox in channel lineup page
.modal.fade{
  opacity: 1;
}
.modal .modal-header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e9ecef;
}
.noBorderRadius {
  border-radius: 0 !important;
}
.d-flex {
  display: flex !important;
}
.pad-30-right {
  padding-right: 30px;
}
.pad-30-left {
  padding-left: 30px;
}
.pad-25-bottom {
  padding-bottom: 25px;
}
.pad-25-top {
  padding-top: 25px;
}
.brf .borderGrayLight6 {
  border-color: #d4d4d4;
}
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.modal-backdrop.show {
  opacity: .5;
}
.modal-header{
  &:before{
    content: none;
  }
  &:after{
    content: none;
  }  
}
.flex-column {
  flex-direction: column !important;
}
@media #{$media-desktop-tab}{
  .flex-sm-row {
    flex-direction: row !important;
  }
}