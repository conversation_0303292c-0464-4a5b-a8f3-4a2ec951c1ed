import { Omniture } from ".";

const trackedVars = ["oARS", "oERR_CLASS", "oERR_DESC", "oPLE"];

function getTleafelByKey(key: string): HTMLDivElement {
    let id: string = "Tealeaf_" + key, el: HTMLDivElement;
    if (!(el = document.getElementById(id) as HTMLDivElement)) {
        el = document.createElement("div");
        el.id = id;
        el.style.display = "none";
        document.body.appendChild(el);
    }
    return el;
}

function updateTealeafOnPage(params: Omniture.IOmniture) {
    trackedVars.forEach(function (key) {
        const el = getTleafelByKey(key);
        const omnikey = "s_" + key;
        if ((params as any)[omnikey] !== undefined && el) el.innerHTML = (params as any)[omnikey];
    });
}

function clearTealeafOnPage() {
    if (location.hash !== "")
        updateTealeafOnPage(trackedVars.reduce(function (acc, key) {
            const omnikey = "s_" + key;
            acc[omnikey] = "";
            return acc;
        }, {} as any));
}


export {
    updateTealeafOnPage,
    clearTealeafOnPage
};