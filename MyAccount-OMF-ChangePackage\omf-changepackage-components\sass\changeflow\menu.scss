@import "mixins";

/* Bell navigator module
 * 
 * Sticky side panel attached to
 * the left side of the screen
*/

$colorWhiteLight: #f4f4f4;

/* Mixins for Navigation */


/* Shaddow Mixin */

@mixin navShaddow($color) {
    content: "";
    box-shadow: inset 0 20px 1px $color;
    position: absolute;
    z-index: 5;
    width: 100%;
    height: 31px;
    left: 0;
    top: -15px;
    transform: rotate(-1deg);
    -webkit-transform: rotate(-1deg);
    -ms-transform: rotate(-1deg);
    -moz-transform: rotate(-1deg);
}


/* Menu Item Right Arrow Mixin */

@mixin navArrow($position) {
    content: " ";
    display: block;
    position: absolute;
    opacity: 0;
    width: 25px;
    height: 25px;
    right: 10px;
    background: black;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    border-radius: 3px;
}

.bell-tv-navigator {
    .bell-tv-search-bar {
        @media #{$media-tab-mobile} {
            padding: 0 15px 15px;
        }
        .bell-search-field {
            .bell-search-field-input {
                box-shadow: none;
                border: none;
                height: 52px;
                padding-right: 44px;
                padding-left: 20px;
                &.clear {
                    padding-right: 90px;
                }
                &:focus {
                    outline: #2672cb auto 5px;
                }
            }
            .bell-search-field-button {
                top: 2px;
                right: 2px;
                height: 46px;
                width: auto;
                border-radius: 0;
                background: $colorWhite;
                line-height: 30px;
                padding: 0;
                button {
                    border: none;
                    background: none !important;
                    background-image: none !important;
                    box-shadow: none !important;
                    border-radius: 0;
                    padding: 12px;
                }
            }
        }
    }
    .bell-search-suggestions {
        width: 100%;
        padding: 0;
        margin-top: 10px;
        z-index: 2;
        .tooltip-arrow {
            top: -30px;
            margin-top: unset;
        }
        .tooltip-inner {
            overflow-y: auto;
            padding: 25px 15px;
            max-width: unset;
            max-height: 210px;
            width: 100%;
            ul>li {
                margin: 4px 0;
                padding: 5px 15px;
                &:hover {
                    background-color: #e1e1e1;
                }
                &.selected {
                    background-color: #e1e1e1;
                }
            }
        }
    }
}

// Set Background color of navigation so white lines do not appear inbetween
ul.bell-tv-navigator-tabs {
    li:last-child {
        background-color: $colorLightGray;
    }
    li:nth-last-child(2):not(.expanded) a {
        -moz-border-radius-bottomright: 3px;
        -moz-border-radius-bottomleft: 3px;
        border-bottom-right-radius: 3px;
        border-bottom-left-radius: 3px;
    }
    li:nth-last-child(2) {
        &.expanded {
            .bell-tv-navigator-tab-more {
                -moz-border-radius-bottomright: 3px;
                -moz-border-radius-bottomleft: 3px;
                border-bottom-right-radius: 3px;
                border-bottom-left-radius: 3px;
            }
        }
    }
}

.bell-tv-navigator {
    .bell-tv-navigator-tab {
        z-index: 0;
        position: relative;
        border-bottom: 1px solid $virginBlack;
        transition: background-color 150ms;
        &.active {
            >.bell-tv-navigator-tab-row::before {
                right: -10px;
                z-index: 2;
                opacity: 1;
            }
            // border-bottom-width: 0;
            >a {
                background-color: $virginBlack;
            }
            &:after {
                opacity: 1;
                right: -15px;
            }
            .bell-tv-navigator-tabs-pointer {
                @media #{$media-desktop} {
                    opacity: 0;
                }
            }
            .bell-tv-navigator-tab-more .bell-tv-navigator-tab-row {
                // background-color: $virginBlack;
                /* Divider on the submenu */
                &:last-child:before {
                    border: 0;
                }
            }
            @media #{$media-tab-mobile} {
                right: auto;
                width: auto;
            }
        }
        &.active+.bell-tv-navigator-tab .bell-tv-navigator-tab-row:after {
            @media #{$media-tab-mobile} {
                display: none;
            }
        }
        &:first-of-type {
            a {
                -moz-border-radius-topright: 3px;
                -moz-border-radius-topleft: 3px;
                border-top-right-radius: 3px;
                border-top-left-radius: 3px;
            }
        }
        &:nth-last-of-type(2) {
            @media #{$media-desktop} {
                border-bottom: 0px !important;
            }
        }
        &:last-of-type {
            // margin-top: 15px;
            padding-top: 25px;
            border: none;
            @media #{$media-tab-mobile} {
                padding-top: 0px;
            }
            a {
                padding-left: 17px;
                &:before {
                    /*Reduced nav arrow on last menu item (Browse all channels) */
                    @include navArrow(15);
                }
                @media #{$media-desktop} {
                    border-radius: 3px;
                }
            }
            &:after {
                border-top: 28px solid transparent;
                border-bottom: 28px solid transparent;
                margin-top: -28px;
            }
            .bell-tv-navigator-tabs-icon {
                /* -- Hiding icon from last element */
                display: none;
            }
        }
        .bell-tv-navigator-tab-row {
            padding: 15px 20px 14px;
            z-index: 2;
            position: relative;
            background-color: $virginGray;
            align-items: center;
            &,
            &:active,
            &:focus,
            &:hover {
                text-decoration: none;
            }
            .bell-tv-navigator-tabs-icon {
                width: 40px;
                height: 40px;
                margin-right: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                >.icon {
                    color: $colorWhite;
                    svg {
                        fill: $colorWhite;
                    }
                    i {
                        position: relative;
                        top: -3px;
                    }
                }
            }
            .bell-tv-navigator-tabs-text {
                .submenu-name {
                    color: $colorWhite;
                }
                .submenu-price {
                    color: $colorWhite;
                }
            }
            .bell-tv-navigator-tabs-pointer {
                display: inline-block;
                color: $colorWhite;
            }
            @media #{$media-desktop-tab} {
                &:before {
                    @include navArrow(15);
                }
            }
        }
        .bell-tv-navigator-tab-more {
            &.active {
                >a {
                    background-color: $virginBlack;
                    border-top: 1px solid $virginGray;
                }
            }
            & a:first-child:after {
                border: none;
                @media #{$media-tab-mobile} {
                    box-shadow: none;
                    position: absolute;
                    z-index: auto;
                    width: 85%;
                    left: 70px;
                    top: calc(100% - 1px);
                    transform: none;
                    -webkit-transform: none;
                    -ms-transform: none;
                    -moz-transform: none;
                    height: auto;
                    border-bottom: 1px solid $colorLightGray;
                }
            }
            @include expandable();
            @media #{$media-tab-mobile} {
                padding-left: 0;
            }
            hr {
                border-color: #2066a0;
                margin: 10px 0;
                @media #{$media-tab-mobile} {
                    border-color: $virginGray;
                }
            }
        }
        &.expanded {
            >.bell-tv-navigator-tab-more .bell-tv-navigator-tab-row:last-child:after {
                @media #{$media-tab-mobile} {
                    border: none;
                }
            }
            .bell-tv-navigator-tab-more {
                max-height: 600px;
                .bell-tv-navigator-tab-row {
                    padding: 8px 30px;
                    .bell-tv-navigator-tabs-text {
                        .submenu-price {
                            font-size: 12px;
                            color: $colorWhite;
                        }
                    }
                    &:after {
                        @media #{$media-mobile} {
                            width: 65%;
                        }
                        @media #{$media-tablet} {
                            width: 85%;
                        }
                    }
                    .submenu-name {
                        color: $colorWhite;
                    }
                    &.active {
                        .submenu-name {
                            color: $colorWhite;
                        }
                    }
                }
            }
            .bell-tv-navigator-tab-row {
                &:after {
                    display: block;
                    content: "";
                    border-bottom: 1px solid $virginBlack;
                    position: absolute;
                    // top: calc(100% - 1px);
                    top: 0px;
                    width: 100%;
                    opacity: 1;
                    left: 0px;
                    border-top: 0;
                    border-left: 0;
                    border-right: 0;
                    margin: 0;
                }
                .bell-tv-navigator-tabs-pointer {
                    opacity: 0;
                }
            }
        }
    }
}

.bell-tv-navigator-page {
    @media #{$media-desktop} {
        padding-left: 30px;
    }
    @media #{$media-tab-mobile} {
        padding-left: 0;
    }
}

.bell-tv-navigator-menu {
    @media #{$media-tab-mobile} {
        &.open-nav-slider {
            width: 310px;
            z-index: 1026;
        }
    }
}

.nav-backdrop {
    content: "";
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    width: 100%;
    position: fixed;
    background-color: rgba(0, 0, 0, 0.65);
    z-index: 1025;
}

.bell-tv-navigator .bell-tv-navigator-tab.active.expanded+.bell-tv-navigator-tab .bell-tv-navigator-tab-row:after,
.bell-tv-navigator-tab:last-child a:after {
    /* removing shaddow from child elements */
    display: none;
}

.mobile-menu-header {
    background-color: $virginGray;
    color: $colorWhite;
    padding: 15px 15px 14px;
}

.virgin-menu-dockbar {
    padding: 10px 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
}