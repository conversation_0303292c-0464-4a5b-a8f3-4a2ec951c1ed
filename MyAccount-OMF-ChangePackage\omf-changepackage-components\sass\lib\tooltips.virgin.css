/*
    *:before,*:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
*/

/* Dialog Box Tooltip */
.tooltip{ 
    position: absolute;
    z-index: 1070;
    display: block;
    font-family: Arial,sans-serif;
    font-style: normal;
    font-weight: normal;
    letter-spacing: normal;
    line-break: auto;
    line-height: 1.42857143;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    white-space: normal;
    word-break: normal;
    word-spacing: normal;
    word-wrap: normal;
    font-size: 12px;
    opacity: 0;
    filter: alpha(opacity=0);
}
.tooltip-inner {
  max-width: 320px;
  padding: 30px;
}

.tooltip.bottom .tooltip-arrow {
  margin-top: 0px;
  margin-left: 0px;
  top: 0;
  border-width: 0 5px 5px;
}
.icon-close:before,
.icon-x-close:before {
  content: "\e915";
}
.tooltip-popover .tooltip-inner p {
  margin-top: 10px;
}
.tooltip-popover .tooltip-popover-close {
  position: absolute;
  top: 20px;
  right: 30px;
  font-size: 10px;
  text-decoration: none !important;
  margin: -18px;
  border: 18px solid transparent;
}

.tooltip-popover .tooltip-popover-close .icon {
  font-weight: 600;
}
.bottom > .tooltip-arrow {
    -webkit-transform: rotate(0deg) translate(-5px, 0px) !important;
    -ms-transform: rotate(0deg) translate(-5px, 0px) !important;
    transform: rotate(0deg) translate(-5px, 0px) !important;
  }

.aria-visible {
  border: 0;
  clip: rect(0 0 0 0);
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  width: 1px;
  position: absolute;
}
.btn-primary {
  color: #000;
  background: none;
  border-color: #000;
  border: 2px solid #000; }

.btn-primary:focus,
.btn-primary.focus {
  color: #000;
  background: none;
  border-color: #000;
  border: 2px solid #000; }

.btn-primary:hover {
  color: #000;
  background-color: #999;
  border-color: #999; }

.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  color: #000;
  background: none;
  border-color: #000;
  border: 2px solid #000; }

.btn-primary:active:hover,
.btn-primary.active:hover,
.open > .dropdown-toggle.btn-primary:hover,
.btn-primary:active:focus,
.btn-primary.active:focus,
.open > .dropdown-toggle.btn-primary:focus,
.btn-primary:active.focus,
.btn-primary.active.focus,
.open > .dropdown-toggle.btn-primary.focus {
  color: #333;
  background-color: #d4d4d4;
  border-color: #8c8c8c; }

.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  background-image: none; }

.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary.focus {
  background-color: #fff;
  border-color: #ccc; }

/* Dropdown Select Tooltip */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 50%;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 220px;
    padding: 0;
    margin: 3px 0 0 -116px;
    list-style: none;
    font-size: 14px;
    text-align: left;
    background-color: #fff;
    border: 1px solid #ccc;
    border: 1px solid rgba(0,0,0,0.15);
    border-radius: 0;
    -webkit-box-shadow: 0 6px 12px rgba(0,0,0,0.175);
    box-shadow: 0 6px 12px rgba(0,0,0,0.175);
    background-clip: padding-box;
}
ul.dropdown-menu {
  border: 2px solid #242424;
}
.btn-dropdown .dropdown-menu li a {
  padding: 14px 20px;
}
.btn-dropdown .dropdown-menu li:not(:last-child) > a {
  border-bottom: 1px solid #d4d4d4;
}
ul.dropdown-menu li > a {
  color: #2390b8;
}
.dropdown-menu>li>a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: normal;
    line-height: 1.42857143;
    color: #333;
    white-space: nowrap;
}
.dropdown-menu>li>a:hover,.dropdown-menu>li>a:focus {
    text-decoration: none;
    color: #262626;
    background-color: #f5f5f5;
}
.dropdown-menu>.active>a,.dropdown-menu>.active>a:hover,.dropdown-menu>.active>a:focus {
    color: #fff;
    text-decoration: none;
    outline: 0;
    background-color: #337ab7;
}
ul.dropdown-menu li > a:hover {
  border-radius: 0;
  color: #fff;
  background-color: #34a8d6;
}
.dropdown-menu .caret {
  /*top: 0;*/
  right: 25%;
  position: absolute;
}
.caret_top {
  top: -8px;
  margin-left: -4px;
  border-top: none;
  border-right: 8px solid transparent;
  border-left: 8px solid transparent;
  border-bottom: 8px solid #242424;
}
.caret_top:after {
  content: " ";
  position: absolute;
  top: 2px;
  left: -8px;
  border-top: none;
  border-right: 8px solid transparent;
  border-left: 8px solid transparent;
  border-bottom: 8px solid #fff;
}
.btn-dropdown .dropdown-menu li a i {
  color: #00549a;
  margin-right: 16px;
  font-size: 20px;
}
.icon-current_bill:before {
  content: "\e916";
}
.icon-multiple:before {
  content: "\e919";
}
ul.dropdown-menu li > a .icon2 {
  color: #34a8d6;
}
ul.dropdown-menu li > a:hover .icon2 {
  color: #fff;
}
@media screen and (max-width: 999px) {
  /* line 196, scss/bill.scss */
  .tooltip-popover:before {
    content: " ";
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
    background-color: #000;
    opacity: 0.5;
    display: block;
    pointer-events: all;
  }
  /* line 209, scss/bill.scss */
  .tooltip-popover .tooltip-arrow {
    display: none;
  }
  /* line 212, scss/bill.scss */
  .tooltip-popover .tooltip-inner {
    position: fixed;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    z-index: 3;
    max-height: 90%;
    overflow: auto;
  }
}
@media screen and (max-width: 999px) and (max-width: 639px) {
  /* line 212, scss/bill.scss */
  .tooltip-popover .tooltip-inner {
    width: 90%;
  }
}
