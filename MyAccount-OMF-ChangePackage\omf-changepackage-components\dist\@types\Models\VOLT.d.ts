export declare namespace Volt {
    interface IMessageButton {
        buttonName: string;
        href: string;
        offeringAction: string;
        orderItemType: string;
    }
    interface IMessage {
        messageCode: string;
        messageTitle: string | null;
        messageBody: string;
        messageText: string;
        type: string;
        messageType: "error" | "Information" | null;
        buttons: Array<IMessageButton> | null;
    }
    interface IRestriction {
        type: string;
        title: string;
        description: string;
        actionLinks: Array<IHypermediaAction>;
        dynamicData: {
            products: Array<string>;
            promotions: Array<{
                name: string;
                price: string;
                expiry: string;
            }>;
        };
        footerDescription: string;
        onComplete?: (intent: string) => void;
    }
    interface IHypermediaActionMessageOption {
        id: string;
        patch: string;
        description: string;
    }
    interface IHypermediaActionMessageBody {
        op: string;
        value: Array<IHypermediaActionMessageOption>;
    }
    interface IHypermediaAction {
        rel: string;
        href: string;
        method: "GET" | "PUT" | "POST" | "PATCH" | "DELETE";
        messageBody?: IHypermediaActionMessageBody;
        name?: any;
        redirectURLKey?: any;
    }
    enum EOfferingState {
        Add = "add",
        Delete = "delete",
        Modify = "modify",
        NoCharge = "noChange",
        Remove = "Remove",
        Change = "Change",
        InitiallySelected = "InitiallySelected",
        NewlySelected = "NewlySelected",
        Removed = "removed",
        NotSelected = "NotSelected",
        Added = "Added",
        UnSelected = "UnSelected",
        Create = "Create",
        NoChange = "NoChange"
    }
    enum EOfferingType {
        BaseOffering = "BaseOffering",
        GroupOffering = "GroupOffering",
        SingleOffering = "SingleOffering"
    }
    interface ICharacteristic {
        name: string;
        value: string;
    }
    interface IPriceDetail {
        price: number;
        priceType: "Recurring" | "OneTime";
    }
    interface IPromotionDetail {
        description: string;
        promotionalPrice: IPriceDetail;
        discountPrice: IPriceDetail;
        discountStartMonth: string;
        discountDuration: number;
        expiryDate: string;
        legalMessage: string;
        state: EOfferingState;
    }
    enum EDIsplayGroupKey {
        TV_BASE_PRODUCT = "TV_BASE_PRODUCT",
        ALACARTE = "ALACARTE",
        MOVIE = "MOVIE",
        TV = "TV",
        SPECIALITY_SPORTS = "SPECIALITY_SPORTS",
        ADD_ON = "ADD_ON",
        INTERNATIONAL = "INTERNATIONAL",
        INTERNATIONAL_COMBOS = "INTERNATIONAL_COMBOS",
        INTERNATIONAL_ALACARTE = "INTERNATIONAL_ALACARTE",
        BASE_PROGRAMMING = "BASE_PROGRAMMING",
        SPECIALITY_CHANNELS = "SPECIALITY_CHANNELS",
        OFFERS = "OFFERS",
        TV_BROWSE_ALL = "TV_BROWSE_ALL",
        PROMOTION = "PROMOTION",
        NONE = "NONE"
    }
    enum EProductOfferingType {
        PACKAGE = "BasePackage",
        COMBO = "Combo",
        CHANNEL = "Channel",
        NONE = "None"
    }
    interface IProductOffering {
        id: string;
        name: string;
        imagePath?: string;
        longDescription: string;
        shortDescription: string;
        usagePlan: string;
        productPath: string;
        channelNumber: string;
        state: EOfferingState;
        type: EOfferingType;
        language: string;
        isSelectable: boolean;
        isCurrent: boolean;
        isSelected: boolean;
        isDisabled: boolean;
        characteristics: Array<ICharacteristic>;
        regularPrice: IPriceDetail;
        promotionDetails: IPromotionDetail;
        productOfferingType: EProductOfferingType;
        offeringAction: Volt.IHypermediaAction;
        actionLink: Volt.IHypermediaAction;
        countOfOffering?: number;
        channels: string[];
        childOfferings: IProductOffering[];
        displayGroupKey: EDIsplayGroupKey;
        multipleWaysToAdd: Array<string>;
        sortPriority: number;
        isAlreadyIncludedIn: string;
    }
    interface IProductCatalog {
        productOfferings: Array<IProductOffering>;
        productConfigurationTotal: IProductConfigurationTotal;
    }
    interface IProductConfigurationTotal {
        priceOvertime: Array<ILineOfBusiness>;
        summaryAction: IHypermediaAction;
        resetAction: IHypermediaAction;
        nextAction: IHypermediaAction;
        productOfferingCount: number;
    }
    interface IChargeDetail {
        name: string;
        priceDetail: IPriceDetail;
    }
    interface ILineOfBusiness {
        currentPrice: IPriceDetail;
        newPrice: IPriceDetail;
        regularCurrentPrice?: IPriceDetail;
        regularNewPrice?: IPriceDetail;
        flowType: "TV" | "Internet" | "AllLOBs";
    }
    enum EProductOfferingGroupType {
        Delta = "Delta",
        New = "New",
        Current = "Current",
        Default = "Default"
    }
    enum ELineOfBusiness {
        TV = "TV",
        Internet = "Internet"
    }
    interface IProductOfferingGroup {
        productOfferingGroupType: EProductOfferingGroupType;
        productOfferingGroupTotal: IPriceDetail | null;
        additionalCharges: Array<IChargeDetail> | null;
        productOfferings: Array<IProductOffering>;
        lineOfBusiness: ELineOfBusiness | null;
    }
    interface IPhoneDetails {
        phoneNumber: string;
        extension: string;
    }
    enum EAppointmentDuration {
        AM = "AM",
        PM = "PM",
        Evening = "Evening",
        AllDay = "AllDay",
        Item0810 = "Item0810",
        Item1012 = "Item1012",
        Item1315 = "Item1315",
        Item1517 = "Item1517",
        Item1719 = "Item1719",
        Item1921 = "Item1921"
    }
    enum EPreferredContactMethod {
        EMAIL = "Email",
        TEXT_MESSAGE = "TextMessage",
        PHONE = "Phone"
    }
    interface IAppointmentDetail {
        availableDates?: Array<IAvailableDates>;
        preferredDate?: any;
        duration?: EAppointmentDuration;
        installationAddress?: IInstallationAddress;
        contactInformation?: IContactInformation;
        additionalDetails?: IAdditionalDetails;
        isInstallationRequired?: boolean;
    }
    interface IAvailableDates {
        date: string;
        timeSlots: Array<ITimeSlots>;
        isPreferredDate?: boolean;
    }
    interface IInstallationAddress {
        address1?: string;
        address2?: string;
        city: string;
        province: string;
        postalCode: string;
    }
    interface IContactInformation {
        preferredContactMethod?: EPreferredContactMethod;
        primaryPhone?: IPrimaryPhone;
        additionalPhone?: string;
        textMessage?: string;
        email?: string;
    }
    interface ITimeSlots {
        intervalType: EAppointmentDuration;
        timeInterval: ITimeInterval;
        isAvailable: boolean;
        isSelected: boolean;
    }
    interface IPrimaryPhone {
        phoneNumber: string;
        phoneExtension?: string;
    }
    interface IAdditionalDetails {
    }
    interface ITimeInterval {
        startTime: number;
        endTime: number;
    }
    interface IDisplayGroupOffering {
        key: EDIsplayGroupKey;
        offeringId: string;
        offeringName: string;
        parentId: string | null;
        parentKey: string | null;
        sortPriority: number;
        isRoot: boolean;
        count: number;
        subTotalPrice: IPriceDetail;
        name: string;
        offeringKey: EDIsplayGroupKey;
        parentDisplayGroup?: EDIsplayGroupKey;
    }
    interface IDisplayGroup {
        baseOffering: IDisplayGroupOffering;
        additionalOfferings: Array<IDisplayGroupOffering>;
    }
    interface IProductOfferingDetail {
        messages: Array<IMessage> | null;
        productOfferingGroups: Array<IProductOfferingGroup>;
        productConfigurationTotal: IProductConfigurationTotal;
        restriction: IRestriction;
        appointment?: IAppointmentDetail;
        customerInformation?: any;
        displayGroup?: IDisplayGroup;
    }
    interface IAPIResponse {
        confirmationNumber: string | null;
        orderDate: string | null;
        productOfferingDetail: IProductOfferingDetail;
    }
    interface IRestrictionAPIResponse {
        restriction: IRestriction;
        redirectURLKey: string;
    }
}
