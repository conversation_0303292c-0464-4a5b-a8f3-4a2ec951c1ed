{"name": "myaccount-omf-changepackage", "domain": "Ordering", "version": "1.0.0", "private": true, "keywords": [], "author": "---", "license": "MIT", "scripts": {"dev": "webpack -w", "build": "webpack", "build:dev": "title myaccount-omf-changepackage && webpack", "build:dev:minor": "title myaccount-omf-changepackage && npm version minor && webpack", "build:dev:major": "title myaccount-omf-changepackage && npm version major && webpack", "build:dev:patch": "title myaccount-omf-changepackage && npm version patch && webpack", "lint": "tslint -p src/tsconfig.json -c tslint.json -t stylish --fix && tsc -p src --pretty --noEmit", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"bwtk": "git+https://gitlab.int.bell.ca/uxp/bwtk.git#v6.1.0", "omf-changepackage-components": "file:../omf-changepackage-components", "omf-changepackage-navigation": "file:../omf-changepackage-navigation", "omf-changepackage-internet": "file:../omf-changepackage-internet", "omf-changepackage-tv": "file:../omf-changepackage-tv", "omf-changepackage-appointment": "file:../omf-changepackage-appointment", "omf-changepackage-review": "file:../omf-changepackage-review", "husky": "4.3.8", "ajv": "^7.1.1"}, "peerDependencies": {"bwtk": "*", "react": "*", "react-dom": "*", "react-intl": "*", "react-redux": "*", "redux-actions": "*", "react-router": "*", "react-router-dom": "*", "redux": "*", "rxjs": "*", "react-bootstrap": "*", "react-hook-form": "4.6.3-beta.4"}, "dependencies": {"js-search": "^1.4.3", "react-router": "^5.1.2", "react-router-dom": "^5.1.2"}, "husky": {"hooks": {"pre-commit": "node pre-commit.js"}}}