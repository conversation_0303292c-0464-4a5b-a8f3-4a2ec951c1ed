import { WidgetLoader } from "bwtk";
import { Actions, Components, EModals, EReviewMode, EWidgetName, Omniture, ValueOf, Volt } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { IStoreState } from "../../models";

const {
  Modal
} = Components;

interface IComponentProps {
  summaryAction: Volt.IHypermediaAction;
  isContinueEnabled: boolean;
  isOpen: boolean;
}

interface IComponentDispatches {
  onContinueClick: () => void;
  closeLightbox: () => void;
  dismissLightbox: () => void;
}

export const ModalId: string = EModals.PREVIEWMODAL;

const Component: React.FC<IComponentProps & IComponentDispatches> = ({
  isOpen,
  summaryAction,
  isContinueEnabled,
  onContinueClick,
  closeLightbox,
  dismissLightbox
}) => <Modal
  modalId={ModalId}
  className={"do-not-center-in"}
  onDismiss={dismissLightbox}
  title={<FormattedMessage id={`${ModalId}_TITLE`} />}>
  {isOpen && <WidgetLoader widget={EWidgetName.PREVIEW} mode={EReviewMode.Summary} summaryAPI={ValueOf<string>(summaryAction, "href")} />}
  <div className="spacer30" aria-hidden="true" />
  <div className="spacer1 bgGrayLight6" aria-hidden="true" />
  <div className="bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg">
    <button id={`BUTTON_CONTINUE_${ModalId}`} disabled={!isContinueEnabled} className="btn btn-primary fill-xs" onClick={onContinueClick}><FormattedMessage id={`${ModalId}_CONTINUE`} /></button>
    <span className="vSpacer15" aria-hidden="true" />
    <button id={`BUTTON_CLOSE_${ModalId}`} className="btn btn-secondary fill-xs" onClick={closeLightbox}><FormattedMessage id={`${ModalId}_CLOSE`} /></button>
  </div>
</Modal>;

export const PreviewLightbox = connect<IComponentProps, IComponentDispatches>(
  ({ lightboxData, summary }: IStoreState) => ({
    summaryAction: ValueOf<Volt.IHypermediaAction>(summary, "summaryAction", null),
    isContinueEnabled: !!ValueOf(summary, "nextAction", false),
    isOpen: lightboxData && lightboxData.lightbox === ModalId
  }),
  (dispatch) => ({
    onContinueClick: () => {
      dispatch(Actions.closeLightbox(ModalId));
      dispatch(Actions.broadcastUpdate(Actions.onContinue()));
    },
    closeLightbox: () => {
      Omniture.useOmniture().trackAction({
        id: "previewLightbox",
        s_oAPT: {
          actionId: 647
        },
        s_oBTN: {
          ref: `BUTTON_CLOSE_${ModalId}`
        }
      });
      dispatch(Actions.closeLightbox(ModalId));
    },
    dismissLightbox: () => {
      Omniture.useOmniture().trackAction({
        id: "previewLightbox",
        s_oAPT: {
          actionId: 647
        },
        s_oBTN: "Close"
      });
      dispatch(Actions.setlightboxData(""));
    }
  })
)(Component);
