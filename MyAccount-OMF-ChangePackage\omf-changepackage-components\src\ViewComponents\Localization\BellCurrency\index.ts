import * as React from "react";
import { connect } from "react-redux";
import Componenet from "./BellCurrency";

/**
 * Returns a <span /> containig the formatted currency in Bell specific format
 *
 * @param {any} value The raw number. Can be in any JS acceptable number format, or Number.
 * @param {string} className Additional class names for the element.
 *
 * @returns React component containig the formatted number string.
 * @type FC
 */
export interface BellCurrencyConnectedProps {
  value: any;
}

export const BellCurrencyComponent = (connect(
  // Map state to props
  ({ localization }: any) => ({ localization }),
  // Map dispatch to props
  (dispatch) => ({})
)(Componenet as any) as any) as  React.FC<BellCurrencyConnectedProps>;
BellCurrencyComponent.displayName = "BellCurrency";
