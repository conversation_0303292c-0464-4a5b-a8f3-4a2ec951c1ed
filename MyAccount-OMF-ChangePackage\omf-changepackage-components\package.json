{"name": "omf-changepackage-components", "version": "0.1.0", "description": "Virgin ordering flow common componenets widget", "main": "dist/omf-changepackage-components.js", "types": "./dist/@types/index.d.ts", "private": true, "scripts": {"dev": "webpack -w", "build": "webpack", "build:prod": "webpack --env -p", "build:dev": "webpack --env -d", "clean": "rm -rf ./node_modules & rm -rf ./dist & rm -rf ./package-lock.json & rm -rf ./yarn.lock", "lint": "tslint -p src/tsconfig.json -c tslint.json -t stylish --fix && tsc -p src --pretty --noEmit", "build:css": "node ./sass/build-ecare.js", "build:css:overview": "node ./sass/build-overview.js"}, "keywords": [], "author": "BELL", "license": "MIT", "devDependencies": {"bwtk": "git+https://gitlab.int.bell.ca/uxp/bwtk.git#v6.1.0", "node-sass": "*", "husky": "4.3.8", "ajv": "^7.1.1"}, "peerDependencies": {"bwtk": "*", "react": "*", "react-dom": "*", "react-intl": "*", "react-redux": "*", "rxjs": "*", "redux": "*", "redux-actions": "*", "redux-observable": "*"}, "dependencies": {"redux-persist": "^6.0.0"}, "husky": {"hooks": {"pre-commit": "node pre-commit.js"}}}