import { Volt } from "omf-changepackage-components";

export interface IPriceDetail {
    Price: number;
    PriceType: string;
}

export interface IPromotionDetail {
    Description: string;
    PromotionalPrice: IPriceDetail;
    ExpiryDate: string;
    LegalMessage: string;
}

export interface IProductOffering {
    Id: string;
    Name: string;
    ShortDescription: string;
    LongDescription: string;
    RegularPrice: IPriceDetail;
    PromotionDetails: IPromotionDetail;
    DisplayGroupKey: string;
}

export interface IAccountDetails extends IProductOffering {
    Unavailable: boolean;
}

export interface IPackage extends Volt.IProductOffering {

}
