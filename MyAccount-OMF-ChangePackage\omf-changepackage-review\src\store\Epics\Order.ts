import { Injectable } from "bwtk";
import { Epic, combineEpics, ofType } from "redux-observable";
import { filter, mergeMap, catchError, concat , of } from "rxjs";

import { EWidgetStatus, Actions, Models, AjaxResponse, Volt, FilterRestrictionObservable, EWidgetRoute, Utils, EFlowType } from "omf-changepackage-components";
import { Client } from "../../Client";
import {
  IStoreState
} from "../../models";
import {
  submitOrder,
  setOrderConfirmation,
  setReviewMessages
} from "../Actions";
import { Config } from "../../Config";

const {
  setWidgetStatus,
  broadcastUpdate,
  historyGo
} = Actions;

@Injectable
export class OrderEpics {
  widgetState: EWidgetStatus = EWidgetStatus.INIT;

  constructor(private client: Client, private config: Config) { }

  combineEpics() {
    return combineEpics(
      this.submitEpic
    );
  }

  private get submitEpic(): OrderEpic {
    return (action$, store) =>
      action$.pipe(
        ofType(submitOrder.toString()),
        filter(() => this.widgetState !== EWidgetStatus.UPDATING),
        mergeMap(({ payload }: ReduxActions.Action<Volt.IHypermediaAction>) =>
          concat(
            of(setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING)),
            this.client.post<AjaxResponse<Volt.IAPIResponse>>(Utils.getURLByFlowType({
              [EFlowType.INTERNET]: this.config.api.internetOrderSubmitAPI,
              [EFlowType.TV]: this.config.api.tvOrderSubmitAPI,
              [EFlowType.ADDTV]: this.config.api.tvAddSubmitAPI,
              [EFlowType.BUNDLE]: this.config.api.bundleOrderSubmitAPI
            }), null).pipe(
              mergeMap((response: AjaxResponse<Volt.IAPIResponse>) => FilterRestrictionObservable(response, [
                setOrderConfirmation(response.data),
                setReviewMessages(response.data),
                setWidgetStatus(EWidgetStatus.RENDERED),
                broadcastUpdate(historyGo(EWidgetRoute.CONFIRMATION), 10),
              ]))
            )
          )
        ),
        catchError(Models.ErrorHandlerObservable(submitOrder)));
  }
}

type OrderEpic = Epic<ReduxActions.Action<any>, ReduxActions.Action<any>, any, IStoreState>;
