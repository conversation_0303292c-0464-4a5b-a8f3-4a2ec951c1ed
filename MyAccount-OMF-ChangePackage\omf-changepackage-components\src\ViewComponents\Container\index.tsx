import * as React from "react";
import { Models } from "../../Models";

export interface IContainerComponentProps extends Models.IBaseComponentProps {
  children?: React.ReactNode;
}

export interface IContainerComponent extends React.FC<IContainerComponentProps> {
}


export const ContainerComponent: IContainerComponent = ({
  className, children
}) => <div className={`container liquid-container ${className}`}>{children}</div>;

ContainerComponent.defaultProps = {
  className: ""
};

export * from "./Panel";
export * from "./BRF3Container";
