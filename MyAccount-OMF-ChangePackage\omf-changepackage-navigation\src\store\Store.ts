import { combineReducers } from "redux";
import { Action, handleActions } from "redux-actions";
import { combineEpics } from "redux-observable";
import { Actions, Reducers, LifecycleEpics, RestricitonsEpics, ModalEpics, EFlowType } from "omf-changepackage-components";

import { Store as BwtkStore, Injectable, CommonFeatures } from "bwtk";

import * as actions from "./Actions";

import { IStoreState, IWidgetProps, ISummary } from "../models";
import { Epics } from "./Epics";
import { Localization } from "../Localization";
import { Routes } from "../utils/History";
import { Client } from "../Client";

const { BaseStore, actionsToComputedPropertyName } = CommonFeatures;

const {
  setWidgetProps
} = actionsToComputedPropertyName(Actions);
const {
  setFlowType,
  setSummaryTotals
} = actionsToComputedPropertyName(actions);

@Injectable
export class Store extends BaseStore<IStoreState> {
  constructor(private client: Client, store: BwtkStore, private epics: Epics, private localization: Localization) {
    super(store);
  }

  get reducer() {
    return combineReducers({
      // =========== Widget lifecycle methods =============
      ...Reducers.WidgetBaseLifecycle(this.localization) as any,
      ...Reducers.WidgetLightboxes(),
      ...Reducers.WidgetRestrictions(),
      // =========== Widget data ===============
      // Sample reducer that sets all widget props on to the store
      defaultRoute: handleActions<any | string>({
        [setWidgetProps]: (state, { payload }: Action<IWidgetProps>) => (payload && payload.defaultRoute) || state,
      }, "/"),
      flowType: handleActions<any | string>({
        [setWidgetProps]: (state, { payload }: Action<IWidgetProps>) => (payload && payload.flowType) || state,
        [setFlowType]: (state, { payload }: Action<EFlowType>) => (payload && payload) || state,
      }, ""),
      routes: handleActions<Array<string>>({
        [setWidgetProps]: (state, { payload }: Action<any>) => (payload && payload.flowType && Routes[payload.flowType as EFlowType]) || state,
        [setFlowType]: (state, { payload }: Action<any>) => (payload && Routes[payload as EFlowType]) || state,
      }, []),
      summary: handleActions<any | string>({
        [setSummaryTotals]: (state, { payload }: Action<ISummary>) => (payload && payload) || state,
      }, {}),
    }) as any;
  }

  /**
   * Middlewares are collected bottom-to-top
   * so, the bottom-most epic will receive the
   * action first, while the top-most -- last
   * @readonly
   * @memberof Store
   */
  get middlewares(): any {
    return combineEpics(this.epics.navigationEpics.combineEpics(), this.epics.combineEpics(), new ModalEpics().combineEpics(), 
      new RestricitonsEpics(this.client, "NAVIGATION_RESTRICTION_MODAL").combineEpics(), new LifecycleEpics().combineEpics());
  }
}
