import * as React from "react";
import { Models } from "../../Models";

export interface IPanelComponentProps extends Models.IBaseComponentProps {
  children?: React.ReactNode;
}

export interface IPanelComponent extends React.FC<IPanelComponentProps> {
}


export const PanelComponent: IPanelComponent = ({
  className, children
}) => <div className={`brf3-panel ${className}`}>{children}</div>;

PanelComponent.defaultProps = {
  className: "",
};
