import { Omniture, Utils, EFlowType } from "omf-changepackage-components";
import { IErrorsList, IAvailableDates, IContactInformation } from "../models";
import { EContactMethod } from "../models/Enums";

export function stripTimeBit(date: string): Date | string {
  try {
    const fragments = date.split("T");
    const newDate = new Date(fragments[0]);
    newDate.setMinutes(new Date(date).getMinutes() + new Date(date).getTimezoneOffset());
    newDate.setHours(0);
    newDate.setMinutes(0);
    return newDate;
  } catch (e) {
    return date;
  }
}

interface EnumType { [s: number]: string }

export function mapEnum(enumerable: EnumType, fn: Function): any[] {
  // get all the members of the enum
  const enumMembers: any[] = Object.keys(enumerable).map((key: any) => enumerable[key]);

  // now map through the enum values
  return enumMembers.map(m => fn(m));
}

export const noSpecialCharRegex = /^[a-zA-Z0-9]+$/i;

export const emailRegex = /^[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+((\.)+[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+)*@([a-z0-9]+([\-][a-z0-9])*)+([\.]([a-z0-9]+([\-][a-z0-9])*)+)+$/i;

export const formattedPhoneRegex = /^[0-9]\d{2}-\d{3}-\d{4}$/i; // ************
// export const formattedPhoneRegex = /^\d{10}$/i; // 10 digits only

export const getMessagesList = (errors: Partial<Record<string, any>>): Array<IErrorsList> => {
  const errorslist: Array<IErrorsList> = [];
  let action;
  if (Utils.getFlowType() === EFlowType.INTERNET)
    action = 523;

  if (Utils.getFlowType() === EFlowType.BUNDLE)
    action = 508;

  Object.keys(errors).map(k => {
    const err: any = errors[k];
    errorslist.push({
      id: err.ref.name,
      error: err.type
    });
  });

  Omniture.useOmniture().trackError(
    errorslist.map((err) => ({
      code: err.id,
      type: Omniture.EErrorType.Validation,
      layer: Omniture.EApplicationLayer.Frontend,
      description: err.error
    })), action);

  return errorslist;
};

/**
 * Function called on Contact Number field onChange
 * It formats the number to ************ format
 * @export
 * @param {string} value
 * @returns
 */
export function autoFormat(value: string) {
  let newVal = filterNumbers(value);
  newVal = newVal.substr(0, 10); // Limit entry to 10 numbers only.
  return newVal.length === 10 ? newVal.slice(0, 3) + "-" + newVal.slice(3, 6) + "-" + newVal.slice(6) : newVal;
}

export function filterNumbers(value: string) {
  const num = value.replace(/\D/g, ""); // only numbers
  return num;
}

export function ValueOf(val: any) {
  return val;
}

// export function getPreferedDates(dates: Array<IAvailableDates>) {
//     return dates.filter(date => date.isPreferredDate === true);
// }

export function getSelectedDate(dates: Array<IAvailableDates>) {
  const selectedDates = dates.filter(date => date.timeSlots.find(time => time.isSelected === true));
  return selectedDates.length > 0 ? selectedDates : [dates[0]];
}

export const getPrimaryValue = (method: EContactMethod, value: IContactInformation | undefined) => {
  switch (method) {
    case EContactMethod.EMAIL:
      return value?.email;
    case EContactMethod.PHONE:
      return value?.primaryPhone?.phoneNumber && autoFormat(value?.primaryPhone?.phoneNumber);
    case EContactMethod.TEXT_MESSAGE:
      return value?.textMessage && autoFormat(value?.textMessage);
    default:
      return "";
  }
};

// export function mergeArrays(...arrays: any) {
//     let jointArray: any = [];

//     arrays.forEach((array: any) => {
//         jointArray = [...jointArray, ...array];
//     });
//     return Array.from(new Set([...jointArray]));
// }
