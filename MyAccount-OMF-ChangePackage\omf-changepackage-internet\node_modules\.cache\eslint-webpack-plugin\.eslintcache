[{"C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\Widget.tsx": "1", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\Pipe.ts": "2", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\Config.ts": "3", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\App.tsx": "4", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\index.ts": "5", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\Store.ts": "6", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\Actions.ts": "7", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\views\\index.tsx": "8", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\Localization.ts": "9", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\Client.ts": "10", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\mutators\\index.ts": "11", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\Epics.ts": "12", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\views\\header\\index.tsx": "13", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\Epics\\UserAccount.ts": "14", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\Epics\\Catalog.ts": "15", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\Epics\\Omniture.ts": "16", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\views\\catalog\\index.tsx": "17", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\utils\\Characteristics.ts": "18", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\views\\catalog\\Legal.tsx": "19", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\views\\catalog\\Package.tsx": "20"}, {"size": 1953, "mtime": *************, "results": "21", "hashOfConfig": "22"}, {"size": 994, "mtime": *************, "results": "23", "hashOfConfig": "22"}, {"size": 829, "mtime": *************, "results": "24", "hashOfConfig": "22"}, {"size": 268, "mtime": *************, "results": "25", "hashOfConfig": "22"}, {"size": 54, "mtime": *************, "results": "26", "hashOfConfig": "22"}, {"size": 2349, "mtime": *************, "results": "27", "hashOfConfig": "22"}, {"size": 1143, "mtime": *************, "results": "28", "hashOfConfig": "22"}, {"size": 1299, "mtime": *************, "results": "29", "hashOfConfig": "22"}, {"size": 617, "mtime": *************, "results": "30", "hashOfConfig": "22"}, {"size": 420, "mtime": *************, "results": "31", "hashOfConfig": "22"}, {"size": 1625, "mtime": *************, "results": "32", "hashOfConfig": "22"}, {"size": 1882, "mtime": *************, "results": "33", "hashOfConfig": "22"}, {"size": 4694, "mtime": 1755882044534, "results": "34", "hashOfConfig": "22"}, {"size": 1435, "mtime": *************, "results": "35", "hashOfConfig": "22"}, {"size": 4561, "mtime": *************, "results": "36", "hashOfConfig": "22"}, {"size": 3116, "mtime": *************, "results": "37", "hashOfConfig": "22"}, {"size": 3315, "mtime": 1755882044534, "results": "38", "hashOfConfig": "22"}, {"size": 446, "mtime": *************, "results": "39", "hashOfConfig": "22"}, {"size": 1673, "mtime": 1755882044532, "results": "40", "hashOfConfig": "22"}, {"size": 7211, "mtime": 1755882044534, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1mov19p", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\Widget.tsx", ["102"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\Pipe.ts", ["103"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\Config.ts", ["104", "105", "106"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\App.tsx", [], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\index.ts", [], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\Store.ts", ["107", "108", "109", "110", "111"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\Actions.ts", ["112", "113", "114"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\views\\index.tsx", ["115", "116"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\Localization.ts", ["117"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\Client.ts", [], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\mutators\\index.ts", [], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\Epics.ts", ["118", "119", "120"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\views\\header\\index.tsx", [], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\Epics\\UserAccount.ts", ["121", "122", "123"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\Epics\\Catalog.ts", ["124", "125", "126", "127"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\store\\Epics\\Omniture.ts", ["128", "129", "130", "131"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\views\\catalog\\index.tsx", [], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\utils\\Characteristics.ts", ["132", "133"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\views\\catalog\\Legal.tsx", [], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\omf-changepackage-internet\\src\\views\\catalog\\Package.tsx", ["134", "135", "136", "137", "138"], [], {"ruleId": "139", "severity": 1, "message": "140", "line": 19, "column": 82, "nodeType": "141", "messageId": "142", "endLine": 19, "endColumn": 85, "suggestions": "143"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 34, "column": 20, "nodeType": "141", "messageId": "142", "endLine": 34, "endColumn": 23, "suggestions": "144"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 26, "column": 45, "nodeType": "141", "messageId": "142", "endLine": 26, "endColumn": 48, "suggestions": "145"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 27, "column": 33, "nodeType": "141", "messageId": "142", "endLine": 27, "endColumn": 36, "suggestions": "146"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 28, "column": 32, "nodeType": "141", "messageId": "142", "endLine": 28, "endColumn": 35, "suggestions": "147"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 31, "column": 61, "nodeType": "141", "messageId": "142", "endLine": 31, "endColumn": 64, "suggestions": "148"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 32, "column": 41, "nodeType": "141", "messageId": "142", "endLine": 32, "endColumn": 44, "suggestions": "149"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 33, "column": 43, "nodeType": "141", "messageId": "142", "endLine": 33, "endColumn": 46, "suggestions": "150"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 42, "column": 11, "nodeType": "141", "messageId": "142", "endLine": 42, "endColumn": 14, "suggestions": "151"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 52, "column": 22, "nodeType": "141", "messageId": "142", "endLine": 52, "endColumn": 25, "suggestions": "152"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 8, "column": 121, "nodeType": "141", "messageId": "142", "endLine": 8, "endColumn": 124, "suggestions": "153"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 10, "column": 109, "nodeType": "141", "messageId": "142", "endLine": 10, "endColumn": 112, "suggestions": "154"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 13, "column": 113, "nodeType": "141", "messageId": "142", "endLine": 13, "endColumn": 116, "suggestions": "155"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 26, "column": 26, "nodeType": "141", "messageId": "142", "endLine": 26, "endColumn": 29, "suggestions": "156"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 47, "column": 33, "nodeType": "141", "messageId": "142", "endLine": 47, "endColumn": 36, "suggestions": "157"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 11, "column": 21, "nodeType": "141", "messageId": "142", "endLine": 11, "endColumn": 24, "suggestions": "158"}, {"ruleId": "159", "severity": 2, "message": "160", "line": 39, "column": 11, "nodeType": "161", "messageId": "162", "endLine": 47, "endColumn": 12}, {"ruleId": "139", "severity": 1, "message": "140", "line": 64, "column": 45, "nodeType": "141", "messageId": "142", "endLine": 64, "endColumn": 48, "suggestions": "163"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 64, "column": 71, "nodeType": "141", "messageId": "142", "endLine": 64, "endColumn": 74, "suggestions": "164"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 30, "column": 46, "nodeType": "141", "messageId": "142", "endLine": 30, "endColumn": 49, "suggestions": "165"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 30, "column": 59, "nodeType": "141", "messageId": "142", "endLine": 30, "endColumn": 62, "suggestions": "166"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 30, "column": 65, "nodeType": "141", "messageId": "142", "endLine": 30, "endColumn": 68, "suggestions": "167"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 81, "column": 67, "nodeType": "141", "messageId": "142", "endLine": 81, "endColumn": 70, "suggestions": "168"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 106, "column": 53, "nodeType": "141", "messageId": "142", "endLine": 106, "endColumn": 56, "suggestions": "169"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 114, "column": 40, "nodeType": "141", "messageId": "142", "endLine": 114, "endColumn": 43, "suggestions": "170"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 115, "column": 41, "nodeType": "141", "messageId": "142", "endLine": 115, "endColumn": 44, "suggestions": "171"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 15, "column": 40, "nodeType": "141", "messageId": "142", "endLine": 15, "endColumn": 43, "suggestions": "172"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 16, "column": 41, "nodeType": "141", "messageId": "142", "endLine": 16, "endColumn": 44, "suggestions": "173"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 45, "column": 49, "nodeType": "141", "messageId": "142", "endLine": 45, "endColumn": 52, "suggestions": "174"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 72, "column": 42, "nodeType": "141", "messageId": "142", "endLine": 72, "endColumn": 45, "suggestions": "175"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 3, "column": 103, "nodeType": "141", "messageId": "142", "endLine": 3, "endColumn": 106, "suggestions": "176"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 10, "column": 14, "nodeType": "141", "messageId": "142", "endLine": 10, "endColumn": 17, "suggestions": "177"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 9, "column": 23, "nodeType": "141", "messageId": "142", "endLine": 9, "endColumn": 26, "suggestions": "178"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 9, "column": 29, "nodeType": "141", "messageId": "142", "endLine": 9, "endColumn": 32, "suggestions": "179"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 51, "column": 31, "nodeType": "141", "messageId": "142", "endLine": 51, "endColumn": 34, "suggestions": "180"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 60, "column": 29, "nodeType": "141", "messageId": "142", "endLine": 60, "endColumn": 32, "suggestions": "181"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 125, "column": 108, "nodeType": "141", "messageId": "142", "endLine": 125, "endColumn": 111, "suggestions": "182"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["183", "184"], ["185", "186"], ["187", "188"], ["189", "190"], ["191", "192"], ["193", "194"], ["195", "196"], ["197", "198"], ["199", "200"], ["201", "202"], ["203", "204"], ["205", "206"], ["207", "208"], ["209", "210"], ["211", "212"], ["213", "214"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", ["215", "216"], ["217", "218"], ["219", "220"], ["221", "222"], ["223", "224"], ["225", "226"], ["227", "228"], ["229", "230"], ["231", "232"], ["233", "234"], ["235", "236"], ["237", "238"], ["239", "240"], ["241", "242"], ["243", "244"], ["245", "246"], ["247", "248"], ["249", "250"], ["251", "252"], ["253", "254"], {"messageId": "255", "fix": "256", "desc": "257"}, {"messageId": "258", "fix": "259", "desc": "260"}, {"messageId": "255", "fix": "261", "desc": "257"}, {"messageId": "258", "fix": "262", "desc": "260"}, {"messageId": "255", "fix": "263", "desc": "257"}, {"messageId": "258", "fix": "264", "desc": "260"}, {"messageId": "255", "fix": "265", "desc": "257"}, {"messageId": "258", "fix": "266", "desc": "260"}, {"messageId": "255", "fix": "267", "desc": "257"}, {"messageId": "258", "fix": "268", "desc": "260"}, {"messageId": "255", "fix": "269", "desc": "257"}, {"messageId": "258", "fix": "270", "desc": "260"}, {"messageId": "255", "fix": "271", "desc": "257"}, {"messageId": "258", "fix": "272", "desc": "260"}, {"messageId": "255", "fix": "273", "desc": "257"}, {"messageId": "258", "fix": "274", "desc": "260"}, {"messageId": "255", "fix": "275", "desc": "257"}, {"messageId": "258", "fix": "276", "desc": "260"}, {"messageId": "255", "fix": "277", "desc": "257"}, {"messageId": "258", "fix": "278", "desc": "260"}, {"messageId": "255", "fix": "279", "desc": "257"}, {"messageId": "258", "fix": "280", "desc": "260"}, {"messageId": "255", "fix": "281", "desc": "257"}, {"messageId": "258", "fix": "282", "desc": "260"}, {"messageId": "255", "fix": "283", "desc": "257"}, {"messageId": "258", "fix": "284", "desc": "260"}, {"messageId": "255", "fix": "285", "desc": "257"}, {"messageId": "258", "fix": "286", "desc": "260"}, {"messageId": "255", "fix": "287", "desc": "257"}, {"messageId": "258", "fix": "288", "desc": "260"}, {"messageId": "255", "fix": "289", "desc": "257"}, {"messageId": "258", "fix": "290", "desc": "260"}, {"messageId": "255", "fix": "291", "desc": "257"}, {"messageId": "258", "fix": "292", "desc": "260"}, {"messageId": "255", "fix": "293", "desc": "257"}, {"messageId": "258", "fix": "294", "desc": "260"}, {"messageId": "255", "fix": "295", "desc": "257"}, {"messageId": "258", "fix": "296", "desc": "260"}, {"messageId": "255", "fix": "297", "desc": "257"}, {"messageId": "258", "fix": "298", "desc": "260"}, {"messageId": "255", "fix": "299", "desc": "257"}, {"messageId": "258", "fix": "300", "desc": "260"}, {"messageId": "255", "fix": "301", "desc": "257"}, {"messageId": "258", "fix": "302", "desc": "260"}, {"messageId": "255", "fix": "303", "desc": "257"}, {"messageId": "258", "fix": "304", "desc": "260"}, {"messageId": "255", "fix": "305", "desc": "257"}, {"messageId": "258", "fix": "306", "desc": "260"}, {"messageId": "255", "fix": "307", "desc": "257"}, {"messageId": "258", "fix": "308", "desc": "260"}, {"messageId": "255", "fix": "309", "desc": "257"}, {"messageId": "258", "fix": "310", "desc": "260"}, {"messageId": "255", "fix": "311", "desc": "257"}, {"messageId": "258", "fix": "312", "desc": "260"}, {"messageId": "255", "fix": "313", "desc": "257"}, {"messageId": "258", "fix": "314", "desc": "260"}, {"messageId": "255", "fix": "315", "desc": "257"}, {"messageId": "258", "fix": "316", "desc": "260"}, {"messageId": "255", "fix": "317", "desc": "257"}, {"messageId": "258", "fix": "318", "desc": "260"}, {"messageId": "255", "fix": "319", "desc": "257"}, {"messageId": "258", "fix": "320", "desc": "260"}, {"messageId": "255", "fix": "321", "desc": "257"}, {"messageId": "258", "fix": "322", "desc": "260"}, {"messageId": "255", "fix": "323", "desc": "257"}, {"messageId": "258", "fix": "324", "desc": "260"}, {"messageId": "255", "fix": "325", "desc": "257"}, {"messageId": "258", "fix": "326", "desc": "260"}, {"messageId": "255", "fix": "327", "desc": "257"}, {"messageId": "258", "fix": "328", "desc": "260"}, {"messageId": "255", "fix": "329", "desc": "257"}, {"messageId": "258", "fix": "330", "desc": "260"}, "suggestUnknown", {"range": "331", "text": "332"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "333", "text": "334"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "335", "text": "332"}, {"range": "336", "text": "334"}, {"range": "337", "text": "332"}, {"range": "338", "text": "334"}, {"range": "339", "text": "332"}, {"range": "340", "text": "334"}, {"range": "341", "text": "332"}, {"range": "342", "text": "334"}, {"range": "343", "text": "332"}, {"range": "344", "text": "334"}, {"range": "345", "text": "332"}, {"range": "346", "text": "334"}, {"range": "347", "text": "332"}, {"range": "348", "text": "334"}, {"range": "349", "text": "332"}, {"range": "350", "text": "334"}, {"range": "351", "text": "332"}, {"range": "352", "text": "334"}, {"range": "353", "text": "332"}, {"range": "354", "text": "334"}, {"range": "355", "text": "332"}, {"range": "356", "text": "334"}, {"range": "357", "text": "332"}, {"range": "358", "text": "334"}, {"range": "359", "text": "332"}, {"range": "360", "text": "334"}, {"range": "361", "text": "332"}, {"range": "362", "text": "334"}, {"range": "363", "text": "332"}, {"range": "364", "text": "334"}, {"range": "365", "text": "332"}, {"range": "366", "text": "334"}, {"range": "367", "text": "332"}, {"range": "368", "text": "334"}, {"range": "369", "text": "332"}, {"range": "370", "text": "334"}, {"range": "371", "text": "332"}, {"range": "372", "text": "334"}, {"range": "373", "text": "332"}, {"range": "374", "text": "334"}, {"range": "375", "text": "332"}, {"range": "376", "text": "334"}, {"range": "377", "text": "332"}, {"range": "378", "text": "334"}, {"range": "379", "text": "332"}, {"range": "380", "text": "334"}, {"range": "381", "text": "332"}, {"range": "382", "text": "334"}, {"range": "383", "text": "332"}, {"range": "384", "text": "334"}, {"range": "385", "text": "332"}, {"range": "386", "text": "334"}, {"range": "387", "text": "332"}, {"range": "388", "text": "334"}, {"range": "389", "text": "332"}, {"range": "390", "text": "334"}, {"range": "391", "text": "332"}, {"range": "392", "text": "334"}, {"range": "393", "text": "332"}, {"range": "394", "text": "334"}, {"range": "395", "text": "332"}, {"range": "396", "text": "334"}, {"range": "397", "text": "332"}, {"range": "398", "text": "334"}, {"range": "399", "text": "332"}, {"range": "400", "text": "334"}, {"range": "401", "text": "332"}, {"range": "402", "text": "334"}, {"range": "403", "text": "332"}, {"range": "404", "text": "334"}, [697, 700], "unknown", [697, 700], "never", [934, 937], [934, 937], [679, 682], [679, 682], [717, 720], [717, 720], [754, 757], [754, 757], [1136, 1139], [1136, 1139], [1182, 1185], [1182, 1185], [1230, 1233], [1230, 1233], [1769, 1772], [1769, 1772], [1996, 1999], [1996, 1999], [487, 490], [487, 490], [743, 746], [743, 746], [1028, 1031], [1028, 1031], [636, 639], [636, 639], [1172, 1175], [1172, 1175], [479, 482], [479, 482], [1829, 1832], [1829, 1832], [1855, 1858], [1855, 1858], [834, 837], [834, 837], [847, 850], [847, 850], [853, 856], [853, 856], [3088, 3091], [3088, 3091], [4192, 4195], [4192, 4195], [4435, 4438], [4435, 4438], [4482, 4485], [4482, 4485], [416, 419], [416, 419], [463, 466], [463, 466], [1304, 1307], [1304, 1307], [2178, 2181], [2178, 2181], [167, 170], [167, 170], [432, 435], [432, 435], [400, 403], [400, 403], [406, 409], [406, 409], [1515, 1518], [1515, 1518], [1829, 1832], [1829, 1832], [6428, 6431], [6428, 6431]]