import { ValueOf, Volt } from "omf-changepackage-components";
import { IObject, IOrderAPIResponse, IOrderSummary } from "../models";
import { has } from "../utils";

/*
 * @return
  {
    currentTotal: Volt.IPriceDetail,
    newTotal: Volt.IPriceDetail,
    lineOfBusiness: {
      TV: {
        New?: Array<Volt.IProductOfferingGroup>,
        Current?: Array<Volt.IProductOfferingGroup>
      },
      Internet: {
        New?: Array<Volt.IProductOfferingGroup>,
        Current?: Array<Volt.IProductOfferingGroup>
      }
    }
  }
 */
export const orderDetailsMutatorFn = ({
  productOfferingDetail: {
    productOfferingGroups,
    productConfigurationTotal,
    displayGroup
  }
}: IOrderAPIResponse): IOrderSummary => {
  const lineOfBusiness: IObject<Array<Volt.IProductOfferingGroup>> = productOfferingGroups
      .filter((group: Volt.IProductOfferingGroup) => group.lineOfBusiness !== null)
      .map(item => {
        item.productOfferings.map(innerItem => {
          if (ValueOf<string>(displayGroup, "baseOffering.key", "") === innerItem.displayGroupKey) {
            innerItem.sortPriority = (displayGroup as any).baseOffering.sortPriority;
          }
          else {
            ValueOf<Array<Volt.IProductOffering>>(displayGroup, "additionalOfferings", []).map(dg => {
              if ((dg as any).key === innerItem.displayGroupKey) {
                innerItem.sortPriority = dg.sortPriority;
              }
            });
          }
          return innerItem;
        });
        return item;
      }).reduce((acc: IObject<Array<Volt.IProductOfferingGroup>>, cur: Volt.IProductOfferingGroup) => ({
        ...acc,
        [cur.lineOfBusiness as string]: has(acc, cur.lineOfBusiness as string) ? [...acc[cur.lineOfBusiness as string], cur] : [cur]
      }), {}) as IObject<Array<Volt.IProductOfferingGroup>>,
    lineOfBusinessGroupedByType: IObject<IObject<Array<Volt.IProductOfferingGroup>>> = Object.keys(lineOfBusiness)
      .reduce((acc: IObject<IObject<Array<Volt.IProductOfferingGroup>>>, cur: string) => {
        const lineByType: IObject<Array<Volt.IProductOfferingGroup>> = lineOfBusiness[cur]
          .reduce((acc: IObject<Array<Volt.IProductOfferingGroup>>, cur: Volt.IProductOfferingGroup) => ({
            ...acc,
            [cur.productOfferingGroupType]: has(acc, cur.productOfferingGroupType) ? [...acc[cur.productOfferingGroupType], cur] : [cur]
          }), {}) as IObject<Array<Volt.IProductOfferingGroup>>;
        return {
          ...acc, [cur]: lineByType
        };
      }, {});
  let sumaryTotals = ValueOf<Array<Volt.ILineOfBusiness>>(productConfigurationTotal, "priceOvertime", []).find(s => s.flowType === "AllLOBs");
  if (!sumaryTotals) {
    sumaryTotals = ValueOf<Array<Volt.ILineOfBusiness>>(productConfigurationTotal, "priceOvertime", []).reduce(
      (totals, lob) => {
        if (!totals.newPrice && !totals.currentPrice) {
          totals = lob;
        } else {
          totals.currentPrice = {
            price: ValueOf(totals, "currentPrice.price", 0) + ValueOf(lob, "currentPrice.price", 0),
            priceType: "Recurring"
          };
          totals.newPrice = {
            price: ValueOf(totals, "newPrice.price", 0) + ValueOf(lob, "newPrice.price", 0),
            priceType: "Recurring"
          };
        }
        return totals;
      }, {} as Volt.ILineOfBusiness
    );
  }
  return {
    currentTotal: ValueOf<Volt.IPriceDetail>(sumaryTotals, "currentPrice", {}),
    newTotal: ValueOf<Volt.IPriceDetail>(sumaryTotals, "newPrice", {}),
    lineOfBusiness: lineOfBusinessGroupedByType
  };
};

export const appointmentDetailsMutatorFn = ({ productOfferingDetail }: Volt.IAPIResponse): any => {
  let appointmentDetails: Volt.IAppointmentDetail;
  try {
    appointmentDetails = productOfferingDetail.appointment as Volt.IAppointmentDetail;
    const selectedDate = (appointmentDetails.availableDates || []).find(
      date => date.timeSlots.find(slot => slot.isSelected) as any
    ) as any;
    appointmentDetails.preferredDate = selectedDate ? {
      date: selectedDate.date,
      intervalType: (selectedDate.timeSlots.find((slot: any) => slot.isSelected) || {}).intervalType
    } : {};
  } catch (e) {
    appointmentDetails = {};
  }
  return {
    appointmentDetails,
    customerInformation: ValueOf(productOfferingDetail, "customerInformation", {})
  };
};

export const orderMessagesMutatorFn = ({ productOfferingDetail }: Volt.IAPIResponse): Array<Volt.IMessage> => productOfferingDetail.messages || [];

export const acceptedTermsMutatorFn = (terms: any, term: string) =>
  terms.some((e: any) => e === term) ? terms.filter((e: any) => e !== term) : [...terms, term];
