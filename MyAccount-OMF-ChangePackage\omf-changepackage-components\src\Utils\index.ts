import { LocalizationState } from "bwtk";
import { persistReducer } from "redux-persist";
import hardSet from "redux-persist/lib/stateReconciler/hardSet";
import storage from "redux-persist/lib/storage/session";
import { EFlowType, EWidgetRoute } from "../Models";
import { ValueOf } from "./ExtractProp";

export namespace Utils {

  declare const window: any;
  declare const document: any;
  /**
   * Pick proper locale from the standard
   * locale notation
   * @export
   * @param {LocalizationState} localization
   * @returns {LocalizationState}
   */
  export function getCurrentLocale(localization: LocalizationState): LocalizationState {
    const locale: string = localization.locale.substr(0, 2);
    return { ...localization, formats: localization.formats[locale] as any, messages: localization.messages[locale] as any };
  }

  /**
   * Open Lightbox and pass subscriberID
   * @export
   * @params {subscriberId}
   */
  declare function $(prop: string): any;
  export function showLightbox(lightboxId: string) {
    const modal = $("#" + lightboxId);
    modal.modal("show");
  }
  export function isLightboxOpen(lightboxId: string) {
    const modal = $("#" + lightboxId);
    return ValueOf(modal.data("bs.modal"), "_isShown", false);
  }
  export function hideLightbox(lightboxId: string) {
    if (isLightboxOpen(lightboxId)) {
      const modal = $("#" + lightboxId);
      modal.modal("hide");
    }
  }

  export function getCookie(cname: string) {
    const decodedCookie = decodeURIComponent(document.cookie);
    const name = cname + "=";
    const ca = decodedCookie.split(";");
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === " ") {
        c = c.substring(1);
      }
      if (c.indexOf(name) === 0) {
        return c.substring(name.length, c.length);
      }
    }
    return "";
  }

  export const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
  export const isIE11 = !!window.MSInputMethodContext && !!document.documentMode;

  export const deviceType = () => {
    const windowWidth = $(window).outerWidth();
    const isMobile = windowWidth > 767 ? false : true;
    const isTablet = windowWidth > 991 ? false : true;
    return {
      isMobile,
      isTablet
    };
  };

  export function debounce(func: Function, wait: number, immediate?: boolean) {
    let timeout: any;
    return function () {
      const context = this, args = arguments;
      const later = function () {
        timeout = null;
        if (!immediate) func.apply(context, args);
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func.apply(context, args);
    };
  }

  /*
   *  @description the caller is the reducer accessor method of the BaseStore class of individual consuming widget
   *  @param {any} config persistConfig object, "key" property value is the "name" value of individual consuming widget from its package.json
   *  @param {any} reducers combined reducers of individual consuming widget
   *  @return {any} persisted reducers required by Redux-persist
   *  usage example:
   *
   *    import { Utils } from "omf-changepackage-components";
   *    export class Store extends BaseStore<IStoreState> {
   *       constructor(store: BwtkStore, private epics: Epics, private localization: Localization) {
   *         super(store);
   *       }
   *
   *       get reducer() {
   *
   *         return Utils.reducer(
   *           {
   *              ...Utils.persistConfig,
   *              key: require("../../package.json").name,
   *              blacklist: ["uiValidationErrors"]
   *           },
   *           combineReducers<IStoreState>({
   *             ...
   */
  export const reducer = (config: any, reducers: any): any => persistReducer(config, reducers);

  export const persistStateExists = (config: any): boolean => Boolean(localStorage.getItem(`omf:${config.key}`));

  export const clearCachedState = (widgets: string[]): void => {
    widgets.forEach((widget: string) => sessionStorage.removeItem(`omf:${widget}`));
  };

  /*
   *  @description to be merged into the config object of individual widgets with their unique key value and black/whitelist if any
   */
  export const persistConfig: any = (name: string, blacklist: Array<string> = []) => ({
    version: 1,
    keyPrefix: "omf:",
    storage,
    stateReconciler: hardSet,
    key: name,
    blacklist: ["localization", "widgetStatus", "error", "lightboxData", "restriction", ...blacklist]
  });

  export function getFlowType(): EFlowType {
    let flowType = sessionStorage.getItem("omf:Flowtype") as EFlowType;
    if (!flowType) {
      const pathname = window.location.pathname;
      switch (true) {
        case pathname.indexOf("Changepackage/Internet") > 0:
          flowType = EFlowType.INTERNET;
          break;
        case pathname.indexOf("Changepackage/TV") > 0:
          flowType = EFlowType.TV;
          break;
        case pathname.indexOf("Add/TV") > 0:
          flowType = EFlowType.ADDTV;
          break;
        case pathname.indexOf("Bundle/") > 0:
          flowType = EFlowType.BUNDLE;
          break;
        default:
          // No specific flow type detected from pathname
          break;
      }
    }
    return flowType;
  }

  export function constructPageRoute(route?: EWidgetRoute, flowType?: EFlowType): string {
    const currentFlowType = flowType || getFlowType();
    switch (currentFlowType) {
      case EFlowType.INTERNET:
        switch (route) {
          case EWidgetRoute.INTERNET: return "/Changepackage/Internet";
          case EWidgetRoute.APPOINTMENT: return "/Changepackage/Internet/Appointment";
          case EWidgetRoute.REVIEW: return "/Changepackage/Internet/Review";
          case EWidgetRoute.CONFIRMATION: return "/Changepackage/Internet/Confirmation";
          default: return "/Changepackage";
        }
      case EFlowType.TV:
        switch (route) {
          case EWidgetRoute.TV: return "/Changepackage/TV";
          case EWidgetRoute.APPOINTMENT: return "/Changepackage/TV/Appointment";
          case EWidgetRoute.REVIEW: return "/Changepackage/TV/Review";
          case EWidgetRoute.CONFIRMATION: return "/Changepackage/TV/Confirmation";
          default: return "/Changepackage";
        }
      case EFlowType.ADDTV:
        switch (route) {
          case EWidgetRoute.TV: return "/Add/TV";
          case EWidgetRoute.REVIEW: return "/Add/TV/Review";
          case EWidgetRoute.CONFIRMATION: return "/Add/TV/Confirmation";
          default: return "/Add";
        }
      case EFlowType.BUNDLE:
        switch (route) {
          case EWidgetRoute.INTERNET: return "/Bundle/Internet";
          case EWidgetRoute.TV: return "/Bundle/TV";
          case EWidgetRoute.APPOINTMENT: return "/Bundle/Internet/Appointment";
          case EWidgetRoute.REVIEW: return "/Bundle/Review";
          case EWidgetRoute.CONFIRMATION: return "/Bundle/Confirmation";
          default: return "/Bundle";
        }
      default:
        return "/";
    }
  }

  export function getPageRoute(): EWidgetRoute | undefined {
    const pathname = window.location.pathname;
    switch (true) {
      case pathname.indexOf("Review") > 0: return EWidgetRoute.REVIEW;
      case pathname.indexOf("Confirm") > 0: return EWidgetRoute.CONFIRMATION;
      case pathname.indexOf("Appoint") > 0: return EWidgetRoute.APPOINTMENT;
      case pathname.indexOf("Internet") > 0: return EWidgetRoute.INTERNET;
      case pathname.indexOf("TV") > 0: return EWidgetRoute.TV;
      default:
        // No specific route detected from pathname
        break;
    }
    return undefined;
  }

  export function getURLByFlowType(scheme: { [key: string]: string }): string {
    const flowType = sessionStorage.getItem("omf:Flowtype") || "Internet";
    return scheme[flowType];
  }

  export function appendRefreshOnce(path: string) {
    const isFistTime = !sessionStorage.getItem("omf:Initilized");
    return (path || "") + (isFistTime ? "?refreshCache=true" : "");
  }
}
export * from "./Assert";
export * from "./ExtractProp";
export * from "./FilterRestrictionObservable";

export * from "./FormattedHTMLMessage";
