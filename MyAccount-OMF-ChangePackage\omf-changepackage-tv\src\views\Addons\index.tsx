import * as React from "react";
import { connect } from "react-redux";
import { ValueOf, Volt, Components, FormattedHTMLMessage } from "omf-changepackage-components";
import { FormattedMessage } from "react-intl";
import { IStoreState } from "../../models";

import Combo from "../Components/Combo";
import { Footer } from "../Components/Legal";
import { sortOfferings } from "../../utils/Characteristics";
import { OmniturePage } from "../Components/Omniture";

interface IComponentConnectedProps {
  packages: Array<Volt.IProductOffering>;
}

interface IComponentDispatches {
}

const Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({
  packages
}) => <OmniturePage name="Addons">
  <div className="flexRow flex-justify-space-between">
    <div className="margin-xs">
      <h2 className="virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs">
        <FormattedMessage id="Addons page" />
      </h2>
      <FormattedHTMLMessage id="Addons page description">
        {
          (__html: any) => <Components.Visible when={Boolean(__html)}>
            <div className="spacer5"></div>
            <p className="noMargintxtSize14">{__html}</p>
          </Components.Visible>
        }
      </FormattedHTMLMessage>
    </div>
  </div>
  <div className="spacer15" aria-hidden="true" />
  {
    sortOfferings(packages).map(combo => <Combo key={combo.id} {...combo} />)
  }
  <Footer pageName={Volt.EDIsplayGroupKey.ADD_ON}
    label={`LEGAL_LABEL_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`}
    content={`LEGAL_COPY_${Volt.EDIsplayGroupKey.ADD_ON}`} />
</OmniturePage>;

export default connect<IComponentConnectedProps, IComponentDispatches>(
  ({ catalog }: IStoreState) => ({
    packages: ValueOf<Array<Volt.IProductOffering>>(catalog, "offerings." + Volt.EDIsplayGroupKey.ADD_ON, [])
      .filter(pack => pack.productOfferingType === Volt.EProductOfferingType.COMBO)
  })
)(Component);
