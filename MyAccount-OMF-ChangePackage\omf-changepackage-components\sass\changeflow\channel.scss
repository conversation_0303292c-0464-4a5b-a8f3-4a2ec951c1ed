@import "mixins";

/* Bell package box module
 * 
 * Base box for a package
 * Movie, or series pack
*/

// .section-bell-tv-package {
//     .bell-tv-channels {
//         margin: -15px 0 0 -10px;
//     }
// }
.bell-tv-channels {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: -15px;
    margin-left: -15px; // margin: -15px 0 0 -15px;
    // justify-content: space-between;
    // &:after {
    //     content: "";
    //     flex: auto;
    // }
    .bell-channels-flex-grid {
        min-width: 0;
        width: 100%;
        padding-left: 15px;
        @media #{$media-mobile} {
            width: 33.33%
        }
    }
}

.bell-tv-channel-nonselectable {
    // margin: 0;
    .bell-channels-flex-grid {
        @media #{$media-desktop-tab} {
            flex-basis: 25%; // Grid of 6
            // &:nth-child(-n+6) {
            //     margin-top: -15px;
            // }
        }
        @media #{$media-mobile} {
            flex-basis: 33.33%; // Grid of 3
            // &:nth-child(-n+3) {
            //     margin-top: -15px;
            // }
            .bell-tv-channel.flexRow-xs {
                flex-direction: column;
            }
        }
    }
}

.bell-tv-channel-picker {
    // margin: 0;
    .bell-channels-flex-grid {
        flex-basis: 100%;
        ;
    }
}

.bell-tv-channel-cross {
    // margin: 0;
    .bell-channels-flex-grid {
        @media #{$media-desktop-tab} {
            flex-basis: 25%; // Grid of 4
            // &:nth-child(-n+4) {
            //     margin-top: -15px;
            // }
        }
        @media #{$media-mobile} {
            flex-basis: 50%; // Grid of 1
            // &:nth-child(-n+2) {
            //     margin-top: -15px;
            // }
        }
    }
}

.bell-tv-channel-cross-wide {
    .bell-channels-flex-grid {
        width: 100%;
    }
}

.bell-tv-channel {
    width: 100%;
    // height: 231px;
    position: relative;
    background-color: #fff;
    border: 1px solid #D4D4D4;
    padding: 10px 15px;
    margin-top: 15px; // margin-left: 15px; // Commented to implement flex grid
    margin-left: auto;
    margin-right: auto;
    &.selected {
        border: 2px solid #000;
    }
    &.error {
        border: 2px solid #BD2025;
        .icon-x-close:before {
            left: -4px;
            top: 4px;
            @media #{$media-tab-mobile} {
                left: -6px;
                top: 6px;
            }
        }
    }
    .bell-tv-channel-icon {
        width: 104px;
        height: 101px;
        margin: 0 auto;
        will-change: contents;
        justify-content: center;
        @media #{$media-mobile} {
            width: 100%;
            height: auto;
        }
        img {
            max-width: 100%;
            width: auto;
            height: auto;
            max-height: 100%;
            -ms-flex: 1;
            will-change: contents;
            @media #{$media-mobile} {
                padding-bottom: 15px;
            }
        }
    }
    .bell-tv-channel-title {
        display: block;
        // white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding-right: 10px;
    }
    .bell-tv-channel-badge {
        position: absolute;
        top: 50px;
        padding: 0 5px;
        width: 100%;
        left: 0;
        >svg {
            display: inline-block!important;
            width: 18px!important;
            height: 23px!important;
        }
    } // Controlls:
    .bell-tv-channel-event-listener {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1;
    }
    .bell-tv-channel-checkbox,
    .bell-tv-channel-remove {
        width: 48px;
        height: 48px;
        top: 0;
        right: 0;
        padding: 5px 8px;
        text-align: right;
        text-decoration: none;
        z-index: 2;
    }
    .bell-tv-channel-tile-description {
        text-overflow: ellipsis;
        overflow: hidden;
        bottom: 5px;
        max-height: 35px;
        @media #{$media-mobile} {
            margin-right: 75px;
            margin-left: 70px;
            top: 7px;
            bottom: unset;
        }
    }
    .bell-tv-channel-checkbox {
        .ctrl_element {
            right: 10px;
            top: 10px;
            left: unset;
            left: auto;
            cursor: pointer;
        }
    }
    &.disabled {
        background-color: #F4F4F4;
        cursor: no-drop;
    }
    p.txtSize12.noMargin {
        font-style: italic;
    }
}

.bell-tv-channel-picker {
    .bell-tv-channel {
        // width: 140px;
        height: 200px; // @media #{$media-tablet} {
        //     width: 162px;
        // }
        @media #{$media-mobile} {
            width: 100%;
            height: auto;
        }
        .bell-tv-channel-container {
            position: relative;
            border: 1px solid #D4D4D4;
            padding: 15px;
            background: #FFF;
        }
        .bell-tv-channel-icon {
            width: 70px;
            height: 70px;
            @media #{$media-mobile} {
                width: 60px;
                min-width: 60px;
                height: 60px;
                margin-right: 30px;
                margin-left: 10px;
            }
        }
        .bell-tv-channel-badge {
            top: 70px;
            padding: 0 10px;
            @media #{$media-mobile} {
                top: auto;
                bottom: 10px;
            }
        }
        .bell-tv-channel-description {
            .bell-tv-channel-title {
                text-align: left;
                white-space: pre-wrap;
            }
            @media #{$media-mobile} {
                padding-right: 40px;
                max-width: 80%;
            }
        }
    }
}

.bell-tv-channel-cross {
    .bell-tv-channel {
        height: 137px;
        &.plus-free {
            background-image: linear-gradient(to right, #d5d5d5 60%, transparent 0%), linear-gradient(#d5d5d5 60%, transparent 0%), linear-gradient(to left, #d5d5d5 60%, transparent 0%), linear-gradient(#d5d5d5 60%, transparent 0%);
            background-position: bottom, right, top, left;
            background-size: 15px 2px, 2px 15px, 15px 2px, 2px 15px;
            background-repeat: repeat-x, repeat-y, repeat-x, repeat-y;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

.bell-tv-channel-cross-wide {
    .bell-tv-channel {
        height: auto;
    }
    margin-bottom: 15px;
}

.section-bell-tv-qcp {
    .bell-tv-prepackaged-programming {
        .graphical_ctrl {
            input {
                top: 0;
            }
        }
        .ctrl_element {
            top: 5px;
        }
    }
}