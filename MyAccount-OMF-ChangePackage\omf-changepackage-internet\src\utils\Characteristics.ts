import { Volt, ValueOf } from "omf-changepackage-components";

export function toCharacteristicsJSON(charactgerstics: Array<Volt.ICharacteristic>): { [key: string]: any } {
  return ValueOf<Array<Volt.ICharacteristic>>(charactgerstics, undefined, []).reduce(
    (json, charactgerstic) => {
      if (charactgerstic.name) {
        json[charactgerstic.name] =  charactgerstic.value;
      }
      return json;
    }, {} as any
  );
}
