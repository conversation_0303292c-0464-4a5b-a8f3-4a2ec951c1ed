import * as React from "react";
import { Actions, EWidgetRoute, Components, Utils, EFlowType, Omniture } from "omf-changepackage-components";
import { useDispatch, useSelector } from "react-redux";
import { FormattedMessage } from "react-intl";
import { selectors } from "../../utils";
import {
  submitOrder
} from "../../store";
import Floater from "./Floater";

const DisabledContinue = () => {
  const [hover, toggleHover] = React.useState(false);
  return <button id="ACCEPT_AND_SUBMIT"
    onClick={() => toggleHover(!hover)}
    onMouseOver={() => toggleHover(true)}
    onMouseOut={() => toggleHover(false)}
    className="btn btn-primary btn-block-xs floatR fullWidth-xs relative" disabled>
    <FormattedMessage id="ACCEPT_AND_SUBMIT" />
    <Components.Visible when={hover}>
      <div className="tooltip fade bs-tooltip-top show" role="tooltip" id="tooltip504192" style={{ position: "absolute", width: "400px", top: "-90px", left: "50%", transform: "translateX(-50%)" }}>
        <div className="arrow" style={{ left: "50%" }} />
        <div className="tooltip-inner" style={{ maxWidth: "400px", width: "100%" }}>
          <div className="flexRow bgWhite txtBlack">
            <div className="olt-icon icon-warning txtSize22 margin-10-right">
              <span className="volt-icon path1 yellowIcon"></span>
              <span className="volt-icon path2"></span>
            </div>
            <div className="margin-5-top"><FormattedMessage id="Check terms to continue" /></div>
          </div>
        </div>
      </div>
    </Components.Visible>
  </button>;
};

export const Footer: React.FunctionComponent = () => {
  const dispatch = useDispatch(),
    acceptedTerms = useSelector(selectors.select("acceptedTerms")),
    hasTVinLineOfBusiness = useSelector(selectors.select("summary.lineOfBusiness.TV")),
    disabled = (Utils.getFlowType() === EFlowType.ADDTV || Utils.getFlowType() === EFlowType.BUNDLE) && acceptedTerms.length < 1; // 2;

  return (
    <>
      <div className="spacer15 clear" aria-hidden="true" />
      <div className="spacer15 d-sm-block clear" aria-hidden="true" />
      <Floater />
      <div className="flexBlock flex-direction-row-reverse flexJustifySpace flexCol-xs accss-focus-outline-override-grey-bg">
        <Components.Visible when={!disabled} placeholder={<DisabledContinue />}>
          <button id="ACCEPT_TERMS_AND_SUBMIT"
            onClick={() => dispatch(submitOrder() as any)}
            className="btn btn-primary btn-block-xs floatR fullWidth-xs"
          >
            <FormattedMessage id="ACCEPT_AND_SUBMIT" />
          </button>
        </Components.Visible>
        <div className="spacer10 d-block d-sm-none" aria-hidden="true"></div>
        <Components.Visible when={hasTVinLineOfBusiness}>
          <button id="MODIFY_SELECTION" className="btn btn-default modifychannel"
            onClick={() => {
              Omniture.useOmniture().trackAction({
                id: "modifySelectionClick",
                s_oAPT: {
                  actionId: 647
                },
                s_oBTN: {
                  ref: "MODIFY_SELECTION"
                }
              });
              dispatch(Actions.broadcastUpdate(Actions.historyGo(EWidgetRoute.TV)) as any);
            }}>
            <FormattedMessage id="MODIFY_MY_CHANNEL_SELECTION" />
          </button>
        </Components.Visible>
      </div>
      <div className="spacer40 clear" />
    </>
  );
};
