@import "mixins";
.bell-support-links {
    // This is necessary for the
    // ios to wrap flex properly
    &.panel-body {
        &:before,
        &:after {
            content: unset;
        }
    }
    .bell-support-link {
        margin-bottom: 45px;
        padding-right: 20px;
        .bell-support-link-icon {
            font-size: 74px;
            line-height: 0;
            margin-right: 25px;
            >.icon:before {
                top: 2px
            }
        }
        .bell-support-link-description {
            // max-width: 260px;
            padding-right: 10%;
            padding-top: 10px;
            @media #{$media-tab-mobile} {
                padding-right: 0
            }
            .icon-link {
                line-height: 1;
                .icon-play-icon {
                    margin-left: 10px;
                    &:before {
                        top: 0.2em;
                    }
                }
                .icon-align-left {
                    float: left;
                    position: relative;
                    top: -2px;
                    margin-left: 0px;
                    margin-right: 10px;
                }
            }
        }
    }
}