import * as React from "react";
import { Models } from "../Models";
export declare const WidgetContext: React.Context<Models.IWidgetContext<any>>;
export declare const ContextProvider: React.ProviderExoticComponent<React.ProviderProps<Models.IWidgetContext<Models.IBaseConfig | any>>>;
export declare const Context: React.ExoticComponent<React.ConsumerProps<Models.IWidgetContext<Models.IBaseConfig | any>>>;
export declare function withContext<T>(Component: any): React.FC<T>;
