@import "mixins";
.form-horizontal .control-label {
    /* text-align:right; */
    text-align: left;
}

.modal-body-padding {
    padding: 30px;
    @media #{$media-mobile} {
        padding: 5px 15px;
    }
}

.modal-header-slider {
    /* To widen the slider to full width. Compensate for model-header padding */
    padding: 0px;
}

.channel-border {
    border: 1px solid #D4D4D4;
    padding: 10px;
    margin: 5px;
}

// .channel-details-modal-slider > {
//   .slick-prev:before, .slick-next:before {
//     background: #e1e1e1; 
//   }
// }
.side-padding {
    padding: 0 25px;
}

.model-icons {
    padding: 7.5px 0;
    .model-icon {
        width: 20px;
        margin-right: 5px;
        svg {
            height: 20px;
            width: 20px;
        }
    }
}

.bell-radio-list {
    .graphical_ctrl.graphical_ctrl_ext {
        .ctrl_element {
            border-radius: 50%;
        }
        input:checked~.ctrl_element {
            .chk-check {
                display: none;
            }
            .chk-radio {
                display: block;
            }
        }
    }
}

// 26821 TVCS - SOAK- eCare/ MBM / Deep linking: Ways to add this channel - some options do not match a mockup
.bell-ways-to-add .bell-radio-list .bell-tv-package.bgGrayLight6 {
    background-color: #f4f4f4;
    @media #{$media-mobile} {
        padding: 15px;
    }
}