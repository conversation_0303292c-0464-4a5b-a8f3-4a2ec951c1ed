/* eslint-disable @stylistic/indent */
import { Actions, Components, EWidgetRoute, FormattedHTMLMessage, Omniture, ValueOf } from "omf-changepackage-components";
import * as React from "react";
import { FormattedDate, FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { IAppointmentDetails, IStoreState } from "../../models";

const {
  Visible
} = Components;

interface IComponentProps extends IAppointmentDetails {
  isReview: boolean;
}

interface IComponentDispatches {
  onEdit: () => void;
}

const Component: React.FC<IComponentProps & IComponentDispatches> = ({
  isReview,
  appointmentDetails,
  customerInformation,
  onEdit
}) => <>
  <Visible when={ValueOf(customerInformation, undefined, false)}>
    <div className="spacer15 clear" aria-hidden="true" />
    <div className="spacer15 d-sm-block clear" aria-hidden="true" />
    <div className="bgWhite pad-30-left pad-40-right padding-25-xs border-radius-3">
      <div className="spacer30 d-none d-sm-block" />
      <div className="spacer25 d-block d-sm-none" />
      <div className="">
        <div className="flexCol">
          <h2 className="txtBlack txtSize22 noMargin differentTextureforHandset floatL virginUltraReg txtUppercase"><FormattedMessage id="customer information" /></h2>
        </div>
      </div>
      <div className="spacer10" />
      <p className="txtSize14 txtGray4A sans-serif no-margin">
        <FormattedMessage id="MAKE_CHANGES" />
      </p>
      <div className="spacer20" />
      <Visible when={ValueOf(customerInformation, "name", false)}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="APPOINTMENT_NAME" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <p className="txtBlack txtSize14 no-margin ">{ValueOf(customerInformation, "name", "")}</p>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <Visible when={ValueOf(customerInformation, "email", false)}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="APPOINTMENT_EMAIL" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <p className="txtBlack txtSize14 no-margin ">{ValueOf(customerInformation, "email", "")}</p>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <Visible when={ValueOf(customerInformation, "address", false)}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="APPOINTMENT_SERVICE_ADDRESS" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <p className="txtBlack txtSize14 no-margin ">
              <div>
                <Visible when={ValueOf(customerInformation, "address.apartmentNumber", false)}>
                  {ValueOf(customerInformation, "address.apartmentNumber", "")}&nbsp;-&nbsp;
                </Visible>{ValueOf(customerInformation, "address.address1", "")}&nbsp;
                {ValueOf(customerInformation, "address.address2", "")}&nbsp;
                {ValueOf(customerInformation, "address.streetType", "")}
              </div>
              <div>{ValueOf(customerInformation, "address.city", "")},</div>
              <div>{ValueOf(customerInformation, "address.province", "")},&nbsp;{ValueOf(customerInformation, "address.postalCode", "")}</div>
            </p>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <Visible when={ValueOf(customerInformation, "paymentMethod", false)}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="PAYMENT_INFORMATION" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <Visible when={ValueOf(customerInformation, "paymentMethod", "") === "Regular"}>
              <p className="txtBlack txtSize14 no-margin "><FormattedMessage id="PAYMENT_INFORMATION_REGULAR" /></p>
            </Visible>
            <Visible when={ValueOf(customerInformation, "paymentMethod", "") === "PreAuthBank"}>
              <p className="txtBlack txtSize14 no-margin "><FormattedMessage id="PAYMENT_INFORMATION_PREAUTH" /></p>
            </Visible>
            <Visible when={ValueOf(customerInformation, "paymentMethod", "") === "PreAuthCreditCard"}>
              <p className="txtBlack txtSize14 no-margin "><FormattedMessage id="PAYMENT_INFORMATION_PREAUTHCC" /></p>
            </Visible>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <div className="spacer15 clear" aria-hidden="true" />
      <div className="spacer15 d-sm-block clear" aria-hidden="true" />
    </div>
  </Visible>

  <Visible when={ValueOf(appointmentDetails, "isInstallationRequired", false)}>
    <div className="spacer15 clear" aria-hidden="true" />
    <div className="spacer15 d-sm-block clear" aria-hidden="true" />
    <div className="bgWhite pad-30-left pad-40-right padding-25-xs border-radius-3">
      <div className="spacer30 d-none d-sm-block" />
      <div className="spacer25 d-block d-sm-none" />
      <div className="">
        <div className="flexCol">
          <h4 className="txtBlack txtSize22 noMargin differentTextureforHandset floatL virginUltraReg txtUppercase">
            <FormattedMessage id="installation details" />
            <Visible when={isReview}>
              <button id="review_installation_edit" onClick={onEdit} className="txtBlue txtSize14 margin-20-left noBorder bgTransparent pointer"><FormattedMessage id="Edit" /><span className="virgin-icon icon-edit txtBlue txtSize14 margin-5-left" aria-hidden="true"></span></button>
            </Visible>
          </h4>
        </div>
      </div>
      <div className="spacer20" />
      <Visible when={ValueOf(appointmentDetails, "preferredDate", false)}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="APPOINTMENT_DATE_AND_TIME" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft txtBlack txtBold txtSize14">
            <p className="txtBlack mb-0"><FormattedDate value={ValueOf(appointmentDetails, "preferredDate.date", "")} year="numeric" weekday="long" month="long" day="2-digit" timeZone="UTC" /></p>
            <div><FormattedHTMLMessage id={`${ValueOf(appointmentDetails, "preferredDate.intervalType", "")}`} /></div>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <Visible when={ValueOf(appointmentDetails, "installationAddress", false)}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="APPOINTMENT_SHIPPING_INSTALLATION_ADDRESS" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <p className="txtBlack txtSize14 no-margin">
              <Visible when={ValueOf(appointmentDetails, "installationAddress.apartmentNumber", false)}>
                {ValueOf(appointmentDetails, "installationAddress.apartmentNumber", "")}&nbsp;-&nbsp;
              </Visible>
              {ValueOf(appointmentDetails, "installationAddress.address1", "")}&nbsp;
              {ValueOf(appointmentDetails, "installationAddress.address2", "")}&nbsp;
              {ValueOf(appointmentDetails, "installationAddress.streetType", "")},&nbsp;
              {ValueOf(appointmentDetails, "installationAddress.city", "")},&nbsp;
              {ValueOf(appointmentDetails, "installationAddress.province", "")},&nbsp;
              {ValueOf(appointmentDetails, "installationAddress.postalCode", "")}
            </p>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <Visible when={ValueOf(appointmentDetails, "contactInformation.preferredContactMethod", "") !== "Email"}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <Visible when={ValueOf(appointmentDetails, "contactInformation.preferredContactMethod", "") === "Phone"}>
              <div className="txtBlack txtBold txtSize14"><FormattedMessage id="APPOINTMENT_PHONE_NUMBER" /></div>
            </Visible>
            <Visible when={ValueOf(appointmentDetails, "contactInformation.preferredContactMethod", "") === "TextMessage"}>
              <div className="txtBlack txtBold txtSize14"><FormattedMessage id="TEXT_MESSAGE" /></div>
            </Visible>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <p className="txtBlack txtSize14 no-margin">
              {ValueOf(appointmentDetails, "contactInformation.primaryPhone.phoneNumber", "")}&nbsp;
              <Visible when={ValueOf(appointmentDetails, "contactInformation.primaryPhone.phoneExtension", false)}>
                <FormattedMessage id="EXT" />&nbsp;
                {ValueOf(appointmentDetails, "contactInformation.primaryPhone.phoneExtension", "")}
              </Visible>
            </p>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <Visible when={ValueOf(appointmentDetails, "contactInformation.preferredContactMethod", "") === "Email"}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="APPOINTMENT_EMAIL" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <p className="txtBlack txtSize14 no-margin">
              {ValueOf(appointmentDetails, "contactInformation.email", "")}
            </p>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <Visible when={ValueOf(appointmentDetails, "contactInformation.additionalPhone", false)}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="ADDITIONAL_PHONE_NUMBER" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <p className="txtBlack txtSize14 no-margin">
              {ValueOf(appointmentDetails, "contactInformation.additionalPhone.phoneNumber", "")}&nbsp;
              <Visible when={ValueOf(appointmentDetails, "contactInformation.additionalPhone.phoneExtension", false)}>
                <FormattedMessage id="EXT" />&nbsp;
                {ValueOf(appointmentDetails, "contactInformation.additionalPhone.phoneExtension", "")}
              </Visible>
            </p>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <Visible when={ValueOf(appointmentDetails, "additionalDetails.apartment", false)}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="APPOINTMENT_APARTMENT_NUMBER" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <p className="txtBlack txtSize14 no-margin">
              {ValueOf(appointmentDetails, "additionalDetails.apartment", "")}
            </p>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <Visible when={ValueOf(appointmentDetails, "additionalDetails.entryCode", false)}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="APPOINTMENT_ENTRY_CODE" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <p className="txtBlack txtSize14 no-margin">
              {ValueOf(appointmentDetails, "additionalDetails.entryCode", "")}
            </p>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <Visible when={ValueOf(appointmentDetails, "additionalDetails.superintendantName", false)}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="APPOINTMENT_SUPERINTENDANT_NAME" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <p className="txtBlack txtSize14 no-margin">
              {ValueOf(appointmentDetails, "additionalDetails.superintendantName", "")}
            </p>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <Visible when={ValueOf(appointmentDetails, "additionalDetails.superintendantPhone", false)}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="APPOINTMENT_SUPERINTENDANT_PHONE" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <p className="txtBlack txtSize14 no-margin">
              {ValueOf(appointmentDetails, "additionalDetails.superintendantPhone", "")}
            </p>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <Visible when={ValueOf(appointmentDetails, "additionalDetails", false)}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="APPOINTMENT_SUPERINTENDANT_INFORMED" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <p className="txtBlack txtSize14 no-margin">
              <Visible when={ValueOf(appointmentDetails, "additionalDetails.informedSuperintendant", false)}
                placeholder={<FormattedMessage id="NO" />}>
                <FormattedMessage id="YES" />
              </Visible>
            </p>
          </div>
        </div>
        <div className="spacer5" />
      </Visible>
      <Visible when={ValueOf(appointmentDetails, "additionalDetails.specialInstructions", false)}>
        <div className="flexRow col-12">
          <div className="col-xs-6 col-sm-6 col-md-3 no-pad">
            <div className="txtBlack txtBold txtSize14"><FormattedMessage id="APPOINTMENT_SPECIAL_INSTRUCTIONS" /></div>
          </div>
          <div className="col-xs-7 col-sm-6 col-md-9 txtLeft">
            <p className="txtBlack txtSize14 no-margin">
              {ValueOf(appointmentDetails, "additionalDetails.specialInstructions", "")}
            </p>
          </div>
        </div>
      </Visible>
      <div className="spacer15 clear" aria-hidden="true" />
      <div className="spacer15 d-sm-block clear" aria-hidden="true" />
    </div>
  </Visible>
</>;

export const Appointment = connect<IComponentProps, IComponentDispatches>(
  ({
    confirmation,
    appointment
  }: IStoreState) => ({
    isReview: !ValueOf(confirmation, "confirmationNumber", false),
    appointmentDetails: ValueOf(appointment, "appointmentDetails", {}),
    customerInformation: ValueOf(appointment, "customerInformation", {})
  }),
  dispatch => ({
    onEdit: () => {
      Omniture.useOmniture().trackAction({
        id: "editClick",
        s_oAPT: {
          actionId: 647
        },
        s_oBTN: {
          ref: "review_installation_edit"
        }
      });
      dispatch(Actions.broadcastUpdate(Actions.historyGo(EWidgetRoute.APPOINTMENT)));
    }
  })
)(Component);
