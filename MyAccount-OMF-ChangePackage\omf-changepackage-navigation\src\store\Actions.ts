import { createAction, Action } from "redux-actions";
import { EFlowType, Volt } from "omf-changepackage-components";
import { ISummary, IAPIResponse } from "../models";
import { summaryTransformerFn, setFlowTypeFn } from "../mutators";

// Widget actions
export const setFlowType = createAction<EFlowType>("SET_FLOW_TYPE", setFlowTypeFn as any) as (type: EFlowType) => Action<EFlowType>;
export const setSummaryTotals = createAction<ISummary>("SET_FLOW_SUMMARY_TOTALS", summaryTransformerFn as any) as (type: IAPIResponse) => Action<ISummary>;
export const checkRestrictions = createAction<Volt.IHypermediaAction>("CHECK_NAVIGATION_RESTRICTIONS") as (action: Volt.IHypermediaAction) => Action<Volt.IHypermediaAction>;

// Piped actions

