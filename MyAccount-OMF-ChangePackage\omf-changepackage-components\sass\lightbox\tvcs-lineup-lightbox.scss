@import "mixins";
.bell-my-channel-lineup {
    // Scrollable content setup:
    &.modal-body {
        padding: 0;
    }
    // @media #{$media-mobile} {
    //     >div {
    //         position: fixed;
    //         top: 50px;
    //         bottom: 0;
    //     }
    // } 
    // ---
    .content-body {
        padding: 30px;
        @media #{$media-mobile} {
            padding: 15px;
        }
    }
    .content-footer {
        position: absolute;
        width: 100%;
        bottom: 0px;
        padding: 15px 30px;
        @media #{$media-mobile} {
            position: static;
        }
    }
    .bell-channel-listing-filter {
        .form-control-select {
            line-height: 1.2em;
        }
    }
    .bell-channel-listing-table {
        // Scrollable content setup (Continued):
        .scrollable-content {
            max-height: 400px;
            overflow-y: scroll;
            @media #{$media-mobile} {
                max-height: unset;
            }
        }
        @media #{$media-desktop} {
            padding: 15px 0 70px;
            .scrollable-content {
                margin-right: 15px;
                >.content-body {
                    padding: 15px 15px 30px 30px;
                }
            }
        } // table styling
        table.table {
            // Table column width
            td {
                vertical-align: middle;
                padding: 15px 30px;
                &:nth-child(1) {
                    width: auto;
                }
                &:nth-child(2) {
                    width: auto;
                }
                &:nth-child(3) {
                    width: 110px;
                }
                @media #{$media-mobile} {
                    padding: 15px;
                    &:nth-child(1) {
                        width: 50%;
                    }
                    &:nth-child(2) {
                        width: auto;
                    }
                    &:nth-child(3) {
                        width: auto;
                    }
                }
            } // ---
            th {
                padding: 0;
            }
            &.table-striped>tbody {
                >tr.channel-list-heading {
                    background-color: unset;
                    .noBorder {border: none}
                }
                >tr:not(.channel-list-heading) {
                    &:nth-of-type(odd) {
                        background-color: #f4f4f4;
                    }
                    &:nth-of-type(even) {
                        background-color: #fff;
                    }
                }
            }
        }
    }
    .channel-details {
        background-color: #f4f4f4;
        img.logo {
            background-color: #fff;
            border: 1px solid #d4d4d4;
            padding: 10px;
        }
    }
}

#lineup-oc-modal {
    .return {
        display: none;
        text-decoration: none;
        margin-right: 20px;
        .icon2:before {
            top: 0
        }
    }
    .channel-details {
        display: none
    }
    .lineup-table {
        display: flex;
        max-height: 600px;
        @media #{$media-mobile} {
            max-height: unset;
            display: block;
        }
    }
    &.showDetails {
        .return {
            display: inline-block
        }
        .channel-details {
            display: block
        }
        .lineup-table {
            display: none
        }
    }
}