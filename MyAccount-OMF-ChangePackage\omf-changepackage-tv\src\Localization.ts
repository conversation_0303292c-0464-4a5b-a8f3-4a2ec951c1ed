import {
  CommonFeatures,
  CommonServices,
  Injectable,
  ServiceLocator
} from "bwtk";
import { EWidgetName } from "omf-changepackage-components";

const { BaseLocalization } = CommonFeatures;

@Injectable
export class Localization extends BaseLocalization {
  static Instance = null;
  static getLocalizedString(id: string): string {
    Localization.Instance =
      Localization.Instance ||
      ServiceLocator.instance.getService(CommonServices.Localization);
    const instance: any = Localization.Instance;
    const result = instance
      ? instance.getLocalizedString(EWidgetName.TV, id, instance.locale)
      : id;
    return Boolean(result) ? result : id;
  }
}
