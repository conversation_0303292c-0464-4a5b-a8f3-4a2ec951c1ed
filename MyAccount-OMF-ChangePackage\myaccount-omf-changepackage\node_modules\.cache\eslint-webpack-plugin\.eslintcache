[{"C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\index.ts": "1", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\controllers\\index.ts": "2", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\utils\\index.ts": "3", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\controllers\\WidgetMapper.ts": "4", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\utils\\Config.ts": "5", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\controllers\\Loader.ts": "6", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\lib\\jQuery.ts": "7"}, {"size": 4669, "mtime": *************, "results": "8", "hashOfConfig": "9"}, {"size": 60, "mtime": *************, "results": "10", "hashOfConfig": "9"}, {"size": 27, "mtime": *************, "results": "11", "hashOfConfig": "9"}, {"size": 904, "mtime": *************, "results": "12", "hashOfConfig": "9"}, {"size": 1144, "mtime": *************, "results": "13", "hashOfConfig": "9"}, {"size": 1288, "mtime": *************, "results": "14", "hashOfConfig": "9"}, {"size": 76, "mtime": *************, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1fkf55r", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\index.ts", ["37", "38", "39", "40", "41", "42", "43", "44", "45", "46"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\controllers\\index.ts", [], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\utils\\index.ts", [], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\controllers\\WidgetMapper.ts", ["47"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\utils\\Config.ts", ["48", "49", "50", "51", "52", "53", "54"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\controllers\\Loader.ts", [], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\lib\\jQuery.ts", ["55", "56"], [], {"ruleId": "57", "severity": 1, "message": "58", "line": 7, "column": 64, "nodeType": "59", "messageId": "60", "endLine": 7, "endColumn": 67, "suggestions": "61"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 8, "column": 59, "nodeType": "59", "messageId": "60", "endLine": 8, "endColumn": 62, "suggestions": "62"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 8, "column": 78, "nodeType": "59", "messageId": "60", "endLine": 8, "endColumn": 81, "suggestions": "63"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 8, "column": 89, "nodeType": "59", "messageId": "60", "endLine": 8, "endColumn": 92, "suggestions": "64"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 23, "column": 11, "nodeType": "59", "messageId": "60", "endLine": 23, "endColumn": 14, "suggestions": "65"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 24, "column": 23, "nodeType": "59", "messageId": "60", "endLine": 24, "endColumn": 26, "suggestions": "66"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 57, "column": 58, "nodeType": "59", "messageId": "60", "endLine": 57, "endColumn": 61, "suggestions": "67"}, {"ruleId": "68", "severity": 2, "message": "69", "line": 59, "column": 9, "nodeType": "70", "messageId": "71", "endLine": 84, "endColumn": 10}, {"ruleId": "57", "severity": 1, "message": "58", "line": 89, "column": 21, "nodeType": "59", "messageId": "60", "endLine": 89, "endColumn": 24, "suggestions": "72"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 94, "column": 80, "nodeType": "59", "messageId": "60", "endLine": 94, "endColumn": 83, "suggestions": "73"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 1, "column": 42, "nodeType": "59", "messageId": "60", "endLine": 1, "endColumn": 45, "suggestions": "74"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 1, "column": 36, "nodeType": "59", "messageId": "60", "endLine": 1, "endColumn": 39, "suggestions": "75"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 14, "column": 32, "nodeType": "59", "messageId": "60", "endLine": 14, "endColumn": 35, "suggestions": "76"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 14, "column": 43, "nodeType": "59", "messageId": "60", "endLine": 14, "endColumn": 46, "suggestions": "77"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 14, "column": 62, "nodeType": "59", "messageId": "60", "endLine": 14, "endColumn": 65, "suggestions": "78"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 18, "column": 18, "nodeType": "59", "messageId": "60", "endLine": 18, "endColumn": 21, "suggestions": "79"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 25, "column": 48, "nodeType": "59", "messageId": "60", "endLine": 25, "endColumn": 51, "suggestions": "80"}, {"ruleId": "81", "severity": 2, "message": "82", "line": 27, "column": 9, "nodeType": "83", "messageId": "84", "endLine": 27, "endColumn": 17}, {"ruleId": "57", "severity": 1, "message": "58", "line": 1, "column": 40, "nodeType": "59", "messageId": "60", "endLine": 1, "endColumn": 43, "suggestions": "85"}, {"ruleId": "57", "severity": 1, "message": "58", "line": 2, "column": 17, "nodeType": "59", "messageId": "60", "endLine": 2, "endColumn": 20, "suggestions": "86"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["87", "88"], ["89", "90"], ["91", "92"], ["93", "94"], ["95", "96"], ["97", "98"], ["99", "100"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", ["101", "102"], ["103", "104"], ["105", "106"], ["107", "108"], ["109", "110"], ["111", "112"], ["113", "114"], ["115", "116"], ["117", "118"], "default-case-last", "Default clause should be the last clause.", "SwitchCase", "notLast", ["119", "120"], ["121", "122"], {"messageId": "123", "fix": "124", "desc": "125"}, {"messageId": "126", "fix": "127", "desc": "128"}, {"messageId": "123", "fix": "129", "desc": "125"}, {"messageId": "126", "fix": "130", "desc": "128"}, {"messageId": "123", "fix": "131", "desc": "125"}, {"messageId": "126", "fix": "132", "desc": "128"}, {"messageId": "123", "fix": "133", "desc": "125"}, {"messageId": "126", "fix": "134", "desc": "128"}, {"messageId": "123", "fix": "135", "desc": "125"}, {"messageId": "126", "fix": "136", "desc": "128"}, {"messageId": "123", "fix": "137", "desc": "125"}, {"messageId": "126", "fix": "138", "desc": "128"}, {"messageId": "123", "fix": "139", "desc": "125"}, {"messageId": "126", "fix": "140", "desc": "128"}, {"messageId": "123", "fix": "141", "desc": "125"}, {"messageId": "126", "fix": "142", "desc": "128"}, {"messageId": "123", "fix": "143", "desc": "125"}, {"messageId": "126", "fix": "144", "desc": "128"}, {"messageId": "123", "fix": "145", "desc": "125"}, {"messageId": "126", "fix": "146", "desc": "128"}, {"messageId": "123", "fix": "147", "desc": "125"}, {"messageId": "126", "fix": "148", "desc": "128"}, {"messageId": "123", "fix": "149", "desc": "125"}, {"messageId": "126", "fix": "150", "desc": "128"}, {"messageId": "123", "fix": "151", "desc": "125"}, {"messageId": "126", "fix": "152", "desc": "128"}, {"messageId": "123", "fix": "153", "desc": "125"}, {"messageId": "126", "fix": "154", "desc": "128"}, {"messageId": "123", "fix": "155", "desc": "125"}, {"messageId": "126", "fix": "156", "desc": "128"}, {"messageId": "123", "fix": "157", "desc": "125"}, {"messageId": "126", "fix": "158", "desc": "128"}, {"messageId": "123", "fix": "159", "desc": "125"}, {"messageId": "126", "fix": "160", "desc": "128"}, {"messageId": "123", "fix": "161", "desc": "125"}, {"messageId": "126", "fix": "162", "desc": "128"}, "suggestUnknown", {"range": "163", "text": "164"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "165", "text": "166"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "167", "text": "164"}, {"range": "168", "text": "166"}, {"range": "169", "text": "164"}, {"range": "170", "text": "166"}, {"range": "171", "text": "164"}, {"range": "172", "text": "166"}, {"range": "173", "text": "164"}, {"range": "174", "text": "166"}, {"range": "175", "text": "164"}, {"range": "176", "text": "166"}, {"range": "177", "text": "164"}, {"range": "178", "text": "166"}, {"range": "179", "text": "164"}, {"range": "180", "text": "166"}, {"range": "181", "text": "164"}, {"range": "182", "text": "166"}, {"range": "183", "text": "164"}, {"range": "184", "text": "166"}, {"range": "185", "text": "164"}, {"range": "186", "text": "166"}, {"range": "187", "text": "164"}, {"range": "188", "text": "166"}, {"range": "189", "text": "164"}, {"range": "190", "text": "166"}, {"range": "191", "text": "164"}, {"range": "192", "text": "166"}, {"range": "193", "text": "164"}, {"range": "194", "text": "166"}, {"range": "195", "text": "164"}, {"range": "196", "text": "166"}, {"range": "197", "text": "164"}, {"range": "198", "text": "166"}, {"range": "199", "text": "164"}, {"range": "200", "text": "166"}, [303, 306], "unknown", [303, 306], "never", [391, 394], [391, 394], [410, 413], [410, 413], [421, 424], [421, 424], [1134, 1137], [1134, 1137], [1162, 1165], [1162, 1165], [2696, 2699], [2696, 2699], [3829, 3832], [3829, 3832], [4061, 4064], [4061, 4064], [41, 44], [41, 44], [35, 38], [35, 38], [378, 381], [378, 381], [389, 392], [389, 392], [408, 411], [408, 411], [549, 552], [549, 552], [897, 900], [897, 900], [39, 42], [39, 42], [61, 64], [61, 64]]