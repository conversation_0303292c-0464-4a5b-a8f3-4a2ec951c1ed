[{"C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\index.ts": "1", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\controllers\\index.ts": "2", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\utils\\index.ts": "3", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\controllers\\WidgetMapper.ts": "4", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\utils\\Config.ts": "5", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\controllers\\Loader.ts": "6", "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\lib\\jQuery.ts": "7"}, {"size": 4769, "mtime": *************, "results": "8", "hashOfConfig": "9"}, {"size": 60, "mtime": *************, "results": "10", "hashOfConfig": "9"}, {"size": 27, "mtime": *************, "results": "11", "hashOfConfig": "9"}, {"size": 904, "mtime": *************, "results": "12", "hashOfConfig": "9"}, {"size": 1204, "mtime": *************, "results": "13", "hashOfConfig": "9"}, {"size": 1288, "mtime": *************, "results": "14", "hashOfConfig": "9"}, {"size": 76, "mtime": *************, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1fkf55r", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\index.ts", ["37", "38", "39", "40", "41", "42", "43", "44", "45"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\controllers\\index.ts", [], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\utils\\index.ts", [], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\controllers\\WidgetMapper.ts", ["46"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\utils\\Config.ts", ["47", "48", "49", "50", "51", "52"], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\controllers\\Loader.ts", [], [], "C:\\WebApplications\\MyAccount-OMF-ChangePackage\\myaccount-omf-changepackage\\src\\lib\\jQuery.ts", ["53", "54"], [], {"ruleId": "55", "severity": 1, "message": "56", "line": 7, "column": 64, "nodeType": "57", "messageId": "58", "endLine": 7, "endColumn": 67, "suggestions": "59"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 8, "column": 59, "nodeType": "57", "messageId": "58", "endLine": 8, "endColumn": 62, "suggestions": "60"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 8, "column": 78, "nodeType": "57", "messageId": "58", "endLine": 8, "endColumn": 81, "suggestions": "61"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 8, "column": 89, "nodeType": "57", "messageId": "58", "endLine": 8, "endColumn": 92, "suggestions": "62"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 23, "column": 11, "nodeType": "57", "messageId": "58", "endLine": 23, "endColumn": 14, "suggestions": "63"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 24, "column": 23, "nodeType": "57", "messageId": "58", "endLine": 24, "endColumn": 26, "suggestions": "64"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 57, "column": 58, "nodeType": "57", "messageId": "58", "endLine": 57, "endColumn": 61, "suggestions": "65"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 92, "column": 21, "nodeType": "57", "messageId": "58", "endLine": 92, "endColumn": 24, "suggestions": "66"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 97, "column": 80, "nodeType": "57", "messageId": "58", "endLine": 97, "endColumn": 83, "suggestions": "67"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 1, "column": 42, "nodeType": "57", "messageId": "58", "endLine": 1, "endColumn": 45, "suggestions": "68"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 1, "column": 36, "nodeType": "57", "messageId": "58", "endLine": 1, "endColumn": 39, "suggestions": "69"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 14, "column": 32, "nodeType": "57", "messageId": "58", "endLine": 14, "endColumn": 35, "suggestions": "70"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 14, "column": 43, "nodeType": "57", "messageId": "58", "endLine": 14, "endColumn": 46, "suggestions": "71"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 14, "column": 62, "nodeType": "57", "messageId": "58", "endLine": 14, "endColumn": 65, "suggestions": "72"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 18, "column": 18, "nodeType": "57", "messageId": "58", "endLine": 18, "endColumn": 21, "suggestions": "73"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 25, "column": 48, "nodeType": "57", "messageId": "58", "endLine": 25, "endColumn": 51, "suggestions": "74"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 1, "column": 40, "nodeType": "57", "messageId": "58", "endLine": 1, "endColumn": 43, "suggestions": "75"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 2, "column": 17, "nodeType": "57", "messageId": "58", "endLine": 2, "endColumn": 20, "suggestions": "76"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["77", "78"], ["79", "80"], ["81", "82"], ["83", "84"], ["85", "86"], ["87", "88"], ["89", "90"], ["91", "92"], ["93", "94"], ["95", "96"], ["97", "98"], ["99", "100"], ["101", "102"], ["103", "104"], ["105", "106"], ["107", "108"], ["109", "110"], ["111", "112"], {"messageId": "113", "fix": "114", "desc": "115"}, {"messageId": "116", "fix": "117", "desc": "118"}, {"messageId": "113", "fix": "119", "desc": "115"}, {"messageId": "116", "fix": "120", "desc": "118"}, {"messageId": "113", "fix": "121", "desc": "115"}, {"messageId": "116", "fix": "122", "desc": "118"}, {"messageId": "113", "fix": "123", "desc": "115"}, {"messageId": "116", "fix": "124", "desc": "118"}, {"messageId": "113", "fix": "125", "desc": "115"}, {"messageId": "116", "fix": "126", "desc": "118"}, {"messageId": "113", "fix": "127", "desc": "115"}, {"messageId": "116", "fix": "128", "desc": "118"}, {"messageId": "113", "fix": "129", "desc": "115"}, {"messageId": "116", "fix": "130", "desc": "118"}, {"messageId": "113", "fix": "131", "desc": "115"}, {"messageId": "116", "fix": "132", "desc": "118"}, {"messageId": "113", "fix": "133", "desc": "115"}, {"messageId": "116", "fix": "134", "desc": "118"}, {"messageId": "113", "fix": "135", "desc": "115"}, {"messageId": "116", "fix": "136", "desc": "118"}, {"messageId": "113", "fix": "137", "desc": "115"}, {"messageId": "116", "fix": "138", "desc": "118"}, {"messageId": "113", "fix": "139", "desc": "115"}, {"messageId": "116", "fix": "140", "desc": "118"}, {"messageId": "113", "fix": "141", "desc": "115"}, {"messageId": "116", "fix": "142", "desc": "118"}, {"messageId": "113", "fix": "143", "desc": "115"}, {"messageId": "116", "fix": "144", "desc": "118"}, {"messageId": "113", "fix": "145", "desc": "115"}, {"messageId": "116", "fix": "146", "desc": "118"}, {"messageId": "113", "fix": "147", "desc": "115"}, {"messageId": "116", "fix": "148", "desc": "118"}, {"messageId": "113", "fix": "149", "desc": "115"}, {"messageId": "116", "fix": "150", "desc": "118"}, {"messageId": "113", "fix": "151", "desc": "115"}, {"messageId": "116", "fix": "152", "desc": "118"}, "suggestUnknown", {"range": "153", "text": "154"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "155", "text": "156"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "157", "text": "154"}, {"range": "158", "text": "156"}, {"range": "159", "text": "154"}, {"range": "160", "text": "156"}, {"range": "161", "text": "154"}, {"range": "162", "text": "156"}, {"range": "163", "text": "154"}, {"range": "164", "text": "156"}, {"range": "165", "text": "154"}, {"range": "166", "text": "156"}, {"range": "167", "text": "154"}, {"range": "168", "text": "156"}, {"range": "169", "text": "154"}, {"range": "170", "text": "156"}, {"range": "171", "text": "154"}, {"range": "172", "text": "156"}, {"range": "173", "text": "154"}, {"range": "174", "text": "156"}, {"range": "175", "text": "154"}, {"range": "176", "text": "156"}, {"range": "177", "text": "154"}, {"range": "178", "text": "156"}, {"range": "179", "text": "154"}, {"range": "180", "text": "156"}, {"range": "181", "text": "154"}, {"range": "182", "text": "156"}, {"range": "183", "text": "154"}, {"range": "184", "text": "156"}, {"range": "185", "text": "154"}, {"range": "186", "text": "156"}, {"range": "187", "text": "154"}, {"range": "188", "text": "156"}, {"range": "189", "text": "154"}, {"range": "190", "text": "156"}, [303, 306], "unknown", [303, 306], "never", [391, 394], [391, 394], [410, 413], [410, 413], [421, 424], [421, 424], [1134, 1137], [1134, 1137], [1162, 1165], [1162, 1165], [2696, 2699], [2696, 2699], [3929, 3932], [3929, 3932], [4161, 4164], [4161, 4164], [41, 44], [41, 44], [35, 38], [35, 38], [378, 381], [378, 381], [389, 392], [389, 392], [408, 411], [408, 411], [549, 552], [549, 552], [897, 900], [897, 900], [39, 42], [39, 42], [61, 64], [61, 64]]