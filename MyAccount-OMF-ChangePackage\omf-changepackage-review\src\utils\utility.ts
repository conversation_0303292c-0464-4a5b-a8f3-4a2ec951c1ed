import { IObject, IOrderSummary } from "../models";
import { Omniture, ValueOf, Volt } from "omf-changepackage-components";

export const has = (obj: IObject<any>, prop: string): boolean => Object.prototype.hasOwnProperty.call(obj, prop);

export const getValue = (path: string, namespace: IObject<any> = {}): any => {
  const paths: Array<string> = ("" + path).split("."),
    lastIndex: number = paths.length - 1;
  return paths.reduce((acc: IObject<any>, path: string, index: number) => {
    if (acc !== null && has(acc, path)) {
      return index === lastIndex ? acc[path] : { ...acc[path] };
    } else {
      return null;
    }
  }, namespace);
};

export const isModeMatching = (mode: string, ...modes: string[]): boolean =>
  modes
    .reduce((acc: boolean, cur: string) =>
      mode.toUpperCase().indexOf(cur.toUpperCase()) >= 0 ? true : acc
    , false);


function isOfferingRemoved(offer: Volt.IProductOffering): boolean {
  return ValueOf(offer, "state") === Volt.EOfferingState.Remove ||
    ValueOf(offer, "state") === Volt.EOfferingState.Removed ||
    ValueOf(offer, "state") === Volt.EOfferingState.Delete;
}

function isOfferingUnchanged(offer: Volt.IProductOffering): boolean {
  return ValueOf(offer, "state") === Volt.EOfferingState.Modify ||
    ValueOf(offer, "state") === Volt.EOfferingState.NoChange ||
    isOfferingRemoved(offer);
}

// function isOfferingChanged(offer: Volt.IProductOffering): boolean {
//   return ValueOf(offer, "state") === Volt.EOfferingState.Add ||
//     ValueOf(offer, "state") === Volt.EOfferingState.Added ||
//     ValueOf(offer, "state") === Volt.EOfferingState.Create ||
//     ValueOf(offer, "state") === Volt.EOfferingState.NewlySelected;
// }

function describeProduct(category: string, offering: Volt.IProductOffering): Omniture.IProduct {
  const product: Omniture.IProduct = {
    category,
    name: ValueOf(offering, "name", ""),
    sku: "",
    quantity: isOfferingRemoved(offering) ? "-1" : "1",
    price: (isOfferingRemoved(offering) ? "-" : "") + ValueOf<string>(offering, "regularPrice.price", "0")
  } as Omniture.IProduct;
  if (ValueOf<boolean>(offering, "promotionDetails.discountPrice.price", false) !== false)
    product.promo = ValueOf<string>(offering, "promotionDetails.discountPrice.price", "");
  return product;
}

export function collectOmnitureProducts(summary: IOrderSummary): Array<Omniture.IProduct> {
  const InternetOld = ValueOf<Array<Volt.IProductOffering>>(
    summary, "lineOfBusiness.Internet.Current.0.productOfferings", []
  ).filter(isOfferingUnchanged);
  const Internet = ValueOf<Array<Volt.IProductOffering>>(
    summary, "lineOfBusiness.Internet.New.0.productOfferings", []
  );
  const TVOld = ValueOf<Array<Volt.IProductOffering>>(
    summary, "lineOfBusiness.TV.Current.0.productOfferings", []
  ).filter(isOfferingUnchanged);
  const TV = ValueOf<Array<Volt.IProductOffering>>(
    summary, "lineOfBusiness.TV.New.0.productOfferings", []
  );
  return [
    ...InternetOld.map(
      offering => describeProduct("Internet", offering)
    ),
    ...Internet.map(
      offering => describeProduct("Internet", offering)
    ),
    ...TVOld.map(
      offering => describeProduct(ValueOf(offering, "displayGroupKey", ""), offering)
    ),
    ...TV.map(
      offering => describeProduct(ValueOf(offering, "displayGroupKey", ""), offering)
    )
  ];
}
