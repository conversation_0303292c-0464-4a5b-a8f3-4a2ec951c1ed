@font-face {
  font-family: 'bellslimregular';
  src: url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bellslim_regular-webfont.eot");
  src: url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bellslim_regular-webfont.eot?#iefix") format("embedded-opentype"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bellslim_regular-webfont.ttf") format("truetype"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bellslim_regular-webfont.svg#bellslimregular") format("svg");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'bellslim_mediumregular';
  src: url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bellslim_medium-webfont.eot");
  src: url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bellslim_medium-webfont.eot?#iefix") format("embedded-opentype"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bellslim_medium-webfont.ttf") format("truetype"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bellslim_medium-webfont.svg#bellslim_mediumregular") format("svg");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'bellslim_semiboldregular';
  src: url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bellslim_semibold-webfont.eot");
  src: url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bellslim_semibold-webfont.eot?#iefix") format("embedded-opentype"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bellslim_semibold-webfont.ttf") format("truetype"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bellslim_semibold-webfont.svg#bellslim_semiboldregular") format("svg");
  font-weight: normal;
  font-style: normal;
}

/*Bell icon fonts*/
@font-face {
  font-family: 'bell-icon';
  src: url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bell-icon.eot?#iefix") format("embedded-opentype"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bell-icon.woff") format("woff"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bell-icon.ttf") format("truetype"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bell-icon.svg") format("svg");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'bell-icon-outline';
  src: url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bell-icon-outline.eot?iw8dli");
  src: url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bell-icon-outline.eot?#iefixiw8dli") format("embedded-opentype"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bell-icon-outline.ttf?iw8dli") format("truetype"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bell-icon-outline.woff?iw8dli") format("woff"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bell-icon-outline.svg?iw8dli#bell-icon-outline") format("svg");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'bell-icon2';
  src: url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bell-icon2.eot?#iefix") format("embedded-opentype"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bell-icon2.woff") format("woff"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bell-icon2.ttf") format("truetype"), url("https://prdbellweb.hs.llnwd.net/Resource/BRF/core/fonts/bell-icon2.svg") format("svg");
  font-weight: normal;
  font-style: normal;
}