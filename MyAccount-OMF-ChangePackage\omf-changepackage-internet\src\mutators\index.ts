import { IServiceAccountAPI, IAccountDetails, IPackage } from "../models";
import { ValueOf, Volt } from "omf-changepackage-components";

export function serviceAccountMutatorFn(response: IServiceAccountAPI): Array<IAccountDetails> {
  return ValueOf<Array<IAccountDetails>>(response, "ProductOfferings", [{ Unavailable: true }]);
}

export function catalogMutatorFn(response: Volt.IAPIResponse): Array<IPackage> {
  const productOfferingGroup: Volt.IProductOfferingGroup =
        ValueOf(response, "productOfferingDetail.productOfferingGroups", [])
          .find((group: Volt.IProductOfferingGroup) => group.lineOfBusiness === Volt.ELineOfBusiness.Internet &&
                group.productOfferingGroupType === Volt.EProductOfferingGroupType.Default);
  return ValueOf(productOfferingGroup, "productOfferings", []);
}

export function orderMutatorFn(response: Volt.IAPIResponse, catalog: Array<IPackage>): Array<IPackage> {
  const productOfferingGroup: Volt.IProductOfferingGroup =
        ValueOf(response, "productOfferingDetail.productOfferingGroups", [])
          .find((group: Volt.IProductOfferingGroup) => group.lineOfBusiness === Volt.ELineOfBusiness.Internet &&
                group.productOfferingGroupType === Volt.EProductOfferingGroupType.Delta);
  const productOfferings: Array<Volt.IProductOffering> = ValueOf(productOfferingGroup, "productOfferings", []);
  productOfferings.forEach(product => {
    const initial: IPackage = catalog.find(pkg => pkg.id === product.id) || {} as IPackage;
    Object.assign(initial, product);
  });
  return [...catalog] as Array<IPackage>;
}
