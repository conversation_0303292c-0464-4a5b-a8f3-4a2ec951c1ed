import * as React from "react";
import { useFormContext } from "react-hook-form";
import { Installation, ContactInformation } from "./Componenets";
import { useDispatch } from "react-redux";
import { setAppointment } from "../store";

let _submitForm: any = null;

interface ComponentProps {
}

const Form: any = (props: ComponentProps) => {
  const submitRef: any = React.useRef(null);

  // React Hooks
  const { handleSubmit } = useFormContext();
  const dispatch = useDispatch();

  _submitForm = () => {
    (submitRef as any).current.click();
  };

  const customSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleSubmit((data: any) => {
      // Redux Dispatch
      dispatch(setAppointment(data));
    })(e);
  };

  return (
    <form id="AppointmentForm" onSubmit={customSubmit} >
      <div className="spacer45 hidden-m"></div>
      <div className="spacer20 d-block d-sm-none"></div>
      <Installation /> { /** Installation details and Calendring */}
      <ContactInformation /> { /** Contact Form Componenet */}
      <button ref={submitRef} type="submit" aria-hidden="true" style={{ display: "none" }} />
    </form >
  );
};

Form.useSubmitRef = (): any => _submitForm;

export default Form;
