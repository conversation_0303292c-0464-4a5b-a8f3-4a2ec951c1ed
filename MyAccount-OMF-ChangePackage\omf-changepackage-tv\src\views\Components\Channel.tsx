import {
  Actions,
  Components,
  ValueOf,
  Volt
} from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage, FormattedNumber } from "react-intl";
import { connect } from "react-redux";
import { IStoreState, ITVChannel } from "../../models";
import { toggleSelection } from "../../store";
import { toCharacteristicsJSON } from "../../utils/Characteristics";
import { ModalId as ChannelDetailsModal } from "../Modals/Details";
import { ModalId as MultipleWaysToAdd } from "../Modals/MultipleWays";
import { Tooltip } from "./Tooltip";

const { Visible } = Components;

interface IComponentProps extends ITVChannel { }

interface IComponentConnectedProps { }

interface IComponentDispatches {
  onInfoClick: (props: IComponentProps, id: string) => void;
  onActionClick: (action: Volt.IHypermediaAction) => void;
  onMultipleWaysToAdd: (props: IComponentProps, id: string) => void;
}

export const Component: React.FC<IComponentProps &
  IComponentConnectedProps &
  IComponentDispatches> = props => {
  const {
    id,
    name,
    imagePath,
    regularPrice,
    promotionDetails,
    offeringAction,
    characteristics,
    isAlreadyIncludedIn,
    isSelectable,
    isSelected,
    isDisabled,
    multipleWaysToAdd,
    onActionClick,
    onInfoClick,
    onMultipleWaysToAdd,
    channelNumber
  } = props;
  const { callSign } = toCharacteristicsJSON(characteristics);
  const isMultipleWaysToAdd =
      isSelectable && ValueOf(multipleWaysToAdd, "length", 0) > 0;
  let popoverCtrl: any;
  const handleInfoClick = (e?: any) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (popoverCtrl) {
      popoverCtrl.hide();
    }
    onInfoClick(props, `channel_${id}`);
  };
  return (
    <Tooltip
      key={id}
      connectCtrl={(ctrl) => {
        popoverCtrl = ctrl;
        ctrl.onTooltipClick = handleInfoClick;
      }}
      {...props}
      className={"col-12 col-sm-3 col-md-3 pad-15-left"}
    >
      <div className="" id={id} data-cs={callSign}>
        <div
          className={`bell-tv-channel flexCol flexRow-xs
              ${isSelectable && isSelected ? " selected" : ""}
              ${!isSelectable ? " bell-tv-channel-nonselectable" : ""}
              ${isSelectable && isDisabled ? " disabled" : ""}`}
        >
          <div
            className="bell-tv-channel-icon flexBlock flexCenter floatL-xs"
            aria-hidden="true"
          >
            <img src={imagePath} alt={name} />
          </div>
          <div className="bell-tv-channel-description flexGrow flex flex-column">
            <div className="spacer5 d-none d-sm-block" aria-hidden="true"></div>
            {
              Boolean(name) &&
                <button
                  id={`channel_${id}`}
                  className="bell-tv-channel-title txtUnderline btn btn-link p-0 m-0 txtSize14 txtNormal flexGrow links-blue-on-bg-white"
                  // href={`#${callSign}`}
                  onClick={handleInfoClick}
                >
                  {name}
                </button>
            }
            <p className="bell-tv-channel-number noMargin">{channelNumber}</p>
            <Visible
              when={isSelectable && ValueOf(regularPrice, "price", 0) > 0}
            >
              <p className="bell-tv-channel-price txtBlue noMargin">
                <FormattedNumber
                  value={
                    ValueOf(
                      promotionDetails,
                      "promotionalPrice.price",
                      false
                    ) || ValueOf(regularPrice, "price", 0)
                  }
                  format="CAD"
                />
                <Visible
                  when={ValueOf(
                    promotionDetails,
                    "promotionalPrice.price",
                    false
                  )}
                >
                    &nbsp;
                  <del>
                    <FormattedNumber
                      value={ValueOf(regularPrice, "price", 0)}
                      format="CAD"
                    />
                  </del>
                </Visible>
              </p>
            </Visible>
          </div>

          {/* <Visible
              when={isSelectable && ValueOf(promotionDetails, undefined, false)}
            >
              <div className="bell-tv-channel-tile-description txtSize12">
                <dfn>
                  {ValueOf(
                    promotionDetails,
                    "legalMessage",
                    <FormattedMessage id="Prices may increase legal" />
                  )}
                </dfn>
              </div>
            </Visible> */}
          <Visible when={!isDisabled && isMultipleWaysToAdd}>
            <div className="bell-tv-channel-tile-description txtSize12">
              <dfn>
                <FormattedMessage id="Multipleways to add" />
              </dfn>
            </div>
          </Visible>
          <Visible when={isSelectable && isDisabled && !Boolean(isAlreadyIncludedIn)}>
            <div className="bell-tv-channel-tile-description txtSize12">
              <dfn>
                <FormattedMessage id="Already selected" />
              </dfn>
            </div>
          </Visible>
          <Visible when={Boolean(isAlreadyIncludedIn)}>
            <div className="bell-tv-channel-tile-description txtSize12">
              <dfn>
                <FormattedMessage id="Already included in" values={{ name: isAlreadyIncludedIn }} />
              </dfn>
            </div>
          </Visible>

          <Visible when={isSelectable}>
            <label
              htmlFor={`offeringWays_${id}`}
              className="bell-tv-channel-checkbox graphical_ctrl graphical_ctrl_checkbox absolute"
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                if (!isDisabled) isMultipleWaysToAdd
                  ? onMultipleWaysToAdd(props, `channel_${id}`)
                  : onActionClick(offeringAction);
              }}
            >
              <input
                id={`offeringWays_${id}`}
                type="checkbox"
                name="packages"
                checked={isSelected}
                disabled={isDisabled}
              />
              <span className="ctrl_element chk_radius"></span>
              <span className="sr-only">{name}</span>
            </label>
          </Visible>
        </div>
      </div>
    </Tooltip>
  );
};

export default connect<
  IComponentConnectedProps,
  IComponentDispatches,
  IComponentProps
>(
  ({ }: IStoreState) => ({}),
  dispatch => ({
    onInfoClick: (data: IComponentProps, id: string) =>
      dispatch(
        Actions.openLightbox({
          lightboxId: ChannelDetailsModal,
          data: { ...data, relativeId: id }
        })
      ),
    onMultipleWaysToAdd: (data: IComponentProps, id: string) =>
      dispatch(
        Actions.openLightbox({
          lightboxId: MultipleWaysToAdd,
          data: { ...data, relativeId: id }
        })
      ),
    onActionClick: (action: Volt.IHypermediaAction) =>
      dispatch(toggleSelection(action))
  })
)(Component);
