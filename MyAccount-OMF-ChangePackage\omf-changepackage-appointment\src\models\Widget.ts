import { Config } from "../Config";
import { Models } from "omf-changepackage-components";

/**
 * props passed to hte widget from it's parent
 * or on DomRender
 * @export
 * @interface IWidgetProps
 */
export interface IWidgetProps extends Models.IBaseWidgetProps {
}

/**
 * immutable props set onto widget
 * context and accessible from
 * any component
 * @export
 * @interface IWidgetContext
 */
export interface IWidgetContext extends Models.IWidgetContext<Config> {
}

export interface IErrorsList {
  id: string;
  error: string;
}
