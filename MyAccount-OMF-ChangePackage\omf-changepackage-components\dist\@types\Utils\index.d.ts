import { LocalizationState } from "bwtk";
import { EFlowType, EWidgetRoute } from "../Models";
export declare namespace Utils {
    function getCurrentLocale(localization: LocalizationState): LocalizationState;
    function showLightbox(lightboxId: string): void;
    function isLightboxOpen(lightboxId: string): any;
    function hideLightbox(lightboxId: string): void;
    function getCookie(cname: string): string;
    const isIOS: boolean;
    const isIE11: boolean;
    const deviceType: () => {
        isMobile: boolean;
        isTablet: boolean;
    };
    function debounce(func: Function, wait: number, immediate?: boolean): () => void;
    const reducer: (config: any, reducers: any) => any;
    const persistStateExists: (config: any) => boolean;
    const clearCachedState: (widgets: string[]) => void;
    const persistConfig: any;
    function getFlowType(): EFlowType;
    function constructPageRoute(route?: EWidgetRoute, flowType?: EFlowType): string;
    function getPageRoute(): EWidgetRoute | undefined;
    function getURLByFlowType(scheme: {
        [key: string]: string;
    }): string;
    function appendRefreshOnce(path: string): string;
}
export * from "./Assert";
export * from "./ExtractProp";
export * from "./FilterRestrictionObservable";
export * from "./FormattedHTMLMessage";
