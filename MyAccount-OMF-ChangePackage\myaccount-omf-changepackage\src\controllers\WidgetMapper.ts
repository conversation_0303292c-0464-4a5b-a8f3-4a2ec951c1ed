export const WidgetMap: { [key: string]: any } = {
  "loader.staticWidgetMappings": {
    "omf-changepackage-components": {
      factory: () => require("omf-changepackage-components"),
      namespace: "Ordering"
    },
    "omf-changepackage-navigation": {
      factory: () => require("omf-changepackage-navigation"),
      namespace: "Ordering"
    },
    "omf-changepackage-internet": {
      factory: () => require("omf-changepackage-internet"),
      namespace: "Ordering"
    },
    "omf-changepackage-tv": {
      factory: () => require("omf-changepackage-tv"),
      namespace: "Ordering"
    },
    "omf-changepackage-appointment": {
      factory: () => require("omf-changepackage-appointment"),
      namespace: "Ordering"
    },
    "omf-changepackage-review": {
      factory: () => require("omf-changepackage-review"),
      namespace: "Ordering"
    }
  }
};
