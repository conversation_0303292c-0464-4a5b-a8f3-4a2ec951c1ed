export function jsonToConfig(json: any) {
  const NODE_TYPE = {
    VALUE: 0,
    NAMESPACE: 1,
    WIDGET: 2
  };
  const getNodeType = (key: string) => {
    switch (true) {
      case /\/$/.test(key): return NODE_TYPE.NAMESPACE;
      case /:$/.test(key): return NODE_TYPE.WIDGET;
      default: return NODE_TYPE.VALUE;
    }
  };
  const Reducer = (collection: any, root: any = null, scope: any = {}) => {
    const keys = Object.keys(collection);
    const $scope = { ...scope };
    const $path = root || "";
    let _config: any = {};
    keys.forEach(key => {
      switch (getNodeType(key)) {
        case NODE_TYPE.NAMESPACE: _config = { ..._config, ...Reducer(collection[key], `${$path}${key}`, $scope) };
          break;
        case NODE_TYPE.WIDGET:
          const $params = { ...$scope, ...collection[key] };
          Object.keys($params).forEach((param: any) => _config[`${$path}${key.slice(0, -1)}/${param}`] = $params[param]);
          break;
        default:
        case NODE_TYPE.VALUE: $scope[key] = collection[key];
      }
    });
    return _config;
  };
  return Reducer(json);
}
