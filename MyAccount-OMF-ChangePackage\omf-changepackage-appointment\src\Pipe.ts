import { CommonFeatures } from "bwtk";
import { Action } from "redux-actions";
import { Actions, EWidgetStatus } from "omf-changepackage-components";
import { Store } from "./store";
import Form from "./views/Form";

const { BasePipe } = CommonFeatures;

/**
 * rxjs pipe provider
 * this fascilitates the direct connection
 * between widgets through rxjs Observable
 * @export
 * @class Pipe
 * @extends {BasePipe}
 */
export class Pipe extends BasePipe {
  static Subscriptions(store: Store) {
    return {
      [Actions.onContinue.toString()]: ({ }: Action<string>) => {
        const ref = Form.useSubmitRef();
        ref && ref();
        store.dispatch(Actions.setWidgetStatus(EWidgetStatus.RENDERED));
      },
    };
  }
  /**
     *Creates a static instance of Pipe.
     * @param {*} arg
     * @memberof Pipe
     */
  static instance: Pipe;
  constructor(arg: any) {
    super(arg);
    Pipe.instance = this;
  }
}
