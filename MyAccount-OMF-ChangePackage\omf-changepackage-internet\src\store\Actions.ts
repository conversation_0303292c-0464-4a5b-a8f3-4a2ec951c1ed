import { createAction, Action } from "redux-actions";
import { Volt } from "omf-changepackage-components";
import { serviceAccountMutatorFn, catalogMutatorFn, orderMutatorFn } from "../mutators";
import { IPackage, IAccountDetails, IServiceAccountAPI } from "../models";

// Widget actions
export const getAccountDetails = createAction("GET_ACCOUNT_DETAILS");
export const setAccountDetails = createAction<Array<IAccountDetails>>("SET_ACCOUNT_DETAILS", serviceAccountMutatorFn as any) as (response: IServiceAccountAPI) => Action<Array<IAccountDetails>>;
export const getInternetCatalog = createAction("GET_INTERNET_CATALOG");
export const setInternetCatalog = createAction<Array<IPackage>>("SET_INTERNET_CATALOG", catalogMutatorFn as any) as (response: Volt.IAPIResponse) => Action<Array<IPackage>>;

export const togglePackageSelection = createAction<Volt.IHypermediaAction>("TOGGLE_INTERNET_PACKAGE");
export const updateInternetCatalog = createAction<Array<IPackage>>("UPDATE_INTERNET_CATALOG", orderMutatorFn as any) as (response: Volt.IAPIResponse, catalog: Array<IPackage>) => Action<Array<IPackage>>;

// Piped actions

