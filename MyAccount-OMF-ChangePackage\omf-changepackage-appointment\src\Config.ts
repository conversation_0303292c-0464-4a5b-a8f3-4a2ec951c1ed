import { Injectable, CommonFeatures } from "bwtk";
import { Models } from "omf-changepackage-components";

const { BaseConfig, configProperty } = CommonFeatures;

interface IAppConfig extends Models.IBaseConfig {
}

interface IAppAPI extends Models.IBaseWidgetAPI {
  orderDetailsAPI: string;
  appointmentAPI: string;
  orderSubmitAPI: string;
}

/**
 * Widget configuration provider
 * Allows the external immutable
 * config setting
 * @export
 * @class Config
 * @extends {BaseConfig<IAppConfig>}
 */
@Injectable
export class Config extends BaseConfig<IAppConfig> {
  @configProperty({}) headers: any;
  @configProperty({}) environmentVariables: any;
  @configProperty({}) mockdata: any;
  @configProperty({base: "http://127.0.0.1:8881", orderDetailsAPI: "/", appointmentAPI: "/", orderSubmitAPI: "/"}) api: IAppAPI;
}
