import { Actions, Components, Context, EFlowType, Utils, EWidgetRoute, FormattedHTMLMessage } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { IStoreState } from "../../models";
import { getPageName, checkAppointmentVisited } from "../../utils/History";
import { ModalId as ExitModalId } from "../modals/ApplicationExit";

interface IHeaderProps {
  flowType: EFlowType;
  location: any;
}

interface IHeaderConnectedProps {
  routes: Array<string>;
}
interface IComponentDispatches {
  onBackClick: (e: any, ref: any) => void;
  onExitClick: (e: string, isConfirmationStep: boolean) => void;
}

function normalize(txt: string): string {
  return (txt || "").replace(/[\W_]+/g, "").toUpperCase();
}

export const Component: React.FC<IHeaderConnectedProps & IHeaderProps & IComponentDispatches> = ({
  flowType,
  location: { pathname },
  onBackClick,
  onExitClick
}) => {
  const isConfirmationStep = Utils.getPageRoute() === EWidgetRoute.CONFIRMATION;
  const path = getPageName(pathname);
  const key = `${path}_AT_${normalize(flowType)}${
    [EWidgetRoute.REVIEW, EWidgetRoute.CONFIRMATION].indexOf(Utils.getPageRoute() as any) > -1 &&
      checkAppointmentVisited() ? "+" : ""
  }`;
  const backLabel = (window.location.pathname.toLowerCase() === "/ordering/changepackage/internet/review") ? "Back to step 1" : (window.location.pathname.toLowerCase() === "/ordering/changepackage/internet") ? "Back to Overview" : "Back";
  return <Context>
    {({ config: { linkURL } }) => <header className="bgPrimary simplified-header container-flex-box-wrap" role="banner">
      <div className="container container-fluid container-flex-box-wrap flex-justify-space-between accss-focus-outline-override-red-bg">
        <div className="page-back-button container-flex-box-wrap fullHeight align-items-center flex">
          {!isConfirmationStep && <a id="back" onClick={(e) => onBackClick(e, "back")} aria-label={backLabel} href={linkURL.exitURL} className="responsive-simplified-header-back txtDecorationNoneHover txtWhite">
            <span className="virgin-icon icon-Left_arrow txtSize15 inlineBlock" aria-hidden="true"></span>
            <span className="txtSize14 hidden-m margin-10-left txtDecoration_hover"><FormattedMessage id="Back" /></span>
          </a>}
        </div>
        <div className="page-heading container-flex-box-wrap fullHeight overflow-ellipsis-parent container-flex-grow-fill justify-center" aria-live="assertive">
          <div className="middle-align-self overflow-ellipsis">
            <h1 className="virginUltraReg txtWhite no-margin overflow-ellipsis txtCenter txtSize22 txtUppercase">
              <FormattedHTMLMessage id={`PAGE_NAME_FOR_${key}`} />
            </h1>
            {!isConfirmationStep && <FormattedHTMLMessage id={`STEP_COUNT_FOR_${key}`}>
              {
                (txt: string) => <Components.Visible when={!!txt && txt !== `STEP_COUNT_FOR_${key}`}>
                  <p className="txtWhite txtSize14 no-margin-bottom sans-serif txtCenter header-steps" dangerouslySetInnerHTML={{ __html: txt }} />
                </Components.Visible>
              }
            </FormattedHTMLMessage>}
          </div>
        </div>
        <div className="page-right-button flex-vCenter d-none d-md-flex d-lg-flex">
          <button id="exit" onClick={() => onExitClick("exit", isConfirmationStep)} data-href={linkURL.exitURL} className="btn btn-secondary-inverted margin-5-right"><FormattedMessage id={`EXIT_CTA`} /></button>
        </div>
      </div>
    </header>}
  </Context>;
};

export const Header = connect<IHeaderConnectedProps, IComponentDispatches, IHeaderProps>(
  ({ routes }: IStoreState) => ({ routes }),
  (dispatch) => ({
    onBackClick: (e, ref) => {
      e.preventDefault();
      e.stopPropagation();
      dispatch(Actions.historyBack(ref));
    },
    onExitClick: (id: string, isConfirmationStep: boolean) => {
      if (isConfirmationStep) dispatch(Actions.applicationExit());
      else dispatch(Actions.openLightbox({ lightboxId: ExitModalId, data: { relativeId: id } }));
    }
  })
)(Component);
