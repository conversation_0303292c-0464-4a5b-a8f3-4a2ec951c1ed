@import "../includes/mixins";

.installation-form-label {
  padding: 0;
  margin: 0;
  width: auto;
  font-size: 14px;
  border: none;
  font-weight: bold;
  max-width: 194px;
  flex-basis: 194px;
  text-align: left;
  padding-right: 15px;
}

.brf3-virgin-form-input {
  height: 44px;
  @extend %brf3-border;
  border-radius: 3px;
  background-color: $colorWhite;
  min-width: 300px;
  @media #{$media-tablet} {
  }
  @media #{$media-mobile} {
    min-width: 100%;
  }
}

.brf3-virgin-form-subInput {
  flex-grow: 1;
  @media #{$media-tablet} {
    margin-top: 15px;
  }
  .installation-form-label {
    padding-left: 97px;
    text-align: left;
    @media #{$media-tablet} {
      padding-left: 0;
    }
    @media #{$media-mobile} {
      padding-left: 0px;
      margin-top: 15px;
    }
  }
  .brf3-virgin-form-input {
    width: 107px;
    min-width: 107px;
  }
}

.sub-option {
  background-color: $virginGrayLigh3;
  margin: 15px -25px 10px -25px;
  padding: 40px 25px;
  width: calc(100% + 50px);
  .topArrow {
    top: -54px;
    left: -5px;
    display: block;
    @media #{$media-mobile} {
      top: -97px;
      left: calc(50% - 7.5px);
    }
  }
  .select-timeslot {
    .timeItem {
      li {
        button {
          background: #fff;
          border: 1px solid #ccc;
          width: 100%;
          display: block;
          padding: 10px 5px;
          font-size: 12px;
          cursor: pointer;
        }
      }
    }
  }
  .allDayContainer {
    .timeItem {
      li {
        button {
          display: flex;
          align-items: center;
          min-height: 188px;
          color: $virginGray;
        }
      }
    }
  }
  &.timeslot-picker {
    .select-timeslot {
      .timeItem {
        button {
          color: $virginGray;
          span {
            color: $virginGray;
            font-weight: normal;
          }
          &:hover {
            border: 1px solid $virginOrange;
            text-decoration: none;
          }
        }
      }
    }
  }
}

.topArrow {
  width: 0;
  height: 0;
  border-style: solid;
  position: absolute;
  border-width: 0 15px 15px 15px;
  border-color: transparent transparent $virginGrayLigh3 transparent;
}

.brf3-textarea {
  @extend %brf3-border;
  border-radius: 3px;
  min-width: 507px;
  min-height: 262px;
  @media #{$media-tablet} {
  }
  @media #{$media-mobile} {
    min-width: 100%;
  }
}

.timeslot-picker {
  padding-left: 80px;
  padding-right: 80px;
  padding-top: 25px;
  padding-bottom: 45px;
  .slick-slider .slick-prev,
  .slick-slider .slick-next {
    font-family: "virgin-icons" !important;
    font-size: 0;
    background: $virginOrange !important;
    top: 54% !important;
    box-shadow: none !important;
    border: 0;
    border-radius: 50%;
    position: absolute;
    z-index: 1;
    width: 35px !important;
    height: 35px !important;
    cursor: pointer;
  }
  .slick-slider .slick-prev {
    left: -48px;
  }
  .slick-slider .slick-next {
    right: -48px;
  }
  .slick-slider .slick-prev:before {
    left: 9px !important;
    content: "\e9be";
  }
  .slick-slider .slick-next:before {
    padding-left: 4px;
    content: "\e9a9";
  }
  .slick-prev:before,
  .slick-next:before {
    font-family: "virgin-icons" !important;
    font-size: 12px !important;
    font-weight: bold;
    top: 8px !important;
    opacity: 1;
  }
  .select-timeslot {
    background: transparent;
    padding: 0;
    margin-right: -2px;
    overflow: visible;

    .timeItem {
      text-align: center;
      a {
        color: $virginGray;
      }
    }
    .selectedContainer ul li {
      &.selected {
        border: 1px solid $virginOrange;
      }
      .arrow:after {
        border-top: 12px solid $virginOrange;
        top: 0;
      }
      .selectedHeader {
        background: $virginOrange;
      }
    }
    &.slick-initialized .slick-slide .day-container {
      flex-direction: column;
      padding: 0 15px !important;
      border-right: 1px solid $virginCustomGray1;
      &:last-child {
        border-right: 0;
      }
      @media #{$media-mobile} {
        border-right: none;
      }
    }
  }
  .slick-slide {
    padding: 0 15px !important;
    border-right: 1px solid $virginCustomGray1;
    div {
      display: flex;
      flex-grow: 1;
      justify-content: center;
      .selectedContainer,
      .allDayContainer {
        height: 100%;
      }
    }
  }
}

.otherOption {
  left: -3px;
  top: 36px;
  @media #{$media-mobile} {
    left: calc(50% - 7.5px);
  }
}
.form-required {
  padding-left: 0px;
  position: relative;
  &:before {
    content: "*";
    left: -7px;
    position: absolute;
  }
}
#section1 .flexBlock,
#section2 .flexBlock {
  .installation-form-label {
    @media #{$media-mobile} {
      flex-basis: auto;
      max-width: 100%;
    }
  }
}
input[type="radio"]:checked + label,
input[type="radio"]:checked + span {
  font-weight: bold !important;
}
.changeBtn {
  top: -10px;
}
