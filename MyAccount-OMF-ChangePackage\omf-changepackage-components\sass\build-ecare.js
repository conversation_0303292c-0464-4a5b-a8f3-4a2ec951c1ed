#!/usr/bin/env node
var fs = require('fs');
var path = require('path');
var sass = require('node-sass');

if (!fs.existsSync('./dist/css/')){
  fs.mkdirSync('./dist/css/', { recursive: true });
}

sass.render({
	file: './sass/changeflow-bundle.scss',
	includePaths: [ './sass/includes/' ],
	outputStyle: 'compressed',
}, function(error, result) { // node-style callback from v3.0.0 onwards
  if (error) {
    console.log(error.status); // used to be "code" in v2x and below
    console.log(error.column);
    console.log(error.message);
    console.log(error.line);
  } else {
    console.log(result.stats);
	fs.writeFileSync(
		'./dist/css/changeflow-bundle.css',
		result.css.toString()
	);
  }
});

// sass.render({
// 	file: './scss/tv-bundle.scss',
// 	includePaths: [ './scss/includes/' ],
// 	outputStyle: 'compressed',
// }, function(error, result) { // node-style callback from v3.0.0 onwards
//   if (error) {
//     console.log(error.status); // used to be "code" in v2x and below
//     console.log(error.column);
//     console.log(error.message);
//     console.log(error.line);
//   } else {
//     console.log(result.stats);
// 	fs.writeFileSync(
// 		'./core/css/tv-bundle.css',
// 		result.css.toString()
// 	);
//   }
// });

// sass.render({
// 	file: './scss/tv-IE.scss',
// 	includePaths: [ './scss/includes/' ],
// 	outputStyle: 'compressed',
// }, function(error, result) { // node-style callback from v3.0.0 onwards
//   if (error) {
//     console.log(error.status); // used to be "code" in v2x and below
//     console.log(error.column);
//     console.log(error.message);
//     console.log(error.line);
//   } else {
//     console.log(result.stats);
// 	fs.writeFileSync(
// 		'./core/css/tv-IE.css',
// 		result.css.toString()
// 	);
//   }
// });


sass.render({
	file: './sass/overview-tv-bundle.scss',
	includePaths: [ './sass/includes/' ],
	outputStyle: 'compressed',
}, function(error, result) { // node-style callback from v3.0.0 onwards
  if (error) {
    console.log(error.status); // used to be "code" in v2x and below
    console.log(error.column);
    console.log(error.message);
    console.log(error.line);
  } else {
    console.log(result.stats);
	fs.writeFileSync(
		'./dist/css/overview-tv-bundle.css',
		result.css.toString()
	);
  }
});