/*
 * External Epics:
 * collection of general purpose sharable epics
 * to be added to individual widgets whenever
 * required.
 *
 * The implementation is as follows:
 * src/Strore/Store.ts
 *
 * @Injectable
 * export class Store extends BaseStore<IStoreState> {
 *     ...
 *   get middlewares() {
 *     return [
 *       createEpicMiddleware(this.epics.combineEpics()),
 *       ...,
 *       createEpicMiddleware(new LifecycleEpics().combineEpics())
 *     ];
 *   }
 * }
 * NOTE:
 * Middlewares are collected bottom-to-top
 * so, the bottom-most epic will receive the
 * action first, while the top-most -- last
 */
export * from "./Lifecycle";
export * from "./Restrictions";
export * from "./Modals";
