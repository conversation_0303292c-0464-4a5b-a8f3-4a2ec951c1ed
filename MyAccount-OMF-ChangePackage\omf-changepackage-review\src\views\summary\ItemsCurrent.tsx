import * as React from "react";
import { useSelector } from "react-redux";
import { FormattedMessage } from "react-intl";
import { Volt } from "omf-changepackage-components";
import CurrentInternet from "./internet/Current";
import CurrentTV from "./tv/Current";
import AdditionalCharge from "./AdditionalCharge";
import TotalCharge from "./TotalCharge";
import { Spacer } from "./Spacer";
import { getLineOfBusiness, selectors } from "../../utils";

const CurrentItems: React.FunctionComponent<{ isConfirmationStep: boolean }> = ({
  isConfirmationStep
}) => {
  const totalCharge: Volt.IPriceDetail = useSelector(selectors.totalCharge("currentTotal")()),
    additionalCharge: Volt.IPriceDetail = useSelector(selectors.additionalCharge("Current")()),
    TVOffering: Array<Volt.IProductOffering> = useSelector(selectors.productOfferings("Current", "TV")()) || [],
    InternetOffering: Array<Volt.IProductOffering> = useSelector(selectors.productOfferings("Current", "Internet")()) || [],
    renderTV: boolean = TVOffering.length > 0,
    renderInternet: boolean = InternetOffering.length > 0,
    lineOfBusiness = getLineOfBusiness({ renderTV, renderInternet });

  return renderInternet || renderTV ? (
    <div className="review-div-section col-lg-6">
      <div className="flexCol fullWidth">
        <div className="review-div-section">
          <div className="review-panel-body  border-radius-top fullHeight pad-30-left pad-40-right padding-25-xs accss-focus-outline-override-white-bg">
            <div>
              <div className="spacer30 d-none d-sm-block" aria-hidden="true" />
              <div className="spacer25 d-block d-sm-none" aria-hidden="true" />
              <h2 className="txtBlack txtSize22 noMargin differentTextureforHandset virginUltraReg txtUppercase">
                <FormattedMessage id={`CURRENT_${lineOfBusiness}`} />
              </h2>
              <Spacer />
            </div>
            {renderInternet && <CurrentInternet productOfferings={InternetOffering} />}
            {renderTV && <CurrentTV productOfferings={TVOffering} />}
          </div>
        </div>
        <AdditionalCharge {...{ additionalCharge }} />
        <TotalCharge {...{ totalCharge, isNew: false, isTV: renderTV }} />
      </div>
    </div>
  ) : null;
};

export default CurrentItems;
