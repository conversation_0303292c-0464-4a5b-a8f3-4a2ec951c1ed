import { Volt, EWidgetRoute } from "omf-changepackage-components";

export interface IPriceDetail {
    Price: number;
    PriceType: string;
}

export interface IPromotionDetail {
    Description: string;
    PromotionalPrice: IPriceDetail;
    ExpiryDate: string;
    LegalMessage: string;
}
export interface IProductOffering {
    Id: string;
    Name: string;
    ShortDescription: string;
    LongDescription: string;
    RegularPrice: IPriceDetail;
    PromotionDetails: IPromotionDetail;
    ChildOfferings: Array<any>;
    ChannelCount: number;
    DisplayGroupKey: Volt.EDIsplayGroupKey;
}

export interface IAccountDetail {
    displayGroupKey: Volt.EDIsplayGroupKey;
    offerings: Array<IProductOffering>;
}

export interface ITVChannel extends Volt.IProductOffering {
    id: string;
    parents?: Array<Volt.IProductOffering>;
    isMultipleWaysToAdd: boolean;
}

export interface ITVCatalog {
    index: Array<Volt.IProductOffering>;
    offerings: Array<{[key: string]: Array<Volt.IProductOffering>}>;
    channels: Array<ITVChannel>;
}

export interface INavigationItem extends Volt.IDisplayGroupOffering {
    route: EWidgetRoute | string;
    children?: Array<Volt.IDisplayGroupOffering>;
}