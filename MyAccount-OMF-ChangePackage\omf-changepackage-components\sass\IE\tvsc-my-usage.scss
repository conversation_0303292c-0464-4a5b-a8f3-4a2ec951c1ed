@import "mixins";

/*IE11 specific fixes*/

.bell-my-usage-page {
    .bell-usage-table-holder {
        .bell-table-usage>table {
            border-collapse: separate;
            thead {
                th {
                    // Carret
                    &[aria-sort="ascending"],
                    &[aria-sort="descending"],
                    &.sortedAsc,
                    &.sortedDesc {
                        &:after {
                            top: 36px;
                            @media #{$media-tablet} {
                                top: 25px;
                            }
                        }
                    }
                }
            }
        }
    }
    
    .custom-ie-dropdown {
        margin-bottom: -20px;
        position: relative;
    }
}