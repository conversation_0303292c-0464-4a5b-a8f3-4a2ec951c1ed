import { Actions, Context } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { IStoreState } from "../../models";
import { ModalId as ExitModalId } from "../modals/ApplicationLogout";

interface IComponentDispatches {
  onLogoutClick: (payload: any) => void;
}

export const Component: React.FC<IComponentDispatches> = ({ onLogoutClick }) => (
  <Context>
    {({ config: { linkURL } }) => (
      <footer className="accss-focus-outline-override-grey-bg">
        <a id="skipToMain" href="#mainContent" className="skip-to-main-link">
          <FormattedMessage id="Skip to main content" />
        </a>
        <div className="simplified-footer pad-15-top pad-30-top-xs container container-fluid flex flex-justify-space-between flexCol-xs pad-20-left pad-20-right">
          <div className="flex-vCenter">
            <ul className="footer-links flex list-unstyled no-margin flexCol-xs">
              <li className="width-100-percent-xs noBorder">
                <a id="privacy" href={linkURL.privacyURL} className="txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray">
                  <FormattedMessage id="Privacy" />
                </a>
              </li>
              {/* <li className="width-100-percent-xs noBorder">
                            <a href={linkURL.securityURL} className="txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right"><FormattedMessage id="Security" /></a>
                        </li> */}
              <li className="width-100-percent-xs noBorder">
                <a id="legal_context" href={linkURL.legalURL} className="txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray">
                  <FormattedMessage id="Legal" />
                </a>
              </li>
              <li className="width-100-percent-xs">
                <a id="feedback" href={linkURL.feedbackURL} className="txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray">
                  <FormattedMessage id="FEEDBACK" />
                </a>
              </li>
            </ul>
            <div className="spacer15" aria-hidden="true" />
            <div className="txtSize14 txtCenter-xs ">
              <FormattedMessage id="Copyright" />
            </div>
          </div>
          <div className="flex flexCol-xs">
            <span className="spacer30 d-block d-sm-none" aria-hidden="true"></span>
            <button id="footerLogout" onClick={() => onLogoutClick("footerLogout")} className="btn btn-secondary flex middle-align-self line-height-1" type="button">
              <FormattedMessage id="Log out" />
            </button>
            <div className="vSpacer30 hidden-m"></div>
            <span className="spacer30 d-block d-sm-none" aria-hidden="true"></span>
            <div className="width-100-percent-xs txtCenter-xs">
              <img className="img-responsive logo-footer" role="link" tabIndex={0} src={linkURL.entrustIMGURL} alt="Entrust label" />
            </div>
          </div>
        </div>
        <div className="spacer40" aria-hidden="true"></div>
      </footer>
    )}
  </Context>
);

export const Footer = connect<{}, IComponentDispatches>(
  ({}: IStoreState) => ({}),
  (dispatch) => ({
    onLogoutClick: (id: any) => dispatch(Actions.openLightbox({ lightboxId: ExitModalId, data: { relativeId: id } })),
  })
)(Component);
