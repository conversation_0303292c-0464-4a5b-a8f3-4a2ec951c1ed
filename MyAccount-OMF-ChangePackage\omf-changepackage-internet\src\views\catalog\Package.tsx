import { Components, ValueOf, Volt } from "omf-changepackage-components";
import * as React from "react";
import { FormattedDate, FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { IPackage, IStoreState } from "../../models";
import { togglePackageSelection } from "../../store";
// import { Localization } from "../../Localization";

declare function $(q: any): any;

const {
  Visible,
  Currency
} = Components;

// interface IParseCharacteristics {
//     download: Volt.ICharacteristic | undefined;
//     upload: Volt.ICharacteristic | undefined;
//     usage: Volt.ICharacteristic | undefined;
// }

// function ParseCharacteristics(characteristics: Array<Volt.ICharacteristic>): IParseCharacteristics {
//     return {
//         download: characteristics.find(chr => chr.characteristicName === "Download"),
//         upload: characteristics.find(chr => chr.characteristicName === "Upload"),
//         usage: characteristics.find(chr => chr.characteristicName === "Usage"),
//     };
// }

interface IComponentDispatches {
  onPackageClicked: (action: Volt.IHypermediaAction) => void;
}

const Component: React.FC<IPackage & IComponentDispatches> = ({
  id,
  name,
  shortDescription,
  usagePlan,
  // state,
  // type,
  // isSelectable,
  // isCurrent,
  isSelectable,
  isSelected,
  // characteristics,
  regularPrice,
  promotionDetails,
  offeringAction,
  onPackageClicked
}) => {
  const onPackageAction = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    if (isSelected) return;
    if (e.keyCode === undefined || e.keyCode === 32 || e.keyCode === 13) {
      onPackageClicked(offeringAction);
    }
  };
  const [uploadExpanded, ExpandUpload] = React.useState(false);
  const onUploadClick = (e: any) => {
    if ((e.keyCode === undefined || e.keyCode === 13) && e.target.classList.contains("txtUnderline")) {
      ExpandUpload(!uploadExpanded);
    }
  };
  React.useEffect(() => {
    $("#" + id)
      .find("[data-toggle]")
      .addClass("txtUnderline txtBlue pointer accss-text-blue-on-bg-white accss-width-fit-content")
      .attr("tabindex", "0")
      .next()
      .addClass("downloadTray")
      .removeAttr("id");
  });
  // const parsedCharacteristics = ParseCharacteristics(ValueOf(characteristics, undefined, []));
  return <div id={id} className={`virgin-internet-box txtGray margin-15-bottom ${isSelected ? "selected" : ""}`}>
    <div className="flexRow bgWhite border-radius-3 virgin-title-block pad-30 pad-15-left-right-sm accss-focus-outline-override-white-bg">
      <div className="package_ctrl">
        <span id={`CTA_${id}`} className="graphical_ctrl ctrl_radioBtn pointer" onClick={onPackageAction}>
          <input id={`OPT_${id}`} name="internetpackage" checked={isSelected} type="radio" aria-labelledby={`PACKAGE_CTA_${id}`} aria-describedby={`PACKAGE_CTA_DESC_${id}`} aria-checked={isSelected} className="radioBtn-active data-feature" />
          <span className="ctrl_element pointer data-addon-active data-addon-border" />
        </span>
      </div>
      <div className="package-desc fill">
        <div id={`PACKAGE_CTA_${id}`} className="fill pad-15-left content-width valign-top pad-0-xs pointer" onClick={onPackageAction}>
          <h2 className="virginUltraReg txtSize16 floatL txtUppercase no-margin">{name}</h2>
        </div>
        <div className="spacer10 d-none d-sm-block d-md-none clear" aria-hidden="true" />
        <div className="spacer15 clear" aria-hidden="true" />
        <div className="spacer1 bgGrayLight6 clear margin-30-right" aria-hidden="true" />
        <div className="spacer15 hidden-m" aria-hidden="true" />
        <div className="pkg-pull-left neg-margin-left-40-sm flexBlock" id={`PACKAGE_CTA_DESC_${id}`}>
          <div className="flexRow fill flexCol-xs">
            <ul id={`UPLOAD_CTA_${id}`} className={`speed-box1 flexRow flexCol-xs mb-0 pl-0 list-unstyled ${uploadExpanded ? "expanded" : ""}`} onKeyUp={onUploadClick} onClick={onUploadClick} dangerouslySetInnerHTML={{ __html: shortDescription }} />
            <ul className="speed-box2 mb-0 list-unstyled" dangerouslySetInnerHTML={{ __html: usagePlan }} />
            <div className="speed-box3">
              <div className="pad-30-left no-pad-xs margin-10-left-xs">
                <Visible when={ValueOf(promotionDetails, "expiryDate", false)}>
                  <span className="txtSize12 txtBlack block txtBold bgGray2 pad-5 border-radius-3">
                    <FormattedDate value={ValueOf(promotionDetails, "expiryDate", "")} format="yMMMMd" timeZone="UTC">
                      {
                        (expiryDate) => <FormattedMessage id="Your monthly credit expires" values={{ expiryDate }} />
                      }
                    </FormattedDate>
                  </span>
                </Visible>
                <Visible when={ValueOf(promotionDetails, "discountDuration", false)}>
                  <span className="txtSize12 txtBlack block txtBold bgGray2 pad-5 border-radius-3">
                    <FormattedMessage id="Get a credit for months" values={{ credit: Math.abs(ValueOf(promotionDetails, "discountPrice.price", 0)), duration: ValueOf(promotionDetails, "discountDuration", 0) }} />
                  </span>
                </Visible>
                <Visible when={ValueOf(!promotionDetails, undefined, false)}>
                  <div className="spacer15 clear hidden-m" aria-hidden="true" />
                </Visible>
                <div className="price virginUltraReg txtSize40 line-height-1 margin-10-top">
                  <Visible when={ValueOf(promotionDetails, undefined, false)}>
                    <FormattedMessage id="Now" />&nbsp;
                  </Visible>
                  <Currency value={
                    ValueOf(promotionDetails, "?promotionalPrice.price", false) === false
                      ? ValueOf(regularPrice, "price", 0)
                      : ValueOf(promotionDetails, "promotionalPrice.price", 0)
                  } monthly={true} />
                  <Visible when={ValueOf(promotionDetails, undefined, false)}>
                    <p className="txtSize12 txtBlack txtBold sans-serif no-margin">
                      <FormattedMessage id="Current Price" values={ValueOf(regularPrice, undefined, {}) as any} />
                    </p>
                  </Visible>
                </div>
                <Visible when={ValueOf(promotionDetails, undefined, false)}>
                  <p className="txtSize12 txtBlack sans-serif no-margin pad-10-top">
                    {ValueOf(promotionDetails, "legalMessage", <FormattedMessage id="Prices may increase legal" />)}
                  </p>
                </Visible>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>;
};


export const Package = connect<{}, IComponentDispatches, IPackage>(
  ({ }: IStoreState) => ({}),
  dispatch => ({
    onPackageClicked: (action: Volt.IHypermediaAction) => dispatch(togglePackageSelection(action))
  })
)(Component);
