import * as React from "react";
import { connect } from "react-redux";
import { Actions, Components, EWidgetName } from "omf-changepackage-components";
import { IStoreState } from "../models";
import { Header } from "./header";
import { Catalog } from "./catalog";

const {
  RestrictionModal
} = Components;

const {
  errorOccured,
  widgetRenderComplete
} = Actions;

interface IComponentProps {
}

interface IComponentDispatches {
  onErrorEncountered: Function;
  widgetRenderComplete: Function;
}

class Component extends React.Component<IComponentProps & IComponentDispatches> {
  componentDidCatch(err: any) {
    this.props.onErrorEncountered(err);
  }

  componentDidMount() {
    this.props.widgetRenderComplete(EWidgetName.INTERNET);
  }

  render() {
    return <main id="mainContent">
      <Header />
      <div className="spacer30" aria-hidden="true" />
      <Catalog />
      <RestrictionModal id="INTERNET_RESTRICTION_MODAL" />
    </main>;
  }
}

export const Application = connect<IComponentProps, IComponentDispatches>(
  ({  }: IStoreState) => ({  }),
  (dispatch) => ({
    onErrorEncountered: (error: any) => dispatch(errorOccured(error)),
    widgetRenderComplete: () => dispatch(widgetRenderComplete())
  })
)(Component);
