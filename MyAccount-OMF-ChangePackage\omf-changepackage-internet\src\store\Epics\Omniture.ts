import { Injectable } from "bwtk";
import { Epic, combineEpics, ofType } from "redux-observable";
import { EWidgetStatus, Omniture, Actions, ValueOf } from "omf-changepackage-components";
import { filter, mergeMap, catchError , of, Observable } from 'rxjs';

import {
  IStoreState
} from "../../models";

const {
  omniPageLoaded,
  omniPageSubmit
} = Actions;

type InputAction = ReduxActions.Action<any>;
type OutputAction = ReduxActions.Action<any>;
type OmnitureEpic = Epic<InputAction, OutputAction, void, IStoreState>;

@Injectable
export class OmnitureEpics {
  widgetState: EWidgetStatus = EWidgetStatus.INIT;

  combineEpics() {
    return combineEpics(
      this.pageLoadedEpic,
      this.pageSubmitEpic
    );
  }

  /**
     * The only Omniture dependecy for pageload
     * on Internet changepackage page is account details
     * so we wait for those to come from API and then
     * fire the beakon
     * @readonly
     * @private
     * @type {UserAccountEpic}
     * @memberof OmnitureEpics
     */
  private get pageLoadedEpic(): OmnitureEpic {
    return (action$: Observable<InputAction>, state$) =>
      action$.pipe(
        ofType(omniPageLoaded.toString()),
        mergeMap(() => {
          const { accountDetails } = (state$ as any).value;
          const omniture = Omniture.useOmniture();
          omniture.trackFragment({
            id: "InternetPage",
            s_oSS1: "~",
            s_oSS2: "~",
            s_oSS3: "~",
            s_oPGN: "~",
            s_oAPT: {
              actionresult: 1
            },
            s_oPLE: {
              type: Omniture.EMessageType.Information,
              content: ValueOf(accountDetails, "0.Name", "")
            }
          });
          return of();
        }),
        catchError((error: Response) => of())
      ) as Observable<OutputAction>;
  }

  private get pageSubmitEpic(): OmnitureEpic {
    return (action$: Observable<InputAction>, state$) =>
      action$.pipe(
        filter((action) => action.type === omniPageSubmit.toString()),
        mergeMap(() => {
          const { catalog } = (state$ as any).value;
          const omniture = Omniture.useOmniture();
          omniture.trackAction({
            id: "internetPageSubmit",
            s_oAPT: {
              actionId: 647
            },
            s_oBTN: "Continue",
            s_oPRD: catalog
              .filter((pkg: { isSelected: boolean; isCurrent: boolean }) => (pkg.isSelected && !pkg.isCurrent))
              .map(
                (pkg: { name: string }) => ({
                  category: "Internet",
                  name: pkg.name,
                  sku: "",
                  quantity: "1",
                  price: ValueOf<string>(pkg, "regularPrice.price", "0"),
                  promo: ValueOf<string>(pkg, "promotionDetails.promotionalPrice.price", "")
                })
              )
          });
          return of();
        }),
        catchError((error: Response) => of())
      ) as Observable<OutputAction>;
  }
}
