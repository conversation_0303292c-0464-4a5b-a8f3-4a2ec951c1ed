include:
  - project: 'uxp/devops/templates'
    ref: master
    file: 'widget-dependency.yml'

stages:
  - checkmarx-scan
  - upstream

trigger_job_myaccount_omf_changepackage:
  stage: upstream
  rules:
    - if: $CI_COMMIT_BRANCH == "Release"
      when: manual
    - when: always
  variables:
    JOBTRIGGER: $CI_PROJECT_NAME
    BRANCHTRIGGER: ${CI_COMMIT_BRANCH}
  trigger:
    project: uxp/myaccount-omf-changepackage
    branch: ${CI_COMMIT_BRANCH}
