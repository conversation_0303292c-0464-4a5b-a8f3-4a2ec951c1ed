import * as React from "react";
import { OmnitureTracker } from "./Tracker";
export * from "./Tracker";
export declare namespace Omniture {
    interface IOmniture {
        s_oPGN?: string | "~" | IElementReffrence;
        s_oLNG?: string | "~" | IElementReffrence;
        s_oPRV?: string | "~" | IElementReffrence;
        s_oSS1?: string | "~" | IElementReffrence;
        s_oSS2?: string | "~" | IElementReffrence;
        s_oSS3?: string | "~" | IElementReffrence;
        s_oLGS?: boolean;
        s_oSID?: string | "~" | IElementReffrence;
        s_oPTE?: boolean;
        s_oLOB?: string | "~" | IElementReffrence;
        s_oACT?: string | "~" | IElementReffrence;
        s_oMOT?: string | "~" | IElementReffrence;
        s_oBUP?: string | "~" | IElementReffrence;
        s_oMOID?: string | "~" | IElementReffrence;
        s_oIID?: string | "~" | IElementReffrence;
        s_oRID?: string | "~" | IElementReffrence;
        s_oESTD?: string | "~" | IElementReffrence;
        s_oESTT?: string | "~" | IElementReffrence;
        s_oAPT?: "~" | IApplicationState;
        s_oPRM?: string | "~" | IElementReffrence;
        s_oLBC?: string | "~" | IElementReffrence;
        s_oPLE?: "*" | IMessage | IElementReffrence | Array<IMessage | IElementReffrence>;
        s_oARS?: "*" | string | IElementReffrence | Array<string | IElementReffrence>;
        s_oERR_CLASS?: "*" | IErrorClass | IElementReffrence | Array<string | IErrorClass | IElementReffrence>;
        s_oERR_DESC?: "*" | IErrorDescription | IElementReffrence | Array<string | IErrorDescription | IElementReffrence>;
        s_oAJC?: boolean;
        s_oBTN?: string | IElementReffrence;
        s_oEPN?: string | IElementReffrence;
        s_oCOSL?: string | IElementReffrence;
        s_oPRD?: "*" | IProduct | Array<IProduct>;
        s_oPID?: string | IElementReffrence;
        s_oSRT?: string | IElementReffrence;
    }
    interface IProps extends IOmniture {
        id: string;
        rel?: "page" | "fragment" | "component";
        ready?: boolean;
        enabled?: boolean;
        once?: boolean;
        timestamp?: string;
    }
    interface IElementReffrence {
        ref: string;
        maxlength?: number;
        regex?: string;
    }
    function Ref(elementId: string): IElementReffrence;
    interface IApplicationState {
        actionId?: string | number;
        actionresult?: string | number;
        applicationState?: string | number;
    }
    interface IMessage {
        type: EMessageType;
        content: string | IElementReffrence;
        errorCodes?: Array<string>;
    }
    interface IProduct {
        category: string;
        name: string;
        sku: string;
        quantity: string;
        price: string;
        promo: string;
    }
    enum EMessageType {
        Confirmation = "C",
        Information = "I",
        Warning = "W",
        Error = "E"
    }
    enum EErrorType {
        Technical = "T",
        Business = "B",
        Validation = "V"
    }
    enum EApplicationLayer {
        Browser = "BR",
        Frontend = "FE",
        ESB = "ESB",
        Backend = "BE",
        Servicegrid = "SG",
        Cache = "C"
    }
    interface IErrorDescription {
        code: string | number;
        description: string | IElementReffrence;
    }
    interface IErrorClass {
        code: string | number;
        type: EErrorType;
        layer: EApplicationLayer;
    }
    interface IError extends IErrorClass, IErrorDescription {
        ajax?: boolean;
        lightbox?: {
            title?: string | IElementReffrence;
            content?: string | IElementReffrence;
        };
    }
    const Component: React.FC<React.PropsWithChildren<IOmniture>>;
    function useOmniture(): OmnitureTracker;
}
