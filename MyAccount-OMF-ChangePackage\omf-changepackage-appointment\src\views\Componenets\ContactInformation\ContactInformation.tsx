import * as React from "react";
import { useFormContext } from "react-hook-form";
import { useSelector } from "react-redux";
import { IAdditionalDetails, IContactInformation, IStoreState } from "../../../models";
import { EContactMethod } from "../../../models/Enums";
import { autoFormat, emailRegex, filterNumbers, formattedPhoneRegex, getPrimaryValue, mapEnum } from "../../../utils/AppointmentUtils";
import { Checkbox, Fieldset, RadioBtn, TextArea, TextInput } from "../FormElements";
import { Heading, HeadingTags } from "../Header";

export const ContactInformation = () => {
  const contactInformation: IContactInformation | undefined = useSelector((state: IStoreState) => state?.contactInformation);
  const additionalDetails: IAdditionalDetails | undefined = useSelector((state: IStoreState) => state?.additionalDetails);
  const [contactMethod, setContactMethod] = React.useState(EContactMethod.PHONE);
  const { setValue } = useFormContext();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value, name } = e.target;
    // Based on Value
    switch (value) {
      case EContactMethod.PHONE:
      case EContactMethod.EMAIL:
      case EContactMethod.TEXT_MESSAGE:
        setContactMethod(value);
        break;
      default:
        break;
    }

    // Based on Name
    switch (name) {
      case EContactMethod.PHONE + "_LABEL":
      case EContactMethod.TEXT_MESSAGE + "_LABEL":
      case "ADDITIONAL_PHONE_NUMBER":
        setValue(name, autoFormat(value), true);
        // this.props.setError(EContactMethod.PHONE + "_LABEL", "maxLength");
        break;
      case EContactMethod.PHONE + "_EXT":
      case "ADDITIONAL_PHONE_EXT":
        setValue(name, filterNumbers(value), true);
        break;
      case "SUPERINTENDANT_PHONE":
        setValue(name, autoFormat(value), true);
        break;
      case EContactMethod.EMAIL + "_LABEL":
        setValue(name, value, true);
        break;
      default:
        break;
    }
  };

  React.useEffect(() => {
    setContactMethod(contactInformation?.preferredContactMethod ? contactInformation.preferredContactMethod : EContactMethod.PHONE as any);
  }, [contactInformation]);

  const headingProps = {
    tag: HeadingTags.H2,
    additionalClass: "txtSize22 txtSize24-xs",
    content: "CONTACT_INFORMATION"
  };

  return (
    <div className="margin-30-bottom" id="section2">
      <Heading {...headingProps} />
      <span className="spacer10 visible-xs"></span>
      <div className="pad-25-top no-pad-xs">
        <Fieldset
          legend={"PREFERED_METHOD_OF_CONTACT"}
          required={true}
          additionalClass={"flexWrap"}
          accessibleLegend={false}
        >
          <div className="flexCol lineHeight18">
            <div className="spacer15 visible-xs"></div>
            {
              mapEnum(EContactMethod, (item: EContactMethod) =>
                <RadioBtn
                  label={"PREFERED_METHOD_OF_CONTACT"}
                  value={item}
                  handleChange={handleChange}
                  checked={item === contactMethod}
                />)
            }
          </div>
          {
            mapEnum(EContactMethod, (item: EContactMethod) => <TextInput
              requiredInput={contactMethod === item}
              label={item + "_LABEL"}
              containerClass={`sub-option flex-wrap ${item === contactMethod ? "show" : "hide"}`}
              subLabel={item + "_FORMAT"}
              extention={item === EContactMethod.PHONE ? item + "_EXT" : false}
              optionalExtenstion={true}
              requiredPattern={item === EContactMethod.EMAIL ? emailRegex : formattedPhoneRegex}
              value={getPrimaryValue(item, contactInformation)}
              subValue={contactInformation?.primaryPhone?.phoneExtension}
              handleChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange(e)}
            />)
          }
        </Fieldset>
        <Fieldset
          legend={"ADDITIONAL_PHONE_NUMBER"}
          required={false}
          accessibleLegend={true}
        >
          <TextInput
            requiredInput={false}
            label={"ADDITIONAL_PHONE_NUMBER"}
            subLabel={"TELEPHONE_FORMAT"}
            extention={"ADDITIONAL_PHONE_EXT"}
            optionalExtenstion={true}
            requiredPattern={formattedPhoneRegex}
            value={contactInformation?.additionalPhone?.phoneNumber}
            subValue={contactInformation?.additionalPhone?.phoneExtension}
            handleChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange(e)}
          />

          <TextInput
            label={"APPARTMENT"}
            value={additionalDetails?.apartment}
            handleChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange(e)}
          />

          <TextInput
            label={"ENTRY_CODE"}
            value={additionalDetails?.entryCode}
            handleChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange(e)}
          />

          <TextInput
            label={"SUPERINTENDANT_NAME"}
            value={additionalDetails?.superintendantName}
            handleChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange(e)}
          />

          <TextInput
            label={"SUPERINTENDANT_PHONE"}
            requiredPattern={formattedPhoneRegex}
            value={additionalDetails?.superintendantPhone}
            handleChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange(e)}
          />

          <Checkbox
            label={"INFORMED_SUPERINTENDANT"}
            value={"YES"}
            checked={additionalDetails?.informedSuperintendant}
            handleChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange(e)}
          />

          <TextArea
            label={"SPECIAL_INSTRUCTIONS"}
            subLabel={"SPECIAL_INSTRUCTIONS_SUBLABEL"}
            value={additionalDetails?.specialInstructions}
            maxLength={200}
            handleChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange(e)}
          />
        </Fieldset>
      </div>
    </div>
  );
};
