import { CommonServices, ServiceLocator } from "bwtk";
import { Action, createAction } from "redux-actions";
import { EWidgetRoute, EWidgetStatus, ILightboxPayload, Models, Volt } from "../Models";

export namespace Actions {
  /* Widget Life cycle Events */
  export const setWidgetStatus = createAction<EWidgetStatus>("SET_WIDGET_STATUS") as (payload: EWidgetStatus) => Action<EWidgetStatus>;
  export const setWidgetProps = createAction<Models.IBaseWidgetProps | any>("SET_WIDGET_PROPS") as (payload: Models.IBaseWidgetProps | any) => Action<Models.IBaseWidgetProps | any>;
  export const getData = createAction("GET_WIDGET_DATA");

  // Loader Life cycle events
  export const showHideLoader = createAction<boolean | null>("SHOW_HIDE_LOADER") as (payload: boolean | null) => Action<boolean>;
  // Widget Error handeling
  export const errorOccured = createAction<Models.IErrorHandlerProps>("ERROR_OCCURED") as (error: Models.IErrorHandlerProps) => Action<Models.IErrorHandlerProps>;

  // Lightboxes/modals
  export const openLightbox = createAction<string | ILightboxPayload>("OPEN_LIGHTBOX") as (lightboxId: string | ILightboxPayload) => ReduxActions.Action<string | ILightboxPayload>;
  export const closeLightbox = createAction<string>("CLOSE_LIGHTBOX") as (lightboxId: string) => ReduxActions.Action<string>;
  export const setlightboxData = createAction<any>("SET_LIGHTBOX_DATA") as (payload: any) => ReduxActions.Action<any>;

  // Piped actions
  export const broadcastUpdate = createAction<Action<any>>("PIPE_SEND_UPDATE", ((action: Action<any>, delay: number = 0) => { setTimeout(() => ServiceLocator.instance.getService(CommonServices.EventStream).send(action.type, action.payload), delay); return action; }) as any) as (action: Action<any>, delay?: number) => ReduxActions.Action<Action<any>>;
  export const refreshTotals = createAction("REFRESH_TOTALS");
  export const toggleTVCategoriesTray = createAction("TOGGLE_TV_CATEGORIES_TRAY");
  export const setProductConfigurationTotal = createAction<Volt.IProductConfigurationTotal>("SET_PRODUCT_CONFIGURATION_TOTAL") as (totals: Volt.IProductConfigurationTotal) => ReduxActions.Action<Volt.IProductConfigurationTotal>;
  export const continueFlow = createAction<EWidgetRoute>("FLOW_CONTINUE") as (route: EWidgetRoute) => Action<EWidgetRoute>;
  export const handleNav = createAction<boolean>("HANDLE_NAV") as (payload: boolean) => Action<boolean>;

  // Browser routes and history events
  export const onContinue = createAction("HISTORY_ON_CONTINUE");
  export const historyGo = createAction<string>("HISTORY_GO") as (payload: string) => ReduxActions.Action<string>;
  export const historyBack = createAction<any>("HISTORY_BACK") as (payload?: any) => ReduxActions.Action<any>;
  export const historyForward = createAction("HISTORY_FORWARD");
  export const applicationReset = createAction("APPLICATION_RESET");
  export const applicationExit = createAction("APPLICATION_EXIT");
  export const applicationLogout = createAction("APPLICATION_LOGOUT");
  export const setHistoryProvider = createAction<any>("SET_HISTORY_PROVIDER") as (payload: any) => ReduxActions.Action<any>;
  export const setAppointmentVisited = createAction("APPOINTMENT_PAGE_VISITED");
  // Third Party Integration
  export const widgetRenderComplete = createAction("WIDGET_RENDER_COMPLETE");
  // Restrictions
  export const raiseRestriction = createAction<Volt.IRestriction>("RESTRICTION_OCCURRED") as (message: Volt.IRestriction, onComplete?: any) => ReduxActions.Action<Volt.IRestriction>;
  export const acceptRestriction = createAction<Volt.IHypermediaAction>("RESTRICTION_ACCEPTED") as (action: Volt.IHypermediaAction) => ReduxActions.Action<Volt.IHypermediaAction>;
  export const declineRestriction = createAction<Volt.IHypermediaAction>("RESTRICTION_DECLINED") as (action?: Volt.IHypermediaAction) => ReduxActions.Action<Volt.IHypermediaAction>;
  export const finalizeRestriction = createAction<any>("RESTRICTION_CYCLE_COMPLETE") as (data?: any) => ReduxActions.Action<any>;
  export const clearCachedState = createAction<string[]>("CLEAR_CACHED_STATE") as (payload: string[]) => ReduxActions.Action<string[]>;
  // Omniture
  export const omniPageLoaded = createAction<{ name: string, data?: any }>("OMNITURE_PAGE_LOADED", ((name?: string, data?: any) => (name ? { name, data } : undefined)) as any) as (name?: string, data?: any) => ReduxActions.Action<{ name: string, data?: any }>;
  export const omniPageSubmit = createAction<string>("OMNITURE_PAGE_SUBMIT") as (payload?: string) => ReduxActions.Action<string>;
  export const omniModalOpen = createAction<string>("OMNITURE_MODAL_OPEN") as (payload?: string) => ReduxActions.Action<string>;
}
