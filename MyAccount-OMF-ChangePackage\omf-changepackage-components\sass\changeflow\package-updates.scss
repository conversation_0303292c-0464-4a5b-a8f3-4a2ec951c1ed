@import "mixins";

/* Bell package box module
 * 
 * Base box for a package
 * Movie, or series pack
*/

.bell-tv-package.bell-tv-base-pack {
  .bell-tv-package-main {
    .bell-tv-package-controls{
      width: 180px;
      @media #{$media-mobile}{
        width: auto;
      }
    }
    .bell-tv-package-description{
      padding: 30px 40px;
      @media #{$media-mobile} {
        padding: 20px 15px;
      }
      @include display-flex(row, wrap);
      .bell-tv-package-icons{
        img {
          flex-basis: 25%;
          width:15%;
          height: auto;
          padding: 10px;
          align-self: center;
          margin-right: 0; /*- Override -*/
          @media #{$media-tablet} {
            flex-basis: 16.66%;
            padding: 7.5px;
            width:16.66%;
          }
        }
      }
      .bell-tv-package-channels-numbers{
          padding: 0 50px 0 10px;
          @media #{$media-mobile} {
            order: 1;
            padding: 0 25px 0 5px;
          }
      }
      &:after{
        display: none !important; /*- Override -*/
      }
    }
  }
}

/* --- Updates in Filters --- */
.bell-tv-package-filters-row{
  ul.bell-tv-package-filters-nav{
    li{
      &.table-cell.active a{
        color: $virginGray;
        font-weight: bold;
      }
    }
  }
}

/* --- Updates in Channels --- */
.bell-tv-package-footer{
  .bell-tv-channel-picker{
    .bell-tv-channel {
      flex-basis: 16.66%;
      margin: auto; // Overide
      border: none; // Override
      background: none; //override

      @media #{$media-mobile} {
        flex-basis: 33.33%;
        padding: 7.5px;
        width:33.33%;
      }
      .bell-tv-channel-container{
        width: auto;
        position: relative;
        border: 1px solid #D4D4D4;
        padding: 15px;
        background: #FFF;
        .bell-tv-channel-icon {
          img {
            height: auto; //Override
          }
          .bell-tv-channel-badge {
            position: absolute;
            bottom: 60px; 
            right: 0;//override
            left: auto; //override
            top: auto; //override
            width: 100%;
            padding: 0 5px;
            .icon{
              margin-left: 0; //override
            }
          }
        }
      }
    }
  }
}



/* ---- Proposing following to add into base scss with flexbox --- */
.txtSize28{
  font-size: 28px;
}
.txtCurrency sup{
  // font-size: 50%;
}

.order1{
  order: 1;
}
.order2{
  order: 2;
}
.order3{
  order: 3;
}
.order4{
  order: 4;
}
.order5{
  order: 5;
}
.order6{
  order: 6;
}
.order7{
  order: 7;
}
.order8{
  order: 8;
}
.order9{
  order: 9;
}
.order10{
  order: 10;
}

// .bell-tv-package {
//   margin-bottom: 20px;
//   border: 2px solid #d8d8d8;
//   background-color: #fff;
//   label, input {
//     cursor: pointer;
//   }
//   &.disabled {
//     background-color: #F4F4F4;
//     border-color: #F4F4F4;
//     label, input {
//       opacity: 1;
//       cursor: auto;
//     }
//   }
//   &.selected {
//     border: 2px solid #00549a;
//     .bell-tv-package-header {
//       background-color: #00549a;
//       color: #fff;
//     }
//   }
//   hr {
//     border-color: #D4D4D4;
//     margin: 15px 0;
//   }
//   .bell-tv-package-body {
//     padding: 15px;
//     .bell-tv-package-left {
//       padding-left: 30px;
//       @media #{$media-tablet} {
//         padding-left: 35px;
//       }
//       @media #{$media-mobile} {
//         padding-left: unset;
//       }
//     }
//   }
//   .bell-tv-package-footer {
//     @include expandable();
//   }
//   .bell-tv-package-icons img {
//     width: auto;
//     height: 22px;
//     margin-right: 15px;
//   }
// }